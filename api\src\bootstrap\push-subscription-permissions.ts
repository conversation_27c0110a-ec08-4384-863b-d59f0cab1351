export default async ({ strapi }) => {
  // Verificar si el rol público existe
  const publicRole = await strapi
    .query("plugin::users-permissions.role")
    .findOne({ where: { type: "authenticated" } });

  if (!publicRole) {
    return;
  }

  // Definir los permisos para push-subscription
  const permissions = {
    "api::push-subscription.push-subscription": {
      controllers: {
        "push-subscription": {
          create: { enabled: true },
          delete: { enabled: true },
          sendNotification: { enabled: true }
        }
      }
    }
  };

  // Actualizar los permisos
  await strapi.query("plugin::users-permissions.permission").update({
    where: { role: publicRole.id, action: "api::push-subscription.push-subscription.create" },
    data: { enabled: true }
  });

  await strapi.query("plugin::users-permissions.permission").update({
    where: { role: publicRole.id, action: "api::push-subscription.push-subscription.delete" },
    data: { enabled: true }
  });

  await strapi.query("plugin::users-permissions.permission").update({
    where: { role: publicRole.id, action: "api::push-subscription.push-subscription.sendNotification" },
    data: { enabled: true }
  });
};
