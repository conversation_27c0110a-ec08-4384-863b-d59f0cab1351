const path = require("path");
const fs = require("fs");

// Ruta base donde se encuentran las plantillas
const TEMPLATES_DIR = path.join(process.cwd(), "config", "email-templates");

// Función para leer el contenido de una plantilla
const readTemplate = (templateName) => {
  const templatePath = path.join(TEMPLATES_DIR, `${templateName}.html`);
  try {
    return fs.readFileSync(templatePath, "utf8");
  } catch (error) {
    console.error(`Error al leer la plantilla ${templateName}:`, error);
    return null;
  }
};

module.exports = {
  config: {
    // Configuración del proveedor de correo (puedes cambiarlo según necesites)
    provider: "sendmail", // Cambia esto por tu proveedor preferido (sendmail, nodemailer-sendgrid, etc.)
    providerOptions: {
      // Opciones específicas del proveedor
      // Ejemplo para SendGrid:
      // apiKey: process.env.SENDGRID_API_KEY,
    },
    settings: {
      defaultFrom: process.env.SENDGRID_EMAIL || "<EMAIL>",
      defaultReplyTo: process.env.SENDGRID_EMAIL || "<EMAIL>",
      // Configuración de reintentos
      retryAttempts: 3,
      retryDelay: 1000,
    },
  },
  // Plantillas personalizadas
  templates: {
    // Plantilla para restablecimiento de contraseña
    resetPassword: {
      subject: "Restablecer contraseña - CCPALCAN",
      text: "Hola <%= user.username %>, para restablecer tu contraseña, haz clic en este enlace: <%= URL %>?code=<%= TOKEN %>",
      html: readTemplate("reset-password"),
    },
    // Plantilla para confirmación de correo
    emailConfirmation: {
      subject: "Confirma tu correo electrónico - CCPALCAN",
      text: "Hola <%= user.username %>, por favor confirma tu correo electrónico haciendo clic en este enlace: <%= URL %>?confirmation=<%= CODE %>",
      html: readTemplate("email-confirmation"),
    },
    // Plantilla para pago verificado
    paymentVerified: {
      subject: "Pago Verificado - CCPALCAN",
      text: "Hola <%= user.username %>, nos complace informarte que tu pago ha sido verificado y procesado correctamente.",
      html: readTemplate("payment-verified"),
    },
    // Plantilla para pago rechazado
    paymentRejected: {
      subject: "Pago Rechazado - CCPALCAN",
      text: "Hola <%= user.username %>, lamentamos informarte que tu pago ha sido rechazado.",
      html: readTemplate("payment-rejected"),
    },
  },
};
