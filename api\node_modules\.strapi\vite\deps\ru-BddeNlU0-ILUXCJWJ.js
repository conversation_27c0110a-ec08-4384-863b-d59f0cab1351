import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/ru-BddeNlU0.mjs
var ru = {
  "coming-soon": "This content is currently under construction and will be back in a few weeks!",
  "components.Row.open": "Открыть",
  "components.Row.regenerate": "Сгенерировать",
  "containers.HomePage.Block.title": "Версии",
  "containers.HomePage.Button.update": "Обновить",
  "containers.HomePage.PluginHeader.title": "Документация — Настройки",
  "containers.HomePage.PopUpWarning.confirm": "Я понимаю",
  "containers.HomePage.PopUpWarning.message": "Вы уверены что хотите удалить эту версию?",
  "containers.HomePage.copied": "Токен скопирован в буфер обмена",
  "containers.HomePage.form.jwtToken": "Получите ваш JWT токен",
  "containers.HomePage.form.jwtToken.description": "Скопируйте этот токен и используйте его в swagger для выполнения запросов",
  "containers.HomePage.form.password": "Пароль",
  "containers.HomePage.form.password.inputDescription": "Установите пароль для доступа к документации",
  "containers.HomePage.form.restrictedAccess": "Ограниченный доступ",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Сделайте вашу документацию приватной. По умолчанию доступ открыт",
  "containers.HomePage.form.showGeneratedFiles": "Показать сгенерированные файлы",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Полезно, если вы хотите изменить сгенерированную документацию. \nПлагин разделяет файлы на модели и плагины. \nПри включенной опции вам будет проще кастомизировать документацию",
  "error.deleteDoc.versionMissing": "Версии, которую вы пытаетесь удалить, не существует.",
  "error.noVersion": "Необходимо указать версию",
  "error.regenerateDoc": "При генерации документации возникла ошибка",
  "error.regenerateDoc.versionMissing": "Версии, которую вы пытаетесь сгенерировать, не существует",
  "notification.delete.success": "Документация удалена",
  "notification.generate.success": "Документация сгенерирована",
  "notification.update.success": "Настройки успешно обновлены",
  "pages.PluginPage.Button.open": "Открыть документацию",
  "pages.PluginPage.header.description": "Возможности плагина документации",
  "pages.PluginPage.table.generated": "Последняя генерация",
  "pages.PluginPage.table.icon.regenerate": "Сгерерировать {target}",
  "pages.PluginPage.table.icon.show": "Открыть {target}",
  "pages.PluginPage.table.version": "Версия",
  "pages.SettingsPage.header.description": "Настройки плагина документации",
  "pages.SettingsPage.header.save": "Сохранить",
  "pages.SettingsPage.toggle.hint": "Сделать документацию приватной",
  "pages.SettingsPage.toggle.label": "Доступ ограничен",
  "plugin.description.long": "Создайте OpenAPI документацию для удобной работы с вашим API через SWAGGER UI.",
  "plugin.description.short": "Создайте OpenAPI документацию для удобной работы с вашим API через SWAGGER UI.",
  "plugin.name": "Документация"
};
export {
  ru as default
};
//# sourceMappingURL=ru-BddeNlU0-ILUXCJWJ.js.map
