{"version": 3, "sources": ["../../../@strapi/strapi/node_modules/@strapi/email/admin/src/utils/getYupInnerErrors.ts", "../../../@strapi/strapi/node_modules/@strapi/email/admin/src/utils/schema.ts", "../../../@strapi/strapi/node_modules/@strapi/email/admin/src/pages/Settings.tsx"], "sourcesContent": ["import type { MessageDescriptor, PrimitiveType } from 'react-intl';\nimport type { ValidationError } from 'yup';\n\ninterface TranslationMessage extends MessageDescriptor {\n  values?: Record<string, PrimitiveType>;\n}\n\nconst extractValuesFromYupError = (\n  errorType?: string | undefined,\n  errorParams?: Record<string, any> | undefined\n) => {\n  if (!errorType || !errorParams) {\n    return {};\n  }\n\n  return {\n    [errorType]: errorParams[errorType],\n  };\n};\n\nconst getYupInnerErrors = (error: ValidationError) =>\n  (error?.inner || []).reduce<Record<string, TranslationMessage>>((acc, currentError) => {\n    if (currentError.path) {\n      acc[currentError.path.split('[').join('.').split(']').join('')] = {\n        id: currentError.message,\n        defaultMessage: currentError.message,\n        values: extractValuesFromYupError(currentError?.type, currentError?.params),\n      };\n    }\n\n    return acc;\n  }, {});\n\nexport { getYupInnerErrors };\n", "import { translatedErrors } from '@strapi/admin/strapi-admin';\nimport * as yup from 'yup';\n\nexport const schema = yup.object().shape({\n  email: yup.string().email(translatedErrors.email.id).required(translatedErrors.required.id),\n});\n", "import * as React from 'react';\n\nimport { Page, useNotification, useFetchClient, Layouts } from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  Flex,\n  Grid,\n  SingleSelectOption,\n  SingleSelect,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Mail } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useQuery, useMutation } from 'react-query';\nimport { styled } from 'styled-components';\nimport { ValidationError } from 'yup';\n\nimport { PERMISSIONS } from '../constants';\nimport { getYupInnerErrors } from '../utils/getYupInnerErrors';\nimport { schema } from '../utils/schema';\n\nimport type { EmailSettings } from '../../../shared/types';\n\nconst DocumentationLink = styled.a`\n  color: ${({ theme }) => theme.colors.primary600};\n`;\n\ninterface MutationBody {\n  to: string;\n}\n\nexport const ProtectedSettingsPage = () => (\n  <Page.Protect permissions={PERMISSIONS.settings}>\n    <SettingsPage />\n  </Page.Protect>\n);\n\nconst SettingsPage = () => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { get, post } = useFetchClient();\n\n  const [testAddress, setTestAddress] = React.useState('');\n  const [isTestAddressValid, setIsTestAddressValid] = React.useState(false);\n\n  // TODO: I'm not sure how to type this. I think it should be Record<string, TranslationMessage> but that type is defined in the helper-plugin\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const [formErrors, setFormErrors] = React.useState<Record<string, any>>({});\n\n  const { data, isLoading } = useQuery(['email', 'settings'], async () => {\n    const res = await get<EmailSettings>('/email/settings');\n    const {\n      data: { config },\n    } = res;\n\n    return config;\n  });\n\n  const mutation = useMutation<void, Error, MutationBody>(\n    async (body) => {\n      await post('/email/test', body);\n    },\n    {\n      onError() {\n        toggleNotification!({\n          type: 'danger',\n          message: formatMessage(\n            {\n              id: 'email.Settings.email.plugin.notification.test.error',\n              defaultMessage: 'Failed to send a test mail to {to}',\n            },\n            { to: testAddress }\n          ),\n        });\n      },\n      onSuccess() {\n        toggleNotification!({\n          type: 'success',\n          message: formatMessage(\n            {\n              id: 'email.Settings.email.plugin.notification.test.success',\n              defaultMessage: 'Email test succeeded, check the {to} mailbox',\n            },\n            { to: testAddress }\n          ),\n        });\n      },\n      retry: false,\n    }\n  );\n\n  React.useEffect(() => {\n    schema\n      .validate({ email: testAddress }, { abortEarly: false })\n      .then(() => setIsTestAddressValid(true))\n      .catch(() => setIsTestAddressValid(false));\n  }, [testAddress]);\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setTestAddress(() => event.target.value);\n  };\n\n  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n\n    try {\n      await schema.validate({ email: testAddress }, { abortEarly: false });\n    } catch (error) {\n      if (error instanceof ValidationError) {\n        setFormErrors(getYupInnerErrors(error));\n      }\n    }\n\n    mutation.mutate({ to: testAddress });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main labelledBy=\"title\" aria-busy={isLoading || mutation.isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: 'email.Settings.email.plugin.title',\n              defaultMessage: 'Configuration',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        id=\"title\"\n        title={formatMessage({\n          id: 'email.Settings.email.plugin.title',\n          defaultMessage: 'Configuration',\n        })}\n        subtitle={formatMessage({\n          id: 'email.Settings.email.plugin.subTitle',\n          defaultMessage: 'Test the settings for the Email plugin',\n        })}\n      />\n\n      <Layouts.Content>\n        {data && (\n          <form onSubmit={handleSubmit}>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n              <Box\n                background=\"neutral0\"\n                hasRadius\n                shadow=\"filterShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'email.Settings.email.plugin.title.config',\n                        defaultMessage: 'Configuration',\n                      })}\n                    </Typography>\n                    <Typography>\n                      {formatMessage(\n                        {\n                          id: 'email.Settings.email.plugin.text.configuration',\n                          defaultMessage:\n                            'The plugin is configured through the {file} file, checkout this {link} for the documentation.',\n                        },\n                        {\n                          file: './config/plugins.js',\n                          link: (\n                            <DocumentationLink\n                              href=\"https://docs.strapi.io/developer-docs/latest/plugins/email.html\"\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                            >\n                              {formatMessage({\n                                id: 'email.link',\n                                defaultMessage: 'Link',\n                              })}\n                            </DocumentationLink>\n                          ),\n                        }\n                      )}\n                    </Typography>\n                  </Flex>\n\n                  <Grid.Root gap={5}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"shipper-email\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.defaultFrom',\n                            defaultMessage: 'Default sender email',\n                          })}\n                        </Field.Label>\n                        <TextInput\n                          placeholder={formatMessage({\n                            id: 'email.Settings.email.plugin.placeholder.defaultFrom',\n                            defaultMessage: \"ex: Strapi No-Reply '<'<EMAIL>'>'\",\n                          })}\n                          disabled\n                          value={data.settings.defaultFrom}\n                        />\n                      </Field.Root>\n                    </Grid.Item>\n\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"response-email\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.defaultReplyTo',\n                            defaultMessage: 'Default response email',\n                          })}\n                        </Field.Label>\n                        <TextInput\n                          placeholder={formatMessage({\n                            id: 'email.Settings.email.plugin.placeholder.defaultReplyTo',\n                            defaultMessage: `ex: Strapi '<'<EMAIL>'>'`,\n                          })}\n                          disabled\n                          value={data.settings.defaultReplyTo}\n                        />\n                      </Field.Root>\n                    </Grid.Item>\n\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"email-provider\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.provider',\n                            defaultMessage: 'Email provider',\n                          })}\n                        </Field.Label>\n                        <SingleSelect disabled value={data.provider}>\n                          <SingleSelectOption value={data.provider}>\n                            {data.provider}\n                          </SingleSelectOption>\n                        </SingleSelect>\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n              </Box>\n\n              <Flex\n                alignItems=\"stretch\"\n                background=\"neutral0\"\n                direction=\"column\"\n                gap={4}\n                hasRadius\n                shadow=\"filterShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n              >\n                <Typography variant=\"delta\" tag=\"h2\">\n                  {formatMessage({\n                    id: 'email.Settings.email.plugin.title.test',\n                    defaultMessage: 'Test email delivery',\n                  })}\n                </Typography>\n\n                <Grid.Root gap={5}>\n                  <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                    <Field.Root\n                      name=\"test-address\"\n                      error={\n                        formErrors.email?.id &&\n                        formatMessage({\n                          id: `email.${formErrors.email?.id}`,\n                          defaultMessage: 'This is not a valid email',\n                        })\n                      }\n                    >\n                      <Field.Label>\n                        {formatMessage({\n                          id: 'email.Settings.email.plugin.label.testAddress',\n                          defaultMessage: 'Recipient email',\n                        })}\n                      </Field.Label>\n                      <TextInput\n                        onChange={handleChange}\n                        value={testAddress}\n                        placeholder={formatMessage({\n                          id: 'email.Settings.email.plugin.placeholder.testAddress',\n                          defaultMessage: 'ex: <EMAIL>',\n                        })}\n                      />\n                    </Field.Root>\n                  </Grid.Item>\n                  <Grid.Item col={7} s={12} direction=\"column\" alignItems=\"start\">\n                    <Button\n                      loading={mutation.isLoading}\n                      disabled={!isTestAddressValid}\n                      type=\"submit\"\n                      startIcon={<Mail />}\n                    >\n                      {formatMessage({\n                        id: 'email.Settings.email.plugin.button.test-email',\n                        defaultMessage: 'Send test email',\n                      })}\n                    </Button>\n                  </Grid.Item>\n                </Grid.Root>\n              </Flex>\n            </Flex>\n          </form>\n        )}\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,4BAA4B,CAChC,WACA,gBACG;AACC,MAAA,CAAC,aAAa,CAAC,aAAa;AAC9B,WAAO,CAAA;EACT;AAEO,SAAA;IACL,CAAC,SAAS,GAAG,YAAY,SAAS;EAAA;AAEtC;AAEA,IAAM,oBAAoB,CAAC,YACxB,+BAAO,UAAS,CAAA,GAAI,OAA2C,CAAC,KAAK,iBAAiB;AACrF,MAAI,aAAa,MAAM;AACrB,QAAI,aAAa,KAAK,MAAM,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI;MAChE,IAAI,aAAa;MACjB,gBAAgB,aAAa;MAC7B,QAAQ,0BAA0B,6CAAc,MAAM,6CAAc,MAAM;IAAA;EAE9E;AAEO,SAAA;AACT,GAAG,CAAA,CAAE;AC5BA,IAAM,SAAaA,QAAO,EAAE,MAAM;EACvC,OAAW,OAAO,EAAE,MAAM,YAAiB,MAAM,EAAE,EAAE,SAAS,YAAiB,SAAS,EAAE;AAC5F,CAAC;ACqBD,IAAM,oBAAoB,GAAO;WACtB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;AAOpC,IAAA,wBAAwB,UACnC,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,UACrC,cAAC,wBAAA,cAAA,CAAA,CAAa,EAChB,CAAA;AAGF,IAAM,eAAe,MAAM;;AACnB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,KAAK,KAAK,IAAI,eAAe;AAErC,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,EAAE;AACvD,QAAM,CAAC,oBAAoB,qBAAqB,IAAU,eAAS,KAAK;AAIxE,QAAM,CAAC,YAAY,aAAa,IAAU,eAA8B,CAAA,CAAE;AAEpE,QAAA,EAAE,MAAM,UAAU,IAAI,SAAS,CAAC,SAAS,UAAU,GAAG,YAAY;AAChE,UAAA,MAAM,MAAM,IAAmB,iBAAiB;AAChD,UAAA;MACJ,MAAM,EAAE,OAAO;IACb,IAAA;AAEG,WAAA;EAAA,CACR;AAED,QAAM,WAAW;IACf,OAAO,SAAS;AACR,YAAA,KAAK,eAAe,IAAI;IAChC;IACA;MACE,UAAU;AACY,2BAAA;UAClB,MAAM;UACN,SAAS;YACP;cACE,IAAI;cACJ,gBAAgB;YAClB;YACA,EAAE,IAAI,YAAY;UACpB;QAAA,CACD;MACH;MACA,YAAY;AACU,2BAAA;UAClB,MAAM;UACN,SAAS;YACP;cACE,IAAI;cACJ,gBAAgB;YAClB;YACA,EAAE,IAAI,YAAY;UACpB;QAAA,CACD;MACH;MACA,OAAO;IACT;EAAA;AAGF,EAAM,gBAAU,MAAM;AAEjB,WAAA,SAAS,EAAE,OAAO,YAAA,GAAe,EAAE,YAAY,MAAA,CAAO,EACtD,KAAK,MAAM,sBAAsB,IAAI,CAAC,EACtC,MAAM,MAAM,sBAAsB,KAAK,CAAC;EAAA,GAC1C,CAAC,WAAW,CAAC;AAEV,QAAA,eAAe,CAAC,UAA+C;AACpD,mBAAA,MAAM,MAAM,OAAO,KAAK;EAAA;AAGnC,QAAA,eAAe,OAAO,UAA4C;AACtE,UAAM,eAAe;AAEjB,QAAA;AACI,YAAA,OAAO,SAAS,EAAE,OAAO,YAAA,GAAe,EAAE,YAAY,MAAA,CAAO;IAAA,SAC5D,OAAO;AACd,UAAI,iBAAiB,iBAAiB;AACtB,sBAAA,kBAAkB,KAAK,CAAC;MACxC;IACF;AAEA,aAAS,OAAO,EAAE,IAAI,YAAa,CAAA;EAAA;AAGrC,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGE,aAAA,yBAAC,KAAK,MAAL,EAAU,YAAW,SAAQ,aAAW,aAAa,SAAS,WAC7D,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM,cAAc;UAClB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MACH;IAAA,EAAA,CAEJ;QACA;MAAC,QAAQ;MAAR;QACC,IAAG;QACH,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;QAAA,wBAEC,QAAQ,SAAR,EACE,UAAA,YAAA,wBACE,QAAK,EAAA,UAAU,cACd,cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;UAAA;QAAC;QAAA;UACC,YAAW;UACX,WAAS;UACT,QAAO;UACP,YAAY;UACZ,eAAe;UACf,aAAa;UACb,cAAc;UAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;gBAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;kBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;kBAAA,wBACC,YACE,EAAA,UAAA;gBACC;kBACE,IAAI;kBACJ,gBACE;gBACJ;gBACA;kBACE,MAAM;kBACN,UACE;oBAAC;oBAAA;sBACC,MAAK;sBACL,QAAO;sBACP,KAAI;sBAEH,UAAc,cAAA;wBACb,IAAI;wBACJ,gBAAgB;sBAAA,CACjB;oBAAA;kBACH;gBAEJ;cAAA,EAAA,CAEJ;YAAA,EAAA,CACF;gBAEC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;kBAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAC,yBAAA,MAAM,MAAN,EAAW,MAAK,iBACf,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACA;kBAAC;kBAAA;oBACC,aAAa,cAAc;sBACzB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,UAAQ;oBACR,OAAO,KAAK,SAAS;kBAAA;gBACvB;cAAA,EAAA,CACF,EACF,CAAA;kBAAA,wBAEC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA,yBAAC,MAAM,MAAN,EAAW,MAAK,kBACf,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACA;kBAAC;kBAAA;oBACC,aAAa,cAAc;sBACzB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,UAAQ;oBACR,OAAO,KAAK,SAAS;kBAAA;gBACvB;cAAA,EAAA,CACF,EACF,CAAA;kBAAA,wBAEC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA,yBAAC,MAAM,MAAN,EAAW,MAAK,kBACf,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACC,wBAAA,cAAA,EAAa,UAAQ,MAAC,OAAO,KAAK,UACjC,cAAC,wBAAA,oBAAA,EAAmB,OAAO,KAAK,UAC7B,UAAA,KAAK,SACR,CAAA,EAAA,CACF;cAAA,EAAA,CACF,EACF,CAAA;YAAA,EAAA,CACF;UAAA,EAAA,CACF;QAAA;MACF;UAEA;QAAC;QAAA;UACC,YAAW;UACX,YAAW;UACX,WAAU;UACV,KAAK;UACL,WAAS;UACT,QAAO;UACP,YAAY;UACZ,eAAe;UACf,aAAa;UACb,cAAc;UAEd,UAAA;gBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YACjB,CAAA,EAAA,CACH;gBAEC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;kBAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;gBAAC,MAAM;gBAAN;kBACC,MAAK;kBACL,SACE,gBAAW,UAAX,mBAAkB,OAClB,cAAc;oBACZ,IAAI,UAAS,gBAAW,UAAX,mBAAkB,EAAE;oBACjC,gBAAgB;kBAAA,CACjB;kBAGH,UAAA;wBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;sBACb,IAAI;sBACJ,gBAAgB;oBACjB,CAAA,EAAA,CACH;wBACA;sBAAC;sBAAA;wBACC,UAAU;wBACV,OAAO;wBACP,aAAa,cAAc;0BACzB,IAAI;0BACJ,gBAAgB;wBAAA,CACjB;sBAAA;oBACH;kBAAA;gBAAA;cAAA,EAAA,CAEJ;kBACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,SACtD,cAAA;gBAAC;gBAAA;kBACC,SAAS,SAAS;kBAClB,UAAU,CAAC;kBACX,MAAK;kBACL,eAAA,wBAAY,eAAK,CAAA,CAAA;kBAEhB,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;gBAAA;cAAA,EAAA,CAEL;YAAA,EAAA,CACF;UAAA;QAAA;MACF;IAAA,EACF,CAAA,EACF,CAAA,EAAA,CAEJ;EACF,EAAA,CAAA;AAEJ;", "names": ["create"]}