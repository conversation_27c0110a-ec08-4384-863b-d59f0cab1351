import { factories } from "@strapi/strapi";
import OpenAI from "openai";

// Configuración del sistema para el asistente legal
const SYSTEM_PROMPT = `Eres un asistente legal especializado en la Ley 675 de 2001 (Ley de Propiedad Horizontal) de Colombia. 
Tu función es responder preguntas sobre:
- Derechos y deberes de los copropietarios
- Asambleas y juntas de administración
- Administración de la propiedad horizontal
- Cobro de expensas y multas
- Reglamento de propiedad horizontal
- Resolución de conflictos

Sé conciso y claro en tus respuestas. Si no estás seguro de algo, indícalo.`;

export default factories.createCoreController(
  "api::legal-assistant.legal-assistant",
  ({ strapi }) => ({
    async query(ctx) {
      try {
        const { message, context = "" } = ctx.request.body as {
          message: string;
          context?: string;
        };

        if (!message) {
          console.error("Error: Mensaje no proporcionado");
          return ctx.badRequest("El mensaje es requerido");
        }

        // Configuración de OpenAI
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });

        // Crear el mensaje para OpenAI
        const messages = [
          { role: "system" as const, content: SYSTEM_PROMPT },
          { role: "user" as const, content: message },
        ];

        // Si hay contexto, lo agregamos
        if (context) {
          messages.splice(1, 0, {
            role: "system" as const,
            content: `Contexto: ${context}`,
          });
        }

        // Llamar a la API de OpenAI
        const completion = await openai.chat.completions
          .create({
            model: process.env.OPENAI_MODEL || "gpt-3.5-turbo",
            messages,
            temperature: parseFloat(process.env.OPENAI_TEMPERATURE || "0.7"),
            max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || "500"),
          })
          .catch((error) => {
            console.error("Error al llamar a la API de OpenAI:", error);
            throw error;
          });

        const response =
          completion.choices[0]?.message?.content ||
          "No se pudo generar una respuesta";

        // Opcional: Guardar la consulta en la base de datos
        try {
          await strapi.entityService.create(
            "api::legal-assistant.legal-assistant",
            {
              data: {
                query: message,
                response,
                user: ctx.state?.user?.id ? { id: ctx.state.user.id } : null,
                publishedAt: new Date(),
              },
            }
          );
        } catch (dbError) {
          console.error("Error al guardar la consulta:", dbError);
          // No fallar si hay error al guardar, solo registrar
        }

        return { response };
      } catch (error) {
        console.error(
          "Error en el controlador de asistente legal:",
          JSON.stringify(error, null, 2)
        );
        return ctx.internalServerError(
          `Error al procesar la consulta: ${error.message}`
        );
      }
    },
  })
);
