{"version": 3, "file": "index-BDwE0068.mjs", "sources": ["../../admin/src/constants.ts", "../../admin/src/pluginId.ts", "../../admin/src/utils/prefixPluginTranslations.ts", "../../admin/src/index.ts"], "sourcesContent": ["export const PERMISSIONS = {\n  // This permission regards the main component (App) and is used to tell\n  // If the plugin link should be displayed in the menu\n  // And also if the plugin is accessible. This use case is found when a user types the url of the\n  // plugin directly in the browser\n  main: [\n    { action: 'plugin::documentation.read', subject: null },\n    { action: 'plugin::documentation.settings.regenerate', subject: null },\n    { action: 'plugin::documentation.settings.update', subject: null },\n  ],\n  open: [\n    { action: 'plugin::documentation.read', subject: null },\n    { action: 'plugin::documentation.settings.regenerate', subject: null },\n  ],\n  regenerate: [{ action: 'plugin::documentation.settings.regenerate', subject: null }],\n  update: [{ action: 'plugin::documentation.settings.update', subject: null }],\n};\n", "export const pluginId = 'documentation';\n", "const prefixPluginTranslations = (trad: Record<string, string>, pluginId?: string) => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n\n  return Object.keys(trad).reduce(\n    (acc, current) => {\n      acc[`${pluginId}.${current}`] = trad[current];\n\n      return acc;\n    },\n    {} as Record<string, string>\n  );\n};\n\nexport { prefixPluginTranslations };\n", "import { Information } from '@strapi/icons';\n\nimport { PERMISSIONS } from './constants';\nimport { pluginId } from './pluginId';\nimport { prefixPluginTranslations } from './utils/prefixPluginTranslations';\n\n// eslint-disable-next-line import/no-default-export\nexport default {\n  register(app: any) {\n    app.addMenuLink({\n      to: `plugins/${pluginId}`,\n      icon: Information,\n      intlLabel: {\n        id: `${pluginId}.plugin.name`,\n        defaultMessage: 'Documentation',\n      },\n      permissions: PERMISSIONS.main,\n      Component: async () => {\n        const { App } = await import('./pages/App');\n        return App;\n      },\n      position: 9,\n    });\n\n    app.registerPlugin({\n      id: pluginId,\n      name: pluginId,\n    });\n  },\n  bootstrap(app: any) {\n    app.addSettingsLink('global', {\n      intlLabel: {\n        id: `${pluginId}.plugin.name`,\n        defaultMessage: 'Documentation',\n      },\n      id: 'documentation',\n      to: pluginId,\n      Component: async () => {\n        const { SettingsPage } = await import('./pages/Settings');\n        return SettingsPage;\n      },\n      permissions: PERMISSIONS.main,\n    });\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, pluginId),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n"], "names": ["pluginId"], "mappings": ";;;;;;;;;;AAAO,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,MAAM;AAAA,IACJ,EAAE,QAAQ,8BAA8B,SAAS,KAAK;AAAA,IACtD,EAAE,QAAQ,6CAA6C,SAAS,KAAK;AAAA,IACrE,EAAE,QAAQ,yCAAyC,SAAS,KAAK;AAAA,EACnE;AAAA,EACA,MAAM;AAAA,IACJ,EAAE,QAAQ,8BAA8B,SAAS,KAAK;AAAA,IACtD,EAAE,QAAQ,6CAA6C,SAAS,KAAK;AAAA,EACvE;AAAA,EACA,YAAY,CAAC,EAAE,QAAQ,6CAA6C,SAAS,MAAM;AAAA,EACnF,QAAQ,CAAC,EAAE,QAAQ,yCAAyC,SAAS,MAAM;AAC7E;AChBO,MAAM,WAAW;ACAxB,MAAM,2BAA2B,CAAC,MAA8BA,cAAsB;AACpF,MAAI,CAACA,WAAU;AACP,UAAA,IAAI,UAAU,yBAAyB;AAAA,EAC/C;AAEO,SAAA,OAAO,KAAK,IAAI,EAAE;AAAA,IACvB,CAAC,KAAK,YAAY;AAChB,UAAI,GAAGA,SAAQ,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO;AAErC,aAAA;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EAAA;AAEL;ACNA,MAAe,QAAA;AAAA,EACb,SAAS,KAAU;AACjB,QAAI,YAAY;AAAA,MACd,IAAI,WAAW,QAAQ;AAAA,MACvB,MAAM;AAAA,MACN,WAAW;AAAA,QACT,IAAI,GAAG,QAAQ;AAAA,QACf,gBAAgB;AAAA,MAClB;AAAA,MACA,aAAa,YAAY;AAAA,MACzB,WAAW,YAAY;AACrB,cAAM,EAAE,IAAA,IAAQ,MAAM,OAAO,oBAAa;AACnC,eAAA;AAAA,MACT;AAAA,MACA,UAAU;AAAA,IAAA,CACX;AAED,QAAI,eAAe;AAAA,MACjB,IAAI;AAAA,MACJ,MAAM;AAAA,IAAA,CACP;AAAA,EACH;AAAA,EACA,UAAU,KAAU;AAClB,QAAI,gBAAgB,UAAU;AAAA,MAC5B,WAAW;AAAA,QACT,IAAI,GAAG,QAAQ;AAAA,QACf,gBAAgB;AAAA,MAClB;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,WAAW,YAAY;AACrB,cAAM,EAAE,aAAA,IAAiB,MAAM,OAAO,yBAAkB;AACjD,eAAA;AAAA,MACT;AAAA,MACA,aAAa,YAAY;AAAA,IAAA,CAC1B;AAAA,EACH;AAAA,EACA,MAAM,cAAc,EAAE,WAAkC;AAChD,UAAA,gBAAgB,MAAM,QAAQ;AAAA,MAClC,QAAQ,IAAI,CAAC,WAAW;AACf,eAAA,qCAA+B,uBAAA,OAAA,EAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,6BAAA,MAAA,OAAA,sBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,GAAA,+BAAA,MAAA,OAAA,wBAAA,GAAA,0BAAA,MAAA,OAAA,mBAAA,EAAA,CAAA,GAAA,kBAAA,MAAA,OAAA,EACnC,KAAK,CAAC,EAAE,SAAS,KAAA,MAAW;AACpB,iBAAA;AAAA,YACL,MAAM,yBAAyB,MAAM,QAAQ;AAAA,YAC7C;AAAA,UAAA;AAAA,QACF,CACD,EACA,MAAM,MAAM;AACJ,iBAAA;AAAA,YACL,MAAM,CAAC;AAAA,YACP;AAAA,UAAA;AAAA,QACF,CACD;AAAA,MAAA,CACJ;AAAA,IAAA;AAGI,WAAA,QAAQ,QAAQ,aAAa;AAAA,EACtC;AACF;"}