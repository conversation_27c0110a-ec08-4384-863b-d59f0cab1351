{"version": 3, "file": "index.js", "sources": ["../../server/src/utils/index.ts", "../../server/src/bootstrap.ts", "../../server/src/controllers/validate-locale-creation.ts", "../../server/src/graphql.ts", "../../server/src/register.ts", "../../server/src/content-types/locale/index.ts", "../../server/src/content-types/index.ts", "../../server/src/services/permissions/actions.ts", "../../server/src/services/permissions/sections-builder.ts", "../../server/src/services/permissions/engine.ts", "../../server/src/services/permissions.ts", "../../server/src/services/metrics.ts", "../../server/src/services/localizations.ts", "../../server/src/constants/index.ts", "../../server/src/services/locales.ts", "../../server/src/services/iso-locales.ts", "../../server/src/services/content-types.ts", "../../server/src/services/index.ts", "../../server/src/routes/admin.ts", "../../server/src/routes/content-api.ts", "../../server/src/routes/index.ts", "../../server/src/validation/locales.ts", "../../server/src/domain/locale.ts", "../../server/src/controllers/locales.ts", "../../server/src/validation/content-types.ts", "../../server/src/controllers/content-types.ts", "../../server/src/controllers/iso-locales.ts", "../../server/src/controllers/index.ts", "../../server/src/index.ts"], "sourcesContent": ["import type { LocaleService } from '../services/locales';\nimport type { PermissionsService } from '../services/permissions';\nimport type { ContentTypesService } from '../services/content-types';\nimport type { MetricsService } from '../services/metrics';\nimport type { ISOLocalesService } from '../services/iso-locales';\nimport type { LocalizationsService } from '../services/localizations';\n\ntype S = {\n  permissions: PermissionsService;\n  metrics: MetricsService;\n  locales: LocaleService;\n  localizations: LocalizationsService;\n  ['iso-locales']: ISOLocalesService;\n  ['content-types']: ContentTypesService;\n};\n\nconst getCoreStore = () => {\n  return strapi.store({ type: 'plugin', name: 'i18n' });\n};\n\n// retrieve a local service\nconst getService = <T extends keyof S>(name: T): ReturnType<S[T]> => {\n  return strapi.plugin('i18n').service(name);\n};\n\nexport { getService, getCoreStore };\n", "import type { Schema } from '@strapi/types';\nimport { getService } from './utils';\n\nconst registerModelsHooks = () => {\n  strapi.db.lifecycles.subscribe({\n    models: ['plugin::i18n.locale'],\n\n    async afterCreate() {\n      await getService('permissions').actions.syncSuperAdminPermissionsWithLocales();\n    },\n\n    async afterDelete() {\n      await getService('permissions').actions.syncSuperAdminPermissionsWithLocales();\n    },\n  });\n\n  strapi.documents.use(async (context, next) => {\n    const schema: Schema.ContentType = context.contentType;\n\n    if (!['create', 'update', 'discardDraft', 'publish'].includes(context.action)) {\n      return next();\n    }\n\n    if (!getService('content-types').isLocalizedContentType(schema)) {\n      return next();\n    }\n\n    // Build a populate array for all non localized fields within the schema\n    const { getNestedPopulateOfNonLocalizedAttributes } = getService('content-types');\n\n    const attributesToPopulate = getNestedPopulateOfNonLocalizedAttributes(schema.uid);\n\n    // Get the result of the document service action\n    const result = (await next()) as any;\n\n    // We may not have received a result with everything populated that we need\n    // Use the id and populate built from non localized fields to get the full\n    // result\n    let resultID;\n    // TODO: fix bug where an empty array can be returned\n    if (Array.isArray(result?.entries) && result.entries[0]?.id) {\n      resultID = result.entries[0].id;\n    } else if (result?.id) {\n      resultID = result.id;\n    } else {\n      return result;\n    }\n\n    if (attributesToPopulate.length > 0) {\n      const populatedResult = await strapi.db\n        .query(schema.uid)\n        .findOne({ where: { id: resultID }, populate: attributesToPopulate });\n\n      await getService('localizations').syncNonLocalizedAttributes(populatedResult, schema);\n    }\n\n    return result;\n  });\n};\n\nexport default async () => {\n  const { sendDidInitializeEvent } = getService('metrics');\n  const { initDefaultLocale } = getService('locales');\n  const { sectionsBuilder, actions, engine } = getService('permissions');\n\n  // Data\n  await initDefaultLocale();\n\n  // Sections Builder\n  sectionsBuilder.registerLocalesPropertyHandler();\n\n  // Actions\n  await actions.registerI18nActions();\n  actions.registerI18nActionsHooks();\n  actions.updateActionsProperties();\n\n  // Engine/Permissions\n  engine.registerI18nPermissionsHandlers();\n\n  // Hooks & Models\n  registerModelsHooks();\n\n  sendDidInitializeEvent();\n};\n", "import { get } from 'lodash/fp';\nimport { errors } from '@strapi/utils';\nimport type { Core, Struct } from '@strapi/types';\nimport { getService } from '../utils';\n\nconst { ApplicationError } = errors;\n\n// TODO: v5 if implemented in the CM => delete this middleware\nconst validateLocaleCreation: Core.MiddlewareHandler = async (ctx, next) => {\n  const { model } = ctx.params;\n  const { query } = ctx.request;\n\n  // Prevent empty body\n  if (!ctx.request.body) {\n    ctx.request.body = {};\n  }\n\n  const body = ctx.request.body as any;\n\n  const { getValidLocale, isLocalizedContentType } = getService('content-types');\n\n  const modelDef = strapi.getModel(model) as Struct.ContentTypeSchema;\n\n  if (!isLocalizedContentType(modelDef)) {\n    return next();\n  }\n\n  // Prevent empty string locale\n  const locale = get('locale', query) || get('locale', body) || undefined;\n\n  // cleanup to avoid creating duplicates in single types\n  ctx.request.query = {};\n\n  let entityLocale;\n  try {\n    entityLocale = await getValidLocale(locale);\n  } catch (e) {\n    throw new ApplicationError(\"This locale doesn't exist\");\n  }\n\n  body.locale = entityLocale;\n\n  if (modelDef.kind === 'singleType') {\n    const entity = await strapi.entityService.findMany(modelDef.uid, {\n      locale: entityLocale,\n    } as any); // TODO: add this type to entityService\n\n    ctx.request.query.locale = body.locale;\n\n    // updating\n    if (entity) {\n      return next();\n    }\n  }\n\n  return next();\n};\n\nexport default validateLocaleCreation;\n", "import { propEq, identity } from 'lodash/fp';\nimport { errors } from '@strapi/utils';\nimport type { Core } from '@strapi/types';\n\nconst { ValidationError } = errors;\n\nconst LOCALE_SCALAR_TYPENAME = 'I18NLocaleCode';\nconst LOCALE_ARG_PLUGIN_NAME = 'I18NLocaleArg';\n\nexport default ({ strapi }: { strapi: Core.Strapi }) => ({\n  register() {\n    const { service: getGraphQLService } = strapi.plugin('graphql');\n    const { service: getI18NService } = strapi.plugin('i18n');\n\n    const { isLocalizedContentType } = getI18NService('content-types');\n\n    const extensionService = getGraphQLService('extension');\n\n    extensionService.shadowCRUD('plugin::i18n.locale').disableMutations();\n\n    // Disable unwanted fields for localized content types\n    Object.entries(strapi.contentTypes).forEach(([uid, ct]) => {\n      if (isLocalizedContentType(ct)) {\n        // Disable locale field in localized inputs\n        extensionService.shadowCRUD(uid).field('locale').disableInput();\n\n        // Disable localizations field in localized inputs\n        extensionService.shadowCRUD(uid).field('localizations').disableInput();\n      }\n    });\n\n    extensionService.use(({ nexus, typeRegistry }: any) => {\n      const i18nLocaleArgPlugin = getI18nLocaleArgPlugin({ nexus, typeRegistry });\n      const i18nLocaleScalar = getLocaleScalar({ nexus });\n      return {\n        plugins: [i18nLocaleArgPlugin],\n        types: [i18nLocaleScalar],\n\n        resolversConfig: {\n          // Modify the default scope associated to find and findOne locale queries to match the actual action name\n          'Query.i18NLocale': { auth: { scope: 'plugin::i18n.locales.listLocales' } },\n          'Query.i18NLocales': { auth: { scope: 'plugin::i18n.locales.listLocales' } },\n        },\n      };\n    });\n  },\n});\n\nconst getLocaleScalar = ({ nexus }: any) => {\n  const { service: getI18NService } = strapi.plugin('i18n');\n\n  const locales = getI18NService('iso-locales').getIsoLocales();\n\n  return nexus.scalarType({\n    name: LOCALE_SCALAR_TYPENAME,\n\n    description: 'A string used to identify an i18n locale',\n\n    serialize: identity,\n    parseValue: identity,\n\n    parseLiteral(ast: any) {\n      if (ast.kind !== 'StringValue') {\n        throw new ValidationError('Locale cannot represent non string type');\n      }\n\n      const isValidLocale = ast.value === '*' || locales.find(propEq('code', ast.value));\n\n      if (!isValidLocale) {\n        throw new ValidationError('Unknown locale supplied');\n      }\n\n      return ast.value;\n    },\n  });\n};\n\nconst getI18nLocaleArgPlugin = ({ nexus, typeRegistry }: any) => {\n  const { service: getI18NService } = strapi.plugin('i18n');\n\n  const { isLocalizedContentType } = getI18NService('content-types');\n\n  return nexus.plugin({\n    name: LOCALE_ARG_PLUGIN_NAME,\n\n    onAddOutputField(config: any) {\n      // Add the locale arg to the queries on localized CTs\n\n      const { parentType } = config;\n\n      // Only target queries or mutations\n      if (parentType !== 'Query' && parentType !== 'Mutation') {\n        return;\n      }\n\n      let contentType;\n\n      if (config?.extensions?.strapi?.contentType) {\n        contentType = config.extensions.strapi.contentType;\n      } else {\n        const registryType = typeRegistry.get(config.type);\n\n        if (!registryType) {\n          return;\n        }\n\n        contentType = registryType.config.contentType;\n      }\n\n      // Ignore non-localized content types\n      if (!isLocalizedContentType(contentType)) {\n        return;\n      }\n\n      if (!config.args) {\n        config.args = {};\n      }\n\n      config.args.locale = nexus.arg({\n        type: LOCALE_SCALAR_TYPENAME,\n        description: 'The locale to use for the query',\n      });\n    },\n  });\n};\n", "import _ from 'lodash';\nimport type { Core } from '@strapi/types';\n\nimport validateLocaleCreation from './controllers/validate-locale-creation';\nimport graphqlProvider from './graphql';\n\nexport default ({ strapi }: { strapi: Core.Strapi }) => {\n  extendContentTypes(strapi);\n  addContentManagerLocaleMiddleware(strapi);\n};\n\n// TODO: v5 if implemented in the CM => delete this middleware\n/**\n * Adds middleware on CM creation routes to use i18n locale passed in a specific param\n * @param {Strapi} strapi\n */\nconst addContentManagerLocaleMiddleware = (strapi: Core.Strapi) => {\n  strapi.server.router.use('/content-manager/collection-types/:model', (ctx, next) => {\n    if (ctx.method === 'POST' || ctx.method === 'PUT') {\n      return validateLocaleCreation(ctx, next);\n    }\n\n    return next();\n  });\n\n  strapi.server.router.use('/content-manager/single-types/:model', (ctx, next) => {\n    if (ctx.method === 'POST' || ctx.method === 'PUT') {\n      return validateLocaleCreation(ctx, next);\n    }\n\n    return next();\n  });\n};\n\n/**\n * Adds locale and localization fields to all content types\n * Even if content type is not localized, it will have these fields\n * @param {Strapi} strapi\n */\nconst extendContentTypes = (strapi: Core.Strapi) => {\n  Object.values(strapi.contentTypes).forEach((contentType) => {\n    const { attributes } = contentType;\n\n    _.set(attributes, 'locale', {\n      writable: true,\n      private: false,\n      configurable: false,\n      visible: false,\n      type: 'string',\n    });\n\n    _.set(attributes, 'localizations', {\n      type: 'relation',\n      relation: 'oneToMany',\n      target: contentType.uid,\n      writable: false,\n      private: false,\n      configurable: false,\n      visible: false,\n      unstable_virtual: true,\n      joinColumn: {\n        name: 'document_id',\n        referencedColumn: 'document_id',\n        referencedTable: strapi.db.metadata.identifiers.getTableName(contentType.collectionName!),\n        // ensure the population will not include the results we already loaded\n        on({ results }: { results: any[] }) {\n          return {\n            id: {\n              $notIn: results.map((r) => r.id),\n            },\n          };\n        },\n      },\n    });\n  });\n\n  if (strapi.plugin('graphql')) {\n    // eslint-disable-next-line @typescript-eslint/no-var-requires\n    graphqlProvider({ strapi }).register();\n  }\n};\n", "import schema from './schema.json';\n\nexport default {\n  schema,\n};\n", "import locale from './locale';\n\nexport default {\n  locale,\n};\n", "import { isArray, getOr, prop } from 'lodash/fp';\nimport { getService } from '../../utils';\n\nconst actions = [\n  {\n    section: 'settings',\n    category: 'Internationalization',\n    subCategory: 'Locales',\n    pluginName: 'i18n',\n    displayName: 'Create',\n    uid: 'locale.create',\n  },\n  {\n    section: 'settings',\n    category: 'Internationalization',\n    subCategory: 'Locales',\n    pluginName: 'i18n',\n    displayName: 'Read',\n    uid: 'locale.read',\n    aliases: [\n      { actionId: 'plugin::content-manager.explorer.read', subjects: ['plugin::i18n.locale'] },\n    ],\n  },\n  {\n    section: 'settings',\n    category: 'Internationalization',\n    subCategory: 'Locales',\n    pluginName: 'i18n',\n    displayName: 'Update',\n    uid: 'locale.update',\n  },\n  {\n    section: 'settings',\n    category: 'Internationalization',\n    subCategory: 'Locales',\n    pluginName: 'i18n',\n    displayName: 'Delete',\n    uid: 'locale.delete',\n  },\n];\n\nconst addLocalesPropertyIfNeeded = ({ value: action }: any) => {\n  const {\n    section,\n    options: { applyToProperties },\n  } = action;\n\n  // Only add the locales property to contentTypes' actions\n  if (section !== 'contentTypes') {\n    return;\n  }\n\n  // If the 'locales' property is already declared within the applyToProperties array, then ignore the next steps\n  if (isArray(applyToProperties) && applyToProperties.includes('locales')) {\n    return;\n  }\n\n  // Add the 'locales' property to the applyToProperties array (create it if necessary)\n  action.options.applyToProperties = isArray(applyToProperties)\n    ? applyToProperties.concat('locales')\n    : ['locales'];\n};\n\nconst shouldApplyLocalesPropertyToSubject = ({ property, subject }: any) => {\n  if (property === 'locales') {\n    const model = strapi.getModel(subject);\n\n    return getService('content-types').isLocalizedContentType(model);\n  }\n\n  return true;\n};\n\nconst addAllLocalesToPermissions = async (permissions: any) => {\n  const { actionProvider } = strapi.service('admin::permission');\n  const { find: findAllLocales } = getService('locales');\n\n  const allLocales = await findAllLocales();\n  const allLocalesCode = allLocales.map(prop('code'));\n\n  return Promise.all(\n    permissions.map(async (permission: any) => {\n      const { action, subject } = permission;\n\n      const appliesToLocalesProperty = await actionProvider.appliesToProperty(\n        'locales',\n        action,\n        subject\n      );\n\n      if (!appliesToLocalesProperty) {\n        return permission;\n      }\n\n      const oldPermissionProperties = getOr({}, 'properties', permission);\n\n      return { ...permission, properties: { ...oldPermissionProperties, locales: allLocalesCode } };\n    })\n  );\n};\n\nconst syncSuperAdminPermissionsWithLocales = async () => {\n  const roleService = strapi.service('admin::role');\n  const permissionService = strapi.service('admin::permission');\n\n  const superAdminRole = await roleService.getSuperAdmin();\n\n  if (!superAdminRole) {\n    return;\n  }\n\n  const superAdminPermissions = await permissionService.findMany({\n    where: {\n      role: {\n        id: superAdminRole.id,\n      },\n    },\n  });\n\n  const newSuperAdminPermissions = await addAllLocalesToPermissions(superAdminPermissions);\n\n  await roleService.assignPermissions(superAdminRole.id, newSuperAdminPermissions);\n};\n\nconst registerI18nActions = async () => {\n  const { actionProvider } = strapi.service('admin::permission');\n\n  await actionProvider.registerMany(actions);\n};\n\nconst registerI18nActionsHooks = () => {\n  const { actionProvider } = strapi.service('admin::permission');\n  const { hooks } = strapi.service('admin::role');\n\n  actionProvider.hooks.appliesPropertyToSubject.register(shouldApplyLocalesPropertyToSubject);\n  hooks.willResetSuperAdminPermissions.register(addAllLocalesToPermissions);\n};\n\nconst updateActionsProperties = () => {\n  const { actionProvider } = strapi.service('admin::permission');\n\n  // Register the transformation for every new action\n  actionProvider.hooks.willRegister.register(addLocalesPropertyIfNeeded);\n\n  // Handle already registered actions\n  actionProvider.values().forEach((action: any) => addLocalesPropertyIfNeeded({ value: action }));\n};\n\nexport default {\n  actions,\n  registerI18nActions,\n  registerI18nActionsHooks,\n  updateActionsProperties,\n  syncSuperAdminPermissionsWithLocales,\n};\n", "import { isEmpty } from 'lodash/fp';\n\nimport { getService } from '../../utils';\n\n/**\n * <PERSON><PERSON> for the permissions layout (sections builder)\n * Adds the locales property to the subjects\n * @param {Action} action\n * @param {ContentTypesSection} section\n * @return {Promise<void>}\n */\nconst localesPropertyHandler = async ({ action, section }: any) => {\n  const { actionProvider } = strapi.service('admin::permission');\n\n  const locales = await getService('locales').find();\n\n  // Do not add the locales property if there is none registered\n  if (isEmpty(locales)) {\n    return;\n  }\n\n  for (const subject of section.subjects) {\n    const applies = await actionProvider.appliesToProperty('locales', action.actionId, subject.uid);\n    const hasLocalesProperty = subject.properties.find(\n      (property: any) => property.value === 'locales'\n    );\n\n    if (applies && !hasLocalesProperty) {\n      subject.properties.push({\n        label: 'Locales',\n        value: 'locales',\n        children: locales.map(({ name, code }: any) => ({ label: name || code, value: code })),\n      });\n    }\n  }\n};\n\nconst registerLocalesPropertyHandler = () => {\n  const { sectionsBuilder } = strapi.service('admin::permission');\n\n  sectionsBuilder.addHandler('singleTypes', localesPropertyHandler);\n  sectionsBuilder.addHandler('collectionTypes', localesPropertyHandler);\n};\n\nexport default {\n  localesPropertyHandler,\n  registerLocalesPropertyHandler,\n};\n", "import { getService } from '../../utils';\n\n/**\n * @typedef {object} WillRegisterPermissionContext\n * @property {Permission} permission\n * @property {object} user\n * @property {object} condition\n */\n\n/**\n * Locales property handler for the permission engine\n * Add the has-locale-access condition if the locales property is defined\n * @param {WillRegisterPermissionContext} context\n */\nconst willRegisterPermission = (context: any) => {\n  const { permission, condition, user } = context;\n  const { subject, properties } = permission;\n\n  const isSuperAdmin = strapi.service('admin::role').hasSuperAdminRole(user);\n\n  if (isSuperAdmin) {\n    return;\n  }\n\n  const { locales } = properties || {};\n  const { isLocalizedContentType } = getService('content-types');\n\n  // If there is no subject defined, ignore the permission\n  if (!subject) {\n    return;\n  }\n\n  const ct = strapi.contentTypes[subject];\n\n  // If the subject exists but isn't localized, ignore the permission\n  if (!isLocalizedContentType(ct)) {\n    return;\n  }\n\n  // If the subject is localized but the locales property is null (access to all locales), ignore the permission\n  if (locales === null) {\n    return;\n  }\n\n  condition.and({\n    locale: {\n      $in: locales || [],\n    },\n  });\n};\n\nconst registerI18nPermissionsHandlers = () => {\n  const { engine } = strapi.service('admin::permission');\n\n  engine.hooks['before-register.permission'].register(willRegisterPermission);\n};\n\nexport default {\n  willRegisterPermission,\n  registerI18nPermissionsHandlers,\n};\n", "import i18nActionsService from './permissions/actions';\nimport sectionsBuilderService from './permissions/sections-builder';\nimport engineService from './permissions/engine';\n\nconst permissions = () => ({\n  actions: i18nActionsService,\n  sectionsBuilder: sectionsBuilderService,\n  engine: engineService,\n});\n\ntype PermissionsService = typeof permissions;\n\nexport default permissions;\nexport type { PermissionsService };\n", "import { reduce } from 'lodash/fp';\nimport { getService } from '../utils';\n\nconst sendDidInitializeEvent = async () => {\n  const { isLocalizedContentType } = getService('content-types');\n\n  // TODO: V5: This event should be renamed numberOfContentTypes in V5 as the name is already taken to describe the number of content types using i18n.\n  const numberOfContentTypes = reduce(\n    (sum, contentType) => (isLocalizedContentType(contentType) ? sum + 1 : sum),\n    0\n  )(strapi.contentTypes as any);\n\n  await strapi.telemetry.send('didInitializeI18n', { groupProperties: { numberOfContentTypes } });\n};\n\nconst sendDidUpdateI18nLocalesEvent = async () => {\n  const numberOfLocales = await getService('locales').count();\n\n  await strapi.telemetry.send('didUpdateI18nLocales', {\n    groupProperties: { numberOfLocales },\n  });\n};\n\nconst metrics = () => ({\n  sendDidInitializeEvent,\n  sendDidUpdateI18nLocalesEvent,\n});\n\ntype MetricsService = typeof metrics;\n\nexport default metrics;\nexport type { MetricsService };\n", "import { cloneDeep, isEmpty } from 'lodash/fp';\n\nimport { type Schema } from '@strapi/types';\nimport { async } from '@strapi/utils';\nimport { getService } from '../utils';\n\n/**\n * Update non localized fields of all the related localizations of an entry with the entry values\n */\nconst syncNonLocalizedAttributes = async (sourceEntry: any, model: Schema.ContentType) => {\n  const { copyNonLocalizedAttributes } = getService('content-types');\n\n  const nonLocalizedAttributes = copyNonLocalizedAttributes(model, sourceEntry);\n  if (isEmpty(nonLocalizedAttributes)) {\n    return;\n  }\n\n  const uid = model.uid;\n  const documentId = sourceEntry.documentId;\n  const locale = sourceEntry.locale;\n  const status = sourceEntry?.publishedAt ? 'published' : 'draft';\n\n  // Find all the entries that need to be updated\n  // this is every other entry of the document in the same status but a different locale\n  const localeEntriesToUpdate = await strapi.db.query(uid).findMany({\n    where: {\n      documentId,\n      publishedAt: status === 'published' ? { $ne: null } : null,\n      locale: { $ne: locale },\n    },\n    select: ['locale', 'id'],\n  });\n\n  const entryData = await strapi.documents(uid).omitComponentData(nonLocalizedAttributes);\n\n  await async.map(localeEntriesToUpdate, async (entry: any) => {\n    const transformedData = await strapi.documents.utils.transformData(\n      cloneDeep(nonLocalizedAttributes),\n      {\n        uid,\n        status,\n        locale: entry.locale,\n        allowMissingId: true,\n      }\n    );\n\n    // Update or create non localized components for the entry\n    const componentData = await strapi\n      .documents(uid)\n      .updateComponents(entry, transformedData as any);\n\n    // Update every other locale entry of this documentId in the same status\n    await strapi.db.query(uid).update({\n      where: {\n        documentId,\n        publishedAt: status === 'published' ? { $ne: null } : null,\n        locale: { $eq: entry.locale },\n      },\n      // The data we send to the update function is the entry data merged with\n      // the updated component data\n      data: Object.assign(cloneDeep(entryData), componentData),\n    });\n  });\n};\n\nconst localizations = () => ({\n  syncNonLocalizedAttributes,\n});\n\ntype LocalizationsService = typeof localizations;\n\nexport default localizations;\nexport type { LocalizationsService };\n", "import isoLocales from './iso-locales.json';\n\n/**\n * Returns the default locale based either on env var or english\n * @returns {string}\n */\nconst getInitLocale = () => {\n  const envLocaleCode = process.env.STRAPI_PLUGIN_I18N_INIT_LOCALE_CODE;\n\n  if (envLocaleCode) {\n    const matchingLocale = isoLocales.find(({ code }) => code === envLocaleCode);\n\n    if (!matchingLocale) {\n      throw new Error(\n        'Unknown locale code provided in the environment variable STRAPI_PLUGIN_I18N_INIT_LOCALE_CODE'\n      );\n    }\n\n    return { ...matchingLocale };\n  }\n\n  return {\n    code: 'en',\n    name: 'English (en)',\n  };\n};\n\nconst DEFAULT_LOCALE = getInitLocale();\n\nexport { isoLocales, DEFAULT_LOCALE, getInitLocale };\n", "import { isNil } from 'lodash/fp';\nimport { DEFAULT_LOCALE } from '../constants';\nimport { getService, getCoreStore } from '../utils';\n\nconst find = (params: any = {}) =>\n  strapi.db.query('plugin::i18n.locale').findMany({ where: params });\n\nconst findById = (id: any) => strapi.db.query('plugin::i18n.locale').findOne({ where: { id } });\n\nconst findByCode = (code: any) =>\n  strapi.db.query('plugin::i18n.locale').findOne({ where: { code } });\n\nconst count = (params: any = {}) => strapi.db.query('plugin::i18n.locale').count({ where: params });\n\nconst create = async (locale: any) => {\n  const result = await strapi.db.query('plugin::i18n.locale').create({ data: locale });\n\n  getService('metrics').sendDidUpdateI18nLocalesEvent();\n\n  return result;\n};\n\nconst update = async (params: any, updates: any) => {\n  const result = await strapi.db\n    .query('plugin::i18n.locale')\n    .update({ where: params, data: updates });\n\n  getService('metrics').sendDidUpdateI18nLocalesEvent();\n\n  return result;\n};\n\nconst deleteFn = async ({ id }: any) => {\n  const localeToDelete = await findById(id);\n\n  if (localeToDelete) {\n    await deleteAllLocalizedEntriesFor({ locale: localeToDelete.code });\n    const result = await strapi.db.query('plugin::i18n.locale').delete({ where: { id } });\n\n    getService('metrics').sendDidUpdateI18nLocalesEvent();\n\n    return result;\n  }\n\n  return localeToDelete;\n};\n\nconst setDefaultLocale = ({ code }: any) =>\n  getCoreStore().set({ key: 'default_locale', value: code });\n\nconst getDefaultLocale = () => getCoreStore().get({ key: 'default_locale' });\n\nconst setIsDefault = async (locales: any) => {\n  if (isNil(locales)) {\n    return locales;\n  }\n\n  const actualDefault = await getDefaultLocale();\n\n  if (Array.isArray(locales)) {\n    return locales.map((locale) => ({ ...locale, isDefault: actualDefault === locale.code }));\n  }\n  // single locale\n  return { ...locales, isDefault: actualDefault === locales.code };\n};\n\nconst initDefaultLocale = async () => {\n  const existingLocalesNb = await strapi.db.query('plugin::i18n.locale').count();\n  if (existingLocalesNb === 0) {\n    await create(DEFAULT_LOCALE);\n    await setDefaultLocale({ code: DEFAULT_LOCALE.code });\n  }\n};\n\nconst deleteAllLocalizedEntriesFor = async ({ locale }: any) => {\n  const { isLocalizedContentType } = getService('content-types');\n\n  const localizedModels = Object.values(strapi.contentTypes).filter(isLocalizedContentType);\n\n  for (const model of localizedModels) {\n    // FIXME: delete many content & their associations\n    await strapi.db.query(model.uid).deleteMany({ where: { locale } });\n  }\n};\n\nconst locales = () => ({\n  find,\n  findById,\n  findByCode,\n  create,\n  update,\n  count,\n  setDefaultLocale,\n  getDefaultLocale,\n  setIsDefault,\n  delete: deleteFn,\n  initDefaultLocale,\n});\n\ntype LocaleService = typeof locales;\n\nexport default locales;\nexport type { LocaleService };\n", "import { isoLocales } from '../constants';\n\nconst getIsoLocales = () => isoLocales;\n\nconst isoLocalesService = () => ({\n  getIsoLocales,\n});\n\ntype ISOLocalesService = typeof isoLocalesService;\n\nexport default isoLocalesService;\nexport type { ISOLocalesService };\n", "import _ from 'lodash';\nimport { pick, pipe, has, prop, isNil, cloneDeep, isArray } from 'lodash/fp';\nimport { errors, contentTypes as contentTypeUtils } from '@strapi/utils';\nimport { getService } from '../utils';\n\nconst {\n  isRelationalAttribute,\n  getVisibleAttributes,\n  isTypedAttribute,\n  getScalarAttributes,\n  getRelationalAttributes,\n} = contentTypeUtils;\nconst { ApplicationError } = errors;\n\nconst hasLocalizedOption = (modelOrAttribute: any) => {\n  return prop('pluginOptions.i18n.localized', modelOrAttribute) === true;\n};\n\nconst getValidLocale = async (locale: any) => {\n  const localesService = getService('locales');\n\n  if (isNil(locale)) {\n    return localesService.getDefaultLocale();\n  }\n\n  const foundLocale = await localesService.findByCode(locale);\n  if (!foundLocale) {\n    throw new ApplicationError('Locale not found');\n  }\n\n  return locale;\n};\n\n/**\n * Returns whether an attribute is localized or not\n * @param {*} attribute\n * @returns\n */\nconst isLocalizedAttribute = (attribute: any) => {\n  return (\n    hasLocalizedOption(attribute) ||\n    isRelationalAttribute(attribute) ||\n    isTypedAttribute(attribute, 'uid')\n  );\n};\n\n/**\n * Returns whether a model is localized or not\n * @param {*} model\n * @returns\n */\nconst isLocalizedContentType = (model: any) => {\n  return hasLocalizedOption(model);\n};\n\n/**\n * Returns the list of attribute names that are not localized\n * @param {object} model\n * @returns {string[]}\n */\nconst getNonLocalizedAttributes = (model: any) => {\n  return getVisibleAttributes(model).filter(\n    (attrName) => !isLocalizedAttribute(model.attributes[attrName])\n  );\n};\n\nconst removeId = (value: any) => {\n  if (typeof value === 'object' && has('id', value)) {\n    delete value.id;\n  }\n};\n\nconst removeIds = (model: any) => (entry: any) => removeIdsMut(model, cloneDeep(entry));\n\nconst removeIdsMut = (model: any, entry: any) => {\n  if (isNil(entry)) {\n    return entry;\n  }\n\n  removeId(entry);\n\n  _.forEach(model.attributes, (attr, attrName) => {\n    const value = entry[attrName];\n    if (attr.type === 'dynamiczone' && isArray(value)) {\n      value.forEach((compo) => {\n        if (has('__component', compo)) {\n          const model = strapi.components[compo.__component];\n          removeIdsMut(model, compo);\n        }\n      });\n    } else if (attr.type === 'component') {\n      const model = strapi.components[attr.component];\n      if (isArray(value)) {\n        value.forEach((compo) => removeIdsMut(model, compo));\n      } else {\n        removeIdsMut(model, value);\n      }\n    }\n  });\n\n  return entry;\n};\n\n/**\n * Returns a copy of an entry picking only its non localized attributes\n * @param {object} model\n * @param {object} entry\n * @returns {object}\n */\nconst copyNonLocalizedAttributes = (model: any, entry: any) => {\n  const nonLocalizedAttributes = getNonLocalizedAttributes(model);\n\n  return pipe(pick(nonLocalizedAttributes), removeIds(model))(entry);\n};\n\n/**\n * Returns the list of attribute names that are localized\n * @param {object} model\n * @returns {string[]}\n */\nconst getLocalizedAttributes = (model: any) => {\n  return getVisibleAttributes(model).filter((attrName) =>\n    isLocalizedAttribute(model.attributes[attrName])\n  );\n};\n\n/**\n * Fill non localized fields of an entry if there are nil\n * @param {Object} entry entry to fill\n * @param {Object} relatedEntry values used to fill\n * @param {Object} options\n * @param {Object} options.model corresponding model\n */\nconst fillNonLocalizedAttributes = (entry: any, relatedEntry: any, { model }: any) => {\n  if (isNil(relatedEntry)) {\n    return;\n  }\n\n  const modelDef = strapi.getModel(model);\n  const relatedEntryCopy = copyNonLocalizedAttributes(modelDef, relatedEntry);\n\n  _.forEach(relatedEntryCopy, (value, field) => {\n    if (isNil(entry[field])) {\n      entry[field] = value;\n    }\n  });\n};\n\n/**\n * build the populate param to\n * @param {String} modelUID uid of the model, could be of a content-type or a component\n */\nconst getNestedPopulateOfNonLocalizedAttributes = (modelUID: any) => {\n  const schema = strapi.getModel(modelUID);\n  const scalarAttributes = getScalarAttributes(schema);\n  const nonLocalizedAttributes = getNonLocalizedAttributes(schema);\n\n  const allAttributes = [...scalarAttributes, ...nonLocalizedAttributes];\n  if (schema.modelType === 'component') {\n    // When called recursively on a non localized component we\n    // need to explicitly populate that components relations\n    allAttributes.push(...getRelationalAttributes(schema));\n  }\n\n  const currentAttributesToPopulate = allAttributes.filter((value, index, self) => {\n    return self.indexOf(value) === index && self.lastIndexOf(value) === index;\n  });\n\n  const attributesToPopulate = [...currentAttributesToPopulate];\n  for (const attrName of currentAttributesToPopulate) {\n    const attr = schema.attributes[attrName];\n    if (attr.type === 'component') {\n      const nestedPopulate = getNestedPopulateOfNonLocalizedAttributes(attr.component).map(\n        (nestedAttr) => `${attrName}.${nestedAttr}`\n      );\n      attributesToPopulate.push(...nestedPopulate);\n    } else if (attr.type === 'dynamiczone') {\n      attr.components.forEach((componentName) => {\n        const nestedPopulate = getNestedPopulateOfNonLocalizedAttributes(componentName).map(\n          (nestedAttr) => `${attrName}.${nestedAttr}`\n        );\n        attributesToPopulate.push(...nestedPopulate);\n      });\n    }\n  }\n\n  return attributesToPopulate;\n};\n\nconst contentTypes = () => ({\n  isLocalizedContentType,\n  getValidLocale,\n  getLocalizedAttributes,\n  getNonLocalizedAttributes,\n  copyNonLocalizedAttributes,\n  fillNonLocalizedAttributes,\n  getNestedPopulateOfNonLocalizedAttributes,\n});\n\ntype ContentTypesService = typeof contentTypes;\n\nexport default contentTypes;\nexport { ContentTypesService };\n", "import permissions from './permissions';\nimport metrics from './metrics';\nimport localizations from './localizations';\nimport locales from './locales';\nimport isoLocales from './iso-locales';\nimport contentTypes from './content-types';\n\nexport default {\n  permissions,\n  metrics,\n  localizations,\n  locales,\n  'iso-locales': isoLocales,\n  'content-types': contentTypes,\n};\n", "export default {\n  type: 'admin',\n  routes: [\n    {\n      method: 'GET',\n      path: '/iso-locales',\n      handler: 'iso-locales.listIsoLocales',\n      config: {\n        policies: [\n          'admin::isAuthenticatedAdmin',\n          {\n            name: 'plugin::content-manager.hasPermissions',\n            config: { actions: ['plugin::i18n.locale.read'] },\n          },\n        ],\n      },\n    },\n    {\n      method: 'GET',\n      path: '/locales',\n      handler: 'locales.listLocales',\n      config: {\n        policies: ['admin::isAuthenticatedAdmin'],\n      },\n    },\n    {\n      method: 'POST',\n      path: '/locales',\n      handler: 'locales.createLocale',\n      config: {\n        policies: [\n          'admin::isAuthenticatedAdmin',\n          {\n            name: 'plugin::content-manager.hasPermissions',\n            config: { actions: ['plugin::i18n.locale.create'] },\n          },\n        ],\n      },\n    },\n    {\n      method: 'PUT',\n      path: '/locales/:id',\n      handler: 'locales.updateLocale',\n      config: {\n        policies: [\n          'admin::isAuthenticatedAdmin',\n          {\n            name: 'plugin::content-manager.hasPermissions',\n            config: { actions: ['plugin::i18n.locale.update'] },\n          },\n        ],\n      },\n    },\n    {\n      method: 'DELETE',\n      path: '/locales/:id',\n      handler: 'locales.deleteLocale',\n      config: {\n        policies: [\n          'admin::isAuthenticatedAdmin',\n          {\n            name: 'plugin::content-manager.hasPermissions',\n            config: { actions: ['plugin::i18n.locale.delete'] },\n          },\n        ],\n      },\n    },\n    {\n      method: 'POST',\n      path: '/content-manager/actions/get-non-localized-fields',\n      handler: 'content-types.getNonLocalizedAttributes',\n      config: {\n        policies: ['admin::isAuthenticatedAdmin'],\n      },\n    },\n  ],\n};\n", "export default {\n  type: 'content-api',\n  routes: [\n    {\n      method: 'GET',\n      path: '/locales',\n      handler: 'locales.listLocales',\n    },\n  ],\n};\n", "import admin from './admin';\nimport contentApi from './content-api';\n\nexport default {\n  admin,\n  'content-api': contentApi,\n};\n", "import { prop } from 'lodash/fp';\nimport { yup, validateYupSchema } from '@strapi/utils';\n\nimport { isoLocales } from '../constants';\n\nconst allowedLocaleCodes = isoLocales.map(prop('code'));\n\nconst createLocaleSchema = yup\n  .object()\n  .shape({\n    name: yup.string().max(50).nullable(),\n    code: yup.string().oneOf(allowedLocaleCodes).required(),\n    isDefault: yup.boolean().required(),\n  })\n  .noUnknown();\n\nconst updateLocaleSchema = yup\n  .object()\n  .shape({\n    name: yup.string().min(1).max(50).nullable(),\n    isDefault: yup.boolean(),\n  })\n  .noUnknown();\n\nconst validateCreateLocaleInput = validateYupSchema(createLocaleSchema);\nconst validateUpdateLocaleInput = validateYupSchema(updateLocaleSchema);\n\nexport { validateCreateLocaleInput, validateUpdateLocaleInput };\n", "const formatLocale = (locale: { name: string; code: string; isDefault: boolean }) => {\n  return {\n    ...locale,\n    name: locale.name || null,\n  };\n};\n\nexport { formatLocale };\n", "import * as utils from '@strapi/utils';\nimport { pick } from 'lodash/fp';\nimport type { Core } from '@strapi/types';\nimport { getService } from '../utils';\nimport { validateCreateLocaleInput, validateUpdateLocaleInput } from '../validation/locales';\nimport { formatLocale } from '../domain/locale';\n\nconst { setCreatorFields } = utils;\nconst { ApplicationError } = utils.errors;\n\nconst sanitizeLocale = (locale: any) => {\n  const model = strapi.getModel('plugin::i18n.locale');\n\n  return strapi.contentAPI.sanitize.output(locale, model);\n};\n\nconst controller: Core.Controller = {\n  async listLocales(ctx) {\n    const localesService = getService('locales');\n\n    const locales = await localesService.find();\n    const sanitizedLocales = await sanitizeLocale(locales);\n\n    ctx.body = await localesService.setIsDefault(sanitizedLocales);\n  },\n\n  async createLocale(ctx) {\n    const { user } = ctx.state;\n    const body = ctx.request.body as any;\n    const { isDefault, ...localeToCreate } = body;\n\n    await validateCreateLocaleInput(body);\n\n    const localesService = getService('locales');\n\n    const existingLocale = await localesService.findByCode(body.code);\n    if (existingLocale) {\n      throw new ApplicationError('This locale already exists');\n    }\n\n    const localeToPersist = setCreatorFields({ user })(formatLocale(localeToCreate));\n\n    const locale = await localesService.create(localeToPersist);\n\n    if (isDefault) {\n      await localesService.setDefaultLocale(locale);\n    }\n\n    const sanitizedLocale = await sanitizeLocale(locale);\n\n    ctx.body = await localesService.setIsDefault(sanitizedLocale);\n  },\n\n  async updateLocale(ctx) {\n    const { user } = ctx.state;\n    const { id } = ctx.params;\n    const body = ctx.request.body as any;\n    const { isDefault, ...updates } = body;\n\n    await validateUpdateLocaleInput(body);\n\n    const localesService = getService('locales');\n\n    const existingLocale = await localesService.findById(id);\n    if (!existingLocale) {\n      return ctx.notFound('locale.notFound');\n    }\n\n    const allowedParams = ['name'];\n    const cleanUpdates = setCreatorFields({ user, isEdition: true })(pick(allowedParams, updates));\n\n    const updatedLocale = await localesService.update({ id }, cleanUpdates);\n\n    if (isDefault) {\n      await localesService.setDefaultLocale(updatedLocale);\n    }\n\n    const sanitizedLocale = await sanitizeLocale(updatedLocale);\n\n    ctx.body = await localesService.setIsDefault(sanitizedLocale);\n  },\n\n  async deleteLocale(ctx) {\n    const { id } = ctx.params;\n\n    const localesService = getService('locales');\n\n    const existingLocale = await localesService.findById(id);\n    if (!existingLocale) {\n      return ctx.notFound('locale.notFound');\n    }\n\n    const defaultLocaleCode = await localesService.getDefaultLocale();\n    if (existingLocale.code === defaultLocaleCode) {\n      throw new ApplicationError('Cannot delete the default locale');\n    }\n\n    await localesService.delete({ id });\n\n    const sanitizedLocale = await sanitizeLocale(existingLocale);\n\n    ctx.body = await localesService.setIsDefault(sanitizedLocale);\n  },\n};\n\nexport default controller;\n", "import { yup, validateYupSchema } from '@strapi/utils';\n\nimport { get } from 'lodash/fp';\n\nconst validateGetNonLocalizedAttributesSchema = yup\n  .object()\n  .shape({\n    model: yup.string().required(),\n    id: yup.mixed().when('model', {\n      is: (model: any) => get('kind', strapi.contentType(model)) === 'singleType',\n      then: yup.strapiID().nullable(),\n      otherwise: yup.strapiID().required(),\n    }),\n    locale: yup.string().required(),\n  })\n  .noUnknown()\n  .required();\n\nconst validateGetNonLocalizedAttributesInput = validateYupSchema(\n  validateGetNonLocalizedAttributesSchema\n);\n\nexport { validateGetNonLocalizedAttributesInput };\n", "import { pick, uniq, prop, getOr, flatten, pipe, map } from 'lodash/fp';\nimport { contentTypes as contentTypesUtils, errors } from '@strapi/utils';\nimport type { Core } from '@strapi/types';\nimport { getService } from '../utils';\nimport { validateGetNonLocalizedAttributesInput } from '../validation/content-types';\n\nconst { ApplicationError } = errors;\n\nconst { PUBLISHED_AT_ATTRIBUTE } = contentTypesUtils.constants;\n\nconst getLocalesProperty = getOr<string[]>([], 'properties.locales');\nconst getFieldsProperty = prop('properties.fields');\n\nconst getFirstLevelPath = map((path: string) => path.split('.')[0]);\n\nconst controller = {\n  async getNonLocalizedAttributes(ctx) {\n    const { user } = ctx.state;\n    const body = ctx.request.body as any;\n    const { model, id, locale } = body;\n\n    await validateGetNonLocalizedAttributesInput({ model, id, locale });\n\n    const {\n      copyNonLocalizedAttributes,\n      isLocalizedContentType,\n      getNestedPopulateOfNonLocalizedAttributes,\n    } = getService('content-types');\n\n    const {\n      default: { READ_ACTION, CREATE_ACTION },\n    } = strapi.service('admin::constants');\n\n    const modelDef = strapi.contentType(model);\n    const attributesToPopulate = getNestedPopulateOfNonLocalizedAttributes(model);\n\n    if (!isLocalizedContentType(modelDef)) {\n      throw new ApplicationError(`Model ${model} is not localized`);\n    }\n\n    const params = modelDef.kind === 'singleType' ? {} : { id };\n\n    const entity = await strapi.db\n      .query(model)\n      .findOne({ where: params, populate: attributesToPopulate });\n\n    if (!entity) {\n      return ctx.notFound();\n    }\n\n    const permissions = await strapi.admin.services.permission.findMany({\n      where: {\n        action: [READ_ACTION, CREATE_ACTION],\n        subject: model,\n        role: {\n          id: user.roles.map(prop('id')),\n        },\n      },\n    });\n\n    const localePermissions = permissions\n      .filter((perm: any) => getLocalesProperty(perm).includes(locale))\n      .map(getFieldsProperty);\n\n    const permittedFields = pipe(flatten, getFirstLevelPath, uniq)(localePermissions);\n\n    const nonLocalizedFields = copyNonLocalizedAttributes(modelDef, entity);\n    const sanitizedNonLocalizedFields = pick(permittedFields, nonLocalizedFields);\n\n    const availableLocalesResult = await strapi.plugins['content-manager']\n      .service('document-metadata')\n      .getMetadata(model, entity, {\n        availableLocales: true,\n      });\n\n    const availableLocales = availableLocalesResult.availableLocales.map((localeResult: any) =>\n      pick(['id', 'locale', PUBLISHED_AT_ATTRIBUTE], localeResult)\n    );\n\n    ctx.body = {\n      nonLocalizedFields: sanitizedNonLocalizedFields,\n      localizations: availableLocales.concat(\n        pick(['id', 'locale', PUBLISHED_AT_ATTRIBUTE], entity)\n      ),\n    };\n  },\n} satisfies Core.Controller;\n\nexport default controller;\n", "import type { Core } from '@strapi/types';\nimport { getService } from '../utils';\n\nconst controller: Core.Controller = {\n  listIsoLocales(ctx) {\n    const isoLocalesService = getService('iso-locales');\n\n    ctx.body = isoLocalesService.getIsoLocales();\n  },\n};\n\nexport default controller;\n", "import locales from './locales';\nimport contentTypes from './content-types';\nimport isoLocales from './iso-locales';\n\nexport default {\n  locales,\n  'iso-locales': isoLocales,\n  'content-types': contentTypes,\n};\n", "import bootstrap from './bootstrap';\nimport register from './register';\nimport contentTypes from './content-types';\nimport services from './services';\nimport routes from './routes';\nimport controllers from './controllers';\n\nexport default () => ({\n  register,\n  bootstrap,\n  routes,\n  controllers,\n  contentTypes,\n  services,\n});\n"], "names": ["schema", "getNestedPopulateOfNonLocalizedAttributes", "sendDidInitializeEvent", "initDefaultLocale", "actions", "ApplicationError", "errors", "getValidLocale", "isLocalizedContentType", "locale", "get", "strapi", "locales", "identity", "propEq", "attributes", "_", "isArray", "permissions", "prop", "getOr", "isEmpty", "reduce", "copyNonLocalizedAttributes", "async", "cloneDeep", "isNil", "contentTypeUtils", "has", "model", "pipe", "pick", "index", "isoLocales", "yup", "validateYupSchema", "utils", "controller", "contentTypesUtils", "map", "flatten", "uniq", "isoLocalesService", "contentTypes"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,eAAe,MAAM;AACzB,SAAO,OAAO,MAAM,EAAE,MAAM,UAAU,MAAM,QAAQ;AACtD;AAGA,MAAM,aAAa,CAAoB,SAA8B;AACnE,SAAO,OAAO,OAAO,MAAM,EAAE,QAAQ,IAAI;AAC3C;ACpBA,MAAM,sBAAsB,MAAM;AACzB,SAAA,GAAG,WAAW,UAAU;AAAA,IAC7B,QAAQ,CAAC,qBAAqB;AAAA,IAE9B,MAAM,cAAc;AAClB,YAAM,WAAW,aAAa,EAAE,QAAQ,qCAAqC;AAAA,IAC/E;AAAA,IAEA,MAAM,cAAc;AAClB,YAAM,WAAW,aAAa,EAAE,QAAQ,qCAAqC;AAAA,IAC/E;AAAA,EAAA,CACD;AAED,SAAO,UAAU,IAAI,OAAO,SAAS,SAAS;AAC5C,UAAMA,UAA6B,QAAQ;AAEvC,QAAA,CAAC,CAAC,UAAU,UAAU,gBAAgB,SAAS,EAAE,SAAS,QAAQ,MAAM,GAAG;AAC7E,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,CAAC,WAAW,eAAe,EAAE,uBAAuBA,OAAM,GAAG;AAC/D,aAAO,KAAK;AAAA,IACd;AAGA,UAAM,EAAE,2CAAAC,2CAAA,IAA8C,WAAW,eAAe;AAE1E,UAAA,uBAAuBA,2CAA0CD,QAAO,GAAG;AAG3E,UAAA,SAAU,MAAM;AAKlB,QAAA;AAEA,QAAA,MAAM,QAAQ,QAAQ,OAAO,KAAK,OAAO,QAAQ,CAAC,GAAG,IAAI;AAChD,iBAAA,OAAO,QAAQ,CAAC,EAAE;AAAA,IAAA,WACpB,QAAQ,IAAI;AACrB,iBAAW,OAAO;AAAA,IAAA,OACb;AACE,aAAA;AAAA,IACT;AAEI,QAAA,qBAAqB,SAAS,GAAG;AACnC,YAAM,kBAAkB,MAAM,OAAO,GAClC,MAAMA,QAAO,GAAG,EAChB,QAAQ,EAAE,OAAO,EAAE,IAAI,SAAY,GAAA,UAAU,sBAAsB;AAEtE,YAAM,WAAW,eAAe,EAAE,2BAA2B,iBAAiBA,OAAM;AAAA,IACtF;AAEO,WAAA;AAAA,EAAA,CACR;AACH;AAEA,MAAA,YAAe,YAAY;AACzB,QAAM,EAAE,wBAAAE,wBAAA,IAA2B,WAAW,SAAS;AACvD,QAAM,EAAE,mBAAAC,mBAAA,IAAsB,WAAW,SAAS;AAClD,QAAM,EAAE,iBAAiB,SAAAC,UAAS,OAAO,IAAI,WAAW,aAAa;AAGrE,QAAMD,mBAAkB;AAGxB,kBAAgB,+BAA+B;AAG/C,QAAMC,SAAQ;AACd,EAAAA,SAAQ,yBAAyB;AACjC,EAAAA,SAAQ,wBAAwB;AAGhC,SAAO,gCAAgC;AAGnB;AAEG,EAAAF;AACzB;AC9EA,MAAM,EAAEG,kBAAAA,mBAAqB,IAAAC;AAG7B,MAAM,yBAAiD,OAAO,KAAK,SAAS;AACpE,QAAA,EAAE,MAAM,IAAI,IAAI;AAChB,QAAA,EAAE,MAAM,IAAI,IAAI;AAGlB,MAAA,CAAC,IAAI,QAAQ,MAAM;AACjB,QAAA,QAAQ,OAAO;EACrB;AAEM,QAAA,OAAO,IAAI,QAAQ;AAEzB,QAAM,EAAE,gBAAAC,iBAAgB,wBAAAC,wBAAuB,IAAI,WAAW,eAAe;AAEvE,QAAA,WAAW,OAAO,SAAS,KAAK;AAElC,MAAA,CAACA,wBAAuB,QAAQ,GAAG;AACrC,WAAO,KAAK;AAAA,EACd;AAGM,QAAAC,UAASC,OAAI,UAAU,KAAK,KAAKA,GAAI,IAAA,UAAU,IAAI,KAAK;AAG1D,MAAA,QAAQ,QAAQ;AAEhB,MAAA;AACA,MAAA;AACa,mBAAA,MAAMH,gBAAeE,OAAM;AAAA,WACnC,GAAG;AACJ,UAAA,IAAIJ,mBAAiB,2BAA2B;AAAA,EACxD;AAEA,OAAK,SAAS;AAEV,MAAA,SAAS,SAAS,cAAc;AAClC,UAAM,SAAS,MAAM,OAAO,cAAc,SAAS,SAAS,KAAK;AAAA,MAC/D,QAAQ;AAAA,IAAA,CACF;AAEJ,QAAA,QAAQ,MAAM,SAAS,KAAK;AAGhC,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAEA,SAAO,KAAK;AACd;ACpDA,MAAM,EAAE,gBAAoB,IAAAC;AAE5B,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB;AAE/B,MAAA,kBAAe,CAAC,EAAE,QAAAK,eAAuC;AAAA,EACvD,WAAW;AACT,UAAM,EAAE,SAAS,kBAAA,IAAsBA,QAAO,OAAO,SAAS;AAC9D,UAAM,EAAE,SAAS,eAAA,IAAmBA,QAAO,OAAO,MAAM;AAExD,UAAM,EAAE,wBAAAH,wBAAA,IAA2B,eAAe,eAAe;AAE3D,UAAA,mBAAmB,kBAAkB,WAAW;AAErC,qBAAA,WAAW,qBAAqB,EAAE,iBAAiB;AAG7D,WAAA,QAAQG,QAAO,YAAY,EAAE,QAAQ,CAAC,CAAC,KAAK,EAAE,MAAM;AACrD,UAAAH,wBAAuB,EAAE,GAAG;AAE9B,yBAAiB,WAAW,GAAG,EAAE,MAAM,QAAQ,EAAE;AAGjD,yBAAiB,WAAW,GAAG,EAAE,MAAM,eAAe,EAAE;MAC1D;AAAA,IAAA,CACD;AAED,qBAAiB,IAAI,CAAC,EAAE,OAAO,mBAAwB;AACrD,YAAM,sBAAsB,uBAAuB,EAAE,OAAO,aAAc,CAAA;AAC1E,YAAM,mBAAmB,gBAAgB,EAAE,MAAO,CAAA;AAC3C,aAAA;AAAA,QACL,SAAS,CAAC,mBAAmB;AAAA,QAC7B,OAAO,CAAC,gBAAgB;AAAA,QAExB,iBAAiB;AAAA;AAAA,UAEf,oBAAoB,EAAE,MAAM,EAAE,OAAO,qCAAqC;AAAA,UAC1E,qBAAqB,EAAE,MAAM,EAAE,OAAO,qCAAqC;AAAA,QAC7E;AAAA,MAAA;AAAA,IACF,CACD;AAAA,EACH;AACF;AAEA,MAAM,kBAAkB,CAAC,EAAE,YAAiB;AAC1C,QAAM,EAAE,SAAS,eAAA,IAAmB,OAAO,OAAO,MAAM;AAExD,QAAMI,WAAU,eAAe,aAAa,EAAE,cAAc;AAE5D,SAAO,MAAM,WAAW;AAAA,IACtB,MAAM;AAAA,IAEN,aAAa;AAAA,IAEb,WAAWC,GAAA;AAAA,IACX,YAAYA,GAAA;AAAA,IAEZ,aAAa,KAAU;AACjB,UAAA,IAAI,SAAS,eAAe;AACxB,cAAA,IAAI,gBAAgB,yCAAyC;AAAA,MACrE;AAEM,YAAA,gBAAgB,IAAI,UAAU,OAAOD,SAAQ,KAAKE,GAAAA,OAAO,QAAQ,IAAI,KAAK,CAAC;AAEjF,UAAI,CAAC,eAAe;AACZ,cAAA,IAAI,gBAAgB,yBAAyB;AAAA,MACrD;AAEA,aAAO,IAAI;AAAA,IACb;AAAA,EAAA,CACD;AACH;AAEA,MAAM,yBAAyB,CAAC,EAAE,OAAO,mBAAwB;AAC/D,QAAM,EAAE,SAAS,eAAA,IAAmB,OAAO,OAAO,MAAM;AAExD,QAAM,EAAE,wBAAAN,wBAAA,IAA2B,eAAe,eAAe;AAEjE,SAAO,MAAM,OAAO;AAAA,IAClB,MAAM;AAAA,IAEN,iBAAiB,QAAa;AAGtB,YAAA,EAAE,WAAe,IAAA;AAGnB,UAAA,eAAe,WAAW,eAAe,YAAY;AACvD;AAAA,MACF;AAEI,UAAA;AAEA,UAAA,QAAQ,YAAY,QAAQ,aAAa;AAC7B,sBAAA,OAAO,WAAW,OAAO;AAAA,MAAA,OAClC;AACL,cAAM,eAAe,aAAa,IAAI,OAAO,IAAI;AAEjD,YAAI,CAAC,cAAc;AACjB;AAAA,QACF;AAEA,sBAAc,aAAa,OAAO;AAAA,MACpC;AAGI,UAAA,CAACA,wBAAuB,WAAW,GAAG;AACxC;AAAA,MACF;AAEI,UAAA,CAAC,OAAO,MAAM;AAChB,eAAO,OAAO;MAChB;AAEO,aAAA,KAAK,SAAS,MAAM,IAAI;AAAA,QAC7B,MAAM;AAAA,QACN,aAAa;AAAA,MAAA,CACd;AAAA,IACH;AAAA,EAAA,CACD;AACH;ACtHA,MAAA,WAAe,CAAC,EAAE,QAAAG,QAAA,MAAsC;AACtD,qBAAmBA,OAAM;AACzB,oCAAkCA,OAAM;AAC1C;AAOA,MAAM,oCAAoC,CAACA,YAAwB;AACjE,EAAAA,QAAO,OAAO,OAAO,IAAI,4CAA4C,CAAC,KAAK,SAAS;AAClF,QAAI,IAAI,WAAW,UAAU,IAAI,WAAW,OAAO;AAC1C,aAAA,uBAAuB,KAAK,IAAI;AAAA,IACzC;AAEA,WAAO,KAAK;AAAA,EAAA,CACb;AAED,EAAAA,QAAO,OAAO,OAAO,IAAI,wCAAwC,CAAC,KAAK,SAAS;AAC9E,QAAI,IAAI,WAAW,UAAU,IAAI,WAAW,OAAO;AAC1C,aAAA,uBAAuB,KAAK,IAAI;AAAA,IACzC;AAEA,WAAO,KAAK;AAAA,EAAA,CACb;AACH;AAOA,MAAM,qBAAqB,CAACA,YAAwB;AAClD,SAAO,OAAOA,QAAO,YAAY,EAAE,QAAQ,CAAC,gBAAgB;AACpD,UAAA,EAAE,YAAAI,YAAe,IAAA;AAErBC,uBAAA,IAAID,aAAY,UAAU;AAAA,MAC1B,UAAU;AAAA,MACV,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,MAAM;AAAA,IAAA,CACP;AAECC,uBAAA,IAAID,aAAY,iBAAiB;AAAA,MACjC,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ,YAAY;AAAA,MACpB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,YAAY;AAAA,QACV,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,iBAAiBJ,QAAO,GAAG,SAAS,YAAY,aAAa,YAAY,cAAe;AAAA;AAAA,QAExF,GAAG,EAAE,WAA+B;AAC3B,iBAAA;AAAA,YACL,IAAI;AAAA,cACF,QAAQ,QAAQ,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,YACjC;AAAA,UAAA;AAAA,QAEJ;AAAA,MACF;AAAA,IAAA,CACD;AAAA,EAAA,CACF;AAEG,MAAAA,QAAO,OAAO,SAAS,GAAG;AAE5B,oBAAgB,EAAE,QAAAA,SAAQ,EAAE,SAAS;AAAA,EACvC;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9EA,MAAe,SAAA;AAAA,EACb;AACF;ACFA,MAAe,iBAAA;AAAA,EACb;AACF;ACDA,MAAM,UAAU;AAAA,EACd;AAAA,IACE,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,MACP,EAAE,UAAU,yCAAyC,UAAU,CAAC,qBAAqB,EAAE;AAAA,IACzF;AAAA,EACF;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,KAAK;AAAA,EACP;AAAA,EACA;AAAA,IACE,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,KAAK;AAAA,EACP;AACF;AAEA,MAAM,6BAA6B,CAAC,EAAE,OAAO,aAAkB;AACvD,QAAA;AAAA,IACJ;AAAA,IACA,SAAS,EAAE,kBAAkB;AAAA,EAC3B,IAAA;AAGJ,MAAI,YAAY,gBAAgB;AAC9B;AAAA,EACF;AAGA,MAAIM,GAAAA,QAAQ,iBAAiB,KAAK,kBAAkB,SAAS,SAAS,GAAG;AACvE;AAAA,EACF;AAGO,SAAA,QAAQ,oBAAoBA,GAAAA,QAAQ,iBAAiB,IACxD,kBAAkB,OAAO,SAAS,IAClC,CAAC,SAAS;AAChB;AAEA,MAAM,sCAAsC,CAAC,EAAE,UAAU,cAAmB;AAC1E,MAAI,aAAa,WAAW;AACpB,UAAA,QAAQ,OAAO,SAAS,OAAO;AAErC,WAAO,WAAW,eAAe,EAAE,uBAAuB,KAAK;AAAA,EACjE;AAEO,SAAA;AACT;AAEA,MAAM,6BAA6B,OAAOC,iBAAqB;AAC7D,QAAM,EAAE,eAAmB,IAAA,OAAO,QAAQ,mBAAmB;AAC7D,QAAM,EAAE,MAAM,eAAe,IAAI,WAAW,SAAS;AAE/C,QAAA,aAAa,MAAM;AACzB,QAAM,iBAAiB,WAAW,IAAIC,GAAAA,KAAK,MAAM,CAAC;AAElD,SAAO,QAAQ;AAAA,IACbD,aAAY,IAAI,OAAO,eAAoB;AACnC,YAAA,EAAE,QAAQ,QAAY,IAAA;AAEtB,YAAA,2BAA2B,MAAM,eAAe;AAAA,QACpD;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAGF,UAAI,CAAC,0BAA0B;AACtB,eAAA;AAAA,MACT;AAEA,YAAM,0BAA0BE,GAAA,MAAM,CAAC,GAAG,cAAc,UAAU;AAE3D,aAAA,EAAE,GAAG,YAAY,YAAY,EAAE,GAAG,yBAAyB,SAAS,eAAA;IAAiB,CAC7F;AAAA,EAAA;AAEL;AAEA,MAAM,uCAAuC,YAAY;AACjD,QAAA,cAAc,OAAO,QAAQ,aAAa;AAC1C,QAAA,oBAAoB,OAAO,QAAQ,mBAAmB;AAEtD,QAAA,iBAAiB,MAAM,YAAY;AAEzC,MAAI,CAAC,gBAAgB;AACnB;AAAA,EACF;AAEM,QAAA,wBAAwB,MAAM,kBAAkB,SAAS;AAAA,IAC7D,OAAO;AAAA,MACL,MAAM;AAAA,QACJ,IAAI,eAAe;AAAA,MACrB;AAAA,IACF;AAAA,EAAA,CACD;AAEK,QAAA,2BAA2B,MAAM,2BAA2B,qBAAqB;AAEvF,QAAM,YAAY,kBAAkB,eAAe,IAAI,wBAAwB;AACjF;AAEA,MAAM,sBAAsB,YAAY;AACtC,QAAM,EAAE,eAAmB,IAAA,OAAO,QAAQ,mBAAmB;AAEvD,QAAA,eAAe,aAAa,OAAO;AAC3C;AAEA,MAAM,2BAA2B,MAAM;AACrC,QAAM,EAAE,eAAmB,IAAA,OAAO,QAAQ,mBAAmB;AAC7D,QAAM,EAAE,MAAU,IAAA,OAAO,QAAQ,aAAa;AAE/B,iBAAA,MAAM,yBAAyB,SAAS,mCAAmC;AACpF,QAAA,+BAA+B,SAAS,0BAA0B;AAC1E;AAEA,MAAM,0BAA0B,MAAM;AACpC,QAAM,EAAE,eAAmB,IAAA,OAAO,QAAQ,mBAAmB;AAG9C,iBAAA,MAAM,aAAa,SAAS,0BAA0B;AAGtD,iBAAA,SAAS,QAAQ,CAAC,WAAgB,2BAA2B,EAAE,OAAO,OAAQ,CAAA,CAAC;AAChG;AAEA,MAAe,qBAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AC/IA,MAAM,yBAAyB,OAAO,EAAE,QAAQ,cAAmB;AACjE,QAAM,EAAE,eAAmB,IAAA,OAAO,QAAQ,mBAAmB;AAE7D,QAAMR,WAAU,MAAM,WAAW,SAAS,EAAE,KAAK;AAG7C,MAAAS,GAAAA,QAAQT,QAAO,GAAG;AACpB;AAAA,EACF;AAEW,aAAA,WAAW,QAAQ,UAAU;AAChC,UAAA,UAAU,MAAM,eAAe,kBAAkB,WAAW,OAAO,UAAU,QAAQ,GAAG;AACxF,UAAA,qBAAqB,QAAQ,WAAW;AAAA,MAC5C,CAAC,aAAkB,SAAS,UAAU;AAAA,IAAA;AAGpC,QAAA,WAAW,CAAC,oBAAoB;AAClC,cAAQ,WAAW,KAAK;AAAA,QACtB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,UAAUA,SAAQ,IAAI,CAAC,EAAE,MAAM,KAAA,OAAiB,EAAE,OAAO,QAAQ,MAAM,OAAO,KAAO,EAAA;AAAA,MAAA,CACtF;AAAA,IACH;AAAA,EACF;AACF;AAEA,MAAM,iCAAiC,MAAM;AAC3C,QAAM,EAAE,gBAAoB,IAAA,OAAO,QAAQ,mBAAmB;AAE9C,kBAAA,WAAW,eAAe,sBAAsB;AAChD,kBAAA,WAAW,mBAAmB,sBAAsB;AACtE;AAEA,MAAe,yBAAA;AAAA,EACb;AAAA,EACA;AACF;ACjCA,MAAM,yBAAyB,CAAC,YAAiB;AAC/C,QAAM,EAAE,YAAY,WAAW,KAAA,IAAS;AAClC,QAAA,EAAE,SAAS,WAAe,IAAA;AAEhC,QAAM,eAAe,OAAO,QAAQ,aAAa,EAAE,kBAAkB,IAAI;AAEzE,MAAI,cAAc;AAChB;AAAA,EACF;AAEA,QAAM,EAAE,SAAAA,SAAA,IAAY,cAAc;AAClC,QAAM,EAAE,wBAAAJ,wBAAA,IAA2B,WAAW,eAAe;AAG7D,MAAI,CAAC,SAAS;AACZ;AAAA,EACF;AAEM,QAAA,KAAK,OAAO,aAAa,OAAO;AAGlC,MAAA,CAACA,wBAAuB,EAAE,GAAG;AAC/B;AAAA,EACF;AAGA,MAAII,aAAY,MAAM;AACpB;AAAA,EACF;AAEA,YAAU,IAAI;AAAA,IACZ,QAAQ;AAAA,MACN,KAAKA,YAAW,CAAC;AAAA,IACnB;AAAA,EAAA,CACD;AACH;AAEA,MAAM,kCAAkC,MAAM;AAC5C,QAAM,EAAE,OAAW,IAAA,OAAO,QAAQ,mBAAmB;AAErD,SAAO,MAAM,4BAA4B,EAAE,SAAS,sBAAsB;AAC5E;AAEA,MAAe,gBAAA;AAAA,EACb;AAAA,EACA;AACF;ACxDA,MAAM,cAAc,OAAO;AAAA,EACzB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,QAAQ;AACV;ACLA,MAAM,yBAAyB,YAAY;AACzC,QAAM,EAAE,wBAAAJ,wBAAA,IAA2B,WAAW,eAAe;AAG7D,QAAM,uBAAuBc,GAAA;AAAA,IAC3B,CAAC,KAAK,gBAAiBd,wBAAuB,WAAW,IAAI,MAAM,IAAI;AAAA,IACvE;AAAA,EAAA,EACA,OAAO,YAAmB;AAEtB,QAAA,OAAO,UAAU,KAAK,qBAAqB,EAAE,iBAAiB,EAAE,qBAAqB,EAAA,CAAG;AAChG;AAEA,MAAM,gCAAgC,YAAY;AAChD,QAAM,kBAAkB,MAAM,WAAW,SAAS,EAAE,MAAM;AAEpD,QAAA,OAAO,UAAU,KAAK,wBAAwB;AAAA,IAClD,iBAAiB,EAAE,gBAAgB;AAAA,EAAA,CACpC;AACH;AAEA,MAAM,UAAU,OAAO;AAAA,EACrB;AAAA,EACA;AACF;ACjBA,MAAM,6BAA6B,OAAO,aAAkB,UAA8B;AACxF,QAAM,EAAE,4BAAAe,4BAAA,IAA+B,WAAW,eAAe;AAE3D,QAAA,yBAAyBA,4BAA2B,OAAO,WAAW;AACxE,MAAAF,GAAAA,QAAQ,sBAAsB,GAAG;AACnC;AAAA,EACF;AAEA,QAAM,MAAM,MAAM;AAClB,QAAM,aAAa,YAAY;AAC/B,QAAMZ,UAAS,YAAY;AACrB,QAAA,SAAS,aAAa,cAAc,cAAc;AAIxD,QAAM,wBAAwB,MAAM,OAAO,GAAG,MAAM,GAAG,EAAE,SAAS;AAAA,IAChE,OAAO;AAAA,MACL;AAAA,MACA,aAAa,WAAW,cAAc,EAAE,KAAK,KAAS,IAAA;AAAA,MACtD,QAAQ,EAAE,KAAKA,QAAO;AAAA,IACxB;AAAA,IACA,QAAQ,CAAC,UAAU,IAAI;AAAA,EAAA,CACxB;AAED,QAAM,YAAY,MAAM,OAAO,UAAU,GAAG,EAAE,kBAAkB,sBAAsB;AAEtF,QAAMe,YAAM,IAAI,uBAAuB,OAAO,UAAe;AAC3D,UAAM,kBAAkB,MAAM,OAAO,UAAU,MAAM;AAAA,MACnDC,GAAAA,UAAU,sBAAsB;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,QACA,QAAQ,MAAM;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,IAAA;AAII,UAAA,gBAAgB,MAAM,OACzB,UAAU,GAAG,EACb,iBAAiB,OAAO,eAAsB;AAGjD,UAAM,OAAO,GAAG,MAAM,GAAG,EAAE,OAAO;AAAA,MAChC,OAAO;AAAA,QACL;AAAA,QACA,aAAa,WAAW,cAAc,EAAE,KAAK,KAAS,IAAA;AAAA,QACtD,QAAQ,EAAE,KAAK,MAAM,OAAO;AAAA,MAC9B;AAAA;AAAA;AAAA,MAGA,MAAM,OAAO,OAAOA,GAAU,UAAA,SAAS,GAAG,aAAa;AAAA,IAAA,CACxD;AAAA,EAAA,CACF;AACH;AAEA,MAAM,gBAAgB,OAAO;AAAA,EAC3B;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DA,MAAM,gBAAgB,MAAM;AACpB,QAAA,gBAAgB,QAAQ,IAAI;AAElC,MAAI,eAAe;AACX,UAAA,iBAAiB,WAAW,KAAK,CAAC,EAAE,WAAW,SAAS,aAAa;AAE3E,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,MAAA;AAAA,IAEJ;AAEO,WAAA,EAAE,GAAG;EACd;AAEO,SAAA;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,EAAA;AAEV;AAEA,MAAM,iBAAiB,cAAc;ACvBrC,MAAM,OAAO,CAAC,SAAc,OAC1B,OAAO,GAAG,MAAM,qBAAqB,EAAE,SAAS,EAAE,OAAO,OAAQ,CAAA;AAEnE,MAAM,WAAW,CAAC,OAAY,OAAO,GAAG,MAAM,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAA,EAAM,CAAA;AAE9F,MAAM,aAAa,CAAC,SAClB,OAAO,GAAG,MAAM,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAA,EAAQ,CAAA;AAEpE,MAAM,QAAQ,CAAC,SAAc,OAAO,OAAO,GAAG,MAAM,qBAAqB,EAAE,MAAM,EAAE,OAAO,OAAQ,CAAA;AAElG,MAAM,SAAS,OAAOhB,YAAgB;AAC9B,QAAA,SAAS,MAAM,OAAO,GAAG,MAAM,qBAAqB,EAAE,OAAO,EAAE,MAAMA,QAAQ,CAAA;AAExE,aAAA,SAAS,EAAE;AAEf,SAAA;AACT;AAEA,MAAM,SAAS,OAAO,QAAa,YAAiB;AAClD,QAAM,SAAS,MAAM,OAAO,GACzB,MAAM,qBAAqB,EAC3B,OAAO,EAAE,OAAO,QAAQ,MAAM,QAAS,CAAA;AAE/B,aAAA,SAAS,EAAE;AAEf,SAAA;AACT;AAEA,MAAM,WAAW,OAAO,EAAE,SAAc;AAChC,QAAA,iBAAiB,MAAM,SAAS,EAAE;AAExC,MAAI,gBAAgB;AAClB,UAAM,6BAA6B,EAAE,QAAQ,eAAe,KAAM,CAAA;AAClE,UAAM,SAAS,MAAM,OAAO,GAAG,MAAM,qBAAqB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAA,EAAM,CAAA;AAEzE,eAAA,SAAS,EAAE;AAEf,WAAA;AAAA,EACT;AAEO,SAAA;AACT;AAEA,MAAM,mBAAmB,CAAC,EAAE,KAC1B,MAAA,aAAA,EAAe,IAAI,EAAE,KAAK,kBAAkB,OAAO,KAAM,CAAA;AAE3D,MAAM,mBAAmB,MAAM,eAAe,IAAI,EAAE,KAAK,kBAAkB;AAE3E,MAAM,eAAe,OAAOG,aAAiB;AACvC,MAAAc,GAAAA,MAAMd,QAAO,GAAG;AACXA,WAAAA;AAAAA,EACT;AAEM,QAAA,gBAAgB,MAAM;AAExB,MAAA,MAAM,QAAQA,QAAO,GAAG;AACnBA,WAAAA,SAAQ,IAAI,CAACH,aAAY,EAAE,GAAGA,SAAQ,WAAW,kBAAkBA,QAAO,KAAA,EAAO;AAAA,EAC1F;AAEA,SAAO,EAAE,GAAGG,UAAS,WAAW,kBAAkBA,SAAQ;AAC5D;AAEA,MAAM,oBAAoB,YAAY;AACpC,QAAM,oBAAoB,MAAM,OAAO,GAAG,MAAM,qBAAqB,EAAE;AACvE,MAAI,sBAAsB,GAAG;AAC3B,UAAM,OAAO,cAAc;AAC3B,UAAM,iBAAiB,EAAE,MAAM,eAAe,KAAM,CAAA;AAAA,EACtD;AACF;AAEA,MAAM,+BAA+B,OAAO,EAAE,QAAAH,cAAkB;AAC9D,QAAM,EAAE,wBAAAD,wBAAA,IAA2B,WAAW,eAAe;AAE7D,QAAM,kBAAkB,OAAO,OAAO,OAAO,YAAY,EAAE,OAAOA,uBAAsB;AAExF,aAAW,SAAS,iBAAiB;AAEnC,UAAM,OAAO,GAAG,MAAM,MAAM,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,QAAAC,QAAO,EAAG,CAAA;AAAA,EACnE;AACF;AAEA,MAAM,UAAU,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AACF;AC/FA,MAAM,gBAAgB,MAAM;AAE5B,MAAM,oBAAoB,OAAO;AAAA,EAC/B;AACF;ACDA,MAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAIkB;AACJ,MAAM,EAAEtB,kBAAAA,mBAAqB,IAAAC;AAE7B,MAAM,qBAAqB,CAAC,qBAA0B;AAC7C,SAAAa,QAAK,gCAAgC,gBAAgB,MAAM;AACpE;AAEA,MAAM,iBAAiB,OAAOV,YAAgB;AACtC,QAAA,iBAAiB,WAAW,SAAS;AAEvC,MAAAiB,GAAAA,MAAMjB,OAAM,GAAG;AACjB,WAAO,eAAe;EACxB;AAEA,QAAM,cAAc,MAAM,eAAe,WAAWA,OAAM;AAC1D,MAAI,CAAC,aAAa;AACV,UAAA,IAAIJ,mBAAiB,kBAAkB;AAAA,EAC/C;AAEO,SAAAI;AACT;AAOA,MAAM,uBAAuB,CAAC,cAAmB;AAE7C,SAAA,mBAAmB,SAAS,KAC5B,sBAAsB,SAAS,KAC/B,iBAAiB,WAAW,KAAK;AAErC;AAOA,MAAM,yBAAyB,CAAC,UAAe;AAC7C,SAAO,mBAAmB,KAAK;AACjC;AAOA,MAAM,4BAA4B,CAAC,UAAe;AACzC,SAAA,qBAAqB,KAAK,EAAE;AAAA,IACjC,CAAC,aAAa,CAAC,qBAAqB,MAAM,WAAW,QAAQ,CAAC;AAAA,EAAA;AAElE;AAEA,MAAM,WAAW,CAAC,UAAe;AAC/B,MAAI,OAAO,UAAU,YAAYmB,GAAI,IAAA,MAAM,KAAK,GAAG;AACjD,WAAO,MAAM;AAAA,EACf;AACF;AAEA,MAAM,YAAY,CAAC,UAAe,CAAC,UAAe,aAAa,OAAOH,GAAAA,UAAU,KAAK,CAAC;AAEtF,MAAM,eAAe,CAAC,OAAY,UAAe;AAC3C,MAAAC,GAAAA,MAAM,KAAK,GAAG;AACT,WAAA;AAAA,EACT;AAEA,WAAS,KAAK;AAEdV,aAAA,QAAE,QAAQ,MAAM,YAAY,CAAC,MAAM,aAAa;AACxC,UAAA,QAAQ,MAAM,QAAQ;AAC5B,QAAI,KAAK,SAAS,iBAAiBC,GAAA,QAAQ,KAAK,GAAG;AAC3C,YAAA,QAAQ,CAAC,UAAU;AACnB,YAAAW,GAAA,IAAI,eAAe,KAAK,GAAG;AAC7B,gBAAMC,SAAQ,OAAO,WAAW,MAAM,WAAW;AACjD,uBAAaA,QAAO,KAAK;AAAA,QAC3B;AAAA,MAAA,CACD;AAAA,IAAA,WACQ,KAAK,SAAS,aAAa;AACpC,YAAMA,SAAQ,OAAO,WAAW,KAAK,SAAS;AAC1C,UAAAZ,GAAAA,QAAQ,KAAK,GAAG;AAClB,cAAM,QAAQ,CAAC,UAAU,aAAaY,QAAO,KAAK,CAAC;AAAA,MAAA,OAC9C;AACL,qBAAaA,QAAO,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EAAA,CACD;AAEM,SAAA;AACT;AAQA,MAAM,6BAA6B,CAAC,OAAY,UAAe;AACvD,QAAA,yBAAyB,0BAA0B,KAAK;AAEvD,SAAAC,GAAA,KAAKC,QAAK,sBAAsB,GAAG,UAAU,KAAK,CAAC,EAAE,KAAK;AACnE;AAOA,MAAM,yBAAyB,CAAC,UAAe;AACtC,SAAA,qBAAqB,KAAK,EAAE;AAAA,IAAO,CAAC,aACzC,qBAAqB,MAAM,WAAW,QAAQ,CAAC;AAAA,EAAA;AAEnD;AASA,MAAM,6BAA6B,CAAC,OAAY,cAAmB,EAAE,YAAiB;AAChF,MAAAL,GAAAA,MAAM,YAAY,GAAG;AACvB;AAAA,EACF;AAEM,QAAA,WAAW,OAAO,SAAS,KAAK;AAChC,QAAA,mBAAmB,2BAA2B,UAAU,YAAY;AAE1EV,aAAAA,QAAE,QAAQ,kBAAkB,CAAC,OAAO,UAAU;AAC5C,QAAIU,SAAM,MAAM,KAAK,CAAC,GAAG;AACvB,YAAM,KAAK,IAAI;AAAA,IACjB;AAAA,EAAA,CACD;AACH;AAMA,MAAM,4CAA4C,CAAC,aAAkB;AAC7D,QAAA1B,UAAS,OAAO,SAAS,QAAQ;AACjC,QAAA,mBAAmB,oBAAoBA,OAAM;AAC7C,QAAA,yBAAyB,0BAA0BA,OAAM;AAE/D,QAAM,gBAAgB,CAAC,GAAG,kBAAkB,GAAG,sBAAsB;AACjE,MAAAA,QAAO,cAAc,aAAa;AAGpC,kBAAc,KAAK,GAAG,wBAAwBA,OAAM,CAAC;AAAA,EACvD;AAEA,QAAM,8BAA8B,cAAc,OAAO,CAAC,OAAOgC,QAAO,SAAS;AACxE,WAAA,KAAK,QAAQ,KAAK,MAAMA,UAAS,KAAK,YAAY,KAAK,MAAMA;AAAA,EAAA,CACrE;AAEK,QAAA,uBAAuB,CAAC,GAAG,2BAA2B;AAC5D,aAAW,YAAY,6BAA6B;AAC5C,UAAA,OAAOhC,QAAO,WAAW,QAAQ;AACnC,QAAA,KAAK,SAAS,aAAa;AAC7B,YAAM,iBAAiB,0CAA0C,KAAK,SAAS,EAAE;AAAA,QAC/E,CAAC,eAAe,GAAG,QAAQ,IAAI,UAAU;AAAA,MAAA;AAEtB,2BAAA,KAAK,GAAG,cAAc;AAAA,IAAA,WAClC,KAAK,SAAS,eAAe;AACjC,WAAA,WAAW,QAAQ,CAAC,kBAAkB;AACnC,cAAA,iBAAiB,0CAA0C,aAAa,EAAE;AAAA,UAC9E,CAAC,eAAe,GAAG,QAAQ,IAAI,UAAU;AAAA,QAAA;AAEtB,6BAAA,KAAK,GAAG,cAAc;AAAA,MAAA,CAC5C;AAAA,IACH;AAAA,EACF;AAEO,SAAA;AACT;AAEA,MAAM,eAAe,OAAO;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AC9LA,MAAe,WAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAeiC;AAAAA,EACf,iBAAiB;AACnB;ACdA,MAAe,QAAA;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAAA,IACN;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,CAAC,0BAA0B,EAAE;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU,CAAC,6BAA6B;AAAA,MAC1C;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,CAAC,4BAA4B,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,CAAC,4BAA4B,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,CAAC,4BAA4B,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,UAAU,CAAC,6BAA6B;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACF;AC5EA,MAAe,aAAA;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAAA,IACN;AAAA,MACE,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;ACNA,MAAe,SAAA;AAAA,EACb;AAAA,EACA,eAAe;AACjB;ACDA,MAAM,qBAAqB,WAAW,IAAId,GAAAA,KAAK,MAAM,CAAC;AAEtD,MAAM,qBAAqBe,MAAA,IACxB,OAAO,EACP,MAAM;AAAA,EACL,MAAMA,MAAI,IAAA,OAAA,EAAS,IAAI,EAAE,EAAE,SAAS;AAAA,EACpC,MAAMA,MAAI,IAAA,OAAA,EAAS,MAAM,kBAAkB,EAAE,SAAS;AAAA,EACtD,WAAWA,MAAA,IAAI,QAAQ,EAAE,SAAS;AACpC,CAAC,EACA,UAAU;AAEb,MAAM,qBAAqBA,MAAA,IACxB,OAAO,EACP,MAAM;AAAA,EACL,MAAMA,MAAAA,IAAI,OAAA,EAAS,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;AAAA,EAC3C,WAAWA,UAAI,QAAQ;AACzB,CAAC,EACA,UAAU;AAEb,MAAM,4BAA4BC,MAAAA,kBAAkB,kBAAkB;AACtE,MAAM,4BAA4BA,wBAAkB,kBAAkB;ACzBtE,MAAM,eAAe,CAAC1B,YAA+D;AAC5E,SAAA;AAAA,IACL,GAAGA;AAAA,IACH,MAAMA,QAAO,QAAQ;AAAA,EAAA;AAEzB;ACEA,MAAM,EAAE,iBAAqB,IAAA2B;AAC7B,MAAM,oBAAE/B,mBAAiB,IAAI+B,iBAAM;AAEnC,MAAM,iBAAiB,CAAC3B,YAAgB;AAChC,QAAA,QAAQ,OAAO,SAAS,qBAAqB;AAEnD,SAAO,OAAO,WAAW,SAAS,OAAOA,SAAQ,KAAK;AACxD;AAEA,MAAM4B,eAA8B;AAAA,EAClC,MAAM,YAAY,KAAK;AACf,UAAA,iBAAiB,WAAW,SAAS;AAErC,UAAAzB,WAAU,MAAM,eAAe;AAC/B,UAAA,mBAAmB,MAAM,eAAeA,QAAO;AAErD,QAAI,OAAO,MAAM,eAAe,aAAa,gBAAgB;AAAA,EAC/D;AAAA,EAEA,MAAM,aAAa,KAAK;AAChB,UAAA,EAAE,KAAK,IAAI,IAAI;AACf,UAAA,OAAO,IAAI,QAAQ;AACzB,UAAM,EAAE,WAAW,GAAG,eAAA,IAAmB;AAEzC,UAAM,0BAA0B,IAAI;AAE9B,UAAA,iBAAiB,WAAW,SAAS;AAE3C,UAAM,iBAAiB,MAAM,eAAe,WAAW,KAAK,IAAI;AAChE,QAAI,gBAAgB;AACZ,YAAA,IAAIP,mBAAiB,4BAA4B;AAAA,IACzD;AAEM,UAAA,kBAAkB,iBAAiB,EAAE,KAAM,CAAA,EAAE,aAAa,cAAc,CAAC;AAE/E,UAAMI,UAAS,MAAM,eAAe,OAAO,eAAe;AAE1D,QAAI,WAAW;AACP,YAAA,eAAe,iBAAiBA,OAAM;AAAA,IAC9C;AAEM,UAAA,kBAAkB,MAAM,eAAeA,OAAM;AAEnD,QAAI,OAAO,MAAM,eAAe,aAAa,eAAe;AAAA,EAC9D;AAAA,EAEA,MAAM,aAAa,KAAK;AAChB,UAAA,EAAE,KAAK,IAAI,IAAI;AACf,UAAA,EAAE,GAAG,IAAI,IAAI;AACb,UAAA,OAAO,IAAI,QAAQ;AACzB,UAAM,EAAE,WAAW,GAAG,QAAA,IAAY;AAElC,UAAM,0BAA0B,IAAI;AAE9B,UAAA,iBAAiB,WAAW,SAAS;AAE3C,UAAM,iBAAiB,MAAM,eAAe,SAAS,EAAE;AACvD,QAAI,CAAC,gBAAgB;AACZ,aAAA,IAAI,SAAS,iBAAiB;AAAA,IACvC;AAEM,UAAA,gBAAgB,CAAC,MAAM;AACvB,UAAA,eAAe,iBAAiB,EAAE,MAAM,WAAW,KAAM,CAAA,EAAEsB,GAAA,KAAK,eAAe,OAAO,CAAC;AAE7F,UAAM,gBAAgB,MAAM,eAAe,OAAO,EAAE,GAAA,GAAM,YAAY;AAEtE,QAAI,WAAW;AACP,YAAA,eAAe,iBAAiB,aAAa;AAAA,IACrD;AAEM,UAAA,kBAAkB,MAAM,eAAe,aAAa;AAE1D,QAAI,OAAO,MAAM,eAAe,aAAa,eAAe;AAAA,EAC9D;AAAA,EAEA,MAAM,aAAa,KAAK;AAChB,UAAA,EAAE,GAAG,IAAI,IAAI;AAEb,UAAA,iBAAiB,WAAW,SAAS;AAE3C,UAAM,iBAAiB,MAAM,eAAe,SAAS,EAAE;AACvD,QAAI,CAAC,gBAAgB;AACZ,aAAA,IAAI,SAAS,iBAAiB;AAAA,IACvC;AAEM,UAAA,oBAAoB,MAAM,eAAe;AAC3C,QAAA,eAAe,SAAS,mBAAmB;AACvC,YAAA,IAAI1B,mBAAiB,kCAAkC;AAAA,IAC/D;AAEA,UAAM,eAAe,OAAO,EAAE,GAAI,CAAA;AAE5B,UAAA,kBAAkB,MAAM,eAAe,cAAc;AAE3D,QAAI,OAAO,MAAM,eAAe,aAAa,eAAe;AAAA,EAC9D;AACF;ACnGA,MAAM,0CAA0C6B,MAAA,IAC7C,OAAO,EACP,MAAM;AAAA,EACL,OAAOA,MAAA,IAAI,OAAO,EAAE,SAAS;AAAA,EAC7B,IAAIA,MAAAA,IAAI,QAAQ,KAAK,SAAS;AAAA,IAC5B,IAAI,CAAC,UAAexB,OAAI,QAAQ,OAAO,YAAY,KAAK,CAAC,MAAM;AAAA,IAC/D,MAAMwB,MAAA,IAAI,SAAS,EAAE,SAAS;AAAA,IAC9B,WAAWA,MAAA,IAAI,SAAS,EAAE,SAAS;AAAA,EAAA,CACpC;AAAA,EACD,QAAQA,MAAA,IAAI,OAAO,EAAE,SAAS;AAChC,CAAC,EACA,YACA;AAEH,MAAM,yCAAyCC,MAAA;AAAA,EAC7C;AACF;ACdA,MAAM,EAAE,iBAAqB,IAAA7B;AAE7B,MAAM,EAAE,uBAAuB,IAAIgC,MAAkB,aAAA;AAErD,MAAM,qBAAqBlB,GAAA,MAAgB,IAAI,oBAAoB;AACnE,MAAM,oBAAoBD,GAAAA,KAAK,mBAAmB;AAElD,MAAM,oBAAoBoB,GAAAA,IAAI,CAAC,SAAiB,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC;AAElE,MAAMF,eAAa;AAAA,EACjB,MAAM,0BAA0B,KAAK;AAC7B,UAAA,EAAE,KAAK,IAAI,IAAI;AACf,UAAA,OAAO,IAAI,QAAQ;AACzB,UAAM,EAAE,OAAO,IAAI,QAAA5B,QAAA,IAAW;AAE9B,UAAM,uCAAuC,EAAE,OAAO,IAAI,QAAAA,QAAQ,CAAA;AAE5D,UAAA;AAAA,MACJ,4BAAAc;AAAA,MACA,wBAAAf;AAAA,MACA,2CAAAP;AAAA,IAAA,IACE,WAAW,eAAe;AAExB,UAAA;AAAA,MACJ,SAAS,EAAE,aAAa,cAAc;AAAA,IAAA,IACpC,OAAO,QAAQ,kBAAkB;AAE/B,UAAA,WAAW,OAAO,YAAY,KAAK;AACnC,UAAA,uBAAuBA,2CAA0C,KAAK;AAExE,QAAA,CAACO,wBAAuB,QAAQ,GAAG;AACrC,YAAM,IAAI,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,IAC9D;AAEA,UAAM,SAAS,SAAS,SAAS,eAAe,CAAC,IAAI,EAAE;AAEvD,UAAM,SAAS,MAAM,OAAO,GACzB,MAAM,KAAK,EACX,QAAQ,EAAE,OAAO,QAAQ,UAAU,qBAAsB,CAAA;AAE5D,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI;IACb;AAEA,UAAMU,eAAc,MAAM,OAAO,MAAM,SAAS,WAAW,SAAS;AAAA,MAClE,OAAO;AAAA,QACL,QAAQ,CAAC,aAAa,aAAa;AAAA,QACnC,SAAS;AAAA,QACT,MAAM;AAAA,UACJ,IAAI,KAAK,MAAM,IAAIC,GAAA,KAAK,IAAI,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IAAA,CACD;AAED,UAAM,oBAAoBD,aACvB,OAAO,CAAC,SAAc,mBAAmB,IAAI,EAAE,SAAST,OAAM,CAAC,EAC/D,IAAI,iBAAiB;AAExB,UAAM,kBAAkBqB,GAAAA,KAAKU,GAAA,SAAS,mBAAmBC,GAAI,IAAA,EAAE,iBAAiB;AAE1E,UAAA,qBAAqBlB,4BAA2B,UAAU,MAAM;AAChE,UAAA,8BAA8BQ,GAAAA,KAAK,iBAAiB,kBAAkB;AAEtE,UAAA,yBAAyB,MAAM,OAAO,QAAQ,iBAAiB,EAClE,QAAQ,mBAAmB,EAC3B,YAAY,OAAO,QAAQ;AAAA,MAC1B,kBAAkB;AAAA,IAAA,CACnB;AAEG,UAAA,mBAAmB,uBAAuB,iBAAiB;AAAA,MAAI,CAAC,iBACpEA,GAAAA,KAAK,CAAC,MAAM,UAAU,sBAAsB,GAAG,YAAY;AAAA,IAAA;AAG7D,QAAI,OAAO;AAAA,MACT,oBAAoB;AAAA,MACpB,eAAe,iBAAiB;AAAA,QAC9BA,GAAAA,KAAK,CAAC,MAAM,UAAU,sBAAsB,GAAG,MAAM;AAAA,MACvD;AAAA,IAAA;AAAA,EAEJ;AACF;ACnFA,MAAM,aAA8B;AAAA,EAClC,eAAe,KAAK;AACZ,UAAAW,qBAAoB,WAAW,aAAa;AAE9C,QAAA,OAAOA,mBAAkB;EAC/B;AACF;ACLA,MAAe,cAAA;AAAA,EAAA,SACb9B;AAAAA,EACA,eAAeqB;AAAAA,EACf,iBAAiBU;AACnB;ACDA,MAAA,QAAe,OAAO;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAAA,cACAA;AAAAA,EACA;AACF;;"}