'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const HomePage = require('./HomePage-T2yVEfC3.js');
const useLicenseLimitNotification = require('./useLicenseLimitNotification-eau4ja6h.js');

const HomePageEE = () => {
  useLicenseLimitNotification.useLicenseLimitNotification();
  return /* @__PURE__ */ jsxRuntime.jsx(HomePage.HomePageCE, {});
};

exports.HomePageEE = HomePageEE;
//# sourceMappingURL=HomePage-yyizKL39.js.map
