/**
 * Tipos extendidos para el modelo de usuario
 */

/**
 * Interfaz extendida para el modelo de usuario que incluye propiedades personalizadas
 */
export interface ExtendedUser {
  role?: {
    id: number;
    name: string;
    type: string;
  };
  address?: string;
  arrendatarios?: Array<{
    id: number;
    username?: string;
    email?: string;
    [key: string]: any;
  }>;
  usuarioPrincipal?: {
    id: number;
    username?: string;
    email?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * Tipos de métodos HTTP válidos
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';
