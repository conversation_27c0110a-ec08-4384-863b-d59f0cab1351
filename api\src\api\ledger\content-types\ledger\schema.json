{"kind": "collectionType", "collectionName": "ledgers", "info": {"singularName": "ledger", "pluralName": "ledgers", "displayName": "Ledger", "description": "Entradas del libro de contabilidad"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"month": {"type": "string", "required": true}, "expensa": {"type": "decimal", "required": true}, "honorarios": {"type": "decimal", "required": true}, "interes": {"type": "decimal", "required": true}, "pagos": {"type": "decimal", "required": true}, "saldo": {"type": "decimal", "required": true}, "owner": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "ledger"}, "apartmentAddress": {"type": "string", "description": "Dirección del apartamento cuando no hay usuario asociado"}, "fecha": {"type": "datetime", "required": true}, "estado": {"type": "enumeration", "enum": ["pendiente", "pagado", "<PERSON><PERSON><PERSON><PERSON>"], "default": "pendiente"}, "comprobante": {"type": "media", "multiple": false, "required": false}, "comentarios": {"type": "text", "required": false}}}