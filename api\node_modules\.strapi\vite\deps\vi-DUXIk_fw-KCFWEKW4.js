import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/vi-DUXIk_fw.mjs
var groups = "Nhóm";
var pageNotFound = "Không Tìm Thấy Trang";
var vi = {
  "EditRelations.title": "Dữ Liệ<PERSON>",
  "components.AddFilterCTA.add": "Lọc",
  "components.AddFilterCTA.hide": "Lọc",
  "components.DraggableAttr.edit": "Nhấn để chỉnh sửa",
  "components.EmptyAttributesBlock.button": "Đến trang cài đặt",
  "components.EmptyAttributesBlock.description": "Bạn có thể thay đổi cài đặt của bạn",
  "components.FilterOptions.button.apply": "Áp dụng",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Áp dụng",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Xóa tất cả",
  "components.FiltersPickWrapper.PluginHeader.description": "Cài đặt các điều kiện để áp dụng cho việc lọc các bản ghi",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Các bộ lọc",
  "components.FiltersPickWrapper.hide": "Ẩn đi",
  "components.LimitSelect.itemsPerPage": "Số lượng bản ghi trong trang",
  "components.Search.placeholder": "Tìm một bản ghi...",
  "components.TableDelete.delete": "Xóa tất cả",
  "components.TableDelete.deleteSelected": "Xóa đã chọn",
  "components.TableEmpty.withFilters": "Không có {contentType} với bộ lọc được dùng",
  "components.TableEmpty.withSearch": "Không có {contentType} tương ứng với tìm kiếm ({search})...",
  "components.TableEmpty.withoutFilter": "Không có {contentType}...",
  "containers.Edit.Link.Layout": "Cấu hình bố cục",
  "containers.Edit.addAnItem": "Thêm một bản ghi...",
  "containers.Edit.clickToJump": "Nhấn để nhảy vào bản ghi",
  "containers.Edit.delete": "Xóa",
  "containers.Edit.editing": "Đăng sửa...",
  "containers.Edit.pluginHeader.title.new": "Tạo một Bản ghi",
  "containers.Edit.reset": "Làm lại",
  "containers.Edit.returnList": "Trở về danh sách",
  "containers.Edit.seeDetails": "Chi tiết",
  "containers.Edit.submit": "Lưu",
  "containers.EditView.notification.errors": "Bảng nhập liệu có vài lỗi",
  "containers.Home.introduction": "Để chỉnh sửa các bản ghi của bạn, đi đến liên kết ở menu bên trái. Plugin này chưa có cách thích hợp để chỉnh sửa các cài đặt và nó vẫn đang được phát triển.",
  "containers.Home.pluginHeaderDescription": "Quản lý các bản ghi thông qua một giao diện mạnh và đẹp.",
  "containers.Home.pluginHeaderTitle": "Quản Lý Nội Dung",
  "containers.List.errorFetchRecords": "Lỗi",
  "containers.list.displayedFields": "Các trường đã được trình bày",
  "containers.SettingPage.attributes": "Các trường thuộc tính",
  "containers.SettingPage.attributes.description": "Định nghĩa thứ tự các thuộc tính",
  "containers.SettingPage.editSettings.description": "Kéo & thả các trường để xây dựng bố cục",
  "containers.SettingPage.editSettings.entry.title": "Tên bản ghi",
  "containers.SettingPage.editSettings.entry.title.description": "Cài đặt trường được trình bày trong bản ghi của bạn",
  "containers.SettingPage.editSettings.title": "Chỉnh sửa hiển thị (các cài đặt)",
  "containers.SettingPage.layout": "Bố cục",
  "containers.SettingPage.listSettings.title": "Hiển thị danh sách (các cài đặt)",
  "containers.SettingPage.settings": "Các cài đặt",
  "containers.SettingViewModel.pluginHeader.title": "Quản Lý Nội Dung - {name}",
  "containers.SettingsPage.Block.contentType.description": "Cấu hình các cài đặt riêng",
  "containers.SettingsPage.Block.generalSettings.title": "Chung",
  "containers.SettingsView.list.title": "Các cấu hình về Trình bày",
  "emptyAttributes.title": "Chưa có trường nào hết",
  "error.attribute.key.taken": "Giá trị này đã tồn tại",
  "error.attribute.sameKeyAndName": "Không thể bằng nhau",
  "error.attribute.taken": "Tên trường này đã tồn tại",
  "error.contentTypeName.taken": "Tên này đã tồn tại",
  "error.model.fetch": "Một lỗi đã xảy ra trong khi lấy về cấu hình nội dung.",
  "error.record.create": "Một lỗi đã xảy ra trong khi tạo bản ghi.",
  "error.record.delete": "Một lỗi đã xảy ra trong khi xoá bản ghi.",
  "error.record.fetch": "Một lỗi đã xảy ra trong khi lấy về bản ghi.",
  "error.record.update": "Một lỗi đã xảy ra trong khi cập nhật bản ghi.",
  "error.records.count": "Một lỗi đã xảy ra trong khi lấy về số lượng bản ghi.",
  "error.records.fetch": "Một lỗi đã xảy ra trong khi lấy về các bản ghi.",
  "error.schema.generation": "Một lỗi đã xảy ra trong khi quá trình tạo ra lược đồ.",
  "error.validation.json": "Đây không phải là định dạng JSON",
  "error.validation.max": "Giá trị quá cao.",
  "error.validation.maxLength": "Giá trị quá dài.",
  "error.validation.min": "Giá trị quá thấp.",
  "error.validation.minLength": "Giá trị quá ngắn.",
  "error.validation.minSupMax": "Không thể là trên mũ",
  "error.validation.regex": "Giá trị không khới với regex.",
  "error.validation.required": "Giá trị này bắt buộc.",
  "form.Input.bulkActions": "Kích hoạt hoạt động gộp",
  "form.Input.defaultSort": "Thuộc tính sắp xếp mặc định",
  "form.Input.description": "Mô tả",
  "form.Input.description.placeholder": "Tên hiển thị trong hồ sơ",
  "form.Input.editable": "Trường chỉnh sửa được",
  "form.Input.filters": "Kích hoạt các bộ lọc",
  "form.Input.label": "Nhãn",
  "form.Input.label.inputDescription": "Giá trị này ghi đè lên nhãn được trình bày trong phần đầu của bảng",
  "form.Input.pageEntries": "Bản ghi trong trang",
  "form.Input.placeholder": "Chỗ chờ giá trị",
  "form.Input.placeholder.placeholder": "Giá trị tuyệt vời của tôi",
  "form.Input.search": "Kích hoạt tìm kiếm",
  "form.Input.search.field": "Kích hoạt tìm kiếm cho trường này",
  "form.Input.sort.field": "Kích hoạt sắp xếp trên trường này",
  "form.Input.wysiwyg": "Trình bày như là WYSIWYG",
  "global.displayedFields": "Các Trường Đã Được Trình Bày",
  groups,
  "groups.numbered": "Nhóm ({number})",
  "notification.error.displayedFields": "Bạn cần trình bày ít nhất một trường",
  "notification.error.relationship.fetch": "Một lỗi đã xảy ra trong khi lấy về mối quan hệ.",
  "notification.info.SettingPage.disableSort": "Bạn cần có một thuộc tính được phép sắp xếp",
  "notification.info.minimumFields": "Bạn cần hiển thị ít nhất một trường",
  "notification.upload.error": "Một lỗi đã xảy ra trong khi tải lên các tập tin của bạn",
  pageNotFound,
  "plugin.description.long": "Cách nhanh để xem, sửa và xoá dữ liệu trong cơ sở dữ liệu của bạn.",
  "plugin.description.short": "Cách nhanh để xem, sửa và xoá dữ liệu trong cơ sở dữ liệu của bạn.",
  "popUpWarning.bodyMessage.contentType.delete": "Bạn có chắc là muốn xoá bản ghi này không?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Bạn có chắc là muốn xoá các bản ghi này không?",
  "popUpWarning.warning.cancelAllSettings": "Bạn có chắc là muốn hủy bỏ các thay đổi của bạn?",
  "popUpWarning.warning.updateAllSettings": "Nó sẽ thay đổi tất cả cài đặt của bạn",
  "success.record.delete": "Đã xoá",
  "success.record.save": "Đã lưu"
};
export {
  vi as default,
  groups,
  pageNotFound
};
//# sourceMappingURL=vi-DUXIk_fw-KCFWEKW4.js.map
