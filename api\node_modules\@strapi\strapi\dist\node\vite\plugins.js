"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const staticFiles = require("../staticFiles.js");
const buildFilesPlugin = (ctx) => {
  const CHUNK_ID = ".strapi/client/app.js";
  return {
    name: "strapi/server/build-files",
    apply: "build",
    buildStart() {
      this.emitFile({
        type: "chunk",
        id: CHUNK_ID,
        name: "strapi"
      });
    },
    async generateBundle(_options, outputBundle) {
      const bundle = outputBundle;
      const entryFile = Object.values(bundle).find(
        (file) => file.type === "chunk" && file.name === "strapi" && file.facadeModuleId?.endsWith(CHUNK_ID)
      );
      if (!entryFile) {
        throw new Error(`Failed to find entry file in bundle (${CHUNK_ID})`);
      }
      if (entryFile.type !== "chunk") {
        throw new Error("Entry file is not a chunk");
      }
      const entryFileName = entryFile.fileName;
      const entryPath = [ctx.basePath.replace(/\/+$/, ""), entryFileName].join("/");
      this.emitFile({
        type: "asset",
        fileName: "index.html",
        source: staticFiles.getDocumentHTML({
          logger: ctx.logger,
          props: {
            entryPath
          }
        })
      });
    }
  };
};
exports.buildFilesPlugin = buildFilesPlugin;
//# sourceMappingURL=plugins.js.map
