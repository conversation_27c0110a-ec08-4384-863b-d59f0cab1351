/**
 * Hook que combina React Query con WebSockets para notificaciones
 * Reemplaza el hook SSE manteniendo la misma interfaz
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Modal, message } from "antd";
import { Http } from "@app/config/http";
import { useAuthContext } from "@app/context/auth/AuthContext";
import { useWebSocketNotifications } from "./use-websocket-notifications";
import type {
  Notification,
  NotificationsResponse,
} from "@app/types/notifications";

const STALE_TIME = 60000; // 1 minuto
const REFRESH_INTERVAL = 600000; // 10 minutos
const NOTIFICATIONS_QUERY_KEY = "notifications";

/**
 * Hook personalizado que combina React Query con WebSockets para gestionar notificaciones
 * @returns Objeto con notificaciones y funciones para gestionarlas
 */
export const useQueryWebSocketNotifications = () => {
  const { user, token, isAuthenticated } = useAuthContext();
  const queryClient = useQueryClient();

  // Hook WebSocket para tiempo real
  const {
    connectionStatus: wsConnectionStatus,
    connectionError: wsConnectionError,
    isConnected: wsIsConnected,
    lastMessage,
  } = useWebSocketNotifications();

  // Query principal para obtener notificaciones
  const {
    data: notificationsData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<NotificationsResponse>({
    queryKey: [NOTIFICATIONS_QUERY_KEY, user?.id],
    queryFn: async () => {
      try {
        const response = await Http.get(`/api/notifications/user/${user?.id}`);
        return response.data;
      } catch (error) {
        console.error("Error al obtener notificaciones:", error);
        throw error;
      }
    },
    enabled: !!user && !!token && isAuthenticated,
    staleTime: STALE_TIME,
    refetchInterval: REFRESH_INTERVAL,
    refetchOnWindowFocus: false,
  });

  // Mutación para marcar como leída
  const markAsReadMutation = useMutation({
    mutationFn: async (id: number | string) => {
      const response = await Http.put(`/api/notifications/${id}/read`);
      return response.data;
    },
    onSuccess: (data, id) => {
      // Actualizar la caché local
      queryClient.setQueryData(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData: NotificationsResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.map((notification) =>
              notification.id === id
                ? { ...notification, read: true }
                : notification,
            ),
          };
        },
      );

      message.success("Notificación marcada como leída");
    },
    onError: (error) => {
      console.error("Error al marcar notificación como leída:", error);
      message.error("Error al marcar la notificación como leída");
    },
  });

  // Mutación para marcar todas como leídas
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const response = await Http.put(
        `/api/notifications/user/${user?.id}/read-all`,
      );
      return response.data;
    },
    onSuccess: () => {
      // Actualizar la caché local
      queryClient.setQueryData(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData: NotificationsResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.map((notification) => ({
              ...notification,
              read: true,
            })),
          };
        },
      );

      message.success("Todas las notificaciones marcadas como leídas");
    },
    onError: (error) => {
      console.error(
        "Error al marcar todas las notificaciones como leídas:",
        error,
      );
      message.error("Error al marcar todas las notificaciones como leídas");
    },
  });

  // Mutación para eliminar notificación
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: number | string) => {
      const response = await Http.delete(`/api/notifications/${id}`);
      return response.data;
    },
    onSuccess: (data, id) => {
      // Actualizar la caché local
      queryClient.setQueryData(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData: NotificationsResponse | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: oldData.data.filter((notification) => notification.id !== id),
          };
        },
      );

      message.success("Notificación eliminada");
    },
    onError: (error) => {
      console.error("Error al eliminar notificación:", error);
      message.error("Error al eliminar la notificación");
    },
  });

  // Función para mostrar detalles de notificación
  const showNotificationDetail = useCallback(
    (notification: Notification) => {
      const content = notification.data
        ? `${notification.message}\n\nDatos adicionales:\n${JSON.stringify(notification.data, null, 2)}`
        : notification.message;

      Modal.info({
        title: notification.title,
        content: content,
        onOk: () => {
          if (!notification.read) {
            markAsReadMutation.mutate(notification.id);
          }
        },
      });
    },
    [markAsReadMutation],
  );

  // Procesar mensajes WebSocket en tiempo real
  useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case "notification":
        // Nueva notificación recibida vía WebSocket
        const notification = lastMessage.data || lastMessage.notification;
        if (notification) {
          console.log("🔔 Nueva notificación WebSocket:", notification);

          // Actualizar caché de React Query
          queryClient.setQueryData(
            [NOTIFICATIONS_QUERY_KEY, user?.id],
            (oldData: NotificationsResponse | undefined) => {
              if (!oldData) return { data: [notification] };

              // Verificar si la notificación ya existe
              const exists = oldData.data.some((n) => n.id === notification.id);

              if (exists) {
                // Actualizar notificación existente
                return {
                  ...oldData,
                  data: oldData.data.map((n) =>
                    n.id === notification.id ? notification : n,
                  ),
                };
              } else {
                // Agregar nueva notificación al principio
                return {
                  ...oldData,
                  data: [notification, ...oldData.data],
                };
              }
            },
          );
        }
        break;

      case "broadcast":
        console.log("📡 Broadcast recibido:", lastMessage);
        break;

      case "connection":
        console.log("🔗 Conexión WebSocket establecida");
        break;

      case "heartbeat":
        // Heartbeat silencioso
        break;

      case "pong":
        // Pong silencioso (respuesta al ping)
        break;

      default:
        console.log("❓ Mensaje WebSocket desconocido:", lastMessage);
    }
  }, [lastMessage, queryClient, user?.id]);

  // Extraer notificaciones de la respuesta
  const notifications = notificationsData?.data || [];

  // Contar notificaciones no leídas
  const unreadCount = notifications.filter((n) => !n.read).length;

  // Determinar si estamos conectados (WebSocket o datos disponibles)
  const isConnected = wsIsConnected || notifications.length > 0;

  // Estado de conexión (priorizar WebSocket)
  const connectionStatus = wsConnectionStatus;
  const connectionError = wsConnectionError;

  return {
    // Datos
    notifications,
    unreadCount,
    isConnected,
    connectionStatus,
    connectionError,
    isLoading,
    isError,
    error,

    // Acciones
    markAsRead: (id: number | string) => markAsReadMutation.mutate(id),
    markAllAsRead: () => markAllAsReadMutation.mutate(),
    deleteNotification: (id: number | string) =>
      deleteNotificationMutation.mutate(id),
    showNotificationDetail,
    loadNotifications: refetch,

    // Funciones de conexión (para compatibilidad)
    connect: () => {
      console.log(
        "🔗 Conectar solicitado (WebSocket se conecta automáticamente)",
      );
    },
    disconnect: () => {
      console.log(
        "🔌 Desconectar solicitado (WebSocket se desconecta automáticamente)",
      );
    },
  };
};

export default useQueryWebSocketNotifications;
