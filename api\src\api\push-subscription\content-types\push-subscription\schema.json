{"kind": "collectionType", "collectionName": "push_subscriptions", "info": {"singularName": "push-subscription", "pluralName": "push-subscriptions", "displayName": "Push Subscription", "description": "Store push notification subscriptions"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"endpoint": {"type": "string", "required": true, "unique": true}, "expirationTime": {"type": "datetime"}, "keys": {"type": "json", "required": true}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}