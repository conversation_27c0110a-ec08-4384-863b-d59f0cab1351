import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/zh-Hans-XLMwjASk.mjs
var zhHans = {
  "components.Row.open": "打开",
  "components.Row.regenerate": "重新生成",
  "containers.HomePage.Block.title": "版本号",
  "containers.HomePage.Button.update": "更新",
  "containers.HomePage.PluginHeader.title": "文档 — 设置",
  "containers.HomePage.PopUpWarning.confirm": "我确定",
  "containers.HomePage.PopUpWarning.message": "您确定要删除此版本吗？",
  "containers.HomePage.copied": "令牌已被复制到粘贴板",
  "containers.HomePage.form.jwtToken": "检索您的 JWT 令牌",
  "containers.HomePage.form.jwtToken.description": "复制此令牌，并用它在 Swagger 中发出请求",
  "containers.HomePage.form.password": "密码",
  "containers.HomePage.form.password.inputDescription": "设置密码以供访问文档",
  "containers.HomePage.form.restrictedAccess": "禁止访问",
  "containers.HomePage.form.restrictedAccess.inputDescription": "将文档端点设为私有。默认情况下，访问权限是公共的",
  "containers.HomePage.form.showGeneratedFiles": "显示生成的文件",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "当您想要覆盖生成的文档的时候很有用。 \n该插件将生成按型号和插件拆分的文件。 \n通过启用此选项，可以更轻松地自定义文档",
  "error.deleteDoc.versionMissing": "您尝试删除的版本不存在。",
  "error.noVersion": "需要一个版本",
  "error.regenerateDoc": "重新生成文件时发生错误",
  "error.regenerateDoc.versionMissing": "您尝试重新生成的版本不存在",
  "notification.delete.success": "文档删除成功",
  "notification.generate.success": "文档生成成功",
  "notification.update.success": "设置更新成功",
  "plugin.description.long": "创建OpenAPI文档并使用SwaggerUI可视化你的API",
  "plugin.description.short": "创建OpenAPI文档并使用SwaggerUI可视化你的API",
  "plugin.name": "文档"
};
export {
  zhHans as default
};
//# sourceMappingURL=zh-Hans-XLMwjASk-5TRKZ33L.js.map
