/**
 * warning controller
 */

import { factories } from '@strapi/strapi'

type ID = string | number;

interface Rule {
  id: ID;
  name: string;
}

interface Resident {
  id: ID;
  firstName: string;
  lastName: string;
  username: string;
  address?: string;
}

interface SanctionBy {
  firstName?: string;
  lastName?: string;
  role?: string;
  userId?: ID;
}

type DateValue = string | Date;
type TimeValue = string | Date;

interface Warning {
  id: ID;
  date?: DateValue;
  time?: TimeValue;
  type?: string;
  description?: string;
  resident?: Resident;
  rules?: Rule[];
  evidencePhotos?: any;
  notifications?: any;
  sanctionBy?: SanctionBy;
}

const sanitizeOutput = (entity: Warning) => {
  const sanitizedEntity = { ...entity };
  
  if (sanitizedEntity.resident) {
    sanitizedEntity.resident = {
      id: entity.resident.id,
      firstName: entity.resident.firstName,
      lastName: entity.resident.lastName,
      username: entity.resident.username,
      address: entity.resident.address
    };
  }

  return sanitizedEntity;
};

export default factories.createCoreController('api::warning.warning', ({ strapi }) => ({
  async delete(ctx) {
    const { id } = ctx.params;

    try {
      // Verificar si la advertencia existe
      const warning = await strapi.entityService.findOne('api::warning.warning', id);
      if (!warning) {
        return ctx.notFound('Warning not found');
      }

      // Eliminar la advertencia
      const deletedWarning = await strapi.entityService.delete('api::warning.warning', id);

      // Retornar 204 No Content para indicar éxito sin contenido
      ctx.response.status = 204;
      return;
    } catch (error) {
      console.error('Error al eliminar la advertencia:', error);
      return ctx.badRequest('Error al eliminar la advertencia');
    }
  },
  async find(ctx) {
    const { data, meta } = await super.find(ctx);

    const populatedData = await Promise.all(
      data.map(async (warning) => {
        const entity = await strapi.entityService.findOne('api::warning.warning', warning.id, {
          populate: ['resident', 'rules', 'evidencePhotos', 'notifications', 'sanctionBy']
        });
        return sanitizeOutput(entity);
      })
    );

    return { data: populatedData, meta };
  },

  async findByResident(ctx) {
    const { id } = ctx.params;
    
    const warnings = await strapi.entityService.findMany('api::warning.warning', {
      filters: {
        resident: id
      },
      populate: ['resident', 'rules', 'evidencePhotos', 'notifications', 'sanctionBy']
    });

    const sanitizedData = warnings.map(warning => sanitizeOutput(warning));

    return { data: sanitizedData };
  },

  async create(ctx) {
    const { data } = ctx.request.body;
    
    // Obtener el usuario actual
    const currentUser = ctx.state.user;

    // Asegurarnos que solo pasamos los campos necesarios
    const sanitizedData = {
      date: data.date,
      time: data.time,
      type: data.type,
      description: data.description,
      resident: data.resident,
      rules: data.rules,
      notifications: data.notifications,
      sanctionBy: {
        firstName: currentUser.firstName || '',
        lastName: currentUser.lastName || '',
        role: currentUser.role?.name || 'authenticated',
        userId: currentUser.id
      }
    };

    const entity = await strapi.entityService.create('api::warning.warning', {
      data: sanitizedData,
      populate: {
        resident: true,
        rules: true,
        evidencePhotos: true,
        notifications: true,
        sanctionBy: true
      }
    }) as Warning;

    // Enviar notificación al residente
    if (entity.resident) {
      const rulesText = entity.rules && entity.rules.length > 0
        ? `Reglas infringidas: ${entity.rules.map(rule => rule.name).join(', ')}`
        : '';

      const message = `Se ha generado una sanción por ${entity.type}. ${rulesText}\nDescripción: ${entity.description}`;

      await strapi.service('api::notification.notification').create({
        data: {
          title: 'Nueva Sanción',
          type: 'warning_created',
          message,
          user: entity.resident.id,
          data: {
            warningId: entity.id,
            type: entity.type,
            date: entity.date,
            time: entity.time
          },
        },
      });
    }

    return { data: sanitizeOutput(entity) };
  }
}));
