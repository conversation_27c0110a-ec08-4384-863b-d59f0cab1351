{"version": 3, "sources": ["../../../shallowequal/index.js", "../../../styled-components/node_modules/tslib/tslib.es6.mjs", "../../../@emotion/is-prop-valid/node_modules/@emotion/memoize/dist/emotion-memoize.esm.js", "../../../@emotion/is-prop-valid/dist/emotion-is-prop-valid.esm.js", "../../../styled-components/node_modules/stylis/src/Enum.js", "../../../styled-components/node_modules/stylis/src/Utility.js", "../../../styled-components/node_modules/stylis/src/Tokenizer.js", "../../../styled-components/node_modules/stylis/src/Parser.js", "../../../styled-components/node_modules/stylis/src/Prefixer.js", "../../../styled-components/node_modules/stylis/src/Serializer.js", "../../../styled-components/node_modules/stylis/src/Middleware.js", "../../../@emotion/unitless/dist/emotion-unitless.esm.js", "../../../styled-components/src/constants.ts", "../../../styled-components/src/utils/checkDynamicCreation.ts", "../../../styled-components/src/utils/empties.ts", "../../../styled-components/src/utils/determineTheme.ts", "../../../styled-components/src/utils/domElements.ts", "../../../styled-components/src/utils/escape.ts", "../../../styled-components/src/utils/generateAlphabeticName.ts", "../../../styled-components/src/utils/hash.ts", "../../../styled-components/src/utils/generateComponentId.ts", "../../../styled-components/src/utils/getComponentName.ts", "../../../styled-components/src/utils/isTag.ts", "../../../styled-components/src/utils/hoist.ts", "../../../styled-components/src/utils/isFunction.ts", "../../../styled-components/src/utils/isStyledComponent.ts", "../../../styled-components/src/utils/joinStrings.ts", "../../../styled-components/src/utils/isPlainObject.ts", "../../../styled-components/src/utils/mixinDeep.ts", "../../../styled-components/src/utils/setToString.ts", "../../../styled-components/src/utils/errors.ts", "../../../styled-components/src/utils/error.ts", "../../../styled-components/src/sheet/GroupedTag.ts", "../../../styled-components/src/sheet/GroupIDAllocator.ts", "../../../styled-components/src/sheet/Rehydration.ts", "../../../styled-components/src/utils/nonce.ts", "../../../styled-components/src/sheet/dom.ts", "../../../styled-components/src/sheet/Tag.ts", "../../../styled-components/src/sheet/Sheet.ts", "../../../styled-components/src/utils/stylis.ts", "../../../styled-components/src/models/StyleSheetManager.tsx", "../../../styled-components/src/models/Keyframes.ts", "../../../styled-components/src/utils/hyphenateStyleName.ts", "../../../styled-components/src/utils/flatten.ts", "../../../styled-components/src/utils/addUnitIfNeeded.ts", "../../../styled-components/src/utils/isStatelessFunction.ts", "../../../styled-components/src/utils/isStaticRules.ts", "../../../styled-components/src/models/ComponentStyle.ts", "../../../styled-components/src/models/ThemeProvider.tsx", "../../../styled-components/src/models/StyledComponent.ts", "../../../styled-components/src/utils/generateDisplayName.ts", "../../../styled-components/src/utils/createWarnTooManyClasses.ts", "../../../styled-components/src/utils/interleave.ts", "../../../styled-components/src/constructors/css.ts", "../../../styled-components/src/constructors/constructWithOptions.ts", "../../../styled-components/src/constructors/styled.tsx", "../../../styled-components/src/models/GlobalStyle.ts", "../../../styled-components/src/constructors/createGlobalStyle.ts", "../../../styled-components/src/constructors/keyframes.ts", "../../../styled-components/src/hoc/withTheme.tsx", "../../../styled-components/src/models/ServerStyleSheet.tsx", "../../../styled-components/src/secretInternals.ts", "../../../styled-components/src/base.ts"], "sourcesContent": ["//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n  return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose;\n    if (async) {\n        if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n        dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n        if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n        dispose = value[Symbol.dispose];\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  function next() {\n    while (env.stack.length) {\n      var rec = env.stack.pop();\n      try {\n        var result = rec.dispose && rec.dispose.call(rec.value);\n        if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n      }\n      catch (e) {\n          fail(e);\n      }\n    }\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n};\n", "function memoize(fn) {\n  var cache = Object.create(null);\n  return function (arg) {\n    if (cache[arg] === undefined) cache[arg] = fn(arg);\n    return cache[arg];\n  };\n}\n\nexport { memoize as default };\n", "import memoize from '@emotion/memoize';\n\nvar reactPropsRegex = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/; // https://esbench.com/bench/5bfee68a4cd7e6009ef61d23\n\nvar isPropValid = /* #__PURE__ */memoize(function (prop) {\n  return reactPropsRegex.test(prop) || prop.charCodeAt(0) === 111\n  /* o */\n  && prop.charCodeAt(1) === 110\n  /* n */\n  && prop.charCodeAt(2) < 91;\n}\n/* Z+1 */\n);\n\nexport { isPropValid as default };\n", "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {MS, MO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from './Enum.js'\nimport {hash, charat, strlen, indexof, replace, substr, match} from './Utility.js'\n\n/**\n * @param {string} value\n * @param {number} length\n * @param {object[]} children\n * @return {string}\n */\nexport function prefix (value, length, children) {\n\tswitch (hash(value, length)) {\n\t\t// color-adjust\n\t\tcase 5103:\n\t\t\treturn WEBKIT + 'print-' + value + value\n\t\t// animation, animation-(delay|direction|duration|fill-mode|iteration-count|name|play-state|timing-function)\n\t\tcase 5737: case 4201: case 3177: case 3433: case 1641: case 4457: case 2921:\n\t\t// text-decoration, filter, clip-path, backface-visibility, column, box-decoration-break\n\t\tcase 5572: case 6356: case 5844: case 3191: case 6645: case 3005:\n\t\t// mask, mask-image, mask-(mode|clip|size), mask-(repeat|origin), mask-position, mask-composite,\n\t\tcase 6391: case 5879: case 5623: case 6135: case 4599: case 4855:\n\t\t// background-clip, columns, column-(count|fill|gap|rule|rule-color|rule-style|rule-width|span|width)\n\t\tcase 4215: case 6389: case 5109: case 5365: case 5621: case 3829:\n\t\t\treturn WEBKIT + value + value\n\t\t// tab-size\n\t\tcase 4789:\n\t\t\treturn MOZ + value + value\n\t\t// appearance, user-select, transform, hyphens, text-size-adjust\n\t\tcase 5349: case 4246: case 4810: case 6968: case 2756:\n\t\t\treturn WEBKIT + value + MOZ + value + MS + value + value\n\t\t// writing-mode\n\t\tcase 5936:\n\t\t\tswitch (charat(value, length + 11)) {\n\t\t\t\t// vertical-l(r)\n\t\t\t\tcase 114:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb') + value\n\t\t\t\t// vertical-r(l)\n\t\t\t\tcase 108:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb-rl') + value\n\t\t\t\t// horizontal(-)tb\n\t\t\t\tcase 45:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'lr') + value\n\t\t\t\t// default: fallthrough to below\n\t\t\t}\n\t\t// flex, flex-direction, scroll-snap-type, writing-mode\n\t\tcase 6828: case 4268: case 2903:\n\t\t\treturn WEBKIT + value + MS + value + value\n\t\t// order\n\t\tcase 6165:\n\t\t\treturn WEBKIT + value + MS + 'flex-' + value + value\n\t\t// align-items\n\t\tcase 5187:\n\t\t\treturn WEBKIT + value + replace(value, /(\\w+).+(:[^]+)/, WEBKIT + 'box-$1$2' + MS + 'flex-$1$2') + value\n\t\t// align-self\n\t\tcase 5443:\n\t\t\treturn WEBKIT + value + MS + 'flex-item-' + replace(value, /flex-|-self/g, '') + (!match(value, /flex-|baseline/) ? MS + 'grid-row-' + replace(value, /flex-|-self/g, '') : '') + value\n\t\t// align-content\n\t\tcase 4675:\n\t\t\treturn WEBKIT + value + MS + 'flex-line-pack' + replace(value, /align-content|flex-|-self/g, '') + value\n\t\t// flex-shrink\n\t\tcase 5548:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'shrink', 'negative') + value\n\t\t// flex-basis\n\t\tcase 5292:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'basis', 'preferred-size') + value\n\t\t// flex-grow\n\t\tcase 6060:\n\t\t\treturn WEBKIT + 'box-' + replace(value, '-grow', '') + WEBKIT + value + MS + replace(value, 'grow', 'positive') + value\n\t\t// transition\n\t\tcase 4554:\n\t\t\treturn WEBKIT + replace(value, /([^-])(transform)/g, '$1' + WEBKIT + '$2') + value\n\t\t// cursor\n\t\tcase 6187:\n\t\t\treturn replace(replace(replace(value, /(zoom-|grab)/, WEBKIT + '$1'), /(image-set)/, WEBKIT + '$1'), value, '') + value\n\t\t// background, background-image\n\t\tcase 5495: case 3959:\n\t\t\treturn replace(value, /(image-set\\([^]*)/, WEBKIT + '$1' + '$`$1')\n\t\t// justify-content\n\t\tcase 4968:\n\t\t\treturn replace(replace(value, /(.+:)(flex-)?(.*)/, WEBKIT + 'box-pack:$3' + MS + 'flex-pack:$3'), /s.+-b[^;]+/, 'justify') + WEBKIT + value + value\n\t\t// justify-self\n\t\tcase 4200:\n\t\t\tif (!match(value, /flex-|baseline/)) return MS + 'grid-column-align' + substr(value, length) + value\n\t\t\tbreak\n\t\t// grid-template-(columns|rows)\n\t\tcase 2592: case 3360:\n\t\t\treturn MS + replace(value, 'template-', '') + value\n\t\t// grid-(row|column)-start\n\t\tcase 4384: case 3616:\n\t\t\tif (children && children.some(function (element, index) { return length = index, match(element.props, /grid-\\w+-end/) })) {\n\t\t\t\treturn ~indexof(value + (children = children[length].value), 'span', 0) ? value : (MS + replace(value, '-start', '') + value + MS + 'grid-row-span:' + (~indexof(children, 'span', 0) ? match(children, /\\d+/) : +match(children, /\\d+/) - +match(value, /\\d+/)) + ';')\n\t\t\t}\n\t\t\treturn MS + replace(value, '-start', '') + value\n\t\t// grid-(row|column)-end\n\t\tcase 4896: case 4128:\n\t\t\treturn (children && children.some(function (element) { return match(element.props, /grid-\\w+-start/) })) ? value : MS + replace(replace(value, '-end', '-span'), 'span ', '') + value\n\t\t// (margin|padding)-inline-(start|end)\n\t\tcase 4095: case 3583: case 4068: case 2532:\n\t\t\treturn replace(value, /(.+)-inline(.+)/, WEBKIT + '$1$2') + value\n\t\t// (min|max)?(width|height|inline-size|block-size)\n\t\tcase 8116: case 7059: case 5753: case 5535:\n\t\tcase 5445: case 5701: case 4933: case 4677:\n\t\tcase 5533: case 5789: case 5021: case 4765:\n\t\t\t// stretch, max-content, min-content, fill-available\n\t\t\tif (strlen(value) - 1 - length > 6)\n\t\t\t\tswitch (charat(value, length + 1)) {\n\t\t\t\t\t// (m)ax-content, (m)in-content\n\t\t\t\t\tcase 109:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (charat(value, length + 4) !== 45)\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t// (f)ill-available, (f)it-content\n\t\t\t\t\tcase 102:\n\t\t\t\t\t\treturn replace(value, /(.+:)(.+)-([^]+)/, '$1' + WEBKIT + '$2-$3' + '$1' + MOZ + (charat(value, length + 3) == 108 ? '$3' : '$2-$3')) + value\n\t\t\t\t\t// (s)tretch\n\t\t\t\t\tcase 115:\n\t\t\t\t\t\treturn ~indexof(value, 'stretch', 0) ? prefix(replace(value, 'stretch', 'fill-available'), length, children) + value : value\n\t\t\t\t}\n\t\t\tbreak\n\t\t// grid-(column|row)\n\t\tcase 5152: case 5920:\n\t\t\treturn replace(value, /(.+?):(\\d+)(\\s*\\/\\s*(span)?\\s*(\\d+))?(.*)/, function (_, a, b, c, d, e, f) { return (MS + a + ':' + b + f) + (c ? (MS + a + '-span:' + (d ? e : +e - +b)) + f : '') + value })\n\t\t// position: sticky\n\t\tcase 4949:\n\t\t\t// stick(y)?\n\t\t\tif (charat(value, length + 6) === 121)\n\t\t\t\treturn replace(value, ':', ':' + WEBKIT) + value\n\t\t\tbreak\n\t\t// display: (flex|inline-flex|grid|inline-grid)\n\t\tcase 6444:\n\t\t\tswitch (charat(value, charat(value, 14) === 45 ? 18 : 11)) {\n\t\t\t\t// (inline-)?fle(x)\n\t\t\t\tcase 120:\n\t\t\t\t\treturn replace(value, /(.+:)([^;\\s!]+)(;|(\\s+)?!.+)?/, '$1' + WEBKIT + (charat(value, 14) === 45 ? 'inline-' : '') + 'box$3' + '$1' + WEBKIT + '$2$3' + '$1' + MS + '$2box$3') + value\n\t\t\t\t// (inline-)?gri(d)\n\t\t\t\tcase 100:\n\t\t\t\t\treturn replace(value, ':', ':' + MS) + value\n\t\t\t}\n\t\t\tbreak\n\t\t// scroll-margin, scroll-margin-(top|right|bottom|left)\n\t\tcase 5719: case 2647: case 2135: case 3927: case 2391:\n\t\t\treturn replace(value, 'scroll-', 'scroll-snap-') + value\n\t}\n\n\treturn value\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine, filter, assign} from './Utility.js'\nimport {copy, lift, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(children = element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, callback = /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n", "var unitlessKeys = {\n  animationIterationCount: 1,\n  aspectRatio: 1,\n  borderImageOutset: 1,\n  borderImageSlice: 1,\n  borderImageWidth: 1,\n  boxFlex: 1,\n  boxFlexGroup: 1,\n  boxOrdinalGroup: 1,\n  columnCount: 1,\n  columns: 1,\n  flex: 1,\n  flexGrow: 1,\n  flexPositive: 1,\n  flexShrink: 1,\n  flexNegative: 1,\n  flexOrder: 1,\n  gridRow: 1,\n  gridRowEnd: 1,\n  gridRowSpan: 1,\n  gridRowStart: 1,\n  gridColumn: 1,\n  gridColumnEnd: 1,\n  gridColumnSpan: 1,\n  gridColumnStart: 1,\n  msGridRow: 1,\n  msGridRowSpan: 1,\n  msGridColumn: 1,\n  msGridColumnSpan: 1,\n  fontWeight: 1,\n  lineHeight: 1,\n  opacity: 1,\n  order: 1,\n  orphans: 1,\n  tabSize: 1,\n  widows: 1,\n  zIndex: 1,\n  zoom: 1,\n  WebkitLineClamp: 1,\n  // SVG-related properties\n  fillOpacity: 1,\n  floodOpacity: 1,\n  stopOpacity: 1,\n  strokeDasharray: 1,\n  strokeDashoffset: 1,\n  strokeMiterlimit: 1,\n  strokeOpacity: 1,\n  strokeWidth: 1\n};\n\nexport { unitlessKeys as default };\n", "declare let SC_DISABLE_SPEEDY: boolean | null | undefined;\ndeclare let __VERSION__: string;\n\nexport const SC_ATTR: string =\n  (typeof process !== 'undefined' &&\n    typeof process.env !== 'undefined' &&\n    (process.env.REACT_APP_SC_ATTR || process.env.SC_ATTR)) ||\n  'data-styled';\n\nexport const SC_ATTR_ACTIVE = 'active';\nexport const SC_ATTR_VERSION = 'data-styled-version';\nexport const SC_VERSION = __VERSION__;\nexport const SPLITTER = '/*!sc*/\\n';\n\nexport const IS_BROWSER = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const DISABLE_SPEEDY = Boolean(\n  typeof SC_DISABLE_SPEEDY === 'boolean'\n    ? SC_DISABLE_SPEEDY\n    : typeof process !== 'undefined' &&\n        typeof process.env !== 'undefined' &&\n        typeof process.env.REACT_APP_SC_DISABLE_SPEEDY !== 'undefined' &&\n        process.env.REACT_APP_SC_DISABLE_SPEEDY !== ''\n      ? process.env.REACT_APP_SC_DISABLE_SPEEDY === 'false'\n        ? false\n        : process.env.REACT_APP_SC_DISABLE_SPEEDY\n      : typeof process !== 'undefined' &&\n          typeof process.env !== 'undefined' &&\n          typeof process.env.SC_DISABLE_SPEEDY !== 'undefined' &&\n          process.env.SC_DISABLE_SPEEDY !== ''\n        ? process.env.SC_DISABLE_SPEEDY === 'false'\n          ? false\n          : process.env.SC_DISABLE_SPEEDY\n        : process.env.NODE_ENV !== 'production'\n);\n\n// Shared empty execution context when generating static styles\nexport const STATIC_EXECUTION_CONTEXT = {};\n", "import { useRef } from 'react';\n\nconst invalidHookCallRe = /invalid hook call/i;\nconst seen = new Set();\n\nexport const checkDynamicCreation = (displayName: string, componentId?: string | undefined) => {\n  if (process.env.NODE_ENV !== 'production') {\n    const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n    const message =\n      `The component ${displayName}${parsedIdString} has been created dynamically.\\n` +\n      \"You may see this warning because you've called styled inside another component.\\n\" +\n      'To resolve this only create new StyledComponents outside of any render method and function component.\\n' +\n      'See https://styled-components.com/docs/basics#define-styled-components-outside-of-the-render-method for more info.\\n';\n\n    // If a hook is called outside of a component:\n    // React 17 and earlier throw an error\n    // React 18 and above use console.error\n\n    const originalConsoleError = console.error;\n    try {\n      let didNotCallInvalidHook = true;\n      console.error = (consoleErrorMessage, ...consoleErrorArgs) => {\n        // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n        // be called outside of a React component.\n        if (invalidHookCallRe.test(consoleErrorMessage)) {\n          didNotCallInvalidHook = false;\n          // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n          seen.delete(message);\n        } else {\n          originalConsoleError(consoleErrorMessage, ...consoleErrorArgs);\n        }\n      };\n      // We purposefully call `useRef` outside of a component and expect it to throw\n      // If it doesn't, then we're inside another component.\n      useRef();\n\n      if (didNotCallInvalidHook && !seen.has(message)) {\n        console.warn(message);\n        seen.add(message);\n      }\n    } catch (error) {\n      // The error here is expected, since we're expecting anything that uses `checkDynamicCreation` to\n      // be called outside of a React component.\n      if (invalidHookCallRe.test((error as Error).message)) {\n        // This shouldn't happen, but resets `warningSeen` if we had this error happen intermittently\n        seen.delete(message);\n      }\n    } finally {\n      console.error = originalConsoleError;\n    }\n  }\n};\n", "import { Dict } from '../types';\n\nexport const EMPTY_ARRAY = Object.freeze([]) as Readonly<any[]>;\nexport const EMPTY_OBJECT = Object.freeze({}) as Readonly<Dict<any>>;\n", "import { DefaultTheme, ExecutionProps } from '../types';\nimport { EMPTY_OBJECT } from './empties';\n\nexport default function determineTheme(\n  props: ExecutionProps,\n  providedTheme?: DefaultTheme | undefined,\n  defaultProps: { theme?: DefaultTheme | undefined } = EMPTY_OBJECT\n): DefaultTheme | undefined {\n  return (props.theme !== defaultProps.theme && props.theme) || providedTheme || defaultProps.theme;\n}\n", "// Thanks to ReactDOMFactories for this handy list!\n\nconst elements = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'use',\n  'var',\n  'video',\n  'wbr', // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'marker',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n] as const;\n\nexport default new Set(elements);\nexport type SupportedHTMLElements = (typeof elements)[number];\n", "// Source: https://www.w3.org/TR/cssom-1/#serialize-an-identifier\n// Control characters and non-letter first symbols are not supported\nconst escapeRegex = /[!\"#$%&'()*+,./:;<=>?@[\\\\\\]^`{|}~-]+/g;\n\nconst dashesAtEnds = /(^-|-$)/g;\n\n/**\n * TODO: Explore using CSS.escape when it becomes more available\n * in evergreen browsers.\n */\nexport default function escape(str: string) {\n  return str // Replace all possible CSS selectors\n    .replace(escapeRegex, '-') // Remove extraneous hyphens at the start and end\n    .replace(dashesAtEnds, '');\n}\n", "const AD_REPLACER_R = /(a)(d)/gi;\n\n/* This is the \"capacity\" of our alphabet i.e. 2x26 for all letters plus their capitalised\n * counterparts */\nconst charsLength = 52;\n\n/* start at 75 for 'a' until 'z' (25) and then start at 65 for capitalised letters */\nconst getAlphabeticChar = (code: number) => String.fromCharCode(code + (code > 25 ? 39 : 97));\n\n/* input a number, usually a hash and convert it to base-52 */\nexport default function generateAlphabeticName(code: number) {\n  let name = '';\n  let x;\n\n  /* get a char and divide by alphabet-length */\n  for (x = Math.abs(code); x > charsLength; x = (x / charsLength) | 0) {\n    name = getAlphabeticChar(x % charsLength) + name;\n  }\n\n  return (getAlphabeticChar(x % charsLength) + name).replace(AD_REPLACER_R, '$1-$2');\n}\n", "export const SEED = 5381;\n\n// When we have separate strings it's useful to run a progressive\n// version of djb2 where we pretend that we're still looping over\n// the same string\nexport const phash = (h: number, x: string) => {\n  let i = x.length;\n\n  while (i) {\n    h = (h * 33) ^ x.charCodeAt(--i);\n  }\n\n  return h;\n};\n\n// This is a djb2 hashing function\nexport const hash = (x: string) => {\n  return phash(SEED, x);\n};\n", "import generateAlphabeticName from './generateAlphabeticName';\nimport { hash } from './hash';\n\nexport default function generateComponentId(str: string) {\n  return generateAlphabeticName(hash(str) >>> 0);\n}\n", "import { StyledTarget } from '../types';\n\nexport default function getComponentName(target: StyledTarget<any>) {\n  return (\n    (process.env.NODE_ENV !== 'production' ? typeof target === 'string' && target : false) ||\n    (target as Exclude<StyledTarget<any>, string>).displayName ||\n    (target as Function).name ||\n    'Component'\n  );\n}\n", "import { StyledTarget } from '../types';\n\nexport default function isTag(target: StyledTarget<'web'>): target is string {\n  return (\n    typeof target === 'string' &&\n    (process.env.NODE_ENV !== 'production'\n      ? target.charAt(0) === target.charAt(0).toLowerCase()\n      : true)\n  );\n}\n", "import React from 'react';\nimport { AnyComponent } from '../types';\n\nconst hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\n// copied from react-is\nconst REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nconst REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\n/**\n * Adapted from hoist-non-react-statics to avoid the react-is dependency.\n */\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n};\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n};\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n};\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n};\n\nconst TYPE_STATICS = {\n  [REACT_FORWARD_REF_TYPE]: FORWARD_REF_STATICS,\n  [REACT_MEMO_TYPE]: MEMO_STATICS,\n};\n\ntype OmniComponent = AnyComponent;\n\n// adapted from react-is\nfunction isMemo(\n  object: OmniComponent | React.MemoExoticComponent<any>\n): object is React.MemoExoticComponent<any> {\n  const $$typeofType = 'type' in object && object.type.$$typeof;\n\n  return $$typeofType === REACT_MEMO_TYPE;\n}\n\nfunction getStatics(component: OmniComponent) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS;\n  }\n\n  // React v16.12 and above\n  return '$$typeof' in component\n    ? TYPE_STATICS[component['$$typeof'] as unknown as string]\n    : REACT_STATICS;\n}\n\nconst defineProperty = Object.defineProperty;\nconst getOwnPropertyNames = Object.getOwnPropertyNames;\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols;\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nconst getPrototypeOf = Object.getPrototypeOf;\nconst objectPrototype = Object.prototype;\n\ntype ExcludeList = {\n  [key: string]: true;\n};\n\nexport type NonReactStatics<S extends OmniComponent, C extends ExcludeList = {}> = {\n  [key in Exclude<\n    keyof S,\n    S extends React.MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : S extends React.ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: S[key];\n};\n\nexport default function hoistNonReactStatics<\n  T extends OmniComponent,\n  S extends OmniComponent,\n  C extends ExcludeList = {},\n>(targetComponent: T, sourceComponent: S, excludelist?: C | undefined) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, excludelist);\n      }\n    }\n\n    let keys: (String | Symbol)[] = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    const targetStatics = getStatics(targetComponent);\n    const sourceStatics = getStatics(sourceComponent);\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i] as unknown as string;\n      if (\n        !(key in KNOWN_STATICS) &&\n        !(excludelist && excludelist[key]) &&\n        !(sourceStatics && key in sourceStatics) &&\n        !(targetStatics && key in targetStatics)\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!);\n        } catch (e) {\n          /* ignore */\n        }\n      }\n    }\n  }\n\n  return targetComponent as T & NonReactStatics<S, C>;\n}\n", "export default function isFunction(test: any): test is Function {\n  return typeof test === 'function';\n}\n", "import { StyledComponentBrand } from '../types';\n\nexport default function isStyledComponent(target: any): target is StyledComponentBrand {\n  return typeof target === 'object' && 'styledComponentId' in target;\n}\n", "/**\n * Convenience function for joining strings to form className chains\n */\nexport function joinStrings(a?: string | undefined, b?: string | undefined): string {\n  return a && b ? `${a} ${b}` : a || b || '';\n}\n\nexport function joinStringArray(arr: string[], sep?: string | undefined): string {\n  if (arr.length === 0) {\n    return '';\n  }\n\n  let result = arr[0];\n  for (let i = 1; i < arr.length; i++) {\n    result += sep ? sep + arr[i] : arr[i];\n  }\n  return result;\n}\n", "export default function isPlainObject(x: any): x is Record<any, any> {\n  return (\n    x !== null &&\n    typeof x === 'object' &&\n    x.constructor.name === Object.name &&\n    /* check for reasonable markers that the object isn't an element for react & preact/compat */\n    !('props' in x && x.$$typeof)\n  );\n}\n", "import isPlainObject from './isPlainObject';\n\nfunction mixinRecursively(target: any, source: any, forceMerge = false) {\n  /* only merge into POJOs, Arrays, but for top level objects only\n   * allow to merge into anything by passing forceMerge = true */\n  if (!forceMerge && !isPlainObject(target) && !Array.isArray(target)) {\n    return source;\n  }\n\n  if (Array.isArray(source)) {\n    for (let key = 0; key < source.length; key++) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  } else if (isPlainObject(source)) {\n    for (const key in source) {\n      target[key] = mixinRecursively(target[key], source[key]);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Arrays & POJOs merged recursively, other objects and value types are overridden\n * If target is not a POJO or an Array, it will get source properties injected via shallow merge\n * Source objects applied left to right.  Mutates & returns target.  Similar to lodash merge.\n */\nexport default function mixinDeep(target: any, ...sources: any[]) {\n  for (const source of sources) {\n    mixinRecursively(target, source, true);\n  }\n\n  return target;\n}\n", "/**\n * If the Object prototype is frozen, the \"toString\" property is non-writable. This means that any objects which inherit this property\n * cannot have the property changed using a \"=\" assignment operator. If using strict mode, attempting that will cause an error. If not using\n * strict mode, attempting that will be silently ignored.\n *\n * If the Object prototype is frozen, inherited non-writable properties can still be shadowed using one of two mechanisms:\n *\n *  1. ES6 class methods: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes#methods\n *  2. Using the `Object.defineProperty()` static method:\n *     https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/defineProperty\n *\n * However, this project uses Babel to transpile ES6 classes, and transforms ES6 class methods to use the assignment operator instead:\n * https://babeljs.io/docs/babel-plugin-transform-class-properties#options\n *\n * Therefore, the most compatible way to shadow the prototype's \"toString\" property is to define a new \"toString\" property on this object.\n */\nexport function setToString(object: object, toStringFn: () => string) {\n  Object.defineProperty(object, 'toString', { value: toStringFn });\n}\n", "export default {\n  '1': 'Cannot create styled-component for component: %s.\\n\\n',\n  '2': \"Can't collect styles once you've consumed a `ServerStyleSheet`'s styles! `ServerStyleSheet` is a one off instance for each server-side render cycle.\\n\\n- Are you trying to reuse it across renders?\\n- Are you accidentally calling collectStyles twice?\\n\\n\",\n  '3': 'Streaming SSR is only supported in a Node.js environment; Please do not try to call this method in the browser.\\n\\n',\n  '4': 'The `StyleSheetManager` expects a valid target or sheet prop!\\n\\n- Does this error occur on the client and is your target falsy?\\n- Does this error occur on the server and is the sheet falsy?\\n\\n',\n  '5': 'The clone method cannot be used on the client!\\n\\n- Are you running in a client-like environment on the server?\\n- Are you trying to run SSR on the client?\\n\\n',\n  '6': \"Trying to insert a new style tag, but the given Node is unmounted!\\n\\n- Are you using a custom target that isn't mounted?\\n- Does your document not have a valid head element?\\n- Have you accidentally removed a style tag manually?\\n\\n\",\n  '7': 'ThemeProvider: Please return an object from your \"theme\" prop function, e.g.\\n\\n```js\\ntheme={() => ({})}\\n```\\n\\n',\n  '8': 'ThemeProvider: Please make your \"theme\" prop an object.\\n\\n',\n  '9': 'Missing document `<head>`\\n\\n',\n  '10': 'Cannot find a StyleSheet instance. Usually this happens if there are multiple copies of styled-components loaded at once. Check out this issue for how to troubleshoot and fix the common cases where this situation can happen: https://github.com/styled-components/styled-components/issues/1941#issuecomment-417862021\\n\\n',\n  '11': '_This error was replaced with a dev-time warning, it will be deleted for v4 final._ [createGlobalStyle] received children which will not be rendered. Please use the component without passing children elements.\\n\\n',\n  '12': 'It seems you are interpolating a keyframe declaration (%s) into an untagged string. This was supported in styled-components v3, but is not longer supported in v4 as keyframes are now injected on-demand. Please wrap your string in the css\\\\`\\\\` helper which ensures the styles are injected correctly. See https://www.styled-components.com/docs/api#css\\n\\n',\n  '13': '%s is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.\\n\\n',\n  '14': 'ThemeProvider: \"theme\" prop is required.\\n\\n',\n  '15': \"A stylis plugin has been supplied that is not named. We need a name for each plugin to be able to prevent styling collisions between different stylis configurations within the same app. Before you pass your plugin to `<StyleSheetManager stylisPlugins={[]}>`, please make sure each plugin is uniquely-named, e.g.\\n\\n```js\\nObject.defineProperty(importedPlugin, 'name', { value: 'some-unique-name' });\\n```\\n\\n\",\n  '16': \"Reached the limit of how many styled components may be created at group %s.\\nYou may only create up to 1,073,741,824 components. If you're creating components dynamically,\\nas for instance in your render method then you may be running into this limitation.\\n\\n\",\n  '17': \"CSSStyleSheet could not be found on HTMLStyleElement.\\nHas styled-components' style tag been unmounted or altered by another script?\\n\",\n  '18': 'ThemeProvider: Please make sure your useTheme hook is within a `<ThemeProvider>`',\n};\n", "import { Dict } from '../types';\nimport errorMap from './errors';\n\nconst ERRORS: Dict<any> = process.env.NODE_ENV !== 'production' ? errorMap : {};\n\n/**\n * super basic version of sprintf\n */\nfunction format(...args: [string, ...any]) {\n  let a = args[0];\n  const b = [];\n\n  for (let c = 1, len = args.length; c < len; c += 1) {\n    b.push(args[c]);\n  }\n\n  b.forEach(d => {\n    a = a.replace(/%[a-z]/, d);\n  });\n\n  return a;\n}\n\n/**\n * Create an error file out of errors.md for development and a simple web link to the full errors\n * in production mode.\n */\nexport default function throwStyledComponentsError(\n  code: string | number,\n  ...interpolations: any[]\n) {\n  if (process.env.NODE_ENV === 'production') {\n    return new Error(\n      `An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#${code} for more information.${\n        interpolations.length > 0 ? ` Args: ${interpolations.join(', ')}` : ''\n      }`\n    );\n  } else {\n    return new Error(format(ERRORS[code], ...interpolations).trim());\n  }\n}\n", "import { SPLITTER } from '../constants';\nimport styledError from '../utils/error';\nimport { GroupedTag, Tag } from './types';\n\n/** Create a GroupedTag with an underlying Tag implementation */\nexport const makeGroupedTag = (tag: Tag) => {\n  return new DefaultGroupedTag(tag);\n};\n\nconst BASE_SIZE = 1 << 9;\n\nconst DefaultGroupedTag = class DefaultGroupedTag implements GroupedTag {\n  groupSizes: Uint32Array;\n  length: number;\n  tag: Tag;\n\n  constructor(tag: Tag) {\n    this.groupSizes = new Uint32Array(BASE_SIZE);\n    this.length = BASE_SIZE;\n    this.tag = tag;\n  }\n\n  indexOfGroup(group: number) {\n    let index = 0;\n    for (let i = 0; i < group; i++) {\n      index += this.groupSizes[i];\n    }\n\n    return index;\n  }\n\n  insertRules(group: number, rules: string[]) {\n    if (group >= this.groupSizes.length) {\n      const oldBuffer = this.groupSizes;\n      const oldSize = oldBuffer.length;\n\n      let newSize = oldSize;\n      while (group >= newSize) {\n        newSize <<= 1;\n        if (newSize < 0) {\n          throw styledError(16, `${group}`);\n        }\n      }\n\n      this.groupSizes = new Uint32Array(newSize);\n      this.groupSizes.set(oldBuffer);\n      this.length = newSize;\n\n      for (let i = oldSize; i < newSize; i++) {\n        this.groupSizes[i] = 0;\n      }\n    }\n\n    let ruleIndex = this.indexOfGroup(group + 1);\n\n    for (let i = 0, l = rules.length; i < l; i++) {\n      if (this.tag.insertRule(ruleIndex, rules[i])) {\n        this.groupSizes[group]++;\n        ruleIndex++;\n      }\n    }\n  }\n\n  clearGroup(group: number) {\n    if (group < this.length) {\n      const length = this.groupSizes[group];\n      const startIndex = this.indexOfGroup(group);\n      const endIndex = startIndex + length;\n\n      this.groupSizes[group] = 0;\n\n      for (let i = startIndex; i < endIndex; i++) {\n        this.tag.deleteRule(startIndex);\n      }\n    }\n  }\n\n  getGroup(group: number) {\n    let css = '';\n    if (group >= this.length || this.groupSizes[group] === 0) {\n      return css;\n    }\n\n    const length = this.groupSizes[group];\n    const startIndex = this.indexOfGroup(group);\n    const endIndex = startIndex + length;\n\n    for (let i = startIndex; i < endIndex; i++) {\n      css += `${this.tag.getRule(i)}${SPLITTER}`;\n    }\n\n    return css;\n  }\n};\n", "import styledError from '../utils/error';\n\nconst MAX_SMI = 1 << (31 - 1);\n\nlet groupIDRegister: Map<string, number> = new Map();\nlet reverseRegister: Map<number, string> = new Map();\nlet nextFreeGroup = 1;\n\nexport const resetGroupIds = () => {\n  groupIDRegister = new Map();\n  reverseRegister = new Map();\n  nextFreeGroup = 1;\n};\n\nexport const getGroupForId = (id: string): number => {\n  if (groupIDRegister.has(id)) {\n    return groupIDRegister.get(id) as any;\n  }\n\n  while (reverseRegister.has(nextFreeGroup)) {\n    nextFreeGroup++;\n  }\n\n  const group = nextFreeGroup++;\n\n  if (process.env.NODE_ENV !== 'production' && ((group | 0) < 0 || group > MAX_SMI)) {\n    throw styledError(16, `${group}`);\n  }\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n  return group;\n};\n\nexport const getIdForGroup = (group: number): void | string => {\n  return reverseRegister.get(group);\n};\n\nexport const setGroupForId = (id: string, group: number) => {\n  // move pointer\n  nextFreeGroup = group + 1;\n\n  groupIDRegister.set(id, group);\n  reverseRegister.set(group, id);\n};\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION, SPLITTER } from '../constants';\nimport { getIdForGroup, setGroupForId } from './GroupIDAllocator';\nimport { Sheet } from './types';\n\nconst SELECTOR = `style[${SC_ATTR}][${SC_ATTR_VERSION}=\"${SC_VERSION}\"]`;\nconst MARKER_RE = new RegExp(`^${SC_ATTR}\\\\.g(\\\\d+)\\\\[id=\"([\\\\w\\\\d-]+)\"\\\\].*?\"([^\"]*)`);\n\nexport const outputSheet = (sheet: Sheet) => {\n  const tag = sheet.getTag();\n  const { length } = tag;\n\n  let css = '';\n  for (let group = 0; group < length; group++) {\n    const id = getIdForGroup(group);\n    if (id === undefined) continue;\n\n    const names = sheet.names.get(id);\n    const rules = tag.getGroup(group);\n    if (names === undefined || !names.size || rules.length === 0) continue;\n\n    const selector = `${SC_ATTR}.g${group}[id=\"${id}\"]`;\n\n    let content = '';\n    if (names !== undefined) {\n      names.forEach(name => {\n        if (name.length > 0) {\n          content += `${name},`;\n        }\n      });\n    }\n\n    // NOTE: It's easier to collect rules and have the marker\n    // after the actual rules to simplify the rehydration\n    css += `${rules}${selector}{content:\"${content}\"}${SPLITTER}`;\n  }\n\n  return css;\n};\n\nconst rehydrateNamesFromContent = (sheet: Sheet, id: string, content: string) => {\n  const names = content.split(',');\n  let name;\n\n  for (let i = 0, l = names.length; i < l; i++) {\n    if ((name = names[i])) {\n      sheet.registerName(id, name);\n    }\n  }\n};\n\nconst rehydrateSheetFromTag = (sheet: Sheet, style: HTMLStyleElement) => {\n  const parts = (style.textContent ?? '').split(SPLITTER);\n  const rules: string[] = [];\n\n  for (let i = 0, l = parts.length; i < l; i++) {\n    const part = parts[i].trim();\n    if (!part) continue;\n\n    const marker = part.match(MARKER_RE);\n\n    if (marker) {\n      const group = parseInt(marker[1], 10) | 0;\n      const id = marker[2];\n\n      if (group !== 0) {\n        // Rehydrate componentId to group index mapping\n        setGroupForId(id, group);\n        // Rehydrate names and rules\n        // looks like: data-styled.g11[id=\"idA\"]{content:\"nameA,\"}\n        rehydrateNamesFromContent(sheet, id, marker[3]);\n        sheet.getTag().insertRules(group, rules);\n      }\n\n      rules.length = 0;\n    } else {\n      rules.push(part);\n    }\n  }\n};\n\nexport const rehydrateSheet = (sheet: Sheet) => {\n  const nodes = document.querySelectorAll(SELECTOR);\n\n  for (let i = 0, l = nodes.length; i < l; i++) {\n    const node = nodes[i] as any as HTMLStyleElement;\n    if (node && node.getAttribute(SC_ATTR) !== SC_ATTR_ACTIVE) {\n      rehydrateSheetFromTag(sheet, node);\n\n      if (node.parentNode) {\n        node.parentNode.removeChild(node);\n      }\n    }\n  }\n};\n", "declare let __webpack_nonce__: string;\n\nexport default function getNonce() {\n  return typeof __webpack_nonce__ !== 'undefined' ? __webpack_nonce__ : null;\n}\n", "import { SC_ATTR, SC_ATTR_ACTIVE, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport { InsertionTarget } from '../types';\nimport styledError from '../utils/error';\nimport getNonce from '../utils/nonce';\n\n/** Find last style element if any inside target */\nconst findLastStyleTag = (target: InsertionTarget): void | HTMLStyleElement => {\n  const arr = Array.from(target.querySelectorAll<HTMLStyleElement>(`style[${SC_ATTR}]`));\n\n  return arr[arr.length - 1];\n};\n\n/** Create a style element inside `target` or <head> after the last */\nexport const makeStyleTag = (target?: InsertionTarget | undefined): HTMLStyleElement => {\n  const head = document.head;\n  const parent = target || head;\n  const style = document.createElement('style');\n  const prevStyle = findLastStyleTag(parent);\n  const nextSibling = prevStyle !== undefined ? prevStyle.nextSibling : null;\n\n  style.setAttribute(SC_ATTR, SC_ATTR_ACTIVE);\n  style.setAttribute(SC_ATTR_VERSION, SC_VERSION);\n\n  const nonce = getNonce();\n\n  if (nonce) style.setAttribute('nonce', nonce);\n\n  parent.insertBefore(style, nextSibling);\n\n  return style;\n};\n\n/** Get the CSSStyleSheet instance for a given style element */\nexport const getSheet = (tag: HTMLStyleElement): CSSStyleSheet => {\n  if (tag.sheet) {\n    return tag.sheet as any as CSSStyleSheet;\n  }\n\n  // Avoid Firefox quirk where the style element might not have a sheet property\n  const { styleSheets } = document;\n  for (let i = 0, l = styleSheets.length; i < l; i++) {\n    const sheet = styleSheets[i];\n    if (sheet.ownerNode === tag) {\n      return sheet as any as CSSStyleSheet;\n    }\n  }\n\n  throw styledError(17);\n};\n", "import { InsertionTarget } from '../types';\nimport { getSheet, makeStyleTag } from './dom';\nimport { SheetOptions, Tag } from './types';\n\n/** Create a CSSStyleSheet-like tag depending on the environment */\nexport const makeTag = ({ isServer, useCSSOMInjection, target }: SheetOptions) => {\n  if (isServer) {\n    return new VirtualTag(target);\n  } else if (useCSSOMInjection) {\n    return new CSSOMTag(target);\n  } else {\n    return new TextTag(target);\n  }\n};\n\nexport const CSSOMTag = class CSSOMTag implements Tag {\n  element: HTMLStyleElement;\n\n  sheet: CSSStyleSheet;\n\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n\n    // Avoid Edge bug where empty style elements don't create sheets\n    this.element.appendChild(document.createTextNode(''));\n\n    this.sheet = getSheet(this.element);\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string): boolean {\n    try {\n      this.sheet.insertRule(rule, index);\n      this.length++;\n      return true;\n    } catch (_error) {\n      return false;\n    }\n  }\n\n  deleteRule(index: number): void {\n    this.sheet.deleteRule(index);\n    this.length--;\n  }\n\n  getRule(index: number): string {\n    const rule = this.sheet.cssRules[index];\n\n    // Avoid IE11 quirk where cssText is inaccessible on some invalid rules\n    if (rule && rule.cssText) {\n      return rule.cssText;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A Tag that emulates the CSSStyleSheet API but uses text nodes */\nexport const TextTag = class TextTag implements Tag {\n  element: HTMLStyleElement;\n  nodes: NodeListOf<Node>;\n  length: number;\n\n  constructor(target?: InsertionTarget | undefined) {\n    this.element = makeStyleTag(target);\n    this.nodes = this.element.childNodes;\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length && index >= 0) {\n      const node = document.createTextNode(rule);\n      const refNode = this.nodes[index];\n      this.element.insertBefore(node, refNode || null);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.element.removeChild(this.nodes[index]);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.nodes[index].textContent as string;\n    } else {\n      return '';\n    }\n  }\n};\n\n/** A completely virtual (server-side) Tag that doesn't manipulate the DOM */\nexport const VirtualTag = class VirtualTag implements Tag {\n  rules: string[];\n\n  length: number;\n\n  constructor(_target?: InsertionTarget | undefined) {\n    this.rules = [];\n    this.length = 0;\n  }\n\n  insertRule(index: number, rule: string) {\n    if (index <= this.length) {\n      this.rules.splice(index, 0, rule);\n      this.length++;\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  deleteRule(index: number) {\n    this.rules.splice(index, 1);\n    this.length--;\n  }\n\n  getRule(index: number) {\n    if (index < this.length) {\n      return this.rules[index];\n    } else {\n      return '';\n    }\n  }\n};\n", "import { DISABLE_SPEEDY, IS_BROWSER } from '../constants';\nimport { InsertionTarget } from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport { setToString } from '../utils/setToString';\nimport { makeGroupedTag } from './GroupedTag';\nimport { getGroupForId } from './GroupIDAllocator';\nimport { outputSheet, rehydrateSheet } from './Rehydration';\nimport { makeTag } from './Tag';\nimport { GroupedTag, Sheet, SheetOptions } from './types';\n\nlet SHOULD_REHYDRATE = IS_BROWSER;\n\ntype SheetConstructorArgs = {\n  isServer?: boolean;\n  useCSSOMInjection?: boolean;\n  target?: InsertionTarget | undefined;\n};\n\ntype GlobalStylesAllocationMap = {\n  [key: string]: number;\n};\ntype NamesAllocationMap = Map<string, Set<string>>;\n\nconst defaultOptions: SheetOptions = {\n  isServer: !IS_BROWSER,\n  useCSSOMInjection: !DISABLE_SPEEDY,\n};\n\n/** Contains the main stylesheet logic for stringification and caching */\nexport default class StyleSheet implements Sheet {\n  gs: GlobalStylesAllocationMap;\n  names: NamesAllocationMap;\n  options: SheetOptions;\n  server: boolean;\n  tag?: GroupedTag | undefined;\n\n  /** Register a group ID to give it an index */\n  static registerId(id: string): number {\n    return getGroupForId(id);\n  }\n\n  constructor(\n    options: SheetConstructorArgs = EMPTY_OBJECT as Object,\n    globalStyles: GlobalStylesAllocationMap = {},\n    names?: NamesAllocationMap | undefined\n  ) {\n    this.options = {\n      ...defaultOptions,\n      ...options,\n    };\n\n    this.gs = globalStyles;\n    this.names = new Map(names as NamesAllocationMap);\n    this.server = !!options.isServer;\n\n    // We rehydrate only once and use the sheet that is created first\n    if (!this.server && IS_BROWSER && SHOULD_REHYDRATE) {\n      SHOULD_REHYDRATE = false;\n      rehydrateSheet(this);\n    }\n\n    setToString(this, () => outputSheet(this));\n  }\n\n  rehydrate(): void {\n    if (!this.server && IS_BROWSER) {\n      rehydrateSheet(this);\n    }\n  }\n\n  reconstructWithOptions(options: SheetConstructorArgs, withNames = true) {\n    return new StyleSheet(\n      { ...this.options, ...options },\n      this.gs,\n      (withNames && this.names) || undefined\n    );\n  }\n\n  allocateGSInstance(id: string) {\n    return (this.gs[id] = (this.gs[id] || 0) + 1);\n  }\n\n  /** Lazily initialises a GroupedTag for when it's actually needed */\n  getTag() {\n    return this.tag || (this.tag = makeGroupedTag(makeTag(this.options)));\n  }\n\n  /** Check whether a name is known for caching */\n  hasNameForId(id: string, name: string): boolean {\n    return this.names.has(id) && (this.names.get(id) as any).has(name);\n  }\n\n  /** Mark a group's name as known for caching */\n  registerName(id: string, name: string) {\n    getGroupForId(id);\n\n    if (!this.names.has(id)) {\n      const groupNames = new Set<string>();\n      groupNames.add(name);\n      this.names.set(id, groupNames);\n    } else {\n      (this.names.get(id) as any).add(name);\n    }\n  }\n\n  /** Insert new rules which also marks the name as known */\n  insertRules(id: string, name: string, rules: string | string[]) {\n    this.registerName(id, name);\n    this.getTag().insertRules(getGroupForId(id), rules);\n  }\n\n  /** Clears all cached names for a given group ID */\n  clearNames(id: string) {\n    if (this.names.has(id)) {\n      (this.names.get(id) as any).clear();\n    }\n  }\n\n  /** Clears all rules for a given group ID */\n  clearRules(id: string) {\n    this.getTag().clearGroup(getGroupForId(id));\n    this.clearNames(id);\n  }\n\n  /** Clears the entire tag which deletes all rules but not its names */\n  clearTag() {\n    // NOTE: This does not clear the names, since it's only used during SSR\n    // so that we can continuously output only new rules\n    this.tag = undefined;\n  }\n}\n", "import * as stylis from 'stylis';\nimport { Stringifier } from '../types';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from './empties';\nimport throwStyledError from './error';\nimport { SEED, phash } from './hash';\n\nconst AMP_REGEX = /&/g;\nconst COMMENT_REGEX = /^\\s*\\/\\/.*$/gm;\n\nexport type ICreateStylisInstance = {\n  options?: { namespace?: string | undefined; prefix?: boolean | undefined } | undefined;\n  plugins?: stylis.Middleware[] | undefined;\n};\n\n/**\n * Takes an element and recurses through it's rules added the namespace to the start of each selector.\n * Takes into account media queries by recursing through child rules if they are present.\n */\nfunction recursivelySetNamepace(compiled: stylis.Element[], namespace: String): stylis.Element[] {\n  return compiled.map(rule => {\n    if (rule.type === 'rule') {\n      // add the namespace to the start\n      rule.value = `${namespace} ${rule.value}`;\n      // add the namespace after each comma for subsequent selectors.\n      rule.value = rule.value.replaceAll(',', `,${namespace} `);\n      rule.props = (rule.props as string[]).map(prop => {\n        return `${namespace} ${prop}`;\n      });\n    }\n\n    if (Array.isArray(rule.children) && rule.type !== '@keyframes') {\n      rule.children = recursivelySetNamepace(rule.children, namespace);\n    }\n    return rule;\n  });\n}\n\nexport default function createStylisInstance(\n  {\n    options = EMPTY_OBJECT as object,\n    plugins = EMPTY_ARRAY as unknown as stylis.Middleware[],\n  }: ICreateStylisInstance = EMPTY_OBJECT as object\n) {\n  let _componentId: string;\n  let _selector: string;\n  let _selectorRegexp: RegExp;\n\n  const selfReferenceReplacer = (match: string, offset: number, string: string) => {\n    if (\n      /**\n       * We only want to refer to the static class directly if the selector is part of a\n       * self-reference selector `& + & { color: red; }`\n       */\n      string.startsWith(_selector) &&\n      string.endsWith(_selector) &&\n      string.replaceAll(_selector, '').length > 0\n    ) {\n      return `.${_componentId}`;\n    }\n\n    return match;\n  };\n\n  /**\n   * When writing a style like\n   *\n   * & + & {\n   *   color: red;\n   * }\n   *\n   * The second ampersand should be a reference to the static component class. stylis\n   * has no knowledge of static class so we have to intelligently replace the base selector.\n   *\n   * https://github.com/thysultan/stylis.js/tree/v4.0.2#abstract-syntax-structure\n   */\n  const selfReferenceReplacementPlugin: stylis.Middleware = element => {\n    if (element.type === stylis.RULESET && element.value.includes('&')) {\n      (element.props as string[])[0] = element.props[0]\n        // catch any hanging references that stylis missed\n        .replace(AMP_REGEX, _selector)\n        .replace(_selectorRegexp, selfReferenceReplacer);\n    }\n  };\n\n  const middlewares = plugins.slice();\n\n  middlewares.push(selfReferenceReplacementPlugin);\n\n  /**\n   * Enables automatic vendor-prefixing for styles.\n   */\n  if (options.prefix) {\n    middlewares.push(stylis.prefixer);\n  }\n\n  middlewares.push(stylis.stringify);\n\n  const stringifyRules: Stringifier = (\n    css: string,\n    selector = '',\n    /**\n     * This \"prefix\" referes to a _selector_ prefix.\n     */\n    prefix = '',\n    componentId = '&'\n  ) => {\n    // stylis has no concept of state to be passed to plugins\n    // but since JS is single-threaded, we can rely on that to ensure\n    // these properties stay in sync with the current stylis run\n    _componentId = componentId;\n    _selector = selector;\n    _selectorRegexp = new RegExp(`\\\\${_selector}\\\\b`, 'g');\n\n    const flatCSS = css.replace(COMMENT_REGEX, '');\n    let compiled = stylis.compile(\n      prefix || selector ? `${prefix} ${selector} { ${flatCSS} }` : flatCSS\n    );\n\n    if (options.namespace) {\n      compiled = recursivelySetNamepace(compiled, options.namespace);\n    }\n\n    const stack: string[] = [];\n\n    stylis.serialize(\n      compiled,\n      stylis.middleware(middlewares.concat(stylis.rulesheet(value => stack.push(value))))\n    );\n\n    return stack;\n  };\n\n  stringifyRules.hash = plugins.length\n    ? plugins\n        .reduce((acc, plugin) => {\n          if (!plugin.name) {\n            throwStyledError(15);\n          }\n\n          return phash(acc, plugin.name);\n        }, SEED)\n        .toString()\n    : '';\n\n  return stringifyRules;\n}\n", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport shallowequal from 'shallowequal';\nimport type stylis from 'stylis';\nimport StyleSheet from '../sheet';\nimport { InsertionTarget, ShouldForwardProp, Stringifier } from '../types';\nimport createStylisInstance from '../utils/stylis';\n\nexport const mainSheet: StyleSheet = new StyleSheet();\nexport const mainStylis: Stringifier = createStylisInstance();\n\nexport type IStyleSheetContext = {\n  shouldForwardProp?: ShouldForwardProp<'web'> | undefined;\n  styleSheet: StyleSheet;\n  stylis: Stringifier;\n};\n\nexport const StyleSheetContext = React.createContext<IStyleSheetContext>({\n  shouldForwardProp: undefined,\n  styleSheet: mainSheet,\n  stylis: mainStylis,\n});\n\nexport const StyleSheetConsumer = StyleSheetContext.Consumer;\n\nexport type IStylisContext = Stringifier | void;\nexport const StylisContext = React.createContext<IStylisContext>(undefined);\nexport const StylisConsumer = StylisContext.Consumer;\n\nexport function useStyleSheetContext() {\n  return useContext(StyleSheetContext);\n}\n\nexport type IStyleSheetManager = React.PropsWithChildren<{\n  /**\n   * If desired, you can pass this prop to disable \"speedy\" insertion mode, which\n   * uses the browser [CSSOM APIs](https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet).\n   * When disabled, rules are inserted as simple text into style blocks.\n   */\n  disableCSSOMInjection?: undefined | boolean;\n  /**\n   * If you are working exclusively with modern browsers, vendor prefixes can often be omitted\n   * to reduce the weight of CSS on the page.\n   */\n  enableVendorPrefixes?: undefined | boolean;\n  /**\n   * Provide an optional selector to be prepended to all generated style rules.\n   */\n  namespace?: undefined | string;\n  /**\n   * Create and provide your own `StyleSheet` if necessary for advanced SSR scenarios.\n   */\n  sheet?: undefined | StyleSheet;\n  /**\n   * Starting in v6, styled-components no longer does its own prop validation\n   * and recommends use of transient props \"$prop\" to pass style-only props to\n   * components. If for some reason you are not able to use transient props, a\n   * prop validation function can be provided via `StyleSheetManager`, such as\n   * `@emotion/is-prop-valid`.\n   *\n   * When the return value is `true`, props will be forwarded to the DOM/underlying\n   * component. If return value is `false`, the prop will be discarded after styles\n   * are calculated.\n   *\n   * Manually composing `styled.{element}.withConfig({shouldForwardProp})` will\n   * override this default.\n   */\n  shouldForwardProp?: undefined | IStyleSheetContext['shouldForwardProp'];\n  /**\n   * An array of plugins to be run by stylis (style processor) during compilation.\n   * Check out [what's available on npm*](https://www.npmjs.com/search?q=keywords%3Astylis).\n   *\n   * \\* The plugin(s) must be compatible with stylis v4 or above.\n   */\n  stylisPlugins?: undefined | stylis.Middleware[];\n  /**\n   * Provide an alternate DOM node to host generated styles; useful for iframes.\n   */\n  target?: undefined | InsertionTarget;\n}>;\n\nexport function StyleSheetManager(props: IStyleSheetManager): React.JSX.Element {\n  const [plugins, setPlugins] = useState(props.stylisPlugins);\n  const { styleSheet } = useStyleSheetContext();\n\n  const resolvedStyleSheet = useMemo(() => {\n    let sheet = styleSheet;\n\n    if (props.sheet) {\n      sheet = props.sheet;\n    } else if (props.target) {\n      sheet = sheet.reconstructWithOptions({ target: props.target }, false);\n    }\n\n    if (props.disableCSSOMInjection) {\n      sheet = sheet.reconstructWithOptions({ useCSSOMInjection: false });\n    }\n\n    return sheet;\n  }, [props.disableCSSOMInjection, props.sheet, props.target, styleSheet]);\n\n  const stylis = useMemo(\n    () =>\n      createStylisInstance({\n        options: { namespace: props.namespace, prefix: props.enableVendorPrefixes },\n        plugins,\n      }),\n    [props.enableVendorPrefixes, props.namespace, plugins]\n  );\n\n  useEffect(() => {\n    if (!shallowequal(plugins, props.stylisPlugins)) setPlugins(props.stylisPlugins);\n  }, [props.stylisPlugins]);\n\n  const styleSheetContextValue = useMemo(\n    () => ({\n      shouldForwardProp: props.shouldForwardProp,\n      styleSheet: resolvedStyleSheet,\n      stylis,\n    }),\n    [props.shouldForwardProp, resolvedStyleSheet, stylis]\n  );\n\n  return (\n    <StyleSheetContext.Provider value={styleSheetContextValue}>\n      <StylisContext.Provider value={stylis}>{props.children}</StylisContext.Provider>\n    </StyleSheetContext.Provider>\n  );\n}\n", "import StyleSheet from '../sheet';\nimport { Keyframes as KeyframesType, Stringifier } from '../types';\nimport styledError from '../utils/error';\nimport { setToString } from '../utils/setToString';\nimport { mainStylis } from './StyleSheetManager';\n\nexport default class Keyframes implements KeyframesType {\n  id: string;\n  name: string;\n  rules: string;\n\n  constructor(name: string, rules: string) {\n    this.name = name;\n    this.id = `sc-keyframes-${name}`;\n    this.rules = rules;\n\n    setToString(this, () => {\n      throw styledError(12, String(this.name));\n    });\n  }\n\n  inject = (styleSheet: StyleSheet, stylisInstance: Stringifier = mainStylis): void => {\n    const resolvedName = this.name + stylisInstance.hash;\n\n    if (!styleSheet.hasNameForId(this.id, resolvedName)) {\n      styleSheet.insertRules(\n        this.id,\n        resolvedName,\n        stylisInstance(this.rules, resolvedName, '@keyframes')\n      );\n    }\n  };\n\n  getName(stylisInstance: Stringifier = mainStylis): string {\n    return this.name + stylisInstance.hash;\n  }\n}\n", "const isUpper = (c: string) => c >= 'A' && c <= 'Z';\n\n/**\n * Hyphenates a camelcased CSS property name, for example:\n *\n *   > hyphenateStyleName('backgroundColor')\n *   < \"background-color\"\n *   > hyphenateStyleName('MozTransition')\n *   < \"-moz-transition\"\n *   > hyphenateStyleName('msTransition')\n *   < \"-ms-transition\"\n *\n * As Modernizr suggests (http://modernizr.com/docs/#prefixed), an `ms` prefix\n * is converted to `-ms-`.\n */\nexport default function hyphenateStyleName(string: string): string {\n  let output = '';\n\n  for (let i = 0; i < string.length; i++) {\n    const c = string[i];\n    // Check for CSS variable prefix\n    if (i === 1 && c === '-' && string[0] === '-') {\n      return string;\n    }\n\n    if (isUpper(c)) {\n      output += '-' + c.toLowerCase();\n    } else {\n      output += c;\n    }\n  }\n\n  return output.startsWith('ms-') ? '-' + output : output;\n}\n", "import Keyframes from '../models/Keyframes';\nimport StyleSheet from '../sheet';\nimport {\n  AnyComponent,\n  Dict,\n  ExecutionContext,\n  Interpolation,\n  IStyledComponent,\n  RuleSet,\n  Stringifier,\n  StyledObject,\n} from '../types';\nimport addUnitIfNeeded from './addUnitIfNeeded';\nimport { EMPTY_ARRAY } from './empties';\nimport getComponentName from './getComponentName';\nimport hyphenate from './hyphenateStyleName';\nimport isFunction from './isFunction';\nimport isPlainObject from './isPlainObject';\nimport isStatelessFunction from './isStatelessFunction';\nimport isStyledComponent from './isStyledComponent';\n\n/**\n * It's falsish not falsy because 0 is allowed.\n */\nconst isFalsish = (chunk: any): chunk is undefined | null | false | '' =>\n  chunk === undefined || chunk === null || chunk === false || chunk === '';\n\nexport const objToCssArray = (obj: Dict<any>): string[] => {\n  const rules = [];\n\n  for (const key in obj) {\n    const val = obj[key];\n    if (!obj.hasOwnProperty(key) || isFalsish(val)) continue;\n\n    // @ts-expect-error Property 'isCss' does not exist on type 'any[]'\n    if ((Array.isArray(val) && val.isCss) || isFunction(val)) {\n      rules.push(`${hyphenate(key)}:`, val, ';');\n    } else if (isPlainObject(val)) {\n      rules.push(`${key} {`, ...objToCssArray(val), '}');\n    } else {\n      rules.push(`${hyphenate(key)}: ${addUnitIfNeeded(key, val)};`);\n    }\n  }\n\n  return rules;\n};\n\nexport default function flatten<Props extends object>(\n  chunk: Interpolation<object>,\n  executionContext?: (ExecutionContext & Props) | undefined,\n  styleSheet?: StyleSheet | undefined,\n  stylisInstance?: Stringifier | undefined\n): RuleSet<Props> {\n  if (isFalsish(chunk)) {\n    return [];\n  }\n\n  /* Handle other components */\n  if (isStyledComponent(chunk)) {\n    return [`.${(chunk as unknown as IStyledComponent<'web', any>).styledComponentId}`];\n  }\n\n  /* Either execute or defer the function */\n  if (isFunction(chunk)) {\n    if (isStatelessFunction(chunk) && executionContext) {\n      const result = chunk(executionContext);\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        typeof result === 'object' &&\n        !Array.isArray(result) &&\n        !(result instanceof Keyframes) &&\n        !isPlainObject(result) &&\n        result !== null\n      ) {\n        console.error(\n          `${getComponentName(\n            chunk as AnyComponent\n          )} is not a styled component and cannot be referred to via component selector. See https://www.styled-components.com/docs/advanced#referring-to-other-components for more details.`\n        );\n      }\n\n      return flatten<Props>(result, executionContext, styleSheet, stylisInstance);\n    } else {\n      return [chunk as unknown as IStyledComponent<'web'>];\n    }\n  }\n\n  if (chunk instanceof Keyframes) {\n    if (styleSheet) {\n      chunk.inject(styleSheet, stylisInstance);\n      return [chunk.getName(stylisInstance)];\n    } else {\n      return [chunk];\n    }\n  }\n\n  /* Handle objects */\n  if (isPlainObject(chunk)) {\n    return objToCssArray(chunk as StyledObject<Props>);\n  }\n\n  if (!Array.isArray(chunk)) {\n    return [chunk.toString()];\n  }\n\n  return flatMap(chunk, chunklet =>\n    flatten<Props>(chunklet, executionContext, styleSheet, stylisInstance)\n  );\n}\n\nfunction flatMap<T, U>(array: T[], transform: (value: T, index: number, array: T[]) => U[]): U[] {\n  return Array.prototype.concat.apply(EMPTY_ARRAY, array.map(transform));\n}\n", "import unitless from '@emotion/unitless';\n\n// Taken from https://github.com/facebook/react/blob/b87aabdfe1b7461e7331abb3601d9e6bb27544bc/packages/react-dom/src/shared/dangerousStyleValue.js\nexport default function addUnitIfNeeded(name: string, value: any) {\n  // https://github.com/amilajack/eslint-plugin-flowtype-errors/issues/133\n  if (value == null || typeof value === 'boolean' || value === '') {\n    return '';\n  }\n\n  if (typeof value === 'number' && value !== 0 && !(name in unitless) && !name.startsWith('--')) {\n    return `${value}px`; // Presumes implicit 'px' suffix for unitless numbers except for CSS variables\n  }\n\n  return String(value).trim();\n}\n", "import isFunction from './isFunction';\n\nexport default function isStatelessFunction(test: any): test is Function {\n  return isFunction(test) && !(test.prototype && test.prototype.isReactComponent);\n}\n", "import { RuleSet } from '../types';\nimport isFunction from './isFunction';\nimport isStyledComponent from './isStyledComponent';\n\nexport default function isStaticRules<Props extends object>(rules: RuleSet<Props>) {\n  for (let i = 0; i < rules.length; i += 1) {\n    const rule = rules[i];\n\n    if (isFunction(rule) && !isStyledComponent(rule)) {\n      // functions are allowed to be static if they're just being\n      // used to get the classname of a nested styled component\n      return false;\n    }\n  }\n\n  return true;\n}\n", "import { SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport generateName from '../utils/generateAlphabeticName';\nimport { hash, phash } from '../utils/hash';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray, joinStrings } from '../utils/joinStrings';\n\nconst SEED = hash(SC_VERSION);\n\n/**\n * ComponentStyle is all the CSS-specific stuff, not the React-specific stuff.\n */\nexport default class ComponentStyle {\n  baseHash: number;\n  baseStyle: ComponentStyle | null | undefined;\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<any>;\n  staticRulesId: string;\n\n  constructor(rules: RuleSet<any>, componentId: string, baseStyle?: ComponentStyle | undefined) {\n    this.rules = rules;\n    this.staticRulesId = '';\n    this.isStatic =\n      process.env.NODE_ENV === 'production' &&\n      (baseStyle === undefined || baseStyle.isStatic) &&\n      isStaticRules(rules);\n    this.componentId = componentId;\n    this.baseHash = phash(SEED, componentId);\n    this.baseStyle = baseStyle;\n\n    // NOTE: This registers the componentId, which ensures a consistent order\n    // for this component's styles compared to others\n    StyleSheet.registerId(componentId);\n  }\n\n  generateAndInjectStyles(\n    executionContext: ExecutionContext,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): string {\n    let names = this.baseStyle\n      ? this.baseStyle.generateAndInjectStyles(executionContext, styleSheet, stylis)\n      : '';\n\n    // force dynamic classnames if user-supplied stylis plugins are in use\n    if (this.isStatic && !stylis.hash) {\n      if (this.staticRulesId && styleSheet.hasNameForId(this.componentId, this.staticRulesId)) {\n        names = joinStrings(names, this.staticRulesId);\n      } else {\n        const cssStatic = joinStringArray(\n          flatten(this.rules, executionContext, styleSheet, stylis) as string[]\n        );\n        const name = generateName(phash(this.baseHash, cssStatic) >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          const cssStaticFormatted = stylis(cssStatic, `.${name}`, undefined, this.componentId);\n          styleSheet.insertRules(this.componentId, name, cssStaticFormatted);\n        }\n\n        names = joinStrings(names, name);\n        this.staticRulesId = name;\n      }\n    } else {\n      let dynamicHash = phash(this.baseHash, stylis.hash);\n      let css = '';\n\n      for (let i = 0; i < this.rules.length; i++) {\n        const partRule = this.rules[i];\n\n        if (typeof partRule === 'string') {\n          css += partRule;\n\n          if (process.env.NODE_ENV !== 'production') dynamicHash = phash(dynamicHash, partRule);\n        } else if (partRule) {\n          const partString = joinStringArray(\n            flatten(partRule, executionContext, styleSheet, stylis) as string[]\n          );\n          // The same value can switch positions in the array, so we include \"i\" in the hash.\n          dynamicHash = phash(dynamicHash, partString + i);\n          css += partString;\n        }\n      }\n\n      if (css) {\n        const name = generateName(dynamicHash >>> 0);\n\n        if (!styleSheet.hasNameForId(this.componentId, name)) {\n          styleSheet.insertRules(\n            this.componentId,\n            name,\n            stylis(css, `.${name}`, undefined, this.componentId)\n          );\n        }\n\n        names = joinStrings(names, name);\n      }\n    }\n\n    return names;\n  }\n}\n", "import React, { useContext, useMemo } from 'react';\nimport styledError from '../utils/error';\nimport isFunction from '../utils/isFunction';\n\n// Helper type for the `DefaultTheme` interface that enforces an object type & exclusively allows\n// for typed keys.\ntype DefaultThemeAsObject<T = object> = Record<keyof T, any>;\n\n/**\n * Override DefaultTheme to get accurate typings for your project.\n *\n * ```\n * // create styled-components.d.ts in your project source\n * // if it isn't being picked up, check tsconfig compilerOptions.types\n * import type { CSSProp } from \"styled-components\";\n * import Theme from './theme';\n *\n * type ThemeType = typeof Theme;\n *\n * declare module \"styled-components\" {\n *  export interface DefaultTheme extends ThemeType {}\n * }\n *\n * declare module \"react\" {\n *  interface DOMAttributes<T> {\n *    css?: CSSProp;\n *  }\n * }\n * ```\n */\nexport interface DefaultTheme extends DefaultThemeAsObject {}\n\ntype ThemeFn = (outerTheme?: DefaultTheme | undefined) => DefaultTheme;\ntype ThemeArgument = DefaultTheme | ThemeFn;\n\ntype Props = {\n  children?: React.ReactNode;\n  theme: ThemeArgument;\n};\n\nexport const ThemeContext = React.createContext<DefaultTheme | undefined>(undefined);\n\nexport const ThemeConsumer = ThemeContext.Consumer;\n\nfunction mergeTheme(theme: ThemeArgument, outerTheme?: DefaultTheme | undefined): DefaultTheme {\n  if (!theme) {\n    throw styledError(14);\n  }\n\n  if (isFunction(theme)) {\n    const themeFn = theme as ThemeFn;\n    const mergedTheme = themeFn(outerTheme);\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      (mergedTheme === null || Array.isArray(mergedTheme) || typeof mergedTheme !== 'object')\n    ) {\n      throw styledError(7);\n    }\n\n    return mergedTheme;\n  }\n\n  if (Array.isArray(theme) || typeof theme !== 'object') {\n    throw styledError(8);\n  }\n\n  return outerTheme ? { ...outerTheme, ...theme } : theme;\n}\n\n/**\n * Returns the current theme (as provided by the closest ancestor `ThemeProvider`.)\n *\n * If no `ThemeProvider` is found, the function will error. If you need access to the theme in an\n * uncertain composition scenario, `React.useContext(ThemeContext)` will not emit an error if there\n * is no `ThemeProvider` ancestor.\n */\nexport function useTheme(): DefaultTheme {\n  const theme = useContext(ThemeContext);\n\n  if (!theme) {\n    throw styledError(18);\n  }\n\n  return theme;\n}\n\n/**\n * Provide a theme to an entire react component tree via context\n */\nexport default function ThemeProvider(props: Props): React.JSX.Element | null {\n  const outerTheme = React.useContext(ThemeContext);\n  const themeContext = useMemo(\n    () => mergeTheme(props.theme, outerTheme),\n    [props.theme, outerTheme]\n  );\n\n  if (!props.children) {\n    return null;\n  }\n\n  return <ThemeContext.Provider value={themeContext}>{props.children}</ThemeContext.Provider>;\n}\n", "import isPropValid from '@emotion/is-prop-valid';\nimport React, { createElement, Ref, useDebugValue } from 'react';\nimport { SC_VERSION } from '../constants';\nimport type {\n  AnyComponent,\n  Attrs,\n  BaseObject,\n  Dict,\n  ExecutionContext,\n  ExecutionProps,\n  IStyledComponent,\n  IStyledComponentFactory,\n  IStyledStatics,\n  OmitNever,\n  RuleSet,\n  StyledOptions,\n  WebTarget,\n} from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport createWarnTooManyClasses from '../utils/createWarnTooManyClasses';\nimport determineTheme from '../utils/determineTheme';\nimport domElements from '../utils/domElements';\nimport { EMPTY_ARRAY, EMPTY_OBJECT } from '../utils/empties';\nimport escape from '../utils/escape';\nimport generateComponentId from '../utils/generateComponentId';\nimport generateDisplayName from '../utils/generateDisplayName';\nimport hoist from '../utils/hoist';\nimport isFunction from '../utils/isFunction';\nimport isStyledComponent from '../utils/isStyledComponent';\nimport isTag from '../utils/isTag';\nimport { joinStrings } from '../utils/joinStrings';\nimport merge from '../utils/mixinDeep';\nimport { setToString } from '../utils/setToString';\nimport ComponentStyle from './ComponentStyle';\nimport { useStyleSheetContext } from './StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from './ThemeProvider';\n\nconst identifiers: { [key: string]: number } = {};\n\n/* We depend on components having unique IDs */\nfunction generateId(\n  displayName?: string | undefined,\n  parentComponentId?: string | undefined\n): string {\n  const name = typeof displayName !== 'string' ? 'sc' : escape(displayName);\n  // Ensure that no displayName can lead to duplicate componentIds\n  identifiers[name] = (identifiers[name] || 0) + 1;\n\n  const componentId = `${name}-${generateComponentId(\n    // SC_VERSION gives us isolation between multiple runtimes on the page at once\n    // this is improved further with use of the babel plugin \"namespace\" feature\n    SC_VERSION + name + identifiers[name]\n  )}`;\n\n  return parentComponentId ? `${parentComponentId}-${componentId}` : componentId;\n}\n\nfunction useInjectedStyle<T extends ExecutionContext>(\n  componentStyle: ComponentStyle,\n  resolvedAttrs: T\n) {\n  const ssc = useStyleSheetContext();\n\n  const className = componentStyle.generateAndInjectStyles(\n    resolvedAttrs,\n    ssc.styleSheet,\n    ssc.stylis\n  );\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(className);\n\n  return className;\n}\n\nfunction resolveContext<Props extends object>(\n  attrs: Attrs<React.HTMLAttributes<Element> & Props>[],\n  props: React.HTMLAttributes<Element> & ExecutionProps & Props,\n  theme: DefaultTheme\n) {\n  const context: React.HTMLAttributes<Element> &\n    ExecutionContext &\n    Props & { [key: string]: any; class?: string; ref?: React.Ref<any> } = {\n    ...props,\n    // unset, add `props.className` back at the end so props always \"wins\"\n    className: undefined,\n    theme,\n  };\n  let attrDef;\n\n  for (let i = 0; i < attrs.length; i += 1) {\n    attrDef = attrs[i];\n    const resolvedAttrDef = isFunction(attrDef) ? attrDef(context) : attrDef;\n\n    for (const key in resolvedAttrDef) {\n      context[key as keyof typeof context] =\n        key === 'className'\n          ? joinStrings(context[key] as string | undefined, resolvedAttrDef[key] as string)\n          : key === 'style'\n            ? { ...context[key], ...resolvedAttrDef[key] }\n            : resolvedAttrDef[key as keyof typeof resolvedAttrDef];\n    }\n  }\n\n  if (props.className) {\n    context.className = joinStrings(context.className, props.className);\n  }\n\n  return context;\n}\n\nlet seenUnknownProps = new Set();\n\nfunction useStyledComponentImpl<Props extends object>(\n  forwardedComponent: IStyledComponent<'web', Props>,\n  props: ExecutionProps & Props,\n  forwardedRef: Ref<Element>\n) {\n  const {\n    attrs: componentAttrs,\n    componentStyle,\n    defaultProps,\n    foldedComponentIds,\n    styledComponentId,\n    target,\n  } = forwardedComponent;\n\n  const contextTheme = React.useContext(ThemeContext);\n  const ssc = useStyleSheetContext();\n  const shouldForwardProp = forwardedComponent.shouldForwardProp || ssc.shouldForwardProp;\n\n  if (process.env.NODE_ENV !== 'production') useDebugValue(styledComponentId);\n\n  // NOTE: the non-hooks version only subscribes to this when !componentStyle.isStatic,\n  // but that'd be against the rules-of-hooks. We could be naughty and do it anyway as it\n  // should be an immutable value, but behave for now.\n  const theme = determineTheme(props, contextTheme, defaultProps) || EMPTY_OBJECT;\n\n  const context = resolveContext<Props>(componentAttrs, props, theme);\n  const elementToBeCreated: WebTarget = context.as || target;\n  const propsForElement: Dict<any> = {};\n\n  for (const key in context) {\n    if (context[key] === undefined) {\n      // Omit undefined values from props passed to wrapped element.\n      // This enables using .attrs() to remove props, for example.\n    } else if (key[0] === '$' || key === 'as' || (key === 'theme' && context.theme === theme)) {\n      // Omit transient props and execution props.\n    } else if (key === 'forwardedAs') {\n      propsForElement.as = context.forwardedAs;\n    } else if (!shouldForwardProp || shouldForwardProp(key, elementToBeCreated)) {\n      propsForElement[key] = context[key];\n\n      if (\n        !shouldForwardProp &&\n        process.env.NODE_ENV === 'development' &&\n        !isPropValid(key) &&\n        !seenUnknownProps.has(key) &&\n        // Only warn on DOM Element.\n        domElements.has(elementToBeCreated as any)\n      ) {\n        seenUnknownProps.add(key);\n        console.warn(\n          `styled-components: it looks like an unknown prop \"${key}\" is being sent through to the DOM, which will likely trigger a React console error. If you would like automatic filtering of unknown props, you can opt-into that behavior via \\`<StyleSheetManager shouldForwardProp={...}>\\` (connect an API like \\`@emotion/is-prop-valid\\`) or consider using transient props (\\`$\\` prefix for automatic filtering.)`\n        );\n      }\n    }\n  }\n\n  const generatedClassName = useInjectedStyle(componentStyle, context);\n\n  if (process.env.NODE_ENV !== 'production' && forwardedComponent.warnTooManyClasses) {\n    forwardedComponent.warnTooManyClasses(generatedClassName);\n  }\n\n  let classString = joinStrings(foldedComponentIds, styledComponentId);\n  if (generatedClassName) {\n    classString += ' ' + generatedClassName;\n  }\n  if (context.className) {\n    classString += ' ' + context.className;\n  }\n\n  propsForElement[\n    // handle custom elements which React doesn't properly alias\n    isTag(elementToBeCreated) &&\n    !domElements.has(elementToBeCreated as Extract<typeof domElements, string>)\n      ? 'class'\n      : 'className'\n  ] = classString;\n\n  // forwardedRef is coming from React.forwardRef.\n  // But it might not exist. Since React 19 handles `ref` like a prop, it only define it if there is a value.\n  // We don't want to inject an empty ref.\n  if (forwardedRef) {\n    propsForElement.ref = forwardedRef;\n  }\n\n  return createElement(elementToBeCreated, propsForElement);\n}\n\nfunction createStyledComponent<\n  Target extends WebTarget,\n  OuterProps extends object,\n  Statics extends object = BaseObject,\n>(\n  target: Target,\n  options: StyledOptions<'web', OuterProps>,\n  rules: RuleSet<OuterProps>\n): ReturnType<IStyledComponentFactory<'web', Target, OuterProps, Statics>> {\n  const isTargetStyledComp = isStyledComponent(target);\n  const styledComponentTarget = target as IStyledComponent<'web', OuterProps>;\n  const isCompositeComponent = !isTag(target);\n\n  const {\n    attrs = EMPTY_ARRAY,\n    componentId = generateId(options.displayName, options.parentComponentId),\n    displayName = generateDisplayName(target),\n  } = options;\n\n  const styledComponentId =\n    options.displayName && options.componentId\n      ? `${escape(options.displayName)}-${options.componentId}`\n      : options.componentId || componentId;\n\n  // fold the underlying StyledComponent attrs up (implicit extend)\n  const finalAttrs =\n    isTargetStyledComp && styledComponentTarget.attrs\n      ? styledComponentTarget.attrs.concat(attrs as unknown as Attrs<OuterProps>[]).filter(Boolean)\n      : (attrs as Attrs<OuterProps>[]);\n\n  let { shouldForwardProp } = options;\n\n  if (isTargetStyledComp && styledComponentTarget.shouldForwardProp) {\n    const shouldForwardPropFn = styledComponentTarget.shouldForwardProp;\n\n    if (options.shouldForwardProp) {\n      const passedShouldForwardPropFn = options.shouldForwardProp;\n\n      // compose nested shouldForwardProp calls\n      shouldForwardProp = (prop, elementToBeCreated) =>\n        shouldForwardPropFn(prop, elementToBeCreated) &&\n        passedShouldForwardPropFn(prop, elementToBeCreated);\n    } else {\n      shouldForwardProp = shouldForwardPropFn;\n    }\n  }\n\n  const componentStyle = new ComponentStyle(\n    rules,\n    styledComponentId,\n    isTargetStyledComp ? (styledComponentTarget.componentStyle as ComponentStyle) : undefined\n  );\n\n  function forwardRefRender(props: ExecutionProps & OuterProps, ref: Ref<Element>) {\n    return useStyledComponentImpl<OuterProps>(WrappedStyledComponent, props, ref);\n  }\n\n  forwardRefRender.displayName = displayName;\n\n  /**\n   * forwardRef creates a new interim component, which we'll take advantage of\n   * instead of extending ParentComponent to create _another_ interim class\n   */\n  let WrappedStyledComponent = React.forwardRef(forwardRefRender) as unknown as IStyledComponent<\n    'web',\n    any\n  > &\n    Statics;\n  WrappedStyledComponent.attrs = finalAttrs;\n  WrappedStyledComponent.componentStyle = componentStyle;\n  WrappedStyledComponent.displayName = displayName;\n  WrappedStyledComponent.shouldForwardProp = shouldForwardProp;\n\n  // this static is used to preserve the cascade of static classes for component selector\n  // purposes; this is especially important with usage of the css prop\n  WrappedStyledComponent.foldedComponentIds = isTargetStyledComp\n    ? joinStrings(styledComponentTarget.foldedComponentIds, styledComponentTarget.styledComponentId)\n    : '';\n\n  WrappedStyledComponent.styledComponentId = styledComponentId;\n\n  // fold the underlying StyledComponent target up since we folded the styles\n  WrappedStyledComponent.target = isTargetStyledComp ? styledComponentTarget.target : target;\n\n  Object.defineProperty(WrappedStyledComponent, 'defaultProps', {\n    get() {\n      return this._foldedDefaultProps;\n    },\n\n    set(obj) {\n      this._foldedDefaultProps = isTargetStyledComp\n        ? merge({}, styledComponentTarget.defaultProps, obj)\n        : obj;\n    },\n  });\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(displayName, styledComponentId);\n\n    WrappedStyledComponent.warnTooManyClasses = createWarnTooManyClasses(\n      displayName,\n      styledComponentId\n    );\n  }\n\n  setToString(WrappedStyledComponent, () => `.${WrappedStyledComponent.styledComponentId}`);\n\n  if (isCompositeComponent) {\n    const compositeComponentTarget = target as AnyComponent;\n\n    hoist<typeof WrappedStyledComponent, typeof compositeComponentTarget>(\n      WrappedStyledComponent,\n      compositeComponentTarget,\n      {\n        // all SC-specific things should not be hoisted\n        attrs: true,\n        componentStyle: true,\n        displayName: true,\n        foldedComponentIds: true,\n        shouldForwardProp: true,\n        styledComponentId: true,\n        target: true,\n      } as { [key in keyof OmitNever<IStyledStatics<'web', OuterProps>>]: true }\n    );\n  }\n\n  return WrappedStyledComponent;\n}\n\nexport default createStyledComponent;\n", "import { StyledTarget } from '../types';\nimport getComponentName from './getComponentName';\nimport isTag from './isTag';\n\nexport default function generateDisplayName(target: StyledTarget<any>) {\n  return isTag(target) ? `styled.${target}` : `Styled(${getComponentName(target)})`;\n}\n", "import { Dict } from '../types';\n\nexport const LIMIT = 200;\n\nexport default (displayName: string, componentId: string) => {\n  let generatedClasses: Dict<any> = {};\n  let warningSeen = false;\n\n  return (className: string) => {\n    if (!warningSeen) {\n      generatedClasses[className] = true;\n      if (Object.keys(generatedClasses).length >= LIMIT) {\n        // Unable to find latestRule in test environment.\n\n        const parsedIdString = componentId ? ` with the id of \"${componentId}\"` : '';\n\n        console.warn(\n          `Over ${LIMIT} classes were generated for component ${displayName}${parsedIdString}.\\n` +\n            'Consider using the attrs method, together with a style object for frequently changed styles.\\n' +\n            'Example:\\n' +\n            '  const Component = styled.div.attrs(props => ({\\n' +\n            '    style: {\\n' +\n            '      background: props.background,\\n' +\n            '    },\\n' +\n            '  }))`width: 100%;`\\n\\n' +\n            '  <Component />'\n        );\n        warningSeen = true;\n        generatedClasses = {};\n      }\n    }\n  };\n};\n", "import { Interpolation } from '../types';\n\nexport default function interleave<Props extends object>(\n  strings: readonly string[],\n  interpolations: Interpolation<Props>[]\n): Interpolation<Props>[] {\n  const result: Interpolation<Props>[] = [strings[0]];\n\n  for (let i = 0, len = interpolations.length; i < len; i += 1) {\n    result.push(interpolations[i], strings[i + 1]);\n  }\n\n  return result;\n}\n", "import {\n  BaseObject,\n  Interpolation,\n  NoInfer,\n  RuleSet,\n  StyledObject,\n  StyleFunction,\n  Styles,\n} from '../types';\nimport { EMPTY_ARRAY } from '../utils/empties';\nimport flatten from '../utils/flatten';\nimport interleave from '../utils/interleave';\nimport isFunction from '../utils/isFunction';\nimport isPlainObject from '../utils/isPlainObject';\n\n/**\n * Used when flattening object styles to determine if we should\n * expand an array of styles.\n */\nconst addTag = <T extends RuleSet<any>>(arg: T): T & { isCss: true } =>\n  Object.assign(arg, { isCss: true } as const);\n\nfunction css(styles: Styles<object>, ...interpolations: Interpolation<object>[]): RuleSet<object>;\nfunction css<Props extends object>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>>;\nfunction css<Props extends object = BaseObject>(\n  styles: Styles<NoInfer<Props>>,\n  ...interpolations: Interpolation<NoInfer<Props>>[]\n): RuleSet<NoInfer<Props>> {\n  if (isFunction(styles) || isPlainObject(styles)) {\n    const styleFunctionOrObject = styles as StyleFunction<Props> | StyledObject<Props>;\n\n    return addTag(\n      flatten<Props>(\n        interleave<Props>(EMPTY_ARRAY, [\n          styleFunctionOrObject,\n          ...interpolations,\n        ]) as Interpolation<object>\n      )\n    );\n  }\n\n  const styleStringArray = styles as TemplateStringsArray;\n\n  if (\n    interpolations.length === 0 &&\n    styleStringArray.length === 1 &&\n    typeof styleStringArray[0] === 'string'\n  ) {\n    return flatten<Props>(styleStringArray);\n  }\n\n  return addTag(\n    flatten<Props>(interleave<Props>(styleStringArray, interpolations) as Interpolation<object>)\n  );\n}\n\nexport default css;\n", "import {\n  Attrs,\n  BaseObject,\n  ExecutionProps,\n  Interpolation,\n  IStyledComponent,\n  IStyledComponentFactory,\n  KnownTarget,\n  NoInfer,\n  Runtime,\n  StyledOptions,\n  StyledTarget,\n  Styles,\n  Substitute,\n} from '../types';\nimport { EMPTY_OBJECT } from '../utils/empties';\nimport styledError from '../utils/error';\nimport css from './css';\n\ntype AttrsResult<T extends Attrs<any>> = T extends (...args: any) => infer P\n  ? P extends object\n    ? P\n    : never\n  : T extends object\n    ? T\n    : never;\n\n/**\n * Based on Attrs being a simple object or function that returns\n * a prop object, inspect the attrs result and attempt to extract\n * any \"as\" prop usage to modify the runtime target.\n */\ntype AttrsTarget<\n  R extends Runtime,\n  T extends Attrs<any>,\n  FallbackTarget extends StyledTarget<R>,\n  Result extends ExecutionProps = AttrsResult<T>,\n> = Result extends { as: infer RuntimeTarget }\n  ? RuntimeTarget extends KnownTarget\n    ? RuntimeTarget\n    : FallbackTarget\n  : FallbackTarget;\n\nexport interface Styled<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object,\n  OuterStatics extends object = BaseObject,\n> {\n  <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, NoInfer<Props>>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, NoInfer<Props>>>[]\n  ): IStyledComponent<R, Substitute<OuterProps, Props>> &\n    OuterStatics &\n    Statics &\n    (R extends 'web'\n      ? Target extends string\n        ? {}\n        : Omit<Target, keyof React.Component<any>>\n      : {});\n\n  attrs: <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) => Styled<\n    R,\n    PrivateResolvedTarget,\n    PrivateResolvedTarget extends KnownTarget\n      ? Substitute<\n          Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n          Props\n        >\n      : PrivateMergedProps,\n    OuterStatics\n  >;\n\n  withConfig: (config: StyledOptions<R, OuterProps>) => Styled<R, Target, OuterProps, OuterStatics>;\n}\n\nexport default function constructWithOptions<\n  R extends Runtime,\n  Target extends StyledTarget<R>,\n  OuterProps extends object = Target extends KnownTarget\n    ? React.ComponentPropsWithRef<Target>\n    : BaseObject,\n  OuterStatics extends object = BaseObject,\n>(\n  componentConstructor: IStyledComponentFactory<R, StyledTarget<R>, object, any>,\n  tag: StyledTarget<R>,\n  options: StyledOptions<R, OuterProps> = EMPTY_OBJECT\n): Styled<R, Target, OuterProps, OuterStatics> {\n  /**\n   * We trust that the tag is a valid component as long as it isn't\n   * falsish. Typically the tag here is a string or function (i.e.\n   * class or pure function component), however a component may also be\n   * an object if it uses another utility, e.g. React.memo. React will\n   * output an appropriate warning however if the `tag` isn't valid.\n   */\n  if (!tag) {\n    throw styledError(1, tag);\n  }\n\n  /* This is callable directly as a template function */\n  const templateFunction = <Props extends object = BaseObject, Statics extends object = BaseObject>(\n    initialStyles: Styles<Substitute<OuterProps, Props>>,\n    ...interpolations: Interpolation<Substitute<OuterProps, Props>>[]\n  ) =>\n    componentConstructor<Substitute<OuterProps, Props>, Statics>(\n      tag,\n      options as StyledOptions<R, Substitute<OuterProps, Props>>,\n      css<Substitute<OuterProps, Props>>(initialStyles, ...interpolations)\n    );\n\n  /**\n   * Attrs allows for accomplishing two goals:\n   *\n   * 1. Backfilling props at runtime more expressively than defaultProps\n   * 2. Amending the prop interface of a wrapped styled component\n   */\n  templateFunction.attrs = <\n    Props extends object = BaseObject,\n    PrivateMergedProps extends object = Substitute<OuterProps, Props>,\n    PrivateAttrsArg extends Attrs<PrivateMergedProps> = Attrs<PrivateMergedProps>,\n    PrivateResolvedTarget extends StyledTarget<R> = AttrsTarget<R, PrivateAttrsArg, Target>,\n  >(\n    attrs: PrivateAttrsArg\n  ) =>\n    constructWithOptions<\n      R,\n      PrivateResolvedTarget,\n      PrivateResolvedTarget extends KnownTarget\n        ? Substitute<\n            Substitute<OuterProps, React.ComponentPropsWithRef<PrivateResolvedTarget>>,\n            Props\n          >\n        : PrivateMergedProps,\n      OuterStatics\n    >(componentConstructor, tag, {\n      ...options,\n      attrs: Array.prototype.concat(options.attrs, attrs).filter(Boolean),\n    });\n\n  /**\n   * If config methods are called, wrap up a new template function\n   * and merge options.\n   */\n  templateFunction.withConfig = (config: StyledOptions<R, OuterProps>) =>\n    constructWithOptions<R, Target, OuterProps, OuterStatics>(componentConstructor, tag, {\n      ...options,\n      ...config,\n    });\n\n  return templateFunction;\n}\n", "import * as React from 'react';\nimport createStyledComponent from '../models/StyledComponent';\nimport { BaseObject, KnownTarget, WebTarget } from '../types';\nimport domElements, { SupportedHTMLElements } from '../utils/domElements';\nimport constructWithOptions, { Styled as StyledInstance } from './constructWithOptions';\n\nconst baseStyled = <Target extends WebTarget, InjectedProps extends object = BaseObject>(\n  tag: Target\n) =>\n  constructWithOptions<\n    'web',\n    Target,\n    Target extends KnownTarget ? React.ComponentPropsWithRef<Target> & InjectedProps : InjectedProps\n  >(createStyledComponent, tag);\n\nconst styled = baseStyled as typeof baseStyled & {\n  [E in SupportedHTMLElements]: StyledInstance<'web', E, React.JSX.IntrinsicElements[E]>;\n};\n\n// Shorthands for all valid HTML Elements\ndomElements.forEach(domElement => {\n  // @ts-expect-error some react typing bs\n  styled[domElement] = baseStyled<typeof domElement>(domElement);\n});\n\nexport default styled;\nexport { StyledInstance };\n\n/**\n * This is the type of the `styled` HOC.\n */\nexport type Styled = typeof styled;\n\n/**\n * Use this higher-order type for scenarios where you are wrapping `styled`\n * and providing extra props as a third-party library.\n */\nexport type LibraryStyled<LibraryProps extends object = BaseObject> = <Target extends WebTarget>(\n  tag: Target\n) => typeof baseStyled<Target, LibraryProps>;\n", "import StyleSheet from '../sheet';\nimport { ExecutionContext, RuleSet, Stringifier } from '../types';\nimport flatten from '../utils/flatten';\nimport isStaticRules from '../utils/isStaticRules';\nimport { joinStringArray } from '../utils/joinStrings';\n\nexport default class GlobalStyle<Props extends object> {\n  componentId: string;\n  isStatic: boolean;\n  rules: RuleSet<Props>;\n\n  constructor(rules: RuleSet<Props>, componentId: string) {\n    this.rules = rules;\n    this.componentId = componentId;\n    this.isStatic = isStaticRules(rules);\n\n    // pre-register the first instance to ensure global styles\n    // load before component ones\n    StyleSheet.registerId(this.componentId + 1);\n  }\n\n  createStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    const flatCSS = joinStringArray(\n      flatten(this.rules as RuleSet<object>, executionContext, styleSheet, stylis) as string[]\n    );\n    const css = stylis(flatCSS, '');\n    const id = this.componentId + instance;\n\n    // NOTE: We use the id as a name as well, since these rules never change\n    styleSheet.insertRules(id, id, css);\n  }\n\n  removeStyles(instance: number, styleSheet: StyleSheet): void {\n    styleSheet.clearRules(this.componentId + instance);\n  }\n\n  renderStyles(\n    instance: number,\n    executionContext: ExecutionContext & Props,\n    styleSheet: StyleSheet,\n    stylis: Stringifier\n  ): void {\n    if (instance > 2) StyleSheet.registerId(this.componentId + instance);\n\n    // NOTE: Remove old styles, then inject the new ones\n    this.removeStyles(instance, styleSheet);\n    this.createStyles(instance, executionContext, styleSheet, stylis);\n  }\n}\n", "import React from 'react';\nimport { STATIC_EXECUTION_CONTEXT } from '../constants';\nimport GlobalStyle from '../models/GlobalStyle';\nimport { useStyleSheetContext } from '../models/StyleSheetManager';\nimport { DefaultTheme, ThemeContext } from '../models/ThemeProvider';\nimport StyleSheet from '../sheet';\nimport { ExecutionContext, ExecutionProps, Interpolation, Stringifier, Styles } from '../types';\nimport { checkDynamicCreation } from '../utils/checkDynamicCreation';\nimport determineTheme from '../utils/determineTheme';\nimport generateComponentId from '../utils/generateComponentId';\nimport css from './css';\n\nexport default function createGlobalStyle<Props extends object>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n) {\n  const rules = css<Props>(strings, ...interpolations);\n  const styledComponentId = `sc-global-${generateComponentId(JSON.stringify(rules))}`;\n  const globalStyle = new GlobalStyle<Props>(rules, styledComponentId);\n\n  if (process.env.NODE_ENV !== 'production') {\n    checkDynamicCreation(styledComponentId);\n  }\n\n  const GlobalStyleComponent: React.ComponentType<ExecutionProps & Props> = props => {\n    const ssc = useStyleSheetContext();\n    const theme = React.useContext(ThemeContext);\n    const instanceRef = React.useRef(ssc.styleSheet.allocateGSInstance(styledComponentId));\n\n    const instance = instanceRef.current;\n\n    if (process.env.NODE_ENV !== 'production' && React.Children.count(props.children)) {\n      console.warn(\n        `The global style component ${styledComponentId} was given child JSX. createGlobalStyle does not render children.`\n      );\n    }\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      rules.some(rule => typeof rule === 'string' && rule.indexOf('@import') !== -1)\n    ) {\n      console.warn(\n        `Please do not use @import CSS syntax in createGlobalStyle at this time, as the CSSOM APIs we use in production do not handle it well. Instead, we recommend using a library such as react-helmet to inject a typical <link> meta tag to the stylesheet, or simply embedding it manually in your index.html <head> section for a simpler app.`\n      );\n    }\n\n    if (ssc.styleSheet.server) {\n      renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n    }\n\n    if (!__SERVER__) {\n      React.useLayoutEffect(() => {\n        if (!ssc.styleSheet.server) {\n          renderStyles(instance, props, ssc.styleSheet, theme, ssc.stylis);\n          return () => globalStyle.removeStyles(instance, ssc.styleSheet);\n        }\n      }, [instance, props, ssc.styleSheet, theme, ssc.stylis]);\n    }\n\n    return null;\n  };\n\n  function renderStyles(\n    instance: number,\n    props: ExecutionProps,\n    styleSheet: StyleSheet,\n    theme: DefaultTheme | undefined,\n    stylis: Stringifier\n  ) {\n    if (globalStyle.isStatic) {\n      globalStyle.renderStyles(\n        instance,\n        STATIC_EXECUTION_CONTEXT as unknown as ExecutionContext & Props,\n        styleSheet,\n        stylis\n      );\n    } else {\n      const context = {\n        ...props,\n        theme: determineTheme(props, theme, GlobalStyleComponent.defaultProps),\n      } as ExecutionContext & Props;\n\n      globalStyle.renderStyles(instance, context, styleSheet, stylis);\n    }\n  }\n\n  return React.memo(GlobalStyleComponent);\n}\n", "import Keyframes from '../models/Keyframes';\nimport { Interpolation, Styles } from '../types';\nimport generateComponentId from '../utils/generateComponentId';\nimport { joinStringArray } from '../utils/joinStrings';\nimport css from './css';\n\nexport default function keyframes<Props extends object = {}>(\n  strings: Styles<Props>,\n  ...interpolations: Array<Interpolation<Props>>\n): Keyframes {\n  /* Warning if you've used keyframes on React Native */\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    typeof navigator !== 'undefined' &&\n    navigator.product === 'ReactNative'\n  ) {\n    console.warn(\n      '`keyframes` cannot be used on ReactNative, only on the web. To do animation in ReactNative please use Animated.'\n    );\n  }\n\n  const rules = joinStringArray(css<Props>(strings, ...interpolations) as string[]);\n  const name = generateComponentId(rules);\n  return new Keyframes(name, rules);\n}\n", "import React from 'react';\nimport { ThemeContext } from '../models/ThemeProvider';\nimport { AnyComponent, ExecutionProps } from '../types';\nimport determineTheme from '../utils/determineTheme';\nimport getComponentName from '../utils/getComponentName';\nimport hoist, { NonReactStatics } from '../utils/hoist';\n\nexport default function withTheme<T extends AnyComponent>(\n  Component: T\n): React.ForwardRefExoticComponent<\n  React.PropsWithoutRef<React.JSX.LibraryManagedAttributes<T, ExecutionProps>> &\n    React.RefAttributes<T>\n> &\n  NonReactStatics<T> {\n  const WithTheme = React.forwardRef<T, React.JSX.LibraryManagedAttributes<T, ExecutionProps>>(\n    (props, ref) => {\n      const theme = React.useContext(ThemeContext);\n      const themeProp = determineTheme(props, theme, Component.defaultProps);\n\n      if (process.env.NODE_ENV !== 'production' && themeProp === undefined) {\n        console.warn(\n          `[withTheme] You are not using a ThemeProvider nor passing a theme prop or a theme in defaultProps in component class \"${getComponentName(\n            Component\n          )}\"`\n        );\n      }\n\n      return <Component {...props} theme={themeProp} ref={ref} />;\n    }\n  );\n\n  WithTheme.displayName = `WithTheme(${getComponentName(Component)})`;\n\n  return hoist(WithTheme, Component);\n}\n", "import React from 'react';\nimport type * as streamInternal from 'stream';\nimport { Readable } from 'stream';\nimport { IS_BROWSER, SC_ATTR, SC_ATTR_VERSION, SC_VERSION } from '../constants';\nimport StyleSheet from '../sheet';\nimport styledError from '../utils/error';\nimport { joinStringArray } from '../utils/joinStrings';\nimport getNonce from '../utils/nonce';\nimport { StyleSheetManager } from './StyleSheetManager';\n\ndeclare const __SERVER__: boolean;\n\nconst CLOSING_TAG_R = /^\\s*<\\/[a-z]/i;\n\nexport default class ServerStyleSheet {\n  instance: StyleSheet;\n  sealed: boolean;\n\n  constructor() {\n    this.instance = new StyleSheet({ isServer: true });\n    this.sealed = false;\n  }\n\n  _emitSheetCSS = (): string => {\n    const css = this.instance.toString();\n    if (!css) return '';\n    const nonce = getNonce();\n    const attrs = [\n      nonce && `nonce=\"${nonce}\"`,\n      `${SC_ATTR}=\"true\"`,\n      `${SC_ATTR_VERSION}=\"${SC_VERSION}\"`,\n    ];\n    const htmlAttr = joinStringArray(attrs.filter(Boolean) as string[], ' ');\n\n    return `<style ${htmlAttr}>${css}</style>`;\n  };\n\n  collectStyles(children: any): React.JSX.Element {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return <StyleSheetManager sheet={this.instance}>{children}</StyleSheetManager>;\n  }\n\n  getStyleTags = (): string => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    return this._emitSheetCSS();\n  };\n\n  getStyleElement = () => {\n    if (this.sealed) {\n      throw styledError(2);\n    }\n\n    const css = this.instance.toString();\n    if (!css) return [];\n\n    const props = {\n      [SC_ATTR]: '',\n      [SC_ATTR_VERSION]: SC_VERSION,\n      dangerouslySetInnerHTML: {\n        __html: css,\n      },\n    };\n\n    const nonce = getNonce();\n    if (nonce) {\n      (props as any).nonce = nonce;\n    }\n\n    // v4 returned an array for this fn, so we'll do the same for v5 for backward compat\n    return [<style {...props} key=\"sc-0-0\" />];\n  };\n\n  // @ts-expect-error alternate return types are not possible due to code transformation\n  interleaveWithNodeStream(input: Readable): streamInternal.Transform {\n    if (!__SERVER__ || IS_BROWSER) {\n      throw styledError(3);\n    } else if (this.sealed) {\n      throw styledError(2);\n    }\n\n    if (__SERVER__) {\n      this.seal();\n\n      const { Transform } = require('stream');\n\n      const readableStream: Readable = input;\n      const { instance: sheet, _emitSheetCSS } = this;\n\n      const transformer: streamInternal.Transform = new Transform({\n        transform: function appendStyleChunks(\n          chunk: string,\n          /* encoding */\n          _: string,\n          callback: Function\n        ) {\n          // Get the chunk and retrieve the sheet's CSS as an HTML chunk,\n          // then reset its rules so we get only new ones for the next chunk\n          const renderedHtml = chunk.toString();\n          const html = _emitSheetCSS();\n\n          sheet.clearTag();\n\n          // prepend style html to chunk, unless the start of the chunk is a\n          // closing tag in which case append right after that\n          if (CLOSING_TAG_R.test(renderedHtml)) {\n            const endOfClosingTag = renderedHtml.indexOf('>') + 1;\n            const before = renderedHtml.slice(0, endOfClosingTag);\n            const after = renderedHtml.slice(endOfClosingTag);\n\n            this.push(before + html + after);\n          } else {\n            this.push(html + renderedHtml);\n          }\n\n          callback();\n        },\n      });\n\n      readableStream.on('error', err => {\n        // forward the error to the transform stream\n        transformer.emit('error', err);\n      });\n\n      return readableStream.pipe(transformer);\n    }\n  }\n\n  seal = (): void => {\n    this.sealed = true;\n  };\n}\n", "import { mainSheet } from './models/StyleSheetManager';\nimport StyleSheet from './sheet';\n\nexport const __PRIVATE__ = {\n  StyleSheet,\n  mainSheet,\n};\n", "/* Import singletons */\nimport { SC_ATTR, SC_VERSION } from './constants';\nimport createGlobalStyle from './constructors/createGlobalStyle';\nimport css from './constructors/css';\nimport keyframes from './constructors/keyframes';\n/* Import Higher Order Components */\nimport withTheme from './hoc/withTheme';\n/* Import hooks */\nimport ServerStyleSheet from './models/ServerStyleSheet';\nimport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n} from './models/StyleSheetManager';\n/* Import components */\nimport ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from './models/ThemeProvider';\nimport isStyledComponent from './utils/isStyledComponent';\n\n/* Warning if you've imported this file on React Native */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  typeof navigator !== 'undefined' &&\n  navigator.product === 'ReactNative'\n) {\n  console.warn(\n    `It looks like you've imported 'styled-components' on React Native.\\nPerhaps you're looking to import 'styled-components/native'?\\nRead more about this at https://www.styled-components.com/docs/basics#react-native`\n  );\n}\n\nconst windowGlobalKey = `__sc-${SC_ATTR}__`;\n\n/* Warning if there are several instances of styled-components */\nif (\n  process.env.NODE_ENV !== 'production' &&\n  process.env.NODE_ENV !== 'test' &&\n  typeof window !== 'undefined'\n) {\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] ||= 0;\n\n  // @ts-expect-error dynamic key not in window object\n  if (window[windowGlobalKey] === 1) {\n    console.warn(\n      `It looks like there are several instances of 'styled-components' initialized in this application. This may cause dynamic styles to not render properly, errors during the rehydration process, a missing theme prop, and makes your application bigger without good reason.\\n\\nSee https://s-c.sh/2BAXzed for more info.`\n    );\n  }\n\n  // @ts-expect-error dynamic key not in window object\n  window[windowGlobalKey] += 1;\n}\n\n/* Export everything */\nexport * from './secretInternals';\nexport { Attrs, DefaultTheme, ShouldForwardProp } from './types';\nexport {\n  IStyleSheetContext,\n  IStyleSheetManager,\n  IStylisContext,\n  ServerStyleSheet,\n  StyleSheetConsumer,\n  StyleSheetContext,\n  StyleSheetManager,\n  ThemeConsumer,\n  ThemeContext,\n  ThemeProvider,\n  createGlobalStyle,\n  css,\n  isStyledComponent,\n  keyframes,\n  useTheme,\n  SC_VERSION as version,\n  withTheme,\n};\n"], "mappings": ";;;;;;;AAAA;AAAA;AAEA,WAAO,UAAU,SAAS,aAAa,MAAM,MAAM,SAAS,gBAAgB;AAC1E,UAAI,MAAM,UAAU,QAAQ,KAAK,gBAAgB,MAAM,IAAI,IAAI;AAE/D,UAAI,QAAQ,QAAQ;AAClB,eAAO,CAAC,CAAC;AAAA,MACX;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM;AAC1E,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,UAAI,QAAQ,OAAO,KAAK,IAAI;AAE5B,UAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AAEA,UAAI,kBAAkB,OAAO,UAAU,eAAe,KAAK,IAAI;AAG/D,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,YAAI,MAAM,MAAM,GAAG;AAEnB,YAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,KAAK,GAAG;AACrB,YAAI,SAAS,KAAK,GAAG;AAErB,cAAM,UAAU,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,IAAI;AAEpE,YAAI,QAAQ,SAAU,QAAQ,UAAU,WAAW,QAAS;AAC1D,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACdO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAASC,IAAGC,KAAI,GAAG,IAAI,UAAU,QAAQA,KAAI,GAAGA,MAAK;AACjD,MAAAD,KAAI,UAAUC,EAAC;AACf,eAASC,MAAKF;AAAG,YAAI,OAAO,UAAU,eAAe,KAAKA,IAAGE,EAAC;AAAG,YAAEA,EAAC,IAAIF,GAAEE,EAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AA6KO,SAAS,cAAc,IAAIC,OAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW;AAAG,aAASC,KAAI,GAAGC,KAAIF,MAAK,QAAQ,IAAIC,KAAIC,IAAGD,MAAK;AACjF,UAAI,MAAM,EAAEA,MAAKD,QAAO;AACpB,YAAI,CAAC;AAAI,eAAK,MAAM,UAAU,MAAM,KAAKA,OAAM,GAAGC,EAAC;AACnD,WAAGA,EAAC,IAAID,MAAKC,EAAC;AAAA,MAClB;AAAA,IACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAKD,KAAI,CAAC;AACzD;;;AC7NA,SAAS,QAAQ,IAAI;AACnB,MAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,SAAO,SAAU,KAAK;AACpB,QAAI,MAAM,GAAG,MAAM;AAAW,YAAM,GAAG,IAAI,GAAG,GAAG;AACjD,WAAO,MAAM,GAAG;AAAA,EAClB;AACF;;;ACJA,IAAI,kBAAkB;AAEtB,IAAI,cAA6B;AAAA,EAAQ,SAAU,MAAM;AACvD,WAAO,gBAAgB,KAAK,IAAI,KAAK,KAAK,WAAW,CAAC,MAAM,OAEzD,KAAK,WAAW,CAAC,MAAM,OAEvB,KAAK,WAAW,CAAC,IAAI;AAAA,EAC1B;AAAA;AAEA;;;;;;;ACZO,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,SAAS;AAEb,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,cAAc;AAIlB,IAAI,SAAS;AAMb,IAAI,YAAY;AAIhB,IAAI,QAAQ;;;AChBZ,IAAI,MAAM,KAAK;AAMf,IAAI,OAAO,OAAO;AAMlB,IAAI,SAAS,OAAO;AAOpB,SAAS,KAAM,OAAOG,SAAQ;AACpC,SAAO,OAAO,OAAO,CAAC,IAAI,QAAYA,WAAU,IAAK,OAAO,OAAO,CAAC,MAAM,IAAK,OAAO,OAAO,CAAC,MAAM,IAAK,OAAO,OAAO,CAAC,MAAM,IAAK,OAAO,OAAO,CAAC,IAAI;AACvJ;AAMO,SAAS,KAAM,OAAO;AAC5B,SAAO,MAAM,KAAK;AACnB;AAOO,SAAS,MAAO,OAAO,SAAS;AACtC,UAAQ,QAAQ,QAAQ,KAAK,KAAK,KAAK,MAAM,CAAC,IAAI;AACnD;AAQO,SAAS,QAAS,OAAO,SAAS,aAAa;AACrD,SAAO,MAAM,QAAQ,SAAS,WAAW;AAC1C;AAQO,SAAS,QAAS,OAAO,QAAQC,WAAU;AACjD,SAAO,MAAM,QAAQ,QAAQA,SAAQ;AACtC;AAOO,SAAS,OAAQ,OAAO,OAAO;AACrC,SAAO,MAAM,WAAW,KAAK,IAAI;AAClC;AAQO,SAAS,OAAQ,OAAO,OAAO,KAAK;AAC1C,SAAO,MAAM,MAAM,OAAO,GAAG;AAC9B;AAMO,SAAS,OAAQ,OAAO;AAC9B,SAAO,MAAM;AACd;AAMO,SAAS,OAAQ,OAAO;AAC9B,SAAO,MAAM;AACd;AAOO,SAAS,OAAQ,OAAO,OAAO;AACrC,SAAO,MAAM,KAAK,KAAK,GAAG;AAC3B;AAOO,SAAS,QAAS,OAAO,UAAU;AACzC,SAAO,MAAM,IAAI,QAAQ,EAAE,KAAK,EAAE;AACnC;AAOO,SAAS,OAAQ,OAAO,SAAS;AACvC,SAAO,MAAM,OAAO,SAAU,OAAO;AAAE,WAAO,CAAC,MAAM,OAAO,OAAO;AAAA,EAAE,CAAC;AACvE;;;AC1HO,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,aAAa;AAYjB,SAAS,KAAM,OAAO,MAAM,QAAQ,MAAM,OAAO,UAAUC,SAAQ,UAAU;AACnF,SAAO,EAAC,OAAc,MAAY,QAAgB,MAAY,OAAc,UAAoB,MAAY,QAAgB,QAAQA,SAAQ,QAAQ,IAAI,SAAkB;AAC3K;AAOO,SAAS,KAAM,MAAM,OAAO;AAClC,SAAO,OAAO,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,KAAK,QAAQ,GAAG,MAAM,EAAC,QAAQ,CAAC,KAAK,OAAM,GAAG,KAAK;AAC1G;AAKO,SAAS,KAAM,MAAM;AAC3B,SAAO,KAAK;AACX,WAAO,KAAK,KAAK,MAAM,EAAC,UAAU,CAAC,IAAI,EAAC,CAAC;AAE1C,SAAO,MAAM,KAAK,QAAQ;AAC3B;AAKO,SAAS,OAAQ;AACvB,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,cAAY,WAAW,IAAI,OAAO,YAAY,EAAE,QAAQ,IAAI;AAE5D,MAAI,UAAU,cAAc;AAC3B,aAAS,GAAG;AAEb,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,cAAY,WAAW,SAAS,OAAO,YAAY,UAAU,IAAI;AAEjE,MAAI,UAAU,cAAc;AAC3B,aAAS,GAAG;AAEb,SAAO;AACR;AAKO,SAAS,OAAQ;AACvB,SAAO,OAAO,YAAY,QAAQ;AACnC;AAKO,SAAS,QAAS;AACxB,SAAO;AACR;AAOO,SAAS,MAAO,OAAO,KAAK;AAClC,SAAO,OAAO,YAAY,OAAO,GAAG;AACrC;AAMO,SAAS,MAAO,MAAM;AAC5B,UAAQ,MAAM;AAAA,IAEb,KAAK;AAAA,IAAG,KAAK;AAAA,IAAG,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AACtC,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAE3D,KAAK;AAAA,IAAI,KAAK;AAAA,IAAK,KAAK;AACvB,aAAO;AAAA,IAER,KAAK;AACJ,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAC/B,aAAO;AAAA,IAER,KAAK;AAAA,IAAI,KAAK;AACb,aAAO;AAAA,EACT;AAEA,SAAO;AACR;AAMO,SAAS,MAAO,OAAO;AAC7B,SAAO,OAAO,SAAS,GAAG,SAAS,OAAO,aAAa,KAAK,GAAG,WAAW,GAAG,CAAC;AAC/E;AAMO,SAAS,QAAS,OAAO;AAC/B,SAAO,aAAa,IAAI;AACzB;AAMO,SAAS,QAAS,MAAM;AAC9B,SAAO,KAAK,MAAM,WAAW,GAAG,UAAU,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AACnG;AAcO,SAAS,WAAY,MAAM;AACjC,SAAO,YAAY,KAAK;AACvB,QAAI,YAAY;AACf,WAAK;AAAA;AAEL;AAEF,SAAO,MAAM,IAAI,IAAI,KAAK,MAAM,SAAS,IAAI,IAAI,KAAK;AACvD;AAwBO,SAAS,SAAU,OAAO,OAAO;AACvC,SAAO,EAAE,SAAS,KAAK;AAEtB,QAAI,YAAY,MAAM,YAAY,OAAQ,YAAY,MAAM,YAAY,MAAQ,YAAY,MAAM,YAAY;AAC7G;AAEF,SAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,GAAG;AAC1E;AAMO,SAAS,UAAW,MAAM;AAChC,SAAO,KAAK;AACX,YAAQ,WAAW;AAAA,MAElB,KAAK;AACJ,eAAO;AAAA,MAER,KAAK;AAAA,MAAI,KAAK;AACb,YAAI,SAAS,MAAM,SAAS;AAC3B,oBAAU,SAAS;AACpB;AAAA,MAED,KAAK;AACJ,YAAI,SAAS;AACZ,oBAAU,IAAI;AACf;AAAA,MAED,KAAK;AACJ,aAAK;AACL;AAAA,IACF;AAED,SAAO;AACR;AAOO,SAAS,UAAW,MAAM,OAAO;AACvC,SAAO,KAAK;AAEX,QAAI,OAAO,cAAc,KAAK;AAC7B;AAAA,aAEQ,OAAO,cAAc,KAAK,MAAM,KAAK,MAAM;AACnD;AAEF,SAAO,OAAO,MAAM,OAAO,WAAW,CAAC,IAAI,MAAM,KAAK,SAAS,KAAK,OAAO,KAAK,CAAC;AAClF;AAMO,SAAS,WAAY,OAAO;AAClC,SAAO,CAAC,MAAM,KAAK,CAAC;AACnB,SAAK;AAEN,SAAO,MAAM,OAAO,QAAQ;AAC7B;;;ACxPO,SAAS,QAAS,OAAO;AAC/B,SAAO,QAAQ,MAAM,IAAI,MAAM,MAAM,MAAM,CAAC,EAAE,GAAG,QAAQ,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACtF;AAcO,SAAS,MAAO,OAAO,MAAM,QAAQ,MAAM,OAAO,UAAU,QAAQ,QAAQ,cAAc;AAChG,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAIC,UAAS;AACb,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAIC,aAAY;AAChB,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,MAAIC,cAAa;AAEjB,SAAO;AACN,YAAQ,WAAWD,YAAWA,aAAY,KAAK,GAAG;AAAA,MAEjD,KAAK;AACJ,YAAI,YAAY,OAAO,OAAOC,aAAYF,UAAS,CAAC,KAAK,IAAI;AAC5D,cAAI,QAAQE,eAAc,QAAQ,QAAQD,UAAS,GAAG,KAAK,KAAK,GAAG,OAAO,IAAI,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK;AAChH,wBAAY;AACb;AAAA,QACD;AAAA,MAED,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AACtB,QAAAC,eAAc,QAAQD,UAAS;AAC/B;AAAA,MAED,KAAK;AAAA,MAAG,KAAK;AAAA,MAAI,KAAK;AAAA,MAAI,KAAK;AAC9B,QAAAC,eAAc,WAAW,QAAQ;AACjC;AAAA,MAED,KAAK;AACJ,QAAAA,eAAc,SAAS,MAAM,IAAI,GAAG,CAAC;AACrC;AAAA,MAED,KAAK;AACJ,gBAAQ,KAAK,GAAG;AAAA,UACf,KAAK;AAAA,UAAI,KAAK;AACb,mBAAO,QAAQ,UAAU,KAAK,GAAG,MAAM,CAAC,GAAG,MAAM,QAAQ,YAAY,GAAG,YAAY;AACpF;AAAA,UACD;AACC,YAAAA,eAAc;AAAA,QAChB;AACA;AAAA,MAED,KAAK,MAAM;AACV,eAAO,OAAO,IAAI,OAAOA,WAAU,IAAI;AAAA,MAExC,KAAK,MAAM;AAAA,MAAU,KAAK;AAAA,MAAI,KAAK;AAClC,gBAAQD,YAAW;AAAA,UAElB,KAAK;AAAA,UAAG,KAAK;AAAK,uBAAW;AAAA,UAE7B,KAAK,KAAK;AAAQ,gBAAI,aAAa;AAAI,cAAAC,cAAa,QAAQA,aAAY,OAAO,EAAE;AAChF,gBAAI,WAAW,KAAM,OAAOA,WAAU,IAAIF;AACzC,qBAAO,WAAW,KAAK,YAAYE,cAAa,KAAK,MAAM,QAAQF,UAAS,GAAG,YAAY,IAAI,YAAY,QAAQE,aAAY,KAAK,EAAE,IAAI,KAAK,MAAM,QAAQF,UAAS,GAAG,YAAY,GAAG,YAAY;AACrM;AAAA,UAED,KAAK;AAAI,YAAAE,eAAc;AAAA,UAEvB;AACC,mBAAO,YAAY,QAAQA,aAAY,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM,QAAQ,CAAC,GAAG,WAAW,CAAC,GAAGF,SAAQ,QAAQ,GAAG,QAAQ;AAE/I,gBAAIC,eAAc;AACjB,kBAAI,WAAW;AACd,sBAAMC,aAAY,MAAM,WAAW,WAAW,OAAO,UAAUF,SAAQ,QAAQ,QAAQ;AAAA;AAEvF,wBAAQ,WAAW,MAAM,OAAOE,aAAY,CAAC,MAAM,MAAM,MAAM,QAAQ;AAAA,kBAEtE,KAAK;AAAA,kBAAK,KAAK;AAAA,kBAAK,KAAK;AAAA,kBAAK,KAAK;AAClC,0BAAM,OAAO,WAAW,WAAW,QAAQ,OAAO,QAAQ,OAAO,WAAW,WAAW,GAAG,GAAG,OAAO,QAAQ,MAAM,OAAO,QAAQ,CAAC,GAAGF,SAAQ,QAAQ,GAAG,QAAQ,GAAG,OAAO,UAAUA,SAAQ,QAAQ,OAAO,QAAQ,QAAQ;AAC3N;AAAA,kBACD;AACC,0BAAME,aAAY,WAAW,WAAW,WAAW,CAAC,EAAE,GAAG,UAAU,GAAG,QAAQ,QAAQ;AAAA,gBACxF;AAAA,QACJ;AAEA,gBAAQ,SAAS,WAAW,GAAG,WAAW,YAAY,GAAG,OAAOA,cAAa,IAAIF,UAAS;AAC1F;AAAA,MAED,KAAK;AACJ,QAAAA,UAAS,IAAI,OAAOE,WAAU,GAAG,WAAW;AAAA,MAC7C;AACC,YAAI,WAAW;AACd,cAAID,cAAa;AAChB,cAAE;AAAA,mBACMA,cAAa,OAAO,cAAc,KAAK,KAAK,KAAK;AACzD;AAAA;AAEF,gBAAQC,eAAc,KAAKD,UAAS,GAAGA,aAAY,UAAU;AAAA,UAE5D,KAAK;AACJ,wBAAY,SAAS,IAAI,KAAKC,eAAc,MAAM;AAClD;AAAA,UAED,KAAK;AACJ,mBAAO,OAAO,KAAK,OAAOA,WAAU,IAAI,KAAK,WAAW,YAAY;AACpE;AAAA,UAED,KAAK;AAEJ,gBAAI,KAAK,MAAM;AACd,cAAAA,eAAc,QAAQ,KAAK,CAAC;AAE7B,qBAAS,KAAK,GAAG,SAASF,UAAS,OAAO,OAAOE,eAAc,WAAW,MAAM,CAAC,CAAC,GAAGD;AACrF;AAAA,UAED,KAAK;AACJ,gBAAI,aAAa,MAAM,OAAOC,WAAU,KAAK;AAC5C,yBAAW;AAAA,QACd;AAAA,IACF;AAED,SAAO;AACR;AAiBO,SAAS,QAAS,OAAO,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,MAAM,OAAO,UAAUF,SAAQ,UAAU;AACpH,MAAI,OAAO,SAAS;AACpB,MAAI,OAAO,WAAW,IAAI,QAAQ,CAAC,EAAE;AACrC,MAAI,OAAO,OAAO,IAAI;AAEtB,WAASG,KAAI,GAAGC,KAAI,GAAGC,KAAI,GAAGF,KAAI,OAAO,EAAEA;AAC1C,aAASG,KAAI,GAAGC,KAAI,OAAO,OAAO,OAAO,GAAG,OAAO,IAAIH,KAAI,OAAOD,EAAC,CAAC,CAAC,GAAGK,KAAI,OAAOF,KAAI,MAAM,EAAEA;AAC9F,UAAIE,KAAI,KAAKJ,KAAI,IAAI,KAAKE,EAAC,IAAI,MAAMC,KAAI,QAAQA,IAAG,QAAQ,KAAKD,EAAC,CAAC,CAAC;AACnE,cAAMD,IAAG,IAAIG;AAEhB,SAAO,KAAK,OAAO,MAAM,QAAQ,WAAW,IAAI,UAAU,MAAM,OAAO,UAAUR,SAAQ,QAAQ;AAClG;AASO,SAAS,QAAS,OAAO,MAAM,QAAQ,UAAU;AACvD,SAAO,KAAK,OAAO,MAAM,QAAQ,SAAS,KAAK,KAAK,CAAC,GAAG,OAAO,OAAO,GAAG,EAAE,GAAG,GAAG,QAAQ;AAC1F;AAUO,SAAS,YAAa,OAAO,MAAM,QAAQA,SAAQ,UAAU;AACnE,SAAO,KAAK,OAAO,MAAM,QAAQ,aAAa,OAAO,OAAO,GAAGA,OAAM,GAAG,OAAO,OAAOA,UAAS,GAAG,EAAE,GAAGA,SAAQ,QAAQ;AACxH;;;ACxLO,SAAS,OAAQ,OAAOS,SAAQ,UAAU;AAChD,UAAQ,KAAK,OAAOA,OAAM,GAAG;AAAA,IAE5B,KAAK;AACJ,aAAO,SAAS,WAAW,QAAQ;AAAA,IAEpC,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAEvE,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAE5D,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAE5D,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAC3D,aAAO,SAAS,QAAQ;AAAA,IAEzB,KAAK;AACJ,aAAO,MAAM,QAAQ;AAAA,IAEtB,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAChD,aAAO,SAAS,QAAQ,MAAM,QAAQ,KAAK,QAAQ;AAAA,IAEpD,KAAK;AACJ,cAAQ,OAAO,OAAOA,UAAS,EAAE,GAAG;AAAA,QAEnC,KAAK;AACJ,iBAAO,SAAS,QAAQ,KAAK,QAAQ,OAAO,sBAAsB,IAAI,IAAI;AAAA,QAE3E,KAAK;AACJ,iBAAO,SAAS,QAAQ,KAAK,QAAQ,OAAO,sBAAsB,OAAO,IAAI;AAAA,QAE9E,KAAK;AACJ,iBAAO,SAAS,QAAQ,KAAK,QAAQ,OAAO,sBAAsB,IAAI,IAAI;AAAA,MAE5E;AAAA,IAED,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAC1B,aAAO,SAAS,QAAQ,KAAK,QAAQ;AAAA,IAEtC,KAAK;AACJ,aAAO,SAAS,QAAQ,KAAK,UAAU,QAAQ;AAAA,IAEhD,KAAK;AACJ,aAAO,SAAS,QAAQ,QAAQ,OAAO,kBAAkB,SAAS,aAAa,KAAK,WAAW,IAAI;AAAA,IAEpG,KAAK;AACJ,aAAO,SAAS,QAAQ,KAAK,eAAe,QAAQ,OAAO,gBAAgB,EAAE,KAAK,CAAC,MAAM,OAAO,gBAAgB,IAAI,KAAK,cAAc,QAAQ,OAAO,gBAAgB,EAAE,IAAI,MAAM;AAAA,IAEnL,KAAK;AACJ,aAAO,SAAS,QAAQ,KAAK,mBAAmB,QAAQ,OAAO,8BAA8B,EAAE,IAAI;AAAA,IAEpG,KAAK;AACJ,aAAO,SAAS,QAAQ,KAAK,QAAQ,OAAO,UAAU,UAAU,IAAI;AAAA,IAErE,KAAK;AACJ,aAAO,SAAS,QAAQ,KAAK,QAAQ,OAAO,SAAS,gBAAgB,IAAI;AAAA,IAE1E,KAAK;AACJ,aAAO,SAAS,SAAS,QAAQ,OAAO,SAAS,EAAE,IAAI,SAAS,QAAQ,KAAK,QAAQ,OAAO,QAAQ,UAAU,IAAI;AAAA,IAEnH,KAAK;AACJ,aAAO,SAAS,QAAQ,OAAO,sBAAsB,OAAO,SAAS,IAAI,IAAI;AAAA,IAE9E,KAAK;AACJ,aAAO,QAAQ,QAAQ,QAAQ,OAAO,gBAAgB,SAAS,IAAI,GAAG,eAAe,SAAS,IAAI,GAAG,OAAO,EAAE,IAAI;AAAA,IAEnH,KAAK;AAAA,IAAM,KAAK;AACf,aAAO,QAAQ,OAAO,qBAAqB,SAAS,QAAa;AAAA,IAElE,KAAK;AACJ,aAAO,QAAQ,QAAQ,OAAO,qBAAqB,SAAS,gBAAgB,KAAK,cAAc,GAAG,cAAc,SAAS,IAAI,SAAS,QAAQ;AAAA,IAE/I,KAAK;AACJ,UAAI,CAAC,MAAM,OAAO,gBAAgB;AAAG,eAAO,KAAK,sBAAsB,OAAO,OAAOA,OAAM,IAAI;AAC/F;AAAA,IAED,KAAK;AAAA,IAAM,KAAK;AACf,aAAO,KAAK,QAAQ,OAAO,aAAa,EAAE,IAAI;AAAA,IAE/C,KAAK;AAAA,IAAM,KAAK;AACf,UAAI,YAAY,SAAS,KAAK,SAAU,SAAS,OAAO;AAAE,eAAOA,UAAS,OAAO,MAAM,QAAQ,OAAO,cAAc;AAAA,MAAE,CAAC,GAAG;AACzH,eAAO,CAAC,QAAQ,SAAS,WAAW,SAASA,OAAM,EAAE,QAAQ,QAAQ,CAAC,IAAI,QAAS,KAAK,QAAQ,OAAO,UAAU,EAAE,IAAI,QAAQ,KAAK,oBAAoB,CAAC,QAAQ,UAAU,QAAQ,CAAC,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,MAAM,UAAU,KAAK,IAAI,CAAC,MAAM,OAAO,KAAK,KAAK;AAAA,MACpQ;AACA,aAAO,KAAK,QAAQ,OAAO,UAAU,EAAE,IAAI;AAAA,IAE5C,KAAK;AAAA,IAAM,KAAK;AACf,aAAQ,YAAY,SAAS,KAAK,SAAU,SAAS;AAAE,eAAO,MAAM,QAAQ,OAAO,gBAAgB;AAAA,MAAE,CAAC,IAAK,QAAQ,KAAK,QAAQ,QAAQ,OAAO,QAAQ,OAAO,GAAG,SAAS,EAAE,IAAI;AAAA,IAEjL,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AACrC,aAAO,QAAQ,OAAO,mBAAmB,SAAS,MAAM,IAAI;AAAA,IAE7D,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IACtC,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IACtC,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAErC,UAAI,OAAO,KAAK,IAAI,IAAIA,UAAS;AAChC,gBAAQ,OAAO,OAAOA,UAAS,CAAC,GAAG;AAAA,UAElC,KAAK;AAEJ,gBAAI,OAAO,OAAOA,UAAS,CAAC,MAAM;AACjC;AAAA,UAEF,KAAK;AACJ,mBAAO,QAAQ,OAAO,oBAAoB,OAAO,SAAS,YAAiB,OAAO,OAAO,OAAOA,UAAS,CAAC,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,UAEzI,KAAK;AACJ,mBAAO,CAAC,QAAQ,OAAO,WAAW,CAAC,IAAI,OAAO,QAAQ,OAAO,WAAW,gBAAgB,GAAGA,SAAQ,QAAQ,IAAI,QAAQ;AAAA,QACzH;AACD;AAAA,IAED,KAAK;AAAA,IAAM,KAAK;AACf,aAAO,QAAQ,OAAO,6CAA6C,SAAUC,IAAGC,IAAGC,IAAGC,IAAG,GAAG,GAAGC,IAAG;AAAE,eAAQ,KAAKH,KAAI,MAAMC,KAAIE,MAAMD,KAAK,KAAKF,KAAI,YAAY,IAAI,IAAI,CAAC,IAAI,CAACC,MAAME,KAAI,MAAM;AAAA,MAAM,CAAC;AAAA,IAErM,KAAK;AAEJ,UAAI,OAAO,OAAOL,UAAS,CAAC,MAAM;AACjC,eAAO,QAAQ,OAAO,KAAK,MAAM,MAAM,IAAI;AAC5C;AAAA,IAED,KAAK;AACJ,cAAQ,OAAO,OAAO,OAAO,OAAO,EAAE,MAAM,KAAK,KAAK,EAAE,GAAG;AAAA,QAE1D,KAAK;AACJ,iBAAO,QAAQ,OAAO,iCAAiC,OAAO,UAAU,OAAO,OAAO,EAAE,MAAM,KAAK,YAAY,MAAM,YAAiB,SAAS,WAAgB,KAAK,SAAS,IAAI;AAAA,QAElL,KAAK;AACJ,iBAAO,QAAQ,OAAO,KAAK,MAAM,EAAE,IAAI;AAAA,MACzC;AACA;AAAA,IAED,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAAA,IAAM,KAAK;AAChD,aAAO,QAAQ,OAAO,WAAW,cAAc,IAAI;AAAA,EACrD;AAEA,SAAO;AACR;;;ACxIO,SAAS,UAAW,UAAU,UAAU;AAC9C,MAAI,SAAS;AAEb,WAASM,KAAI,GAAGA,KAAI,SAAS,QAAQA;AACpC,cAAU,SAAS,SAASA,EAAC,GAAGA,IAAG,UAAU,QAAQ,KAAK;AAE3D,SAAO;AACR;AASO,SAAS,UAAW,SAAS,OAAO,UAAU,UAAU;AAC9D,UAAQ,QAAQ,MAAM;AAAA,IACrB,KAAK;AAAO,UAAI,QAAQ,SAAS;AAAQ;AAAA,IACzC,KAAK;AAAA,IAAQ,KAAK;AAAa,aAAO,QAAQ,SAAS,QAAQ,UAAU,QAAQ;AAAA,IACjF,KAAK;AAAS,aAAO;AAAA,IACrB,KAAK;AAAW,aAAO,QAAQ,SAAS,QAAQ,QAAQ,MAAM,UAAU,QAAQ,UAAU,QAAQ,IAAI;AAAA,IACtG,KAAK;AAAS,UAAI,CAAC,OAAO,QAAQ,QAAQ,QAAQ,MAAM,KAAK,GAAG,CAAC;AAAG,eAAO;AAAA,EAC5E;AAEA,SAAO,OAAO,WAAW,UAAU,QAAQ,UAAU,QAAQ,CAAC,IAAI,QAAQ,SAAS,QAAQ,QAAQ,MAAM,WAAW,MAAM;AAC3H;;;ACxBO,SAAS,WAAY,YAAY;AACvC,MAAIC,UAAS,OAAO,UAAU;AAE9B,SAAO,SAAU,SAAS,OAAO,UAAU,UAAU;AACpD,QAAI,SAAS;AAEb,aAASC,KAAI,GAAGA,KAAID,SAAQC;AAC3B,gBAAU,WAAWA,EAAC,EAAE,SAAS,OAAO,UAAU,QAAQ,KAAK;AAEhE,WAAO;AAAA,EACR;AACD;AAMO,SAAS,UAAW,UAAU;AACpC,SAAO,SAAU,SAAS;AACzB,QAAI,CAAC,QAAQ;AACZ,UAAI,UAAU,QAAQ;AACrB,iBAAS,OAAO;AAAA;AAAA,EACnB;AACD;AAQO,SAAS,SAAU,SAAS,OAAO,UAAU,UAAU;AAC7D,MAAI,QAAQ,SAAS;AACpB,QAAI,CAAC,QAAQ;AACZ,cAAQ,QAAQ,MAAM;AAAA,QACrB,KAAK;AAAa,kBAAQ,SAAS,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAChF;AAAA,QACD,KAAK;AACJ,iBAAO,UAAU,CAAC,KAAK,SAAS,EAAC,OAAO,QAAQ,QAAQ,OAAO,KAAK,MAAM,MAAM,EAAC,CAAC,CAAC,GAAG,QAAQ;AAAA,QAC/F,KAAK;AACJ,cAAI,QAAQ;AACX,mBAAO,QAAQ,WAAW,QAAQ,OAAO,SAAU,OAAO;AACzD,sBAAQ,MAAM,OAAO,WAAW,uBAAuB,GAAG;AAAA,gBAEzD,KAAK;AAAA,gBAAc,KAAK;AACvB,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,QAAQ,OAAO,eAAe,MAAM,MAAM,IAAI,CAAC,EAAC,CAAC,CAAC;AAC9E,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC;AACpC,yBAAO,SAAS,EAAC,OAAO,OAAO,UAAU,QAAQ,EAAC,CAAC;AACnD;AAAA,gBAED,KAAK;AACJ,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,QAAQ,OAAO,cAAc,MAAM,SAAS,UAAU,CAAC,EAAC,CAAC,CAAC;AACtF,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,QAAQ,OAAO,cAAc,MAAM,MAAM,IAAI,CAAC,EAAC,CAAC,CAAC;AAC7E,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,QAAQ,OAAO,cAAc,KAAK,UAAU,CAAC,EAAC,CAAC,CAAC;AAC5E,uBAAK,KAAK,SAAS,EAAC,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC;AACpC,yBAAO,SAAS,EAAC,OAAO,OAAO,UAAU,QAAQ,EAAC,CAAC;AACnD;AAAA,cACF;AAEA,qBAAO;AAAA,YACR,CAAC;AAAA,MACJ;AAAA;AACH;;;ACxEA,IAAI,eAAe;AAAA,EACjB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,eAAe;AAAA,EACf,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,iBAAiB;AAAA;AAAA,EAEjB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;;;AC7CO,IAAMC,IACS,eAAA,OAAZC,WAAAA,WACCA,QAAQC,QACdD,QAAQC,IAAIC,qBAAqBF,QAAQC,IAAIF,YAChD;AAJK,IAMMI,IAAiB;AANvB,IAOMC,IAAkB;AAPxB,IAQMC,IAAa;AARnB,IASMC,IAAW;AATjB,IAWMC,IAA+B,eAAA,OAAXC,UAA8C,eAAA,OAAbC;AAX3D,IAaMC,IAAiBC,QACC,aAAA,OAAtBC,oBACHA,oBACmB,eAAA,OAAZZ,WAAAA,WACEA,QAAQC,OAAAA,WACRD,QAAQC,IAAIY,+BACyB,OAA5Cb,QAAQC,IAAIY,8BACgC,YAA5Cb,QAAQC,IAAIY,+BAEVb,QAAQC,IAAIY,8BACK,eAAA,OAAZb,WAAAA,WACEA,QAAQC,OAAAA,WACRD,QAAQC,IAAIW,qBACe,OAAlCZ,QAAQC,IAAIW,oBACsB,YAAlCZ,QAAQC,IAAIW,qBAEVZ,QAAQC,IAAIW,oBACW,IAAbE;AA9Bf,IAkCMC,IAA2B,CAAE;AAlCnC,ICDDC,IAAoB;ADCnB,ICADC,IAAO,oBAAIC;ADAV,ICEMC,IAAuB,SAACC,GAAqBC,GAAAA;AACxD,MAA6B,MAAc;AACzC,QAAMC,KAAiBD,IAAc,oBAAoBE,OAAAF,GAAc,GAAA,IAAG,IACpEG,KACJ,iBAAAD,OAAiBH,CAAAA,EAAWG,OAAGD,IAAgD,kCAAA,IAA/E,gTASIG,KAAuBC,QAAQC;AACrC,QAAA;AACE,UAAIC,KAAAA;AACJF,cAAQC,QAAQ,SAACE,IAAAA;AAAAA,iBAAwCC,KAAA,CAAA,GAAAC,KAAA,GAAnBA,KAAmBC,UAAAC,QAAnBF;AAAAD,UAAAA,GAAmBC,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAGnDf,UAAkBkB,KAAKL,EAAAA,KACzBD,KAAAA,OAEAX,EAAKkB,OAAOX,EAAAA,KAEZC,GAAqBW,MAAAA,QAAAC,cAAA,CAAAR,EAAAA,GAAwBC,IAAAA,KAAkB,CAAA;MAEnE,OAGAQ,aAAAA,QAAAA,GAEIV,MAAAA,CAA0BX,EAAKsB,IAAIf,EAAAA,MACrCE,QAAQc,KAAKhB,EAAAA,GACbP,EAAKwB,IAAIjB,EAAAA;IAEZ,SAAQG,GAAAA;AAGHX,QAAkBkB,KAAMP,EAAgBe,OAAAA,KAE1CzB,EAAKkB,OAAOX,EAAAA;IAEf,UAAS;AACRE,cAAQC,QAAQF;IACjB;EACF;AACH;ADhDO,IEDMkB,IAAcC,OAAOC,OAAO,CAAA,CAAA;AFClC,IEAMC,IAAeF,OAAOC,OAAO,CAAA,CAAA;ACAlB,SAAAE,EACtBC,GACAC,GACAC,GAAAA;AAEA,SAAA,WAFAA,MAAAA,IAAiEJ,IAEzDE,EAAMG,UAAUD,EAAaC,SAASH,EAAMG,SAAUF,KAAiBC,EAAaC;AAC9F;ACPA,IAwIAC,IAAe,oBAAIlC,IAxIF,CACf,KACA,QACA,WACA,QACA,WACA,SACA,SACA,KACA,QACA,OACA,OACA,OACA,cACA,QACA,MACA,UACA,UACA,WACA,QACA,QACA,OACA,YACA,QACA,YACA,MACA,OACA,WACA,OACA,UACA,OACA,MACA,MACA,MACA,SACA,YACA,cACA,UACA,UACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,UACA,UACA,MACA,QACA,KACA,UACA,OACA,SACA,OACA,OACA,UACA,SACA,UACA,MACA,QACA,QACA,OACA,QACA,QACA,YACA,QACA,SACA,OACA,YACA,UACA,MACA,YACA,UACA,UACA,KACA,SACA,WACA,OACA,YACA,KACA,MACA,MACA,QACA,KACA,QACA,UACA,WACA,UACA,SACA,UACA,QACA,UACA,SACA,OACA,WACA,OACA,SACA,SACA,MACA,YACA,SACA,MACA,SACA,QACA,MACA,SACA,KACA,MACA,OACA,OACA,SACA,OACA,UACA,YACA,QACA,WACA,iBACA,KACA,SACA,QACA,kBACA,UACA,QACA,QACA,WACA,WACA,YACA,kBACA,QACA,QACA,OACA,QACA,OAAA,CAAA;AArIF,ICAMmC,IAAc;ADApB,ICEMC,IAAe;AAMG,SAAAC,EAAOC,GAAAA;AAC7B,SAAOA,EACJC,QAAQJ,GAAa,GAAA,EACrBI,QAAQH,GAAc,EAAA;AAC3B;ACdA,IAAMI,IAAgB;AAAtB,IAIMC,IAAc;AAJpB,IAOMC,IAAoB,SAACC,GAAAA;AAAiB,SAAAC,OAAOC,aAAaF,KAAQA,IAAO,KAAK,KAAK,GAAA;AAA7C;AAGpB,SAAAG,EAAuBH,GAAAA;AAC7C,MACII,GADAC,IAAO;AAIX,OAAKD,IAAIE,KAAKC,IAAIP,CAAAA,GAAOI,IAAIN,GAAaM,IAAKA,IAAIN,IAAe;AAChEO,QAAON,EAAkBK,IAAIN,CAAAA,IAAeO;AAG9C,UAAQN,EAAkBK,IAAIN,CAAAA,IAAeO,GAAMT,QAAQC,GAAe,OAAA;AAC5E;ACpBO,IAAA;AAAA,IAAMW,IAAO;AAAb,IAKMC,IAAQ,SAACC,GAAWN,GAAAA;AAG/B,WAFIO,IAAIP,EAAEhC,QAEHuC;AACLD,QAAS,KAAJA,IAAUN,EAAEQ,WAAAA,EAAaD,CAAAA;AAGhC,SAAOD;AACT;AAbO,IAgBMG,IAAO,SAACT,GAAAA;AACnB,SAAOK,EAAMD,GAAMJ,CAAAA;AACrB;ACfwB,SAAAU,EAAoBnB,GAAAA;AAC1C,SAAOQ,EAAuBU,EAAKlB,CAAAA,MAAS,CAAA;AAC9C;ACHwB,SAAAoB,EAAiBC,GAAAA;AACvC,SAC6D,YAAA,OAAXA,KAAuBA,KACtEA,EAA8CzD,eAC9CyD,EAAoBX,QACrB;AAEJ;ACPwB,SAAAY,EAAMD,GAAAA;AAC5B,SACoB,YAAA,OAAXA,KAEHA,EAAOE,OAAO,CAAA,MAAOF,EAAOE,OAAO,CAAA,EAAGC,YAAAA;AAG9C;ACNA,IAAMC,IAA8B,cAAA,OAAXC,UAAyBA,OAAOC;AAAzD,IAGMC,IAAkBH,IAAYC,OAAOC,IAAI,YAAA,IAAgB;AAH/D,IAIME,IAAyBJ,IAAYC,OAAOC,IAAI,mBAAA,IAAuB;AAJ7E,IASMG,IAAgB,EACpBC,mBAAAA,MACAC,aAAAA,MACAC,cAAAA,MACAvC,cAAAA,MACA9B,aAAAA,MACAsE,iBAAAA,MACAC,0BAAAA,MACAC,0BAAAA,MACAC,QAAAA,MACAC,WAAAA,MACAC,MAAAA,KAAM;AApBR,IAuBMC,IAAgB,EACpB9B,MAAAA,MACAjC,QAAAA,MACAgE,WAAAA,MACAC,QAAAA,MACAC,QAAAA,MACAnE,WAAAA,MACAoE,OAAAA,KAAO;AA9BT,IAyCMC,IAAe,EACnBC,UAAAA,MACAC,SAAAA,MACArD,cAAAA,MACA9B,aAAAA,MACA0E,WAAAA,MACAC,MAAAA,KAAM;AA/CR,IAkDMS,MAAYC,IAAA,CAAA,GACfpB,CAAAA,IAlByB,EAC1BiB,UAAAA,MACAI,QAAAA,MACAxD,cAAAA,MACA9B,aAAAA,MACA0E,WAAAA,KAAW,GAcXW,EAACrB,CAAAA,IAAkBiB,GAAAA;AAcrB,SAASM,EAAWC,GAAAA;AAElB,UAPqB,WAFrBC,IASWD,MAP8BC,EAAOd,KAAKO,cAE7BlB,IAMfiB,IAIF,cAAcO,IACjBJ,EAAaI,EAAoB,QAAA,IACjCtB;AAjBN,MACEuB;AAiBF;AAEA,IAAMC,IAAiBlE,OAAOkE;AAA9B,IACMC,IAAsBnE,OAAOmE;AADnC,IAEMC,IAAwBpE,OAAOoE;AAFrC,IAGMC,KAA2BrE,OAAOqE;AAHxC,IAIMC,KAAiBtE,OAAOsE;AAJ9B,IAKMC,KAAkBvE,OAAOqD;AAiBP,SAAAmB,GAItBC,GAAoBC,GAAoBC,GAAAA;AACxC,MAA+B,YAAA,OAApBD,GAA8B;AAGvC,QAAIH,IAAiB;AACnB,UAAMK,KAAqBN,GAAeI,CAAAA;AACtCE,MAAAA,MAAsBA,OAAuBL,MAC/CC,GAAqBC,GAAiBG,IAAoBD,CAAAA;IAE7D;AAED,QAAIE,KAA4BV,EAAoBO,CAAAA;AAEhDN,UACFS,KAAOA,GAAKlG,OAAOyF,EAAsBM,CAAAA,CAAAA;AAM3C,aAHMI,KAAgBf,EAAWU,CAAAA,GAC3BM,KAAgBhB,EAAWW,CAAAA,GAExB9C,KAAI,GAAGA,KAAIiD,GAAKxF,QAAAA,EAAUuC,IAAG;AACpC,UAAMoD,KAAMH,GAAKjD,EAAAA;AACjB,UAAA,EACIoD,MAAO5B,KACPuB,KAAeA,EAAYK,EAAAA,KAC3BD,MAAiBC,MAAOD,MACxBD,MAAiBE,MAAOF,KAC1B;AACA,YAAMG,KAAaZ,GAAyBK,GAAiBM,EAAAA;AAE7D,YAAA;AAEEd,YAAeO,GAAiBO,IAAKC,EAAAA;QACtC,SAAQC,IAAAA;QAER;MACF;IACF;EACF;AAED,SAAOT;AACT;ACpJwB,SAAAU,GAAW7F,GAAAA;AACjC,SAAuB,cAAA,OAATA;AAChB;ACAwB,SAAA8F,GAAkBnD,GAAAA;AACxC,SAAyB,YAAA,OAAXA,KAAuB,uBAAuBA;AAC9D;ACDgB,SAAAoD,GAAYC,GAAwBC,GAAAA;AAClD,SAAOD,KAAKC,IAAI,GAAA,OAAGD,GAAC,GAAA,EAAA3G,OAAI4G,CAAAA,IAAMD,KAAKC,KAAK;AAC1C;AAEgB,SAAAC,GAAgBC,GAAeC,GAAAA;AAC7C,MAAmB,MAAfD,EAAIpG;AACN,WAAO;AAIT,WADIsG,IAASF,EAAI,CAAA,GACR7D,KAAI,GAAGA,KAAI6D,EAAIpG,QAAQuC;AAC9B+D,SAAUD,IAAMA,IAAMD,EAAI7D,EAAAA,IAAK6D,EAAI7D,EAAAA;AAErC,SAAO+D;AACT;ACjBwB,SAAAC,GAAcvE,GAAAA;AACpC,SACQ,SAANA,KACa,YAAA,OAANA,KACPA,EAAEwE,YAAYvE,SAAStB,OAAOsB,QAAAA,EAE5B,WAAWD,KAAKA,EAAEqC;AAExB;ACNA,SAASoC,GAAiB7D,GAAa8D,GAAaC,GAAAA;AAGlD,MAAA,WAHkDA,MAAAA,IAAAA,QAAkB,CAG/DA,KAAAA,CAAeJ,GAAc3D,CAAAA,KAAAA,CAAYgE,MAAMC,QAAQjE,CAAAA;AAC1D,WAAO8D;AAGT,MAAIE,MAAMC,QAAQH,CAAAA;AAChB,aAASf,KAAM,GAAGA,KAAMe,EAAO1G,QAAQ2F;AACrC/C,QAAO+C,EAAAA,IAAOc,GAAiB7D,EAAO+C,EAAAA,GAAMe,EAAOf,EAAAA,CAAAA;WAE5CY,GAAcG,CAAAA;AACvB,aAAWf,MAAOe;AAChB9D,QAAO+C,EAAAA,IAAOc,GAAiB7D,EAAO+C,EAAAA,GAAMe,EAAOf,EAAAA,CAAAA;AAIvD,SAAO/C;AACT;ACJgB,SAAAkE,GAAYlC,GAAgBmC,GAAAA;AAC1CpG,SAAOkE,eAAeD,GAAQ,YAAY,EAAEoC,OAAOD,EAAAA,CAAAA;AACrD;AClBA,ICGME,KAA6C,ODHpC,EACb,GAAK,yDACL,GAAK,iQACL,GAAK,uHACL,GAAK,uMACL,GAAK,mKACL,GAAK,6OACL,GAAK,sHACL,GAAK,+DACL,GAAK,iCACL,IAAM,kUACN,IAAM,yNACN,IAAM,sWACN,IAAM,0LACN,IAAM,gDACN,IAAM,4ZACN,IAAM,wQACN,IAAM,0IACN,IAAM,mFAAA,ICfqE,CAAA;AAK7E,SAASC,KAAAA;AAAAA,WAAgCC,IAAA,CAAA,GAAArH,IAAA,GAAzBA,IAAyBC,UAAAC,QAAzBF;AAAAqH,MAAyBrH,CAAAA,IAAAC,UAAAD,CAAAA;AAIvC,WAHImG,IAAIkB,EAAK,CAAA,GACPjB,KAAI,CAAA,GAEDkB,KAAI,GAAGC,KAAMF,EAAKnH,QAAQoH,KAAIC,IAAKD,MAAK;AAC/ClB,IAAAA,GAAEoB,KAAKH,EAAKC,EAAAA,CAAAA;AAOd,SAJAlB,GAAEqB,QAAQ,SAAAC,IAAAA;AACRvB,QAAIA,EAAEzE,QAAQ,UAAUgG,EAAAA;EAC1B,CAAA,GAEOvB;AACT;AAMwB,SAAAwB,GACtB7F,GAAAA;AAAAA,WACwB8F,IAAA,CAAA,GAAA5H,KAAA,GAAxBA,KAAwBC,UAAAC,QAAxBF;AAAA4H,MAAwB5H,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAExB,SAA6B,QACpB,IAAI6H,MACT,0IAAArI,OAA0IsC,GAAI,wBAAA,EAAAtC,OAC5IoI,EAAe1H,SAAS,IAAI,UAAUV,OAAAoI,EAAeE,KAAK,IAAA,CAAA,IAAU,EAAA,CAAA,IAIjE,IAAID,MAAMT,GAAAA,MAAAA,QAAAA,cAAAA,CAAOD,GAAOrF,CAAAA,CAAAA,GAAU8F,GAAAA,KAAc,CAAA,EAAEG,KAAAA,CAAAA;AAE7D;ACnCO,IAMDC,KAAiB,WAAA;AAKrB,WAAAA,EAAYC,IAAAA;AACVC,SAAKC,aAAa,IAAIC,YARR,GAAA,GASdF,KAAKhI,SATS,KAUdgI,KAAKD,MAAMA;EACZ;AAyEH,SAvEED,EAAY9D,UAAAmE,eAAZ,SAAaC,IAAAA;AAEX,aADIC,IAAQ,GACH9F,IAAI,GAAGA,IAAI6F,IAAO7F;AACzB8F,WAASL,KAAKC,WAAW1F,CAAAA;AAG3B,WAAO8F;EAAAA,GAGTP,EAAA9D,UAAAsE,cAAA,SAAYF,IAAeG,GAAAA;AACzB,QAAIH,MAASJ,KAAKC,WAAWjI,QAAQ;AAKnC,eAJMwI,IAAYR,KAAKC,YACjBQ,KAAUD,EAAUxI,QAEtB0I,KAAUD,IACPL,MAASM;AAEd,aADAA,OAAY,KACE;AACZ,gBAAMC,GAAY,IAAI,GAAA,OAAGP,EAAAA,CAAAA;AAI7BJ,WAAKC,aAAa,IAAIC,YAAYQ,EAAAA,GAClCV,KAAKC,WAAWW,IAAIJ,CAAAA,GACpBR,KAAKhI,SAAS0I;AAEd,eAASnG,KAAIkG,IAASlG,KAAImG,IAASnG;AACjCyF,aAAKC,WAAW1F,EAAAA,IAAK;IAExB;AAID,aAFIsG,KAAYb,KAAKG,aAAaC,KAAQ,CAAA,GAE1BU,MAAPvG,KAAI,GAAOgG,EAAMvI,SAAQuC,KAAIuG,IAAGvG;AACnCyF,WAAKD,IAAIgB,WAAWF,IAAWN,EAAMhG,EAAAA,CAAAA,MACvCyF,KAAKC,WAAWG,EAAAA,KAChBS;EAAAA,GAKNf,EAAU9D,UAAAgF,aAAV,SAAWZ,IAAAA;AACT,QAAIA,KAAQJ,KAAKhI,QAAQ;AACvB,UAAMiJ,IAASjB,KAAKC,WAAWG,EAAAA,GACzBc,IAAalB,KAAKG,aAAaC,EAAAA,GAC/Be,KAAWD,IAAaD;AAE9BjB,WAAKC,WAAWG,EAAAA,IAAS;AAEzB,eAAS7F,KAAI2G,GAAY3G,KAAI4G,IAAU5G;AACrCyF,aAAKD,IAAIqB,WAAWF,CAAAA;IAEvB;EAAA,GAGHpB,EAAQ9D,UAAAqF,WAAR,SAASjB,IAAAA;AACP,QAAIkB,IAAM;AACV,QAAIlB,MAASJ,KAAKhI,UAAqC,MAA3BgI,KAAKC,WAAWG,EAAAA;AAC1C,aAAOkB;AAOT,aAJMtJ,IAASgI,KAAKC,WAAWG,EAAAA,GACzBc,KAAalB,KAAKG,aAAaC,EAAAA,GAC/Be,KAAWD,KAAalJ,GAErBuC,KAAI2G,IAAY3G,KAAI4G,IAAU5G;AACrC+G,WAAO,GAAAhK,OAAG0I,KAAKD,IAAIwB,QAAQhH,EAAAA,CAAAA,EAAKjD,OAAAjB,CAAAA;AAGlC,WAAOiL;EAAAA,GAEVxB;AAAD,EAAA;AAxFO,ICHD0B,KAAU,KAAC;ADGV,ICDHC,KAAuC,oBAAIC;ADCxC,ICAHC,KAAuC,oBAAID;ADAxC,ICCHE,KAAgB;ADDb,ICSMC,KAAgB,SAACC,GAAAA;AAC5B,MAAIL,GAAgBnJ,IAAIwJ,CAAAA;AACtB,WAAOL,GAAgBM,IAAID,CAAAA;AAG7B,SAAOH,GAAgBrJ,IAAIsJ,EAAAA;AACzBA;AAGF,MAAMxB,IAAQwB;AAEd,OAAuD,IAARxB,KAAa,KAAKA,IAAQoB;AACvE,UAAMb,GAAY,IAAI,GAAA,OAAGP,CAAAA,CAAAA;AAK3B,SAFAqB,GAAgBb,IAAIkB,GAAI1B,CAAAA,GACxBuB,GAAgBf,IAAIR,GAAO0B,CAAAA,GACpB1B;AACT;AD3BO,ICiCM4B,KAAgB,SAACF,GAAY1B,GAAAA;AAExCwB,OAAgBxB,IAAQ,GAExBqB,GAAgBb,IAAIkB,GAAI1B,CAAAA,GACxBuB,GAAgBf,IAAIR,GAAO0B,CAAAA;AAC7B;ADvCO,IEDDG,KAAW,SAAS3K,OAAAxB,GAAAA,IAAAA,EAAAA,OAAYK,GAAe,IAAA,EAAAmB,OAAKlB,GAAU,IAAA;AFC7D,IEAD8L,KAAY,IAAIC,OAAO,IAAI7K,OAAAxB,GAAqD,8CAAA,CAAA;AFA/E,IEkCDsM,KAA4B,SAACC,GAAcP,GAAYQ,GAAAA;AAI3D,WAFIrI,IADEsI,KAAQD,EAAQE,MAAM,GAAA,GAGnBjI,KAAI,GAAGuG,KAAIyB,GAAMvK,QAAQuC,KAAIuG,IAAGvG;AAAAA,KAClCN,KAAOsI,GAAMhI,EAAAA,MAChB8H,EAAMI,aAAaX,GAAI7H,EAAAA;AAG7B;AF3CO,IE6CDyI,KAAwB,SAACL,GAAcM,GAAAA;AAI3C,WAAA,GAHMC,MAA8B,UAArBpG,IAAAmG,EAAME,gBAAAA,WAAerG,IAAAA,IAAA,IAAIgG,MAAMnM,CAAAA,GACxCkK,KAAkB,CAAA,GAEfhG,KAAI,GAAGuG,KAAI8B,GAAM5K,QAAQuC,KAAIuG,IAAGvG,MAAK;AAC5C,QAAMuI,KAAOF,GAAMrI,EAAAA,EAAGsF,KAAAA;AACtB,QAAKiD,IAAL;AAEA,UAAMC,KAASD,GAAKE,MAAMd,EAAAA;AAE1B,UAAIa,IAAQ;AACV,YAAM3C,KAAkC,IAA1B6C,SAASF,GAAO,CAAA,GAAI,EAAA,GAC5BjB,KAAKiB,GAAO,CAAA;AAEJ,cAAV3C,OAEF4B,GAAcF,IAAI1B,EAAAA,GAGlBgC,GAA0BC,GAAOP,IAAIiB,GAAO,CAAA,CAAA,GAC5CV,EAAMa,OAAAA,EAAS5C,YAAYF,IAAOG,EAAAA,IAGpCA,GAAMvI,SAAS;MAChB;AACCuI,QAAAA,GAAMjB,KAAKwD,EAAAA;IAnBO;EAqBrB;AACH;AFzEO,IE2EMK,KAAiB,SAACd,GAAAA;AAG7B,WAFMe,IAAQ5M,SAAS6M,iBAAiBpB,EAAAA,GAE/B1H,IAAI,GAAGuG,KAAIsC,EAAMpL,QAAQuC,IAAIuG,IAAGvG,KAAK;AAC5C,QAAM+I,KAAOF,EAAM7I,CAAAA;AACf+I,IAAAA,MAAQA,GAAKC,aAAazN,CAAAA,MAAaI,MACzCwM,GAAsBL,GAAOiB,EAAAA,GAEzBA,GAAKE,cACPF,GAAKE,WAAWC,YAAYH,EAAAA;EAGjC;AACH;AC3Fc,SAAUI,KAAAA;AACtB,SAAoC,eAAA,OAAtBC,oBAAoCA,oBAAoB;AACxE;ACEA,IAOaC,KAAe,SAAChJ,GAAAA;AAC3B,MAAMiJ,IAAOrN,SAASqN,MAChBC,IAASlJ,KAAUiJ,GACnBlB,KAAQnM,SAASuN,cAAc,OAAA,GAC/BC,KAXiB,SAACpJ,IAAAA;AACxB,QAAMwD,KAAMQ,MAAMqF,KAAKrJ,GAAOyI,iBAAmC,SAAS/L,OAAAxB,GAAU,GAAA,CAAA,CAAA;AAEpF,WAAOsI,GAAIA,GAAIpG,SAAS,CAAA;EAC1B,EAOqC8L,CAAAA,GAC7BI,KAAAA,WAAcF,KAA0BA,GAAUE,cAAc;AAEtEvB,EAAAA,GAAMwB,aAAarO,GAASI,CAAAA,GAC5ByM,GAAMwB,aAAahO,GAAiBC,CAAAA;AAEpC,MAAMgO,KAAQV,GAAAA;AAMd,SAJIU,MAAOzB,GAAMwB,aAAa,SAASC,EAAAA,GAEvCN,EAAOO,aAAa1B,IAAOuB,EAAAA,GAEpBvB;AACT;AAxBA,ICSa2B,KAAQ,WAAA;AAOnB,WAAAA,EAAY1J,IAAAA;AACVoF,SAAKuE,UAAUX,GAAahJ,EAAAA,GAG5BoF,KAAKuE,QAAQC,YAAYhO,SAASiO,eAAe,EAAA,CAAA,GAEjDzE,KAAKqC,QDKe,SAACtC,IAAAA;AACvB,UAAIA,GAAIsC;AACN,eAAOtC,GAAIsC;AAKb,eADQqC,IAAgBlO,SAAQkO,aACvBnK,IAAI,GAAGuG,KAAI4D,EAAY1M,QAAQuC,IAAIuG,IAAGvG,KAAK;AAClD,YAAM8H,KAAQqC,EAAYnK,CAAAA;AAC1B,YAAI8H,GAAMsC,cAAc5E;AACtB,iBAAOsC;MAEV;AAED,YAAM1B,GAAY,EAAA;IACpB,ECpB0BX,KAAKuE,OAAAA,GAC3BvE,KAAKhI,SAAS;EACf;AA2BH,SAzBEsM,EAAAtI,UAAA+E,aAAA,SAAWV,IAAeuE,GAAAA;AACxB,QAAA;AAGE,aAFA5E,KAAKqC,MAAMtB,WAAW6D,GAAMvE,EAAAA,GAC5BL,KAAKhI,UAAAA;IAEN,SAAQ6M,IAAAA;AACP,aAAA;IACD;EAAA,GAGHP,EAAUtI,UAAAoF,aAAV,SAAWf,IAAAA;AACTL,SAAKqC,MAAMjB,WAAWf,EAAAA,GACtBL,KAAKhI;EAAAA,GAGPsM,EAAOtI,UAAAuF,UAAP,SAAQlB,IAAAA;AACN,QAAMuE,IAAO5E,KAAKqC,MAAMyC,SAASzE,EAAAA;AAGjC,WAAIuE,KAAQA,EAAKG,UACRH,EAAKG,UAEL;EAAA,GAGZT;AAAD,EAAA;ADnDA,ICsDaU,KAAO,WAAA;AAKlB,WAAAA,EAAYpK,IAAAA;AACVoF,SAAKuE,UAAUX,GAAahJ,EAAAA,GAC5BoF,KAAKoD,QAAQpD,KAAKuE,QAAQU,YAC1BjF,KAAKhI,SAAS;EACf;AA0BH,SAxBEgN,EAAAhJ,UAAA+E,aAAA,SAAWV,IAAeuE,GAAAA;AACxB,QAAIvE,MAASL,KAAKhI,UAAUqI,MAAS,GAAG;AACtC,UAAMiD,IAAO9M,SAASiO,eAAeG,CAAAA;AAIrC,aAFA5E,KAAKuE,QAAQF,aAAaf,GADVtD,KAAKoD,MAAM/C,EAAAA,KACgB,IAAA,GAC3CL,KAAKhI,UAAAA;IAEN;AACC,WAAA;EAAO,GAIXgN,EAAUhJ,UAAAoF,aAAV,SAAWf,IAAAA;AACTL,SAAKuE,QAAQd,YAAYzD,KAAKoD,MAAM/C,EAAAA,CAAAA,GACpCL,KAAKhI;EAAAA,GAGPgN,EAAOhJ,UAAAuF,UAAP,SAAQlB,IAAAA;AACN,WAAIA,KAAQL,KAAKhI,SACRgI,KAAKoD,MAAM/C,EAAAA,EAAOwC,cAElB;EAAA,GAGZmC;AAAD,EAAA;ADzFA,IC4FaE,KAAU,WAAA;AAKrB,WAAAA,EAAYC,IAAAA;AACVnF,SAAKO,QAAQ,CAAA,GACbP,KAAKhI,SAAS;EACf;AAwBH,SAtBEkN,EAAAlJ,UAAA+E,aAAA,SAAWV,IAAeuE,GAAAA;AACxB,WAAIvE,MAASL,KAAKhI,WAChBgI,KAAKO,MAAM6E,OAAO/E,IAAO,GAAGuE,CAAAA,GAC5B5E,KAAKhI,UAAAA;EACE,GAMXkN,EAAUlJ,UAAAoF,aAAV,SAAWf,IAAAA;AACTL,SAAKO,MAAM6E,OAAO/E,IAAO,CAAA,GACzBL,KAAKhI;EAAAA,GAGPkN,EAAOlJ,UAAAuF,UAAP,SAAQlB,IAAAA;AACN,WAAIA,KAAQL,KAAKhI,SACRgI,KAAKO,MAAMF,EAAAA,IAEX;EAAA,GAGZ6E;AAAD,EAAA;AD5HA,IEIIG,KAAmB/O;AFJvB,IEiBMgP,KAA+B,EACnCC,UAAAA,CAAWjP,GACXkP,mBAAAA,CAAoB/O,EAAAA;AFnBtB,IEuBAgP,KAAA,WAAA;AAYE,WAAAA,EACEC,IACAC,GACApD,IAAAA;AAAAA,eAFAmD,OAAAA,KAAgC7M,IAAAA,WAChC8M,MAAAA,IAA4C,CAAA;AAF9C,QAqBCC,KAAA5F;AAhBCA,SAAK0F,UAAOG,SAAAA,SAAA,CAAA,GACPP,EAAAA,GACAI,EAAAA,GAGL1F,KAAK8F,KAAKH,GACV3F,KAAKuC,QAAQ,IAAIb,IAAIa,EAAAA,GACrBvC,KAAK+F,SAAAA,CAAAA,CAAWL,GAAQH,UAAAA,CAGnBvF,KAAK+F,UAAUzP,KAAc+O,OAChCA,KAAAA,OACAlC,GAAenD,IAAAA,IAGjBlB,GAAYkB,MAAM,WAAA;AAAM,aJtDD,SAACqC,IAAAA;AAK1B,iBAJMtC,IAAMsC,GAAMa,OAAAA,GACVlL,KAAW+H,EAAG/H,QAElBsJ,KAAM,IAAA0E,KAAA,SACD5F,IAAAA;AACP,cAAM0B,KDqBmB,SAAC1B,IAAAA;AAC5B,mBAAOuB,GAAgBI,IAAI3B,EAAAA;UAC7B,ECvB6BA,EAAAA;AACzB,cAAA,WAAI0B;AAA2B,mBAAA;AAE/B,cAAMS,KAAQF,GAAME,MAAMR,IAAID,EAAAA,GACxBvB,KAAQR,EAAIsB,SAASjB,EAAAA;AAC3B,cAAA,WAAImC,MAAAA,CAAwBA,GAAM0D,QAAyB,MAAjB1F,GAAMvI;AAAuB,mBAAA;AAEvE,cAAMkO,KAAW,GAAG5O,OAAAxB,GAAAA,IAAAA,EAAAA,OAAYsK,IAAK,OAAA,EAAA9I,OAAQwK,IAAE,IAAA,GAE3CQ,KAAU;AAAA,qBACVC,MACFA,GAAMhD,QAAQ,SAAAtF,IAAAA;AACRA,YAAAA,GAAKjC,SAAS,MAChBsK,MAAW,GAAAhL,OAAG2C,IAAI,GAAA;UAEtB,CAAA,GAKFqH,MAAO,GAAGhK,OAAAiJ,EAAAA,EAAQjJ,OAAA4O,IAAAA,YAAAA,EAAAA,OAAqB5D,IAAO,IAAA,EAAAhL,OAAKjB,CAAAA;QAAAA,GArB5C+J,KAAQ,GAAGA,KAAQpI,IAAQoI;AAAAA,UAAAA,GAA3BA,EAAAA;AAwBT,eAAOkB;MACT,EIwBwCsE,EAAAA;IAAK,CAAA;EAC1C;AAoEH,SA7FSH,EAAUU,aAAjB,SAAkBrE,IAAAA;AAChB,WAAOD,GAAcC,EAAAA;EAAAA,GA0BvB2D,EAAAzJ,UAAAoK,YAAA,WAAA;AAAA,KACOpG,KAAK+F,UAAUzP,KAClB6M,GAAenD,IAAAA;EAAAA,GAInByF,EAAAzJ,UAAAqK,yBAAA,SAAuBX,GAA+BY,IAAAA;AACpD,WAAA,WADoDA,OAAAA,KAAAA,OAC7C,IAAIb,EACJI,SAAAA,SAAA,CAAA,GAAA7F,KAAK0F,OAAAA,GAAYA,CAAAA,GACtB1F,KAAK8F,IACJQ,MAAatG,KAAKuC,SAAAA,MAAUgE;EAAAA,GAIjCd,EAAkBzJ,UAAAwK,qBAAlB,SAAmB1E,IAAAA;AACjB,WAAQ9B,KAAK8F,GAAGhE,EAAAA,KAAO9B,KAAK8F,GAAGhE,EAAAA,KAAO,KAAK;EAAA,GAI7C2D,EAAAzJ,UAAAkH,SAAA,WAAA;AACE,WAAOlD,KAAKD,QAAQC,KAAKD,ON/EEA,KKAR,SAACvD,IAAAA;AAAE,UAAUgJ,IAAiBhJ,GAAAgJ,mBAAE5K,IAAM4B,GAAA5B;AAC3D,aAAAiD,GAAA,WACS,IAAIqH,GAAWtK,CAAAA,IACb4K,IACF,IAAIlB,GAAS1J,CAAAA,IAEb,IAAIoK,GAAQpK,CAAAA;IAEvB,ECuE0DoF,KAAK0F,OAAAA,GN9EtD,IAAI5F,GAAkBC,EAAAA;AADD,QAACA;EAAAA,GMmF7B0F,EAAAzJ,UAAAyK,eAAA,SAAa3E,IAAY7H,GAAAA;AACvB,WAAO+F,KAAKuC,MAAMjK,IAAIwJ,EAAAA,KAAQ9B,KAAKuC,MAAMR,IAAID,EAAAA,EAAYxJ,IAAI2B,CAAAA;EAAAA,GAI/DwL,EAAAzJ,UAAAyG,eAAA,SAAaX,IAAY7H,GAAAA;AAGvB,QAFA4H,GAAcC,EAAAA,GAET9B,KAAKuC,MAAMjK,IAAIwJ,EAAAA;AAKjB9B,WAAKuC,MAAMR,IAAID,EAAAA,EAAYtJ,IAAIyB,CAAAA;SALT;AACvB,UAAMyM,IAAa,oBAAIzP;AACvByP,QAAWlO,IAAIyB,CAAAA,GACf+F,KAAKuC,MAAM3B,IAAIkB,IAAI4E,CAAAA;IACpB;EAAA,GAMHjB,EAAAzJ,UAAAsE,cAAA,SAAYwB,IAAY7H,GAAcsG,GAAAA;AACpCP,SAAKyC,aAAaX,IAAI7H,CAAAA,GACtB+F,KAAKkD,OAAAA,EAAS5C,YAAYuB,GAAcC,EAAAA,GAAKvB,CAAAA;EAAAA,GAI/CkF,EAAUzJ,UAAA2K,aAAV,SAAW7E,IAAAA;AACL9B,SAAKuC,MAAMjK,IAAIwJ,EAAAA,KAChB9B,KAAKuC,MAAMR,IAAID,EAAAA,EAAY8E,MAAAA;EAAAA,GAKhCnB,EAAUzJ,UAAA6K,aAAV,SAAW/E,IAAAA;AACT9B,SAAKkD,OAAAA,EAASlC,WAAWa,GAAcC,EAAAA,CAAAA,GACvC9B,KAAK2G,WAAW7E,EAAAA;EAAAA,GAIlB2D,EAAAzJ,UAAA8K,WAAA,WAAA;AAGE9G,SAAKD,MAAAA;EAAMwG,GAEdd;AAAD,EAAA;AF5HA,IGAMsB,KAAY;AHAlB,IGCMC,KAAgB;AAWtB,SAASC,GAAuBC,GAA4BC,GAAAA;AAC1D,SAAOD,EAASE,IAAI,SAAAxC,IAAAA;AAclB,WAbkB,WAAdA,GAAK9I,SAEP8I,GAAK5F,QAAQ,GAAG1H,OAAA6P,GAAAA,GAAAA,EAAAA,OAAavC,GAAK5F,KAAAA,GAElC4F,GAAK5F,QAAQ4F,GAAK5F,MAAMqI,WAAW,KAAK,IAAA/P,OAAI6P,GAAS,GAAA,CAAA,GACrDvC,GAAK7L,QAAS6L,GAAK7L,MAAmBqO,IAAI,SAAAE,IAAAA;AACxC,aAAO,GAAGhQ,OAAA6P,GAAa,GAAA,EAAA7P,OAAAgQ,EAAAA;IACzB,CAAA,IAGE1I,MAAMC,QAAQ+F,GAAK2C,QAAAA,KAA2B,iBAAd3C,GAAK9I,SACvC8I,GAAK2C,WAAWN,GAAuBrC,GAAK2C,UAAUJ,CAAAA,IAEjDvC;EACT,CAAA;AACF;AAEwB,SAAA4C,GACtBhL,GAAAA;AAAA,MAKIiL,GACAC,GACAC,IAPJC,KAAAA,WAAApL,IAG2B3D,IAAsB2D,GAF/CqL,KAAAD,GAAAlC,SAAAA,KAAAA,WAAOmC,KAAGhP,IAAsBgP,IAChCC,KAAuDF,GAAAG,SAAvDA,KAAAA,WAAOD,KAAGpP,IAA6CoP,IAOnDE,KAAwB,SAAChF,IAAeiF,IAAgBC,IAAAA;AAC5D,WAKEA,GAAOC,WAAWT,CAAAA,KAClBQ,GAAOE,SAASV,CAAAA,KAChBQ,GAAOb,WAAWK,GAAW,EAAA,EAAI1P,SAAS,IAEnC,IAAAV,OAAImQ,CAAAA,IAGNzE;EACT,GAuBMqF,KAAcN,GAAQO,MAAAA;AAE5BD,EAAAA,GAAY/I,KAX8C,SAAAiF,IAAAA;AACpDA,IAAAA,GAAQzI,SAAgByM,WAAWhE,GAAQvF,MAAMwJ,SAAS,GAAA,MAC3DjE,GAAQxL,MAAmB,CAAA,IAAKwL,GAAQxL,MAAM,CAAA,EAE5CS,QAAQuN,IAAWW,CAAAA,EACnBlO,QAAQmO,IAAiBK,EAAAA;EAEhC,CAAA,GASItC,GAAQ+C,UACVJ,GAAY/I,KAAYoJ,QAAAA,GAG1BL,GAAY/I,KAAYqJ,SAAAA;AAExB,MAAMC,KAA8B,SAClCtH,IACA4E,IAIAuC,IACArR,IAAAA;AAAAA,eALA8O,OAAAA,KAAa,KAAA,WAIbuC,OAAAA,KAAW,KAAA,WACXrR,OAAAA,KAAiB,MAKjBqQ,IAAerQ,IACfsQ,IAAYxB,IACZyB,KAAkB,IAAIxF,OAAO,KAAA7K,OAAKoQ,GAAc,KAAA,GAAE,GAAA;AAElD,QAAMmB,KAAUvH,GAAI9H,QAAQwN,IAAe,EAAA,GACvCE,KAAkB4B,QACpBL,MAAUvC,KAAW,GAAA,OAAGuC,IAAM,GAAA,EAAAnR,OAAI4O,IAAQ,KAAA,EAAA5O,OAAMuR,IAAO,IAAA,IAAOA,EAAAA;AAG5DnD,IAAAA,GAAQyB,cACVD,KAAWD,GAAuBC,IAAUxB,GAAQyB,SAAAA;AAGtD,QAAM4B,KAAkB,CAAA;AAOxB,WALOC,UACL9B,IACO+B,WAAWZ,GAAY/Q,OAAc4R,UAAU,SAAAlK,IAAAA;AAAS,aAAA+J,GAAMzJ,KAAKN,EAAAA;IAAM,CAAA,CAAA,CAAA,CAAA,GAG3E+J;EACT;AAcA,SAZAH,GAAenO,OAAOsN,GAAQ/P,SAC1B+P,GACGoB,OAAO,SAACC,IAAKC,IAAAA;AAKZ,WAJKA,GAAOpP,QACVqP,GAAiB,EAAA,GAGZjP,EAAM+O,IAAKC,GAAOpP,IAAAA;EAC1B,GAAEG,CAAAA,EACFmP,SAAAA,IACH,IAEGX;AACT;AC1IO,IAAMY,KAAwB,IAAI/D;AAAlC,IACMgE,KAA0BjC,GAAAA;AADhC,IASMkC,KAAoBC,aAAAA,QAAMC,cAAkC,EACvEC,mBAAAA,QACAC,YAAYN,IACZO,QAAQN,GAAAA,CAAAA;AAZH,IAeMO,KAAqBN,GAAkBO;AAf7C,IAkBMC,KAAgBP,aAAAA,QAAMC,cAAAA,MAA8BrD;AAAAA,SAGjD4D,KAAAA;AACd,aAAOC,aAAAA,YAAWV,EAAAA;AACpB;AAkDM,SAAUW,GAAkBtR,GAAAA;AAC1B,MAAAyD,QAAwB8N,aAAAA,UAASvR,EAAMwR,aAAAA,GAAtCxC,IAAOvL,EAAA,CAAA,GAAEgO,KAAAA,EAAAA,CAAAA,GACRV,KAAeK,GAAAA,EAAAA,YAEjBM,SAAqBC,aAAAA,SAAQ,WAAA;AACjC,QAAIrI,KAAQyH;AAYZ,WAVI/Q,EAAMsJ,QACRA,KAAQtJ,EAAMsJ,QACLtJ,EAAM6B,WACfyH,KAAQA,GAAMgE,uBAAuB,EAAEzL,QAAQ7B,EAAM6B,OAAAA,GAAAA,KAAU,IAG7D7B,EAAM4R,0BACRtI,KAAQA,GAAMgE,uBAAuB,EAAEb,mBAAAA,MAAmB,CAAA,IAGrDnD;EACT,GAAG,CAACtJ,EAAM4R,uBAAuB5R,EAAMsJ,OAAOtJ,EAAM6B,QAAQkP,EAAAA,CAAAA,GAEtDC,SAASW,aAAAA,SACb,WAAA;AACE,WAAAlD,GAAqB,EACnB9B,SAAS,EAAEyB,WAAWpO,EAAMoO,WAAWsB,QAAQ1P,EAAM6R,qBAAAA,GACrD7C,SAAOA,EAAAA,CAAAA;EAFT,GAIF,CAAChP,EAAM6R,sBAAsB7R,EAAMoO,WAAWY,CAAAA,CAAAA;AAGhD8C,mBAAAA,WAAU,WAAA;AACHC,4BAAAA,SAAa/C,GAAShP,EAAMwR,aAAAA,KAAgBC,GAAWzR,EAAMwR,aAAAA;EACpE,GAAG,CAACxR,EAAMwR,aAAAA,CAAAA;AAEV,MAAMQ,QAAyBL,aAAAA,SAC7B,WAAA;AAAM,WAAC,EACLb,mBAAmB9Q,EAAM8Q,mBACzBC,YAAYW,IACZV,QAAMA,GAAAA;EAHF,GAKN,CAAChR,EAAM8Q,mBAAmBY,IAAoBV,EAAAA,CAAAA;AAGhD,SACEJ,aAAAA,QAAAA,cAACD,GAAkBsB,UAAS,EAAAhM,OAAO+L,EAAAA,GACjCpB,aAAAA,QAAA5F,cAACmG,GAAcc,UAAQ,EAAChM,OAAO+K,GAAAA,GAAShR,EAAMwO,QAAAA,CAAAA;AAGpD;ACzHA,IAAA0D,KAAA,WAAA;AAKE,WAAYA,EAAAhR,IAAcsG,GAAAA;AAA1B,QAQCqF,IAAA5F;AAEDA,SAAAkL,SAAS,SAACpB,IAAwBqB,IAAAA;AAAAA,iBAAAA,OAAAA,KAAwC1B;AACxE,UAAM2B,KAAexF,EAAK3L,OAAOkR,GAAe1Q;AAE3CqP,MAAAA,GAAWrD,aAAab,EAAK9D,IAAIsJ,EAAAA,KACpCtB,GAAWxJ,YACTsF,EAAK9D,IACLsJ,IACAD,GAAevF,EAAKrF,OAAO6K,IAAc,YAAA,CAAA;IAG/C,GAnBEpL,KAAK/F,OAAOA,IACZ+F,KAAK8B,KAAK,gBAAgBxK,OAAA2C,EAAAA,GAC1B+F,KAAKO,QAAQA,GAEbzB,GAAYkB,MAAM,WAAA;AAChB,YAAMW,GAAY,IAAI9G,OAAO+L,EAAK3L,IAAAA,CAAAA;IACpC,CAAA;EACD;AAiBH,SAHEgR,EAAOjP,UAAAqP,UAAP,SAAQF,IAAAA;AACN,WAAA,WADMA,OAAAA,KAAwC1B,KACvCzJ,KAAK/F,OAAOkR,GAAe1Q;EAAAA,GAErCwQ;AAAD,EAAA;AA9BA,ICNMK,KAAU,SAAClM,GAAAA;AAAc,SAAAA,KAAK,OAAOA,KAAK;AAAA;AAexB,SAAAmM,GAAmBrD,GAAAA;AAGzC,WAFIsD,IAAS,IAEJjR,IAAI,GAAGA,IAAI2N,EAAOlQ,QAAQuC,KAAK;AACtC,QAAM6E,KAAI8I,EAAO3N,CAAAA;AAEjB,QAAU,MAANA,KAAiB,QAAN6E,MAA2B,QAAd8I,EAAO,CAAA;AACjC,aAAOA;AAGLoD,OAAQlM,EAAAA,IACVoM,KAAU,MAAMpM,GAAErE,YAAAA,IAElByQ,KAAUpM;EAEb;AAED,SAAOoM,EAAOrD,WAAW,KAAA,IAAS,MAAMqD,IAASA;AACnD;ACTA,IAAMC,KAAY,SAACC,GAAAA;AACjB,SAAAA,QAAAA,KAAAA,UAAyCA,KAA6B,OAAVA;AAA5D;AADF,IAGaC,KAAgB,SAACC,GAAAA;AAC5B,MCzBsC3R,GAAc+E,IDyB9CuB,KAAQ,CAAA;AAEd,WAAW5C,MAAOiO,GAAK;AACrB,QAAMC,KAAMD,EAAIjO,EAAAA;AACXiO,MAAIE,eAAenO,EAAAA,KAAAA,CAAQ8N,GAAUI,EAAAA,MAGrCjN,MAAMC,QAAQgN,EAAAA,KAAQA,GAAIE,SAAUjO,GAAW+N,EAAAA,IAClDtL,GAAMjB,KAAK,GAAAhI,OAAG0U,GAAUrO,EAAAA,GAAI,GAAA,GAAKkO,IAAK,GAAA,IAC7BtN,GAAcsN,EAAAA,IACvBtL,GAAMjB,KAANnH,MAAAoI,IAAAA,cAAAA,cAAAA,CAAW,GAAGjJ,OAAAqG,IAAO,IAAA,CAAA,GAAKgO,GAAcE,EAAAA,GAAAA,KAAI,GAAA,CAAE,GAAA,GAAA,KAAK,CAAA,IAEnDtL,GAAMjB,KAAK,GAAGhI,OAAA0U,GAAUrO,EAAAA,GAAS,IAAA,EAAArG,QCrCC2C,IDqCe0D,ICnCxC,SAFuCqB,KDqCM6M,OCnCpB,aAAA,OAAV7M,MAAiC,OAAVA,KAC1C,KAGY,YAAA,OAAVA,MAAgC,MAAVA,MAAiB/E,KAAQgS,gBAAchS,EAAKkO,WAAW,IAAA,IAIjFtO,OAAOmF,EAAAA,EAAOa,KAAAA,IAHZ,GAAGvI,OAAA0H,IAAS,IAAA,ID8ByC,GAAA,CAAA;EAE7D;AAED,SAAOuB;AACT;AAEc,SAAU2L,GACtBR,GACAS,GACArC,GACAqB,IAAAA;AAEA,MAAIM,GAAUC,CAAAA;AACZ,WAAO,CAAA;AAIT,MAAI3N,GAAkB2N,CAAAA;AACpB,WAAO,CAAC,IAAKpU,OAAAoU,EAAkDU,iBAAAA,CAAAA;AAIjE,MAAItO,GAAW4N,CAAAA,GAAQ;AACrB,QAAA,CE7DK5N,GADmC7F,KF8DhByT,CAAAA,KE7DGzT,GAAK+D,aAAa/D,GAAK+D,UAAUqQ,oBAAAA,CF6D1BF;AAoBhC,aAAO,CAACT,CAAAA;AAnBR,QAAMpN,KAASoN,EAAMS,CAAAA;AAiBrB,WAboB,YAAA,OAAX7N,MACNM,MAAMC,QAAQP,EAAAA,KACbA,cAAkB2M,MACnB1M,GAAcD,EAAAA,KACJ,SAAXA,MAEA7G,QAAQC,MACN,GAAGJ,OAAAqD,EACD+Q,CAAAA,GACiL,kLAAA,CAAA,GAIhLQ,GAAe5N,IAAQ6N,GAAkBrC,GAAYqB,EAAAA;EAI/D;AEpFqB,MAAoBlT;AFsF1C,SAAIyT,aAAiBT,KACfnB,KACF4B,EAAMR,OAAOpB,GAAYqB,EAAAA,GAClB,CAACO,EAAML,QAAQF,EAAAA,CAAAA,KAEf,CAACO,CAAAA,IAKRnN,GAAcmN,CAAAA,IACTC,GAAcD,CAAAA,IAGlB9M,MAAMC,QAAQ6M,CAAAA,IAUZ9M,MAAM5C,UAAU1E,OAAOa,MAAMO,GANrBgT,EAMwCtE,IANjC,SAAAkF,IAAAA;AACpB,WAAAJ,GAAeI,IAAUH,GAAkBrC,GAAYqB,EAAAA;EAAvD,CAAA,CAAA,IAJO,CAACO,EAAMnC,SAAAA,CAAAA;AAMlB;AGzGwB,SAAAgD,GAAoChM,GAAAA;AAC1D,WAAShG,IAAI,GAAGA,IAAIgG,EAAMvI,QAAQuC,KAAK,GAAG;AACxC,QAAMqK,IAAOrE,EAAMhG,CAAAA;AAEnB,QAAIuD,GAAW8G,CAAAA,KAAAA,CAAU7G,GAAkB6G,CAAAA;AAGzC,aAAA;EAEH;AAED,SAAA;AACF;ACPA,IAAMxK,KAAOK,EAAKrE,CAAAA;AAAlB,IAKAoW,KAAA,WAAA;AAQE,WAAAA,EAAYjM,IAAqBnJ,GAAqBqV,GAAAA;AACpDzM,SAAKO,QAAQA,IACbP,KAAK0M,gBAAgB,IACrB1M,KAAK2M,WACsB,OAG3B3M,KAAK5I,cAAcA,GACnB4I,KAAK4M,WAAWvS,EAAMD,IAAMhD,CAAAA,GAC5B4I,KAAKyM,YAAYA,GAIjBhH,GAAWU,WAAW/O,CAAAA;EACvB;AAmEH,SAjEEoV,EAAAxQ,UAAA6Q,0BAAA,SACEV,IACArC,GACAC,GAAAA;AAEA,QAAIxH,KAAQvC,KAAKyM,YACbzM,KAAKyM,UAAUI,wBAAwBV,IAAkBrC,GAAYC,CAAAA,IACrE;AAGJ,QAAI/J,KAAK2M,YAAAA,CAAa5C,EAAOtP;AAC3B,UAAIuF,KAAK0M,iBAAiB5C,EAAWrD,aAAazG,KAAK5I,aAAa4I,KAAK0M,aAAAA;AACvEnK,QAAAA,KAAQvE,GAAYuE,IAAOvC,KAAK0M,aAAAA;WAC3B;AACL,YAAMI,KAAY3O,GAChB+N,GAAQlM,KAAKO,OAAO4L,IAAkBrC,GAAYC,CAAAA,CAAAA,GAE9CgD,KAAOC,EAAa3S,EAAM2F,KAAK4M,UAAUE,EAAAA,MAAe,CAAA;AAE9D,YAAA,CAAKhD,EAAWrD,aAAazG,KAAK5I,aAAa2V,EAAAA,GAAO;AACpD,cAAME,KAAqBlD,EAAO+C,IAAW,IAAIxV,OAAAyV,EAAAA,GAAAA,QAAmB/M,KAAK5I,WAAAA;AACzE0S,YAAWxJ,YAAYN,KAAK5I,aAAa2V,IAAME,EAAAA;QAChD;AAED1K,QAAAA,KAAQvE,GAAYuE,IAAOwK,EAAAA,GAC3B/M,KAAK0M,gBAAgBK;MACtB;SACI;AAIL,eAHIG,KAAc7S,EAAM2F,KAAK4M,UAAU7C,EAAOtP,IAAAA,GAC1C6G,KAAM,IAED/G,KAAI,GAAGA,KAAIyF,KAAKO,MAAMvI,QAAQuC,MAAK;AAC1C,YAAM4S,KAAWnN,KAAKO,MAAMhG,EAAAA;AAE5B,YAAwB,YAAA,OAAb4S;AACT7L,UAAAA,MAAO6L,IAEoCD,KAAc7S,EAAM6S,IAAaC,EAAAA;iBACnEA,IAAU;AACnB,cAAMC,KAAajP,GACjB+N,GAAQiB,IAAUhB,IAAkBrC,GAAYC,CAAAA,CAAAA;AAGlDmD,UAAAA,KAAc7S,EAAM6S,IAAaE,KAAa7S,EAAAA,GAC9C+G,MAAO8L;QACR;MACF;AAED,UAAI9L,IAAK;AACP,YAAM+L,IAAOL,EAAaE,OAAgB,CAAA;AAErCpD,UAAWrD,aAAazG,KAAK5I,aAAaiW,CAAAA,KAC7CvD,EAAWxJ,YACTN,KAAK5I,aACLiW,GACAtD,EAAOzI,IAAK,IAAIhK,OAAA+V,CAAAA,GAAAA,QAAmBrN,KAAK5I,WAAAA,CAAAA,GAI5CmL,KAAQvE,GAAYuE,IAAO8K,CAAAA;MAC5B;IACF;AAED,WAAO9K;EAAAA,GAEViK;AAAD,EAAA;AA9FA,IC+Bac,KAAe3D,aAAAA,QAAMC,cAAAA,MAAwCrD;AD/B1E,ICiCagH,KAAgBD,GAAarD;AAAAA,SAmC1BuD,KAAAA;AACd,MAAMtU,QAAQkR,aAAAA,YAAWkD,EAAAA;AAEzB,MAAA,CAAKpU;AACH,UAAMyH,GAAY,EAAA;AAGpB,SAAOzH;AACT;AAKwB,SAAAuU,GAAc1U,GAAAA;AACpC,MAAM2U,IAAa/D,aAAAA,QAAMS,WAAWkD,EAAAA,GAC9BK,SAAejD,aAAAA,SACnB,WAAA;AAAM,WAjDV,SAAoBxR,IAAsBwU,IAAAA;AACxC,UAAA,CAAKxU;AACH,cAAMyH,GAAY,EAAA;AAGpB,UAAI7C,GAAW5E,EAAAA,GAAQ;AACrB,YACM0U,KADU1U,GACYwU,EAAAA;AAE5B,YAEmB,SAAhBE,MAAwBhP,MAAMC,QAAQ+O,EAAAA,KAAuC,YAAA,OAAhBA;AAE9D,gBAAMjN,GAAY,CAAA;AAGpB,eAAOiN;MACR;AAED,UAAIhP,MAAMC,QAAQ3F,EAAAA,KAA2B,YAAA,OAAVA;AACjC,cAAMyH,GAAY,CAAA;AAGpB,aAAO+M,KAAkB7H,SAAAA,SAAA,CAAA,GAAA6H,EAAAA,GAAexU,EAAAA,IAAUA;IACpD,EAyBqBH,EAAMG,OAAOwU,CAAAA;EAAW,GACzC,CAAC3U,EAAMG,OAAOwU,CAAAA,CAAAA;AAGhB,SAAK3U,EAAMwO,WAIJoC,aAAAA,QAAC5F,cAAAuJ,GAAatC,UAAS,EAAAhM,OAAO2O,GAAAA,GAAe5U,EAAMwO,QAAAA,IAHjD;AAIX;ACjEA,IAAMsG,KAAyC,CAAA;AAA/C,IAyEIC,KAAmB,oBAAI7W;AA0F3B,SAAS8W,GAKPnT,GACA8K,IACAnF,IAAAA;AAEA,MAAMyN,KAAqBjQ,GAAkBnD,CAAAA,GACvCqT,KAAwBrT,GACxBsT,KAAAA,CAAwBrT,EAAMD,CAAAA,GAGlC4B,KAGEkJ,GAAOyI,OAHTA,IAAAA,WAAAA,KAAQzV,IAAW8D,IACnBoL,IAEElC,GAFsEtO,aAAxEA,KAAAA,WAAcwQ,IA/KlB,SACEzQ,IACAiX,GAAAA;AAEA,QAAMnU,IAA8B,YAAA,OAAhB9C,KAA2B,OAAOmC,EAAOnC,EAAAA;AAE7D0W,OAAY5T,CAAAA,KAAS4T,GAAY5T,CAAAA,KAAS,KAAK;AAE/C,QAAM7C,KAAc,GAAGE,OAAA2C,GAAAA,GAAAA,EAAAA,OAAQS,EAG7BtE,IAAa6D,IAAO4T,GAAY5T,CAAAA,CAAAA,CAAAA;AAGlC,WAAOmU,IAAoB,GAAG9W,OAAA8W,GAAqB,GAAA,EAAA9W,OAAAF,EAAAA,IAAgBA;EACrE,EAgK6BsO,GAAQvO,aAAauO,GAAQ0I,iBAAAA,IAAkBxG,GACxEC,KACEnC,GADuCvO,aAAzCA,KAAAA,WAAc0Q,KCpNM,SAAoBjN,IAAAA;AAC1C,WAAOC,EAAMD,EAAAA,IAAU,UAAUtD,OAAAsD,EAAAA,IAAW,UAAUtD,OAAAqD,EAAiBC,EAAAA,GAAAA,GAAAA;EACzE,EDkNsCA,CAAAA,IAAAA,IAG9BwR,KACJ1G,GAAQvO,eAAeuO,GAAQtO,cAC3B,GAAAE,OAAGgC,EAAOoM,GAAQvO,WAAAA,GAAgB,GAAA,EAAAG,OAAAoO,GAAQtO,WAAAA,IAC1CsO,GAAQtO,eAAeA,IAGvBiX,KACJL,MAAsBC,GAAsBE,QACxCF,GAAsBE,MAAM7W,OAAO6W,CAAAA,EAAyCG,OAAO5X,OAAAA,IAClFyX,GAEDtE,KAAsBnE,GAAOmE;AAEnC,MAAImE,MAAsBC,GAAsBpE,mBAAmB;AACjE,QAAM0E,KAAsBN,GAAsBpE;AAElD,QAAInE,GAAQmE,mBAAmB;AAC7B,UAAM2E,KAA4B9I,GAAQmE;AAG1CA,MAAAA,KAAoB,SAACvC,IAAMmH,GAAAA;AACzB,eAAAF,GAAoBjH,IAAMmH,CAAAA,KAC1BD,GAA0BlH,IAAMmH,CAAAA;MADhC;IAEH;AACC5E,MAAAA,KAAoB0E;EAEvB;AAED,MAAMG,KAAiB,IAAIlC,GACzBjM,IACA6L,IACA4B,KAAsBC,GAAsBS,iBAAAA,MAAoCnI;AAGlF,WAASoI,GAAiB5V,IAAoC6V,IAAAA;AAC5D,WA9IJ,SACEC,IACA9V,IACA+V,IAAAA;AAGE,UAAOC,KAMLF,GAAkBV,OALpBO,KAKEG,GALYH,gBACdzV,KAIE4V,GAAkB5V,cAHpB+V,KAGEH,GAHgBG,oBAClB5C,KAEEyC,GAAkBzC,mBADpBxR,KACEiU,GAAAA,QAEEI,KAAetF,aAAAA,QAAMS,WAAWkD,EAAAA,GAChC4B,KAAM/E,GAAAA,GACNN,KAAoBgF,GAAmBhF,qBAAqBqF,GAAIrF;AAEzC,UAAcsF,aAAAA,eAAc/C,EAAAA;AAKzD,UAAMlT,KAAQJ,EAAeC,IAAOkW,IAAchW,EAAAA,KAAiBJ,GAE7DuW,KA/DR,SACEjB,IACApV,GACAG,IAAAA;AAYA,iBAFImW,IARED,KAAAA,SAAAA,SAAAA,CAAAA,GAGDrW,CAAAA,GAAK,EAERuW,WAAAA,QACApW,OAAKA,GAAAA,CAAAA,GAIEqB,KAAI,GAAGA,KAAI4T,GAAMnW,QAAQuC,MAAK,GAAG;AAExC,cAAMgV,KAAkBzR,GADxBuR,KAAUlB,GAAM5T,EAAAA,CAAAA,IAC8B8U,GAAQD,EAAAA,IAAWC;AAEjE,mBAAW1R,MAAO4R;AAChBH,YAAAA,GAAQzR,EAAAA,IACE,gBAARA,KACIK,GAAYoR,GAAQzR,EAAAA,GAA4B4R,GAAgB5R,EAAAA,CAAAA,IACxD,YAARA,KAAAA,SAAAA,SAAAA,CAAAA,GACOyR,GAAQzR,EAAAA,CAAAA,GAAS4R,GAAgB5R,EAAAA,CAAAA,IACtC4R,GAAgB5R,EAAAA;QAE3B;AAMD,eAJI5E,EAAMuW,cACRF,GAAQE,YAAYtR,GAAYoR,GAAQE,WAAWvW,EAAMuW,SAAAA,IAGpDF;MACT,EA6BwCL,IAAgBhW,IAAOG,EAAAA,GACvDuV,KAAgCW,GAAQI,MAAM5U,IAC9C6U,KAA6B,CAAA;AAEnC,eAAW9R,MAAOyR;AAAAA,mBACZA,GAAQzR,EAAAA,KAGU,QAAXA,GAAI,CAAA,KAAsB,SAARA,MAAyB,YAARA,MAAmByR,GAAQlW,UAAUA,OAEhE,kBAARyE,KACT8R,GAAgBD,KAAKJ,GAAQM,cACnB7F,MAAAA,CAAqBA,GAAkBlM,IAAK8Q,EAAAA,MACtDgB,GAAgB9R,EAAAA,IAAOyR,GAAQzR,EAAAA,GAG5BkM,MACwB,SACxB8F,YAAYhS,EAAAA,KACZmQ,GAAiBxV,IAAIqF,EAAAA,KAAAA,CAEtBxE,EAAYb,IAAImW,EAAAA,MAEhBX,GAAiBtV,IAAImF,EAAAA,GACrBlG,QAAQc,KACN,qDAAA,OAAqDoF,IAAG,sVAAA,CAAA;AAMhE,UAAMiS,KA/GR,SACElB,IACAmB,GAAAA;AAEA,YAAMX,IAAM/E,GAAAA,GAENmF,KAAYZ,GAAe7B,wBAC/BgD,GACAX,EAAIpF,YACJoF,EAAInF,MAAAA;AAKN,mBAF2CoF,aAAAA,eAAcG,EAAAA,GAElDA;MACT,EAgG8CZ,IAAgBU,EAAAA;AAE/B,MAAgBP,GAAmBiB,sBAC9DjB,GAAmBiB,mBAAmBF,EAAAA;AAGxC,UAAIG,KAAc/R,GAAYgR,IAAoB5C,EAAAA;AAuBlD,aAtBIwD,OACFG,MAAe,MAAMH,KAEnBR,GAAQE,cACVS,MAAe,MAAMX,GAAQE,YAG/BG,GAEE5U,EAAM4T,EAAAA,KAAAA,CACLtV,EAAYb,IAAImW,EAAAA,IACb,UACA,WAAA,IACFsB,IAKAjB,OACFW,GAAgBb,MAAME,SAGjB/K,aAAAA,eAAc0K,IAAoBgB,EAAAA;IAC3C,EAwD8CO,IAAwBjX,IAAO6V,EAAAA;EAC1E;AAEDD,EAAAA,GAAiBxX,cAAcA;AAM/B,MAAI6Y,KAAyBrG,aAAAA,QAAMsG,WAAWtB,EAAAA;AA+D9C,SA1DAqB,GAAuB7B,QAAQE,IAC/B2B,GAAuBtB,iBAAiBA,IACxCsB,GAAuB7Y,cAAcA,IACrC6Y,GAAuBnG,oBAAoBA,IAI3CmG,GAAuBhB,qBAAqBhB,KACxChQ,GAAYiQ,GAAsBe,oBAAoBf,GAAsB7B,iBAAAA,IAC5E,IAEJ4D,GAAuB5D,oBAAoBA,IAG3C4D,GAAuBpV,SAASoT,KAAqBC,GAAsBrT,SAASA,GAEpFjC,OAAOkE,eAAemT,IAAwB,gBAAgB,EAC5DjO,KAAG,WAAA;AACD,WAAO/B,KAAKkQ;EACb,GAEDtP,KAAAA,SAAIgL,IAAAA;AACF5L,SAAKkQ,sBAAsBlC,KrBvQT,SAAUpT,IAAAA;AAAAA,eAA8BuV,IAAA,CAAA,GAAArY,IAAA,GAAjBA,IAAiBC,UAAAC,QAAjBF;AAAAqY,UAAiBrY,IAAA,CAAA,IAAAC,UAAAD,CAAAA;AAC9D,eAAqB0E,KAAA,GAAA4T,KAAOD,GAAP3T,KAAAA,GAAAA,QAAAA;AACnBiC,WAAiB7D,IADFwV,GAAA5T,EAAAA,GAAAA,IACkB;AAGnC,aAAO5B;IACT,EqBkQgB,CAAE,GAAEqT,GAAsBhV,cAAc2S,EAAAA,IAC9CA;EACL,EAAA,CAAA,GAID1U,EAAqBC,IAAaiV,EAAAA,GAElC4D,GAAuBF,qBEvSZ,yBAAC3Y,IAAqBC,GAAAA;AACnC,QAAIiZ,IAA8B,CAAA,GAC9BC,KAAAA;AAEJ,WAAO,SAAChB,IAAAA;AACN,UAAA,CAAKgB,OACHD,EAAiBf,EAAAA,IAAAA,MACb3W,OAAO6E,KAAK6S,CAAAA,EAAkBrY,UATnB,MASoC;AAGjD,YAAMX,KAAiBD,IAAc,oBAAoBE,OAAAF,GAAc,GAAA,IAAG;AAE1EK,gBAAQc,KACN,QAAAjB,OAfW,KAe2C,wCAAA,EAAAA,OAAAH,EAAAA,EAAcG,OAAAD,IAAmB,KAAA,IAAvF,6PAAA,GAUFiZ,KAAAA,MACAD,IAAmB,CAAA;MACpB;IAEL;EACD,EF4QKlZ,IACAiV,EAAAA,GAIJtN,GAAYkR,IAAwB,WAAA;AAAM,WAAA,IAAA1Y,OAAI0Y,GAAuB5D,iBAAAA;EAA3B,CAAA,GAEtC8B,MAGFqC,GACEP,IAH+BpV,GAK/B,EAEEuT,OAAAA,MACAO,gBAAAA,MACAvX,aAAAA,MACA6X,oBAAAA,MACAnF,mBAAAA,MACAuC,mBAAAA,MACAxR,QAAAA,KAAQ,CAAA,GAKPoV;AACT;AGrUc,SAAUQ,GACtBC,GACA/Q,GAAAA;AAIA,WAFMpB,IAAiC,CAACmS,EAAQ,CAAA,CAAA,GAEvClW,KAAI,GAAG8E,KAAMK,EAAe1H,QAAQuC,KAAI8E,IAAK9E,MAAK;AACzD+D,MAAOgB,KAAKI,EAAenF,EAAAA,GAAIkW,EAAQlW,KAAI,CAAA,CAAA;AAG7C,SAAO+D;AACT;ACMA,IAAMoS,KAAS,SAAyBC,GAAAA;AACtC,SAAAhY,OAAOiY,OAAOD,GAAK,EAAE5E,OAAAA,KAAO,CAAA;AAA5B;AAOF,SAASzK,GACPuP,GAAAA;AAAAA,WACkDnR,IAAA,CAAA,GAAA5H,KAAA,GAAlDA,KAAkDC,UAAAC,QAAlDF;AAAA4H,MAAkD5H,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAElD,MAAIgG,GAAW+S,CAAAA,KAAWtS,GAAcsS,CAAAA;AAGtC,WAAOH,GACLxE,GACEsE,GAAkB9X,GAAWN,cAAA,CAJHyY,CAAAA,GAMrBnR,GAAAA,IAAc,CAAA,CAAA,CAAA;AAMzB,MAAMoR,KAAmBD;AAEzB,SAC4B,MAA1BnR,EAAe1H,UACa,MAA5B8Y,GAAiB9Y,UACc,YAAA,OAAxB8Y,GAAiB,CAAA,IAEjB5E,GAAe4E,EAAAA,IAGjBJ,GACLxE,GAAesE,GAAkBM,IAAkBpR,CAAAA,CAAAA,CAAAA;AAEvD;AC0BwB,SAAAqR,GAQtBC,GACAjR,IACA2F,IAAAA;AASA,MAAA,WATAA,OAAAA,KAAoD7M,IAAAA,CAS/CkH;AACH,UAAMY,GAAY,GAAGZ,EAAAA;AAIvB,MAAMkR,KAAmB,SACvBC,GAAAA;AAAAA,aACiExR,KAAA,CAAA,GAAA5H,KAAA,GAAjEA,KAAiEC,UAAAC,QAAjEF;AAAA4H,MAAAA,GAAiE5H,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAEjE,WAAAkZ,EACEjR,IACA2F,IACApE,GAAmCnJ,MAAAA,QAAAC,cAAA,CAAA8Y,CAAAA,GAAkBxR,IAAAA,KACtD,CAAA,CAAA;EAJD;AA6CF,SAjCAuR,GAAiB9C,QAAQ,SAMvBA,GAAAA;AAEA,WAAA4C,GAUEC,GAAsBjR,IACnB8F,SAAAA,SAAA,CAAA,GAAAH,EAAAA,GACH,EAAAyI,OAAOvP,MAAM5C,UAAU1E,OAAOoO,GAAQyI,OAAOA,CAAAA,EAAOG,OAAO5X,OAAAA,EAAAA,CAAAA,CAAAA;EAZ7D,GAmBFua,GAAiBE,aAAa,SAACC,GAAAA;AAC7B,WAAAL,GAA0DC,GAAsBjR,IAC3E8F,SAAAA,SAAA,CAAA,GAAAH,EAAAA,GACA0L,CAAAA,CAAAA;EAFL,GAKKH;AACT;ACvJA,IAAMI,KAAa,SACjBtR,GAAAA;AAEA,SAAAgR,GAIEhD,IAAuBhO,CAAAA;AAJzB;AAHF,IASMuR,KAASD;AAKflY,EAAYoG,QAAQ,SAAAgS,GAAAA;AAElBD,KAAOC,CAAAA,IAAcF,GAA8BE,CAAAA;AACrD,CAAA;ACjBA,IAAAC,KAAA,WAAA;AAKE,WAAYA,EAAAjR,IAAuBnJ,GAAAA;AACjC4I,SAAKO,QAAQA,IACbP,KAAK5I,cAAcA,GACnB4I,KAAK2M,WAAWJ,GAAchM,EAAAA,GAI9BkF,GAAWU,WAAWnG,KAAK5I,cAAc,CAAA;EAC1C;AAkCH,SAhCEoa,EAAYxV,UAAAyV,eAAZ,SACEC,IACAvF,GACArC,GACAC,IAAAA;AAEA,QAGMzI,KAAMyI,GAHI5L,GACd+N,GAAQlM,KAAKO,OAA0B4L,GAAkBrC,GAAYC,EAAAA,CAAAA,GAE3C,EAAA,GACtBjI,KAAK9B,KAAK5I,cAAcsa;AAG9B5H,MAAWxJ,YAAYwB,IAAIA,IAAIR,EAAAA;EAAAA,GAGjCkQ,EAAAxV,UAAA2V,eAAA,SAAaD,IAAkB5H,GAAAA;AAC7BA,MAAWjD,WAAW7G,KAAK5I,cAAcsa,EAAAA;EAAAA,GAG3CF,EAAYxV,UAAA4V,eAAZ,SACEF,IACAvF,GACArC,GACAC,IAAAA;AAEI2H,IAAAA,KAAW,KAAGjM,GAAWU,WAAWnG,KAAK5I,cAAcsa,EAAAA,GAG3D1R,KAAK2R,aAAaD,IAAU5H,CAAAA,GAC5B9J,KAAKyR,aAAaC,IAAUvF,GAAkBrC,GAAYC,EAAAA;EAAAA,GAE7DyH;AAAD,EAAA;ACzCwB,SAAAK,GACtBpB,GAAAA;AAAAA,WAC8C/Q,KAAA,CAAA,GAAA5H,KAAA,GAA9CA,KAA8CC,UAAAC,QAA9CF;AAAA4H,IAAAA,GAA8C5H,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAE9C,MAAMyI,KAAQe,GAAGnJ,MAAAA,QAAAC,cAAA,CAAQqY,CAAAA,GAAY/Q,IAAAA,KAAAA,CAAAA,GAC/B0M,KAAoB,aAAa9U,OAAAoD,EAAoBoX,KAAKnJ,UAAUpI,EAAAA,CAAAA,CAAAA,GACpEwR,KAAc,IAAIP,GAAmBjR,IAAO6L,EAAAA;AAErB,EAC3BlV,EAAqBkV,EAAAA;AAGvB,MAAM4F,KAAoE,SAAAjZ,GAAAA;AACxE,QAAMmW,IAAM/E,GAAAA,GACNjR,KAAQyQ,aAAAA,QAAMS,WAAWkD,EAAAA,GAGzBoE,KAFc/H,aAAAA,QAAMtR,OAAO6W,EAAIpF,WAAWtD,mBAAmB4F,EAAAA,CAAAA,EAEtC6F;AA8B7B,WA5B6CtI,aAAAA,QAAMuI,SAASC,MAAMpZ,EAAMwO,QAAAA,KACtE9P,QAAQc,KACN,8BAAA,OAA8B6T,IAAiB,mEAAA,CAAA,GAMjD7L,GAAM6R,KAAK,SAAAxN,IAAAA;AAAQ,aAAgB,YAAA,OAATA,MAAAA,OAAqBA,GAAKyN,QAAQ,SAAA;IAAiB,CAAA,KAE7E5a,QAAQc,KACN,8UAAA,GAIA2W,EAAIpF,WAAW/D,UACjB6L,GAAaF,IAAU3Y,GAAOmW,EAAIpF,YAAY5Q,IAAOgW,EAAInF,MAAAA,GAIzDJ,aAAAA,QAAM2I,gBAAgB,WAAA;AACpB,UAAA,CAAKpD,EAAIpF,WAAW/D;AAElB,eADA6L,GAAaF,IAAU3Y,GAAOmW,EAAIpF,YAAY5Q,IAAOgW,EAAInF,MAAAA,GAClD,WAAA;AAAM,iBAAAgI,GAAYJ,aAAaD,IAAUxC,EAAIpF,UAAAA;QAAAA;IAExD,GAAG,CAAC4H,IAAU3Y,GAAOmW,EAAIpF,YAAY5Q,IAAOgW,EAAInF,MAAAA,CAAAA,GAG3C;EACT;AAEA,WAAS6H,GACPF,GACA3Y,IACA+Q,IACA5Q,IACA6Q,IAAAA;AAEA,QAAIgI,GAAYpF;AACdoF,MAAAA,GAAYH,aACVF,GACA5a,GACAgT,IACAC,EAAAA;SAEG;AACL,UAAMqF,KAAUvJ,SAAAA,SAAA,CAAA,GACX9M,EAAAA,GACH,EAAAG,OAAOJ,EAAeC,IAAOG,IAAO8Y,GAAqB/Y,YAAAA,EAAAA,CAAAA;AAG3D8Y,MAAAA,GAAYH,aAAaF,GAAUtC,IAAStF,IAAYC,EAAAA;IACzD;EACF;AAED,SAAOJ,aAAAA,QAAM4I,KAAKP,EAAAA;AACpB;ACjFwB,SAAAQ,GACtB/B,GAAAA;AAAAA,WAC8C/Q,IAAA,CAAA,GAAA5H,KAAA,GAA9CA,KAA8CC,UAAAC,QAA9CF;AAAA4H,MAA8C5H,KAAA,CAAA,IAAAC,UAAAD,EAAAA;AAInB,EACJ,eAAA,OAAd2a,aACe,kBAAtBA,UAAUC,WAEVjb,QAAQc,KACN,iHAAA;AAIJ,MAAMgI,KAAQpC,GAAgBmD,GAAWnJ,MAAAA,QAAAC,cAAA,CAAAqY,CAAAA,GAAY/Q,GAAAA,KAA2B,CAAA,CAAA,GAC1EzF,KAAOS,EAAoB6F,EAAAA;AACjC,SAAO,IAAI0K,GAAUhR,IAAMsG,EAAAA;AAC7B;ACjBwB,SAAAoS,GACtBC,GAAAA;AAMA,MAAMC,IAAYlJ,aAAAA,QAAMsG,WACtB,SAAClX,IAAO6V,IAAAA;AACN,QACMkE,KAAYha,EAAeC,IADnB4Q,aAAAA,QAAMS,WAAWkD,EAAAA,GACgBsF,EAAU3Z,YAAAA;AAUzD,WARgBpC,WAA6Bic,MAC3Crb,QAAQc,KACN,yHAAyHjB,OAAAqD,EACvHiY,CAAAA,GACE,GAAA,CAAA,GAIDjJ,aAAAA,QAAC5F,cAAA6O,GAAc/M,SAAA,CAAA,GAAA9M,IAAO,EAAAG,OAAO4Z,IAAWlE,KAAKA,GAAAA,CAAAA,CAAAA;EACtD,CAAA;AAKF,SAFAiE,EAAU1b,cAAc,aAAAG,OAAaqD,EAAiBiY,CAAAA,GAAU,GAAA,GAEzDrC,GAAMsC,GAAWD,CAAAA;AAC1B;ACpBA,IAAAG,KAAA,WAAA;AAIE,WAAAA,IAAAA;AAAA,QAGCnN,KAAA5F;AAEDA,SAAAgT,gBAAgB,WAAA;AACd,UAAM1R,IAAMsE,GAAK8L,SAASnI,SAAAA;AAC1B,UAAA,CAAKjI;AAAK,eAAO;AACjB,UAAM8C,IAAQV,GAAAA,GAMRuP,KAAW9U,GALH,CACZiG,KAAS,UAAU9M,OAAA8M,GAAQ,GAAA,GAC3B,GAAA9M,OAAGxB,GAAgB,SAAA,GACnB,GAAGwB,OAAAnB,GAAoB,IAAA,EAAAmB,OAAAlB,GAAa,GAAA,CAAA,EAECkY,OAAO5X,OAAAA,GAAsB,GAAA;AAEpE,aAAO,UAAUY,OAAA2b,IAAY,GAAA,EAAA3b,OAAAgK,GAAAA,UAAAA;IAC/B,GAUAtB,KAAAkT,eAAe,WAAA;AACb,UAAItN,GAAKuN;AACP,cAAMxS,GAAY,CAAA;AAGpB,aAAOiF,GAAKoN,cAAAA;IACd,GAEAhT,KAAAoT,kBAAkB,WAAA;AAAA,UAAA;AAChB,UAAIxN,GAAKuN;AACP,cAAMxS,GAAY,CAAA;AAGpB,UAAMW,KAAMsE,GAAK8L,SAASnI,SAAAA;AAC1B,UAAA,CAAKjI;AAAK,eAAO,CAAA;AAEjB,UAAMvI,OAAKyD,IAAA,CAAA,GACR1G,CAAAA,IAAU,IACX0G,EAACrG,CAAAA,IAAkBC,GACnBoG,EAAA6W,0BAAyB,EACvBC,QAAQhS,GAAAA,GAAAA,IAIN8C,KAAQV,GAAAA;AAMd,aALIU,OACDrL,GAAcqL,QAAQA,KAIlB,CAACuF,aAAAA,QAAAA,cAAAA,SAAAA,SAAAA,CAAAA,GAAW5Q,IAAK,EAAE4E,KAAI,SAAA,CAAA,CAAA,CAAA;IAChC,GAyDAqC,KAAAuT,OAAO,WAAA;AACL3N,MAAAA,GAAKuN,SAAAA;IACP,GApHEnT,KAAK0R,WAAW,IAAIjM,GAAW,EAAEF,UAAAA,KAAU,CAAA,GAC3CvF,KAAKmT,SAAAA;EACN;AAmHH,SAnGEJ,EAAa/W,UAAAwX,gBAAb,SAAcjM,IAAAA;AACZ,QAAIvH,KAAKmT;AACP,YAAMxS,GAAY,CAAA;AAGpB,WAAOgJ,aAAAA,QAAA5F,cAACsG,IAAiB,EAAChI,OAAOrC,KAAK0R,SAAAA,GAAWnK,EAAAA;EAAAA,GAqCnDwL,EAAwB/W,UAAAyX,2BAAxB,SAAyBC,IAAAA;AAErB,UAAM/S,GAAY,CAAA;EAAA,GAuDvBoS;AAAD,EAAA;AA1HA,ICXaY,KAAc,EACzBlO,YAAUA,IACV+D,WAASA,GAAAA;ACmBY,eAAA,OAAdiJ,aACe,kBAAtBA,UAAUC,WAEVjb,QAAQc,KACN,sNAAA;AAIJ,IAAMqb,KAAkB,QAAQtc,OAAAxB,GAAAA,IAAAA;AAMZ,eAAA,OAAXS,WAGPA,OAAOqd,EAAAA,MAAPrd,OAAOqd,EAAAA,IAAqB,IAGI,MAA5Brd,OAAOqd,EAAAA,KACTnc,QAAQc,KACN,0TAAA,GAKJhC,OAAOqd,EAAAA,KAAoB;", "names": ["__assign", "s", "i", "p", "from", "i", "l", "length", "position", "length", "length", "character", "characters", "i", "j", "k", "x", "y", "z", "length", "_", "a", "b", "c", "f", "i", "length", "i", "SC_ATTR", "process", "env", "REACT_APP_SC_ATTR", "SC_ATTR_ACTIVE", "SC_ATTR_VERSION", "SC_VERSION", "SPLITTER", "IS_BROWSER", "window", "document", "DISABLE_SPEEDY", "Boolean", "SC_DISABLE_SPEEDY", "REACT_APP_SC_DISABLE_SPEEDY", "NODE_ENV", "STATIC_EXECUTION_CONTEXT", "invalidHookCallRe", "seen", "Set", "checkDynamicCreation", "displayName", "componentId", "parsedIdString", "concat", "message_1", "originalConsoleError_1", "console", "error", "didNotCallInvalidHook_1", "consoleErrorMessage", "consoleErrorArgs", "_i", "arguments", "length", "test", "delete", "apply", "__spread<PERSON><PERSON>y", "useRef", "has", "warn", "add", "message", "EMPTY_ARRAY", "Object", "freeze", "EMPTY_OBJECT", "determineTheme", "props", "providedTheme", "defaultProps", "theme", "dom<PERSON><PERSON>s", "escapeRegex", "dashesAtEnds", "escape", "str", "replace", "AD_REPLACER_R", "chars<PERSON><PERSON><PERSON>", "getAlphabeticChar", "code", "String", "fromCharCode", "generateAlphabeticName", "x", "name", "Math", "abs", "SEED", "phash", "h", "i", "charCodeAt", "hash", "generateComponentId", "getComponentName", "target", "isTag", "char<PERSON>t", "toLowerCase", "hasSymbol", "Symbol", "for", "REACT_MEMO_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "type", "KNOWN_STATICS", "prototype", "caller", "callee", "arity", "MEMO_STATICS", "$$typeof", "compare", "TYPE_STATICS", "_a", "render", "getStatics", "component", "object", "defineProperty", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "excludelist", "inheritedComponent", "keys", "targetStatics", "sourceStatics", "key", "descriptor", "e", "isFunction", "isStyledComponent", "joinStrings", "a", "b", "joinStringArray", "arr", "sep", "result", "isPlainObject", "constructor", "mixinRecursively", "source", "forceMerge", "Array", "isArray", "setToString", "toStringFn", "value", "ERRORS", "format", "args", "c", "len", "push", "for<PERSON>ach", "d", "throwStyledComponentsError", "interpolations", "Error", "join", "trim", "DefaultGroupedTag", "tag", "this", "groupSizes", "Uint32Array", "indexOfGroup", "group", "index", "insertRules", "rules", "<PERSON><PERSON><PERSON><PERSON>", "oldSize", "newSize", "styledError", "set", "ruleIndex", "l", "insertRule", "clearGroup", "length_1", "startIndex", "endIndex", "deleteRule", "getGroup", "css", "getRule", "MAX_SMI", "groupIDRegister", "Map", "reverseRegister", "nextFreeGroup", "getGroupForId", "id", "get", "setGroupForId", "SELECTOR", "MARKER_RE", "RegExp", "rehydrateNamesFromContent", "sheet", "content", "names", "split", "registerName", "rehydrateSheetFromTag", "style", "parts", "textContent", "part", "marker", "match", "parseInt", "getTag", "rehydrateSheet", "nodes", "querySelectorAll", "node", "getAttribute", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getNonce", "__webpack_nonce__", "makeStyleTag", "head", "parent", "createElement", "prevStyle", "from", "nextS<PERSON>ling", "setAttribute", "nonce", "insertBefore", "CSSOMTag", "element", "append<PERSON><PERSON><PERSON>", "createTextNode", "styleSheets", "ownerNode", "rule", "_error", "cssRules", "cssText", "TextTag", "childNodes", "VirtualTag", "_target", "splice", "SHOULD_REHYDRATE", "defaultOptions", "isServer", "useCSSOMInjection", "StyleSheet", "options", "globalStyles", "_this", "__assign", "gs", "server", "r", "size", "selector", "registerId", "rehydrate", "reconstructWithOptions", "with<PERSON><PERSON>s", "undefined", "allocateGSInstance", "hasNameForId", "groupNames", "clearNames", "clear", "clearRules", "clearTag", "AMP_REGEX", "COMMENT_REGEX", "recursivelySetNamepace", "compiled", "namespace", "map", "replaceAll", "prop", "children", "createStylisInstance", "_componentId", "_selector", "_selectorRegexp", "_b", "_c", "_d", "plugins", "selfReferenceReplacer", "offset", "string", "startsWith", "endsWith", "middlewares", "slice", "RULESET", "includes", "prefix", "prefixer", "stringify", "stringifyRules", "flatCSS", "compile", "stack", "serialize", "middleware", "rulesheet", "reduce", "acc", "plugin", "throwStyledError", "toString", "mainSheet", "mainStylis", "StyleSheetContext", "React", "createContext", "shouldForwardProp", "styleSheet", "stylis", "StyleSheetConsumer", "Consumer", "StylisContext", "useStyleSheetContext", "useContext", "StyleSheetManager", "useState", "stylisPlugins", "setPlugins", "resolvedStyleSheet", "useMemo", "disableCSSOMInjection", "enableVendorPrefixes", "useEffect", "shallowequal", "styleSheetContextValue", "Provider", "Keyframes", "inject", "stylisInstance", "resolvedName", "getName", "isUpper", "hyphenateStyleName", "output", "isFalsish", "chunk", "objToCssArray", "obj", "val", "hasOwnProperty", "isCss", "hyphenate", "unitless", "flatten", "executionContext", "styledComponentId", "isReactComponent", "chunklet", "isStaticRules", "ComponentStyle", "baseStyle", "staticRulesId", "isStatic", "baseHash", "generateAndInjectStyles", "cssStatic", "name_1", "generateName", "cssStaticFormatted", "dynamicHash", "partRule", "partString", "name_2", "ThemeContext", "ThemeConsumer", "useTheme", "ThemeProvider", "outerTheme", "themeContext", "mergedTheme", "identifiers", "seenUnknownProps", "createStyledComponent", "isTargetStyledComp", "styledComponentTarget", "isCompositeComponent", "attrs", "parentComponentId", "finalAttrs", "filter", "shouldForwardPropFn_1", "passedShouldForwardPropFn_1", "elementToBeCreated", "componentStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "forwardedComponent", "forwardedRef", "componentAttrs", "foldedComponentIds", "contextTheme", "ssc", "useDebugValue", "context", "attrDef", "className", "resolvedAttrDef", "as", "propsForElement", "forwardedAs", "isPropValid", "generatedClassName", "resolvedAttrs", "warnTooManyClasses", "classString", "WrappedStyledComponent", "forwardRef", "_foldedDefaultProps", "sources", "sources_1", "generatedClasses", "warningSeen", "hoist", "interleave", "strings", "addTag", "arg", "assign", "styles", "styleStringArray", "constructWithOptions", "componentConstructor", "templateFunction", "initialStyles", "withConfig", "config", "baseStyled", "styled", "dom<PERSON>lement", "GlobalStyle", "createStyles", "instance", "removeStyles", "renderStyles", "createGlobalStyle", "JSON", "globalStyle", "GlobalStyleComponent", "current", "Children", "count", "some", "indexOf", "useLayoutEffect", "memo", "keyframes", "navigator", "product", "withTheme", "Component", "WithTheme", "themeProp", "ServerStyleSheet", "_emitSheetCSS", "htmlAttr", "getStyleTags", "sealed", "getStyleElement", "dangerouslySetInnerHTML", "__html", "seal", "collectStyles", "interleaveWithNodeStream", "input", "__PRIVATE__", "windowGlobalKey"]}