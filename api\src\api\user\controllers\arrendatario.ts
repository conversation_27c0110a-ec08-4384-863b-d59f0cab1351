"use strict";

import { Context } from "koa";

interface UserWithRole {
  id: number;
  role?: {
    id: number;
    name: string;
    type: string;
  };
  [key: string]: any; // Para otras propiedades
}

interface StrapiContext extends Context {
  state: { user?: any };
  params: { id?: string };
}

// Sanitize para la salida (ajústalo según tus necesidades)
const sanitizeUserOutput = (entity: any) => {
  if (!entity) return null;
  return {
    id: entity.id,
    username: entity.username,
    email: entity.email,
    firstName: entity.firstName,
    lastName: entity.lastName,
    address: entity.address,
    phone: entity.phone,
    confirmed: entity.confirmed,
    blocked: entity.blocked,
    imgUrl: entity.imgUrl
      ? Array.isArray(entity.imgUrl)
        ? entity.imgUrl[0]?.url
        : entity.imgUrl.url
      : null,
    role: entity.role
      ? {
          id: entity.role.id,
          name: entity.role.name,
          type: entity.role.type,
        }
      : undefined,
    usuarioPrincipal: entity.usuarioPrincipal
      ? {
          id: entity.usuarioPrincipal.id,
          username: entity.usuarioPrincipal.username,
          email: entity.usuarioPrincipal.email,
        }
      : undefined,
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };
};

export default {
  /**
   * Crear un nuevo arrendatario asociado al usuario actual.
   * (Incluye subida de imagen)
   */
  async createArrendatario(ctx: StrapiContext) {
    try {
      const { user } = ctx.state;
      if (!user) return ctx.unauthorized("No autorizado");

      // Parsear datos y archivo
      const data =
        typeof ctx.request.body?.data === "string"
          ? JSON.parse(ctx.request.body.data)
          : ctx.request.body?.data || {};

      const imgFile =
        ctx.request.files?.imgUrl || ctx.request.files?.["files.imgUrl"];

      // Buscar rol Arrendatario
      const arrendatarioRole = await strapi
        .query("plugin::users-permissions.role")
        .findOne({ where: { name: "Arrendatario" } });

      if (!arrendatarioRole)
        return ctx.badRequest("No se encontró el rol Arrendatario");

      // Obtener datos del usuario residente
      const usuarioPrincipal = await strapi.entityService.findOne(
        'plugin::users-permissions.user',
        user.id,
        { 
          fields: ['coefficient', 'address'],
          populate: { role: { fields: ['name'] } }
        }
      );

      // Validar que el usuario principal tenga dirección
      if (!usuarioPrincipal?.address) {
        return ctx.badRequest("El usuario residente no tiene una dirección configurada");
      }

      // Generar nombre de usuario automáticamente
      const generateUsername = (email: string, address: string) => {
        if (!email || !address) return '';
        const emailPart = email.split('@')[0]; // Parte antes del @
        const addressPart = address.toLowerCase().replace(/\s+/g, '');
        return `${emailPart}-${addressPart}`;
      };

      // Crear el objeto de datos del usuario
      const userData: any = {
        username: generateUsername(data.email, usuarioPrincipal.address),
        email: data.email,
        password: data.password, // La contraseña se hasheará automáticamente
        firstName: data.firstName,
        lastName: data.lastName,
        address: usuarioPrincipal.address, // Heredar la dirección del residente
        coefficient: usuarioPrincipal?.coefficient || 0, // Heredar el coeficiente del residente
        documentoIdentidad: data.documentoIdentidad,
        role: arrendatarioRole.id,
        usuarioPrincipal: user.id,
        confirmed: true,
        blocked: false,
        provider: 'local',
      };

      // Usar el servicio de usuarios para crear el usuario
      const userService = strapi.plugin('users-permissions').service('user');
      const arrendatario = await userService.add(userData);

      // Si hay imagen, subirla y asociarla
      if (imgFile) {
        const filesToUpload = Array.isArray(imgFile) ? imgFile : [imgFile];
        const uploadedFiles = await strapi
          .plugin("upload")
          .service("upload")
          .upload({
            data: {
              ref: "plugin::users-permissions.user",
              refId: arrendatario.id,
              field: "imgUrl",
            },
            files: filesToUpload,
          });

        if (uploadedFiles && uploadedFiles.length > 0) {
          await strapi.entityService.update(
            "plugin::users-permissions.user",
            arrendatario.id,
            { data: { imgUrl: uploadedFiles[0].id } }
          );
        }
      }

      // Popula y devuelve usuario final
      const arrendatarioFinal = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        arrendatario.id,
        { populate: ["imgUrl", "role", "usuarioPrincipal"] }
      );

      return { data: sanitizeUserOutput(arrendatarioFinal) };
    } catch (err) {
      console.error("Error en createArrendatario:", err);
      return ctx.badRequest({
        error: err.message + " Error al crear el arrendatario.",
      });
    }
  },

  /**
   * Actualizar un arrendatario (incluyendo imagen nueva si se envía)
   */
  async updateArrendatario(ctx: StrapiContext) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;

      if (!user) return ctx.unauthorized("No autorizado");

      const data =
        typeof ctx.request.body?.data === "string"
          ? JSON.parse(ctx.request.body.data)
          : ctx.request.body?.data || {};

      const imgFile =
        ctx.request.files?.imgUrl || ctx.request.files?.["files.imgUrl"];

      // Verificar propietario y rol de arrendatario
      const arrendatario = (await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        id,
        { 
          populate: ["usuarioPrincipal", "role"] 
        }
      )) as any;
      
      if (!arrendatario) return ctx.notFound("Usuario no encontrado");
      
      // Verificar que el usuario sea un arrendatario
      const isArrendatario = arrendatario.role?.name?.toLowerCase() === 'arrendatario' || 
                            arrendatario.role?.type?.toLowerCase() === 'arrendatario';
      
      if (!isArrendatario) return ctx.badRequest("El usuario seleccionado no es un arrendatario");
      
      // Verificar que el usuario actual sea el propietario o un administrador
      const isAdmin = ["admin", "administrator"].includes(user.role?.type?.toLowerCase() || "");
      const isOwner = arrendatario.usuarioPrincipal?.id === user.id;
      
      if (!isAdmin && !isOwner) {
        return ctx.forbidden("No tienes permiso para actualizar este arrendatario");
      }

      // Preparar datos para actualizar
      const updateData = { ...data };

      // Asegurarse de que el teléfono tenga el formato correcto (objeto JSON) si se está actualizando
      if (updateData.phone) {
        updateData.phone = { number: updateData.phone, type: "mobile" };
      }

      // Actualizar datos principales
      await strapi.entityService.update("plugin::users-permissions.user", id, {
        data: updateData,
      });

      // Si hay nueva imagen, subir y asociar
      if (imgFile) {
        const filesToUpload = Array.isArray(imgFile) ? imgFile : [imgFile];
        const uploadedFiles = await strapi
          .plugin("upload")
          .service("upload")
          .upload({
            data: {
              ref: "plugin::users-permissions.user",
              refId: id,
              field: "imgUrl",
            },
            files: filesToUpload,
          });
        if (uploadedFiles && uploadedFiles.length > 0) {
          await strapi.entityService.update(
            "plugin::users-permissions.user",
            id,
            { data: { imgUrl: uploadedFiles[0].id } }
          );
        }
      }

      // Popula y devuelve usuario final
      const arrendatarioFinal = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        id,
        { populate: ["imgUrl", "role", "usuarioPrincipal"] }
      );

      return { data: sanitizeUserOutput(arrendatarioFinal) };
    } catch (err) {
      console.error("Error en updateArrendatario:", err);
      return ctx.badRequest("Error al actualizar el arrendatario");
    }
  },

  /**
   * Obtener todos los arrendatarios del usuario actual (o todos si admin)
   */
  async getMyArrendatarios(ctx: StrapiContext) {
    try {
      const { user } = ctx.state;
      if (!user) return ctx.unauthorized("No autorizado");

      const isAdmin = ["admin", "administrator"].includes(
        user.role?.type?.toLowerCase() || ""
      );

      const filters = isAdmin
        ? {
            $or: [
              { role: { type: "arrendatario" } },
              { role: { name: "Arrendatario" } },
            ],
          }
        : {
            $and: [
              {
                $or: [
                  { role: { type: "arrendatario" } },
                  { role: { name: "Arrendatario" } },
                ],
              },
              { usuarioPrincipal: user.id },
            ],
          };

      // Obtener los arrendatarios con sus relaciones
      const arrendatarios = await strapi.entityService.findMany(
        "plugin::users-permissions.user",
        {
          filters,
          populate: ["imgUrl", "role", "usuarioPrincipal"],
        }
      ) as UserWithRole[];

      // Asegurarse de que cada arrendatario tenga su rol cargado
      const arrendatariosConRol = await Promise.all(
        arrendatarios.map(async (arr) => {
          // Si no tiene el rol cargado, cargarlo
          if (!arr.role) {
            const usuarioCompleto = await strapi.entityService.findOne(
              "plugin::users-permissions.user",
              arr.id,
              { populate: ["role"] }
            ) as UserWithRole;
            return { ...arr, role: usuarioCompleto.role };
          }
          return arr;
        })
      ) as UserWithRole[];

      return {
        data: arrendatariosConRol.map((arr) => sanitizeUserOutput(arr)),
      };
    } catch (err) {
      console.error("Error al obtener arrendatarios:", err);
      return ctx.badRequest("Error al obtener los arrendatarios");
    }
  },

  /**
   * Eliminar un arrendatario
   */
  async deleteArrendatario(ctx: StrapiContext) {
    try {
      const { user } = ctx.state;
      const { id } = ctx.params;
      if (!user) return ctx.unauthorized("No autorizado");

      const arrendatario = (await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        id,
        { populate: ["usuarioPrincipal"] }
      )) as any;
      
      if (!arrendatario) return ctx.notFound("Arrendatario no encontrado");
      
      // Verificar que el usuario actual sea el dueño o admin
      if (arrendatario.usuarioPrincipal?.id !== user.id && user.role?.type !== "administrador") {
        return ctx.forbidden("No tienes permiso para eliminar este arrendatario");
      }

      await strapi.entityService.delete("plugin::users-permissions.user", id);
      return { message: "Arrendatario eliminado correctamente" };
    } catch (err) {
      console.error("Error al eliminar arrendatario:", err);
      return ctx.badRequest("Error al eliminar el arrendatario");
    }
  },
};
