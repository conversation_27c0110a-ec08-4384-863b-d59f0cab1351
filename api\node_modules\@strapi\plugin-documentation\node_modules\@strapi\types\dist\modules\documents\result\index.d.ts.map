{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/modules/documents/result/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,MAAM,iBAAiB,CAAC;AAC/C,OAAO,KAAK,KAAK,IAAI,MAAM,eAAe,CAAC;AAE3C,OAAO,KAAK,KAAK,GAAG,MAAM,cAAc,CAAC;AACzC,OAAO,KAAK,EACV,KAAK,EACL,SAAS,EACT,KAAK,EACL,MAAM,EACN,IAAI,EACJ,EAAE,EACF,OAAO,EACP,UAAU,EACV,EAAE,EACF,WAAW,EACX,OAAO,EACR,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC;AAEjC,KAAK,UAAU,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,QAAQ,EAAE,MAAM,CAAC;IAAC,SAAS,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC;AAEvF,MAAM,MAAM,WAAW,GAAG;IAAE,UAAU,EAAE,MAAM,CAAC;IAAC,EAAE,EAAE,MAAM,CAAA;CAAE,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,CAAC;AAEtF,MAAM,MAAM,MAAM,CAChB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,GAAG,KAAK,IACpE,EAAE,CACJ,SAAS,CAAC,2BAA2B,EACrC,SAAS,CACP,UAAU,EACV,KAAK,CAAC,KAAK,CACT,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAC5C,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAChD,EACD,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CACjD,EACD,WAAW,CACZ,CAAC;AAEF,MAAM,MAAM,QAAQ,CAClB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,GAAG,KAAK,IACpE,EAAE,CACJ,SAAS,CAAC,2BAA2B,EACrC,SAAS,CACP,UAAU,EACV,KAAK,CAAC,KAAK,CACT,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,EAC5C,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAChD,EACD,KAAK,CAAC,KAAK,CACT,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,EAChD,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAC7C,CACF,EACD,WAAW,CACZ,CAAC;AAEF,MAAM,MAAM,eAAe,CACzB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,GAAG,KAAK,IACpE,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;AAE3C,MAAM,MAAM,eAAe,CACzB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,GAAG,UAAU,CAAC,GAAG,KAAK,IACpE;IACF,OAAO,EAAE,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;IACzC,UAAU,EAAE,UAAU,CAAC;CACxB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,SAAS,CACnB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SACL,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,EACrF,SAAS,SACP,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,IAChF,EAAE,CACJ,SAAS,CAAC,2BAA2B,EACrC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,SAAS,MAAM,KAAK,SACrF,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,GAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAC9B,KAAK,EACT,WAAW,CACZ,CAAC;AAEF,KAAK,aAAa,CAChB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,IACvD,OAAO,CACT,UAAU,CACR;IAEE;QACE,EAAE,CACA,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EACnD,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CACtD;QACD,KAAK;KACN;IAED;QACE,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC1D,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;KACvF;IAED;QACE,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACzD,iBAAiB,CACf,UAAU,EACV,IAAI,CACF,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,EACpE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CACzC,CACF;KACF;CACF,CACF,EACD,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAChD,CAAC;AAEF,KAAK,iBAAiB,CACpB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,OAAO,SAAS,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,IACtD,UAAU,CACZ;IACE;QACE,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,MAAM,CAAC,4BAA4B,CAAC,UAAU,CAAC;KAChD;IACD;QAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAAE,OAAO;KAAC;IACtE;QACE,OAAO,CAAC,OAAO,EAAE,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;QACvC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;KACvD;CACF,CACF,CAAC;AAEF,KAAK,eAAe,CAClB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,SAAS,SAAS,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,SAAS,IAC3D,OAAO,CACT,UAAU,CACR;IAEE;QAAC,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAAE,KAAK;KAAC;IAExF;QACE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9D,mBAAmB,CACjB,UAAU,EACV,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAC5D;KACF;IAED;QACE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAC7D,mBAAmB,CACjB,UAAU,EACV,IAAI,CACF,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,EACxE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAC3C,CACF;KACF;IAED;QACE,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC9D,mBAAmB,CACjB,UAAU,EAEV,IAAI,CAAC,MAAM,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAClE;KACF;CACF,CACF,EACD,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAC7C,CAAC;AAEF,KAAK,wBAAwB,CAC3B,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,SAAS,SAAS,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAC1D,IAAI,CACN,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAC7C,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAC7C,CAAC;AAEF,KAAK,mBAAmB,CACtB,UAAU,SAAS,GAAG,CAAC,MAAM,EAC7B,SAAS,SAAS,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAC1D,UAAU,CACZ;IACE;QACE,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,SAAS,CAAC;QACxD,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC;KAC7C;IACD;QACE,OAAO,CAAC,SAAS,EAAE,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;QACzC,wBAAwB,CACtB,UAAU,EACV,IAAI,CACF,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,EACxD,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAC3C,CACF;KACF;IACD;QAAC,OAAO,CAAC,SAAS,EAAE,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;QAAE,wBAAwB,CAAC,UAAU,EAAE,SAAS,CAAC;KAAC;CAC7F,EACD,SAAS,CACV,CAAC"}