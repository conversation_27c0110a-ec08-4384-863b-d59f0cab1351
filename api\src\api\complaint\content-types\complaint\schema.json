{"kind": "collectionType", "collectionName": "complaints", "info": {"singularName": "complaint", "pluralName": "complaints", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": "Collection of user complaints and requests"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "minLength": 3, "maxLength": 100}, "description": {"type": "text", "required": true, "minLength": 10}, "category": {"type": "enumeration", "enum": ["maintenance", "security", "noise", "pets", "common_areas", "other", "MANTENIMIENTO", "SEGURIDAD", "CONVIVENCIA", "OTRO"], "required": true}, "status": {"type": "enumeration", "enum": ["pending", "in_progress", "resolved", "rejected"], "default": "pending", "required": true}, "date": {"type": "date", "required": true}, "time": {"type": "time", "required": true}, "response": {"type": "text"}, "respondedAt": {"type": "datetime"}, "attachments": {"type": "media", "multiple": true, "allowedTypes": ["images", "files", "videos"]}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "complaints"}, "respondedBy": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "template": {"type": "relation", "relation": "manyToOne", "target": "api::complaint-template.complaint-template", "inversedBy": "complaints"}}}