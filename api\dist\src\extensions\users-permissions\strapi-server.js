// Extensión de plugin para personalizar el controlador de usuarios (con fix de firma/signature)
module.exports = (plugin) => {
    // Extender el controlador de usuarios
    const sanitizeOutput = (user) => {
        const { password, resetPasswordToken, confirmationToken, ...sanitizedUser } = user;
        // Normalizar el campo phone
        if (sanitizedUser.phone && Object.keys(sanitizedUser.phone).length === 0) {
            sanitizedUser.phone = null;
        }
        return sanitizedUser;
    };
    // Extender el controlador de autenticación
    const originalCallback = plugin.controllers.auth.callback;
    plugin.controllers.auth.callback = async (ctx) => {
        await originalCallback(ctx);
        if (ctx.body && ctx.body.user) {
            const userWithImg = await strapi
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: ctx.body.user.id },
                select: [
                    "id",
                    "documentId",
                    "username",
                    "email",
                    "firstName",
                    "lastName",
                    "address",
                    "coefficient",
                    "phone",
                    "status",
                    "createdAt",
                    "updatedAt",
                    "publishedAt",
                    "locale",
                ],
                populate: {
                    imgUrl: {
                        fields: [
                            "name",
                            "width",
                            "height",
                            "formats",
                            "url",
                            "alternativeText",
                        ],
                    },
                    role: true,
                },
            });
            ctx.body.user = sanitizeOutput(userWithImg);
        }
    };
    // Sobrescribir el controlador `me`
    plugin.controllers.user.me = async (ctx) => {
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized();
        }
        const userWithImg = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
            where: { id: user.id },
            select: [
                "id",
                "documentId",
                "username",
                "email",
                "firstName",
                "lastName",
                "address",
                "profession",
                "coefficient",
                "phone",
                "status",
                "createdAt",
                "updatedAt",
                "publishedAt",
                "locale",
            ],
            populate: {
                imgUrl: {
                    fields: [
                        "name",
                        "width",
                        "height",
                        "formats",
                        "url",
                        "alternativeText",
                    ],
                },
                signature: {
                    fields: [
                        "name",
                        "width",
                        "height",
                        "formats",
                        "url",
                        "alternativeText",
                    ],
                },
                role: true,
            },
        });
        ctx.body = sanitizeOutput(userWithImg);
    };
    // Extender el controlador update para manejar campos personalizados y notificaciones
    const originalUpdate = plugin.controllers.user.update;
    plugin.controllers.user.update = async (ctx) => {
        var _a, _b, _c, _d, _e;
        const { id } = ctx.params;
        let { body } = ctx.request;
        const { files } = ctx.request;
        const adminUser = ctx.state.user;
        // 1. Obtener el usuario actual
        const previousUser = (await strapi.entityService.findOne("plugin::users-permissions.user", id, { populate: ["role", "signature"] })); // 👈 CASTEA AQUÍ
        // 2. Manejar FormData
        if ((_a = ctx.request.type) === null || _a === void 0 ? void 0 : _a.includes("multipart/form-data")) {
            if (body.data && typeof body.data === "string") {
                try {
                    const parsedData = JSON.parse(body.data);
                    body = { ...body, ...parsedData };
                    delete body.data;
                }
                catch (err) {
                    console.error("Error al parsear datos del formulario:", err);
                }
            }
            // Procesar archivo de firma si se envió
            if (files === null || files === void 0 ? void 0 : files.signature) {
                try {
                    if ((_b = previousUser.signature) === null || _b === void 0 ? void 0 : _b.id) {
                        await strapi.plugins["upload"].services.upload.remove(previousUser.signature);
                    }
                    const uploadedFiles = await strapi.plugins["upload"].services.upload.upload({
                        data: {
                            refId: id,
                            ref: "plugin::users-permissions.user",
                            field: "signature",
                        },
                        files: files.signature,
                    });
                    if (uploadedFiles && uploadedFiles.length > 0) {
                        body.signature = uploadedFiles[0].id;
                    }
                    else {
                        body.signature = null;
                    }
                }
                catch (error) {
                    console.error("Error al subir la firma:", error);
                    body.signature = ((_c = previousUser.signature) === null || _c === void 0 ? void 0 : _c.id) || null;
                }
            }
            else if (body.signature === "null") {
                if ((_d = previousUser.signature) === null || _d === void 0 ? void 0 : _d.id) {
                    await strapi.plugins["upload"].services.upload.remove(previousUser.signature);
                }
                body.signature = null;
            }
        }
        else if (body.data && typeof body.data === "string") {
            try {
                body = { ...body, ...JSON.parse(body.data) };
                delete body.data;
            }
            catch (err) {
                console.error("Error al parsear JSON:", err);
            }
        }
        // 3. Asegurarse de que la profesión se maneje correctamente
        if (body.profession === undefined) {
            body.profession = previousUser.profession;
        }
        // 4. Mostrar datos finales para depuración
        console.log(JSON.stringify({
            ...body,
            signature: body.signature ? `[Media ID: ${body.signature}]` : null,
        }, null, 2));
        // 5. Llamar al controlador original
        try {
            ctx.request.body = body;
            await originalUpdate(ctx);
        }
        catch (error) {
            console.error("Error durante la actualización:", error);
            throw error;
        }
        // 6. Obtener y devolver el usuario actualizado
        const updatedUser = await strapi.entityService.findOne("plugin::users-permissions.user", id, {
            populate: {
                role: true,
                imgUrl: true,
                signature: {
                    fields: ["id", "name", "url", "alternativeText"],
                },
            },
        });
        // Si el que actualiza es un administrador y es diferente al usuario actualizado
        if (adminUser &&
            ((_e = adminUser.role) === null || _e === void 0 ? void 0 : _e.type) === "admin" &&
            adminUser.id !== updatedUser.id) {
            const changes = [];
            if (previousUser.status !== updatedUser.status) {
                changes.push(`estado de pago a ${updatedUser.status ? "Al dia" : "moroso"}`);
            }
            if (previousUser.coefficient !== updatedUser.coefficient) {
                changes.push(`coeficiente a ${updatedUser.coefficient}`);
            }
            if (previousUser.address !== updatedUser.address) {
                changes.push(`dirección a ${updatedUser.address}`);
            }
            if (changes.length > 0) {
                try {
                    const notification = await strapi.entityService.create("api::notification.notification", {
                        data: {
                            title: "Actualización de tu perfil",
                            message: `Un administrador ha actualizado tu ${changes.join(", ")}`,
                            type: "info",
                            read: false,
                            user: updatedUser.id,
                            publishedAt: new Date(),
                            data: {
                                changes,
                                updatedFields: {
                                    status: updatedUser.status,
                                    coefficient: updatedUser.coefficient,
                                    address: updatedUser.address,
                                },
                            },
                        },
                    });
                    // Enviar notificación en tiempo real
                    if (strapi.websocket) {
                        strapi.websocket.sendNotificationToUser(updatedUser.id, {
                            id: notification.id,
                            title: "Actualización de tu perfil",
                            message: `Un administrador ha actualizado tu ${changes.join(", ")}`,
                            type: "info",
                            read: false,
                            createdAt: notification.createdAt,
                            data: {
                                changes,
                                updatedFields: {
                                    status: updatedUser.status,
                                    coefficient: updatedUser.coefficient,
                                    address: updatedUser.address,
                                },
                            },
                        });
                    }
                }
                catch (error) {
                    console.error("Error al enviar notificación:", error);
                }
            }
        }
        ctx.body = sanitizeOutput(updatedUser);
    };
    // Registrar rutas personalizadas de activity-log para que aparezcan en el panel de administración
    const bootstrap = plugin.bootstrap;
    plugin.bootstrap = async (ctx) => {
        // Ejecutar el bootstrap original
        await bootstrap(ctx);
        // Registrar rutas personalizadas de activity-log
        const actionsToRegister = [
            {
                section: "activity-log",
                displayName: "Obtener actividades de un usuario",
                uid: "api::activity-log.findByUser",
                subCategory: "activity-log",
                pluginName: "api",
            },
            {
                section: "activity-log",
                displayName: "Obtener actividades de arrendatarios",
                uid: "api::activity-log.findMyTenantsActivities",
                subCategory: "activity-log",
                pluginName: "api",
            },
            {
                section: "activity-log",
                displayName: "Obtener resumen de actividades",
                uid: "api::activity-log.getActivitySummary",
                subCategory: "activity-log",
                pluginName: "api",
            },
        ];
        // Registrar cada acción en el sistema de permisos
        // En Strapi v5, la estructura de servicios puede haber cambiado
        // Verificamos si el servicio existe antes de usarlo
        try {
            const permissionsService = plugin.services.userspermissions;
            if (permissionsService &&
                typeof permissionsService.registerPermission === "function") {
                actionsToRegister.forEach((action) => {
                    permissionsService.registerPermission(action);
                });
            }
            else {
                console.warn("El servicio userspermissions o el método registerPermission no está disponible en Strapi v5");
                // Alternativa: intentar registrar permisos de otra manera si es necesario
            }
        }
        catch (error) {
            console.warn("Error al registrar permisos personalizados:", error);
            // No interrumpir el bootstrap por este error
        }
    };
    return plugin;
};
