'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const ListPage = require('./ListPage-GRLtmRuy.js');
const useLicenseLimitNotification = require('./useLicenseLimitNotification-eau4ja6h.js');

const UserListPageEE = () => {
  useLicenseLimitNotification.useLicenseLimitNotification();
  return /* @__PURE__ */ jsxRuntime.jsx(ListPage.ListPageCE, {});
};

exports.UserListPageEE = UserListPageEE;
//# sourceMappingURL=ListPage-UEsKYDPH.js.map
