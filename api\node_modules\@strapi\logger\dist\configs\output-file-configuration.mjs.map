{"version": 3, "file": "output-file-configuration.mjs", "sources": ["../../src/configs/output-file-configuration.ts"], "sourcesContent": ["import { transports, LoggerOptions } from 'winston';\n\nimport { LEVEL_LABEL, LEVELS } from '../constants';\nimport { prettyPrint, excludeColors } from '../formats';\n\nexport default (\n  filename: string,\n  fileTransportOptions: transports.FileTransportOptions = {}\n): LoggerOptions => {\n  return {\n    level: LEVEL_LABEL,\n    levels: LEVELS,\n    format: prettyPrint(),\n    transports: [\n      new transports.Console(),\n      new transports.File({\n        level: 'error',\n        filename,\n        format: excludeColors,\n        ...fileTransportOptions,\n      }),\n    ],\n  };\n};\n"], "names": ["filename", "fileTransportOptions", "level", "LEVEL_LABEL", "levels", "LEVELS", "format", "<PERSON><PERSON><PERSON><PERSON>", "transports", "<PERSON><PERSON><PERSON>", "File", "excludeColors"], "mappings": ";;;;;;AAKA,8BAAe,CAAA,CACbA,QACAC,EAAAA,oBAAAA,GAAwD,EAAE,GAAA;IAE1D,OAAO;QACLC,KAAOC,EAAAA,WAAAA;QACPC,MAAQC,EAAAA,MAAAA;QACRC,MAAQC,EAAAA,WAAAA,EAAAA;QACRC,UAAY,EAAA;AACV,YAAA,IAAIA,WAAWC,OAAO,EAAA;YACtB,IAAID,UAAAA,CAAWE,IAAI,CAAC;gBAClBR,KAAO,EAAA,OAAA;AACPF,gBAAAA,QAAAA;gBACAM,MAAQK,EAAAA,aAAAA;AACR,gBAAA,GAAGV;AACL,aAAA;AACD;AACH,KAAA;AACF,CAAA;;;;"}