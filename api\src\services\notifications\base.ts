/**
 * Base notification service
 */

import { Strapi } from '@strapi/strapi';

export interface NotificationPayload {
  type: string;
  title: string;
  message: string;
  data?: any;
}

export interface NotificationConfig {
  enabled: boolean;
  channels: {
    email: boolean;
    whatsapp: boolean;
  };
}

export interface Recipient {
  id: number;
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
}

export default ({ strapi }: { strapi: Strapi }) => ({
  async send(recipient: Recipient, notification: NotificationPayload, config: NotificationConfig) {
    try {
      if (!config?.enabled) {
        return;
      }

      const { channels } = config;

      // 1. Crear notificación interna en Strapi
      await strapi.service('api::notification.notification').create({
        data: {
          title: notification.title,
          type: notification.type,
          message: notification.message,
          user: recipient.id,
          data: notification.data
        }
      });

      // 2. Enviar notificación por SSE
      await strapi.plugin('sse').service('sse').send(
        recipient.id,
        notification.type,
        notification
      );

      // 3. Si el email está habilitado, enviar correo
      if (channels.email && recipient.email) {
        await strapi.plugins['email'].services.email.send({
          to: recipient.email,
          subject: notification.title,
          text: notification.message,
          html: this.getEmailTemplate(notification, recipient)
        });
      }

      // 4. Enviar WhatsApp si está habilitado
      if (channels.whatsapp && recipient.phone) {
        try {
          await strapi.service('api::whatsapp.whatsapp').send(
            recipient.phone,
            `${notification.title}\\n\\n${notification.message}`
          );
        } catch (error) {
          console.error('Error sending WhatsApp notification:', error);
        }
      }

    } catch (error) {
      console.error('Error sending notification:', error);
    }
  },

  getEmailTemplate(notification: NotificationPayload, recipient: Recipient): string {
    return `
      <h2>Estimado(a) ${recipient.firstName} ${recipient.lastName}</h2>
      <p>${notification.message}</p>
      <p>Por favor, ingrese a la plataforma para más detalles.</p>
    `;
  }
});
