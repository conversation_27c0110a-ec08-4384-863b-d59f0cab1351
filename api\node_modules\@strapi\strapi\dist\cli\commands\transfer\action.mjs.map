{"version": 3, "file": "action.mjs", "sources": ["../../../../src/cli/commands/transfer/action.ts"], "sourcesContent": ["import { isObject } from 'lodash/fp';\nimport { engine as engineDataTransfer, strapi as strapiDataTransfer } from '@strapi/data-transfer';\n\nimport {\n  buildTransferTable,\n  createStrapiInstance,\n  DEFAULT_IGNORED_CONTENT_TYPES,\n  formatDiagnostic,\n  loadersFactory,\n  exitMessageText,\n  abortTransfer,\n  getTransferTelemetryPayload,\n  setSignalHandler,\n  getDiffHandler,\n  getAssetsBackupHandler,\n  parseRestoreFromOptions,\n} from '../../utils/data-transfer';\nimport { exitWith } from '../../utils/helpers';\n\nconst { createTransferEngine } = engineDataTransfer;\nconst {\n  providers: {\n    createRemoteStrapiDestinationProvider,\n    createLocalStrapiSourceProvider,\n    createLocalStrapiDestinationProvider,\n    createRemoteStrapiSourceProvider,\n  },\n} = strapiDataTransfer;\n\ninterface CmdOptions {\n  from?: URL;\n  fromToken: string;\n  to: URL;\n  toToken: string;\n  only?: (keyof engineDataTransfer.TransferGroupFilter)[];\n  exclude?: (keyof engineDataTransfer.TransferGroupFilter)[];\n  throttle?: number;\n  force?: boolean;\n}\n/**\n * Transfer command.\n *\n * Transfers data between local Strapi and remote Strapi instances\n */\nexport default async (opts: CmdOptions) => {\n  // Validate inputs from Commander\n  if (!isObject(opts)) {\n    exitWith(1, 'Could not parse command arguments');\n  }\n\n  if (!(opts.from || opts.to) || (opts.from && opts.to)) {\n    exitWith(1, 'Exactly one source (from) or destination (to) option must be provided');\n  }\n\n  const strapi = await createStrapiInstance();\n  let source;\n  let destination;\n\n  // if no URL provided, use local Strapi\n  if (!opts.from) {\n    source = createLocalStrapiSourceProvider({\n      getStrapi: () => strapi,\n    });\n  }\n  // if URL provided, set up a remote source provider\n  else {\n    if (!opts.fromToken) {\n      exitWith(1, 'Missing token for remote destination');\n    }\n\n    source = createRemoteStrapiSourceProvider({\n      getStrapi: () => strapi,\n      url: opts.from,\n      auth: {\n        type: 'token',\n        token: opts.fromToken,\n      },\n    });\n  }\n\n  // if no URL provided, use local Strapi\n  if (!opts.to) {\n    destination = createLocalStrapiDestinationProvider({\n      getStrapi: () => strapi,\n      strategy: 'restore',\n      restore: parseRestoreFromOptions(opts),\n    });\n  }\n  // if URL provided, set up a remote destination provider\n  else {\n    if (!opts.toToken) {\n      exitWith(1, 'Missing token for remote destination');\n    }\n\n    destination = createRemoteStrapiDestinationProvider({\n      url: opts.to,\n      auth: {\n        type: 'token',\n        token: opts.toToken,\n      },\n      strategy: 'restore',\n      restore: parseRestoreFromOptions(opts),\n    });\n  }\n\n  if (!source || !destination) {\n    exitWith(1, 'Could not create providers');\n  }\n\n  const engine = createTransferEngine(source, destination, {\n    versionStrategy: 'exact',\n    schemaStrategy: 'strict',\n    exclude: opts.exclude,\n    only: opts.only,\n    throttle: opts.throttle,\n    transforms: {\n      links: [\n        {\n          filter(link) {\n            return (\n              !DEFAULT_IGNORED_CONTENT_TYPES.includes(link.left.type) &&\n              !DEFAULT_IGNORED_CONTENT_TYPES.includes(link.right.type)\n            );\n          },\n        },\n      ],\n      entities: [\n        {\n          filter(entity) {\n            return !DEFAULT_IGNORED_CONTENT_TYPES.includes(entity.type);\n          },\n        },\n      ],\n    },\n  });\n\n  engine.diagnostics.onDiagnostic(formatDiagnostic('transfer'));\n\n  const progress = engine.progress.stream;\n\n  const { updateLoader } = loadersFactory();\n\n  engine.onSchemaDiff(getDiffHandler(engine, { force: opts.force, action: 'transfer' }));\n\n  engine.addErrorHandler(\n    'ASSETS_DIRECTORY_ERR',\n    getAssetsBackupHandler(engine, { force: opts.force, action: 'transfer' })\n  );\n\n  progress.on(`stage::start`, ({ stage, data }) => {\n    updateLoader(stage, data).start();\n  });\n\n  progress.on('stage::finish', ({ stage, data }) => {\n    updateLoader(stage, data).succeed();\n  });\n\n  progress.on('stage::progress', ({ stage, data }) => {\n    updateLoader(stage, data);\n  });\n\n  progress.on('stage::error', ({ stage, data }) => {\n    updateLoader(stage, data).fail();\n  });\n\n  progress.on('transfer::start', async () => {\n    console.log(`Starting transfer...`);\n\n    await strapi.telemetry.send('didDEITSProcessStart', getTransferTelemetryPayload(engine));\n  });\n\n  let results: Awaited<ReturnType<typeof engine.transfer>>;\n  try {\n    // Abort transfer if user interrupts process\n    setSignalHandler(() => abortTransfer({ engine, strapi }));\n\n    results = await engine.transfer();\n\n    // Note: we need to await telemetry or else the process ends before it is sent\n    await strapi.telemetry.send('didDEITSProcessFinish', getTransferTelemetryPayload(engine));\n\n    try {\n      const table = buildTransferTable(results.engine);\n      console.log(table?.toString());\n    } catch (e) {\n      console.error('There was an error displaying the results of the transfer.');\n    }\n\n    exitWith(0, exitMessageText('transfer'));\n  } catch (e) {\n    await strapi.telemetry.send('didDEITSProcessFail', getTransferTelemetryPayload(engine));\n    exitWith(1, exitMessageText('transfer', true));\n  }\n};\n"], "names": ["engineDataTransfer", "strapiDataTransfer", "strapi", "engine"], "mappings": ";;;;AAmBA,MAAM,EAAE,qBAAyB,IAAAA;AACjC,MAAM;AAAA,EACJ,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,IAAIC;AAiBJ,MAAe,SAAA,OAAO,SAAqB;AAErC,MAAA,CAAC,SAAS,IAAI,GAAG;AACnB,aAAS,GAAG,mCAAmC;AAAA,EACjD;AAEI,MAAA,EAAE,KAAK,QAAQ,KAAK,OAAQ,KAAK,QAAQ,KAAK,IAAK;AACrD,aAAS,GAAG,uEAAuE;AAAA,EACrF;AAEM,QAAAC,UAAS,MAAM;AACjB,MAAA;AACA,MAAA;AAGA,MAAA,CAAC,KAAK,MAAM;AACd,aAAS,gCAAgC;AAAA,MACvC,WAAW,MAAMA;AAAA,IAAA,CAClB;AAAA,EAAA,OAGE;AACC,QAAA,CAAC,KAAK,WAAW;AACnB,eAAS,GAAG,sCAAsC;AAAA,IACpD;AAEA,aAAS,iCAAiC;AAAA,MACxC,WAAW,MAAMA;AAAA,MACjB,KAAK,KAAK;AAAA,MACV,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,KAAK;AAAA,MACd;AAAA,IAAA,CACD;AAAA,EACH;AAGI,MAAA,CAAC,KAAK,IAAI;AACZ,kBAAc,qCAAqC;AAAA,MACjD,WAAW,MAAMA;AAAA,MACjB,UAAU;AAAA,MACV,SAAS,wBAAwB,IAAI;AAAA,IAAA,CACtC;AAAA,EAAA,OAGE;AACC,QAAA,CAAC,KAAK,SAAS;AACjB,eAAS,GAAG,sCAAsC;AAAA,IACpD;AAEA,kBAAc,sCAAsC;AAAA,MAClD,KAAK,KAAK;AAAA,MACV,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO,KAAK;AAAA,MACd;AAAA,MACA,UAAU;AAAA,MACV,SAAS,wBAAwB,IAAI;AAAA,IAAA,CACtC;AAAA,EACH;AAEI,MAAA,CAAC,UAAU,CAAC,aAAa;AAC3B,aAAS,GAAG,4BAA4B;AAAA,EAC1C;AAEM,QAAAC,UAAS,qBAAqB,QAAQ,aAAa;AAAA,IACvD,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,SAAS,KAAK;AAAA,IACd,MAAM,KAAK;AAAA,IACX,UAAU,KAAK;AAAA,IACf,YAAY;AAAA,MACV,OAAO;AAAA,QACL;AAAA,UACE,OAAO,MAAM;AACX,mBACE,CAAC,8BAA8B,SAAS,KAAK,KAAK,IAAI,KACtD,CAAC,8BAA8B,SAAS,KAAK,MAAM,IAAI;AAAA,UAE3D;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,UACE,OAAO,QAAQ;AACb,mBAAO,CAAC,8BAA8B,SAAS,OAAO,IAAI;AAAA,UAC5D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EAAA,CACD;AAED,EAAAA,QAAO,YAAY,aAAa,iBAAiB,UAAU,CAAC;AAEtD,QAAA,WAAWA,QAAO,SAAS;AAE3B,QAAA,EAAE,iBAAiB;AAElB,EAAAA,QAAA,aAAa,eAAeA,SAAQ,EAAE,OAAO,KAAK,OAAO,QAAQ,WAAW,CAAC,CAAC;AAE9E,EAAAA,QAAA;AAAA,IACL;AAAA,IACA,uBAAuBA,SAAQ,EAAE,OAAO,KAAK,OAAO,QAAQ,YAAY;AAAA,EAAA;AAG1E,WAAS,GAAG,gBAAgB,CAAC,EAAE,OAAO,WAAW;AAClC,iBAAA,OAAO,IAAI,EAAE,MAAM;AAAA,EAAA,CACjC;AAED,WAAS,GAAG,iBAAiB,CAAC,EAAE,OAAO,WAAW;AACnC,iBAAA,OAAO,IAAI,EAAE,QAAQ;AAAA,EAAA,CACnC;AAED,WAAS,GAAG,mBAAmB,CAAC,EAAE,OAAO,WAAW;AAClD,iBAAa,OAAO,IAAI;AAAA,EAAA,CACzB;AAED,WAAS,GAAG,gBAAgB,CAAC,EAAE,OAAO,WAAW;AAClC,iBAAA,OAAO,IAAI,EAAE,KAAK;AAAA,EAAA,CAChC;AAEQ,WAAA,GAAG,mBAAmB,YAAY;AACzC,YAAQ,IAAI,sBAAsB;AAElC,UAAMD,QAAO,UAAU,KAAK,wBAAwB,4BAA4BC,OAAM,CAAC;AAAA,EAAA,CACxF;AAEG,MAAA;AACA,MAAA;AAEF,qBAAiB,MAAM,cAAc,EAAE,QAAAA,SAAQ,QAAAD,QAAA,CAAQ,CAAC;AAE9C,cAAA,MAAMC,QAAO;AAGvB,UAAMD,QAAO,UAAU,KAAK,yBAAyB,4BAA4BC,OAAM,CAAC;AAEpF,QAAA;AACI,YAAA,QAAQ,mBAAmB,QAAQ,MAAM;AACvC,cAAA,IAAI,OAAO,SAAU,CAAA;AAAA,aACtB,GAAG;AACV,cAAQ,MAAM,4DAA4D;AAAA,IAC5E;AAES,aAAA,GAAG,gBAAgB,UAAU,CAAC;AAAA,WAChC,GAAG;AACV,UAAMD,QAAO,UAAU,KAAK,uBAAuB,4BAA4BC,OAAM,CAAC;AACtF,aAAS,GAAG,gBAAgB,YAAY,IAAI,CAAC;AAAA,EAC/C;AACF;"}