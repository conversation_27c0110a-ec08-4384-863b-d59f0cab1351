export enum NotificationType {
  // Notificaciones generales
  INFO = "info",
  SUCCESS = "success",
  WARNING = "warning",
  ERROR = "error",

  // Notificaciones de huéspedes
  GUEST_ARRIVAL = "guest_arrival",
  GUEST_CONFIRMATION = "guest_confirmation",
  GUEST_CHECKOUT = "guest_checkout",
  GUEST_LATE_ARRIVAL = "guest_late_arrival",

  // Notificaciones de reservas
  NEW_RESERVATION = "new_reservation",
  RESERVATION_CANCELLED = "reservation_cancelled",
  RESERVATION_MODIFIED = "reservation_modified",
  PAYMENT_RECEIVED = "payment_received",

  // Notificaciones del sistema
  SYSTEM_MAINTENANCE = "system_maintenance",
  SYSTEM_UPDATE = "system_update",
  BACKUP_COMPLETED = "backup_completed",

  // Notificaciones de staff
  STAFF_MESSAGE = "staff_message",
  SHIFT_CHANGE = "shift_change",
  TASK_ASSIGNED = "task_assigned",

  // Alertas críticas
  SECURITY_ALERT = "security_alert",
  EMERGENCY = "emergency",
  SYSTEM_ERROR = "system_error",
}

export enum NotificationPriority {
  LOW = "low",
  NORMAL = "normal",
  HIGH = "high",
  CRITICAL = "critical",
}

export interface Notification {
  id: number | string;
  documentId?: string;
  title: string;
  message?: string;
  description?: string;
  type: NotificationType | string;
  priority?: NotificationPriority;
  read: boolean;
  createdAt: string;
  updatedAt?: string;
  publishedAt?: string;
  locale?: string | null;
  data?: any;
  actionUrl?: string;
  actionLabel?: string;
  expiresAt?: string;
  user?: {
    id: number;
    documentId?: string;
    firstName?: string;
    lastName?: string;
    address?: string;
    username?: string;
  };
}

export interface NotificationsResponse {
  data: Notification[];
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

// Configuraciones de notificaciones por tipo
export const NotificationConfig = {
  [NotificationType.GUEST_ARRIVAL]: {
    icon: "🏨",
    color: "#1890ff",
    antdType: "info" as const,
    sound: true,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.GUEST_CONFIRMATION]: {
    icon: "✅",
    color: "#52c41a",
    antdType: "success" as const,
    sound: true,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.GUEST_CHECKOUT]: {
    icon: "🚪",
    color: "#fa8c16",
    antdType: "warning" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.NEW_RESERVATION]: {
    icon: "📅",
    color: "#722ed1",
    antdType: "info" as const,
    sound: true,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.PAYMENT_RECEIVED]: {
    icon: "💰",
    color: "#52c41a",
    antdType: "success" as const,
    sound: true,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SECURITY_ALERT]: {
    icon: "🚨",
    color: "#ff4d4f",
    antdType: "error" as const,
    sound: true,
    persistent: true,
    priority: NotificationPriority.CRITICAL,
  },
  [NotificationType.EMERGENCY]: {
    icon: "🆘",
    color: "#ff4d4f",
    antdType: "error" as const,
    sound: true,
    persistent: true,
    priority: NotificationPriority.CRITICAL,
  },
  [NotificationType.SYSTEM_ERROR]: {
    icon: "⚠️",
    color: "#ff4d4f",
    antdType: "error" as const,
    sound: false,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.STAFF_MESSAGE]: {
    icon: "💬",
    color: "#1890ff",
    antdType: "info" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SYSTEM_MAINTENANCE]: {
    icon: "🔧",
    color: "#faad14",
    antdType: "warning" as const,
    sound: false,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.INFO]: {
    icon: "ℹ️",
    color: "#1890ff",
    antdType: "info" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SUCCESS]: {
    icon: "✅",
    color: "#52c41a",
    antdType: "success" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.WARNING]: {
    icon: "⚠️",
    color: "#faad14",
    antdType: "warning" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.ERROR]: {
    icon: "❌",
    color: "#ff4d4f",
    antdType: "error" as const,
    sound: false,
    persistent: false,
    priority: NotificationPriority.HIGH,
  },
};

// Funciones helper
export const getNotificationConfig = (type: NotificationType | string) => {
  return (
    NotificationConfig[type as NotificationType] ||
    NotificationConfig[NotificationType.INFO]
  );
};

export const getPriorityColor = (priority: NotificationPriority) => {
  switch (priority) {
    case NotificationPriority.CRITICAL:
      return "#ff4d4f";
    case NotificationPriority.HIGH:
      return "#fa8c16";
    case NotificationPriority.NORMAL:
      return "#1890ff";
    case NotificationPriority.LOW:
      return "#52c41a";
    default:
      return "#1890ff";
  }
};
