// Definición de tipo para Strapi ya que no está exportado directamente
type Strapi = any;
import fs from "fs";
import path from "path";

// Función para forzar la salida de logs
const log = (...args: any[]) => {
  const message = args
    .map((arg) =>
      typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
    )
    .join(" ");
  process.stdout.write(`[${new Date().toISOString()}] ${message}\n`);
};

// Rutas de los archivos de plantilla
const TEMPLATES_DIR = path.join(process.cwd(), "config", "email-templates");
const RESET_PASSWORD_PATH = path.join(TEMPLATES_DIR, "reset-password.html");
const EMAIL_CONFIRMATION_PATH = path.join(
  TEMPLATES_DIR,
  "email-confirmation.html"
);
const PAYMENT_VERIFIED_PATH = path.join(TEMPLATES_DIR, "payment-verified.html");
const PAYMENT_REJECTED_PATH = path.join(TEMPLATES_DIR, "payment-rejected.html");

// Función para leer el contenido de una plantilla HTML
const readTemplateContent = (filePath: string): string => {
  try {
    return fs.readFileSync(filePath, "utf8");
  } catch (error) {
    log(`❌ Error al leer la plantilla ${filePath}:`, error);
    return "";
  }
};

// Función para extraer el contenido del cuerpo del mensaje de una plantilla HTML
const extractMessageBody = (htmlContent: string): string => {
  // Extraer solo el contenido relevante para el mensaje
  // En este caso, vamos a extraer todo lo que está dentro del body
  const bodyMatch = htmlContent.match(/<body>([\s\S]*?)<\/body>/i);
  if (bodyMatch && bodyMatch[1]) {
    // Simplificar el contenido para la interfaz de Strapi
    let content = bodyMatch[1].trim();
    
    // Reemplazar las variables de plantilla al formato que espera Strapi
    content = content.replace(/<%=\s*user\.username\s*%>/g, "<%= user.username %>");
    content = content.replace(/<%=\s*URL\s*%>/g, "<%= URL %>");
    content = content.replace(/<%=\s*TOKEN\s*%>/g, "<%= TOKEN %>");
    content = content.replace(/<%=\s*CODE\s*%>/g, "<%= CODE %>");
    
    // Simplificar a formato párrafo para la interfaz de Strapi
    content = content.replace(/<div[^>]*>/g, "<p>");
    content = content.replace(/<\/div>/g, "</p>");
    
    return content;
  }
  return "";
};

// Función principal para sincronizar las plantillas
const syncEmailTemplates = async ({ strapi }: { strapi: Strapi }) => {
  try {
    log("🔄 INICIANDO: Sincronización de plantillas de correo con la interfaz de Strapi");

    // Verificar que las plantillas existan
    if (!fs.existsSync(RESET_PASSWORD_PATH) || !fs.existsSync(EMAIL_CONFIRMATION_PATH)) {
      log("❌ Error: Las plantillas básicas HTML no existen en el directorio config/email-templates/");
      log("📋 Ejecuta primero el script setup-email-templates.js para crear las plantillas");
      return;
    }
    
    // Verificar las plantillas de pagos
    const paymentVerifiedExists = fs.existsSync(PAYMENT_VERIFIED_PATH);
    const paymentRejectedExists = fs.existsSync(PAYMENT_REJECTED_PATH);
    
    if (!paymentVerifiedExists) {
      log("⚠️ Advertencia: La plantilla de pago verificado no existe");
    }
    
    if (!paymentRejectedExists) {
      log("⚠️ Advertencia: La plantilla de pago rechazado no existe");
    }

    // Leer las plantillas
    const resetPasswordHtml = readTemplateContent(RESET_PASSWORD_PATH);
    const emailConfirmationHtml = readTemplateContent(EMAIL_CONFIRMATION_PATH);
    
    // Leer las plantillas de pagos si existen
    const paymentVerifiedHtml = paymentVerifiedExists ? readTemplateContent(PAYMENT_VERIFIED_PATH) : "";
    const paymentRejectedHtml = paymentRejectedExists ? readTemplateContent(PAYMENT_REJECTED_PATH) : "";

    // Extraer el contenido del mensaje
    const resetPasswordMessage = extractMessageBody(resetPasswordHtml);
    const emailConfirmationMessage = extractMessageBody(emailConfirmationHtml);
    const paymentVerifiedMessage = paymentVerifiedExists ? extractMessageBody(paymentVerifiedHtml) : "";
    const paymentRejectedMessage = paymentRejectedExists ? extractMessageBody(paymentRejectedHtml) : "";

    // Actualizar las plantillas en la base de datos de Strapi
    log("📝 Actualizando plantilla de restablecimiento de contraseña en Strapi...");
    try {
      // En Strapi v5, usamos el servicio de email directamente
      await strapi.plugin("email").service("email").updateTemplate(
        "reset-password",
        {
          subject: "Restablecer contraseña",
          message: resetPasswordMessage,
        }
      );
      log("✅ Plantilla de restablecimiento de contraseña actualizada correctamente");
    } catch (error) {
      log("❌ Error al actualizar plantilla de restablecimiento de contraseña:", error.message);
    }

    log("📝 Actualizando plantilla de confirmación de correo en Strapi...");
    try {
      // En Strapi v5, usamos el servicio de email directamente
      await strapi.plugin("email").service("email").updateTemplate(
        "email-confirmation",
        {
          subject: "Confirmación de cuenta",
          message: emailConfirmationMessage,
        }
      );
      log("✅ Plantilla de confirmación de correo actualizada correctamente");
    } catch (error) {
      log("❌ Error al actualizar plantilla de confirmación de correo:", error.message);
    }
    
    // Actualizar plantilla de pago verificado si existe
    if (paymentVerifiedExists) {
      log("📝 Actualizando plantilla de pago verificado en Strapi...");
      try {
        await strapi.plugin("email").service("email").updateTemplate(
          "payment-verified",
          {
            subject: "Pago Verificado - CCPALCAN",
            message: paymentVerifiedMessage,
          }
        );
        log("✅ Plantilla de pago verificado actualizada correctamente");
      } catch (error) {
        log("❌ Error al actualizar plantilla de pago verificado:", error.message);
      }
    }
    
    // Actualizar plantilla de pago rechazado si existe
    if (paymentRejectedExists) {
      log("📝 Actualizando plantilla de pago rechazado en Strapi...");
      try {
        await strapi.plugin("email").service("email").updateTemplate(
          "payment-rejected",
          {
            subject: "Pago Rechazado - CCPALCAN",
            message: paymentRejectedMessage,
          }
        );
        log("✅ Plantilla de pago rechazado actualizada correctamente");
      } catch (error) {
        log("❌ Error al actualizar plantilla de pago rechazado:", error.message);
      }
    }

    log("✅ Plantillas sincronizadas correctamente con la interfaz de Strapi");
    log("🔍 Ahora deberías poder ver las plantillas actualizadas en la interfaz de administración");
    log("📋 Nota: Es posible que necesites refrescar la página de administración para ver los cambios");

  } catch (error) {
    log("❌ ERROR: Fallo en la sincronización de plantillas");
    log("🔧 Detalles del error:", error.message);
    log("📌 Stack:", error.stack);
  }
};

export default async ({ strapi }: { strapi: Strapi }) => {
  // Ejecutar la sincronización
  await syncEmailTemplates({ strapi });
};
