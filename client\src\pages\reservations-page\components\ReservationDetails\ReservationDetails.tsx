import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button, Descriptions, Space, Tag, Modal, Input, message } from "antd";
import { Http } from "@app/config/http";
import { PageTitle } from "@app/components/common/PageTitle/PageTitle";
import { BaseCard } from "@app/components/common/BaseCard/BaseCard";
import { BaseRow } from "@app/components/common/BaseRow/BaseRow";
import { BaseCol } from "@app/components/common/BaseCol/BaseCol";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { formatPhoneNumber } from "@app/pages/HousesPage/components/HouseCard";

const { TextArea } = Input;
const { confirm } = Modal;

interface Reservation {
  id: string;
  commonArea: {
    name: string;
    description: string;
    capacity: number;
  };
  user: {
    firstName: string;
    lastName: string;
    email: string;
    phone: {
      countryCode: number;
      areaCode: string;
      phoneNumber: string;
      isoCode: string;
    };
  };
  reservationDate: string;
  startTime: string;
  endTime: string;
  purpose: string;
  attendees: number;
  additionalNotes?: string;
  status: "pending" | "confirmed" | "rejected";
  adminComments?: string;
  createdAt: string;
  resolvedAt?: string;
}

export const ReservationDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [comments, setComments] = useState<string>("");

  const { data: reservation, isLoading } = useQuery<Reservation>({
    queryKey: ["reservation", id],
    queryFn: () => Http.get(`/api/reservations/${id}`).then((res) => res.data),
    enabled: !!id,
  });

  const updateReservation = useMutation({
    mutationFn: ({ status, comments }: { status: string; comments?: string }) =>
      Http.put(`/api/reservations/${id}`, {
        data: { status, adminComments: comments },
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
      message.success("Reserva actualizada correctamente");
      navigate("/admin-manager/reservas-zonas-comunes");
    },
    onError: () => {
      message.error("Error al actualizar la reserva");
    },
  });

  const showConfirmModal = (status: "confirmed" | "rejected") => {
    confirm({
      title: `¿Está seguro de ${status === "confirmed" ? "confirmar" : "rechazar"} esta reserva?`,
      icon: <ExclamationCircleOutlined />,
      content: (
        <TextArea
          placeholder="Agregar comentarios (opcional)"
          onChange={(e) => setComments(e.target.value)}
        />
      ),
      onOk() {
        updateReservation.mutate({
          status,
          comments: comments || undefined,
        });
      },
    });
  };

  if (isLoading || !reservation) {
    return null;
  }

  const getStatusTag = (status: string) => {
    const colors: Record<string, string> = {
      pending: "processing",
      confirmed: "success",
      rejected: "error",
    };

    const texts: Record<string, string> = {
      pending: "Pendiente",
      confirmed: "Confirmada",
      rejected: "Rechazada",
    };

    return <Tag color={colors[status]}>{texts[status]}</Tag>;
  };

  const getLabelPropuse = (purpose: string) => {
    const purposeLabels: Record<string, string> = {
      birthday: "Fiesta de cumpleaños",
      meeting: "Reunión familiar",
      celebration: "Celebración especial",
      babyShower: "Baby Shower",
      graduation: "Graduación",
      other: "Otro",
    };
    return purposeLabels[purpose] || "Otro";
  };

  return (
    <>
      <PageTitle>Detalles de la Reserva</PageTitle>
      <BaseCard>
        <Descriptions title="Información de la Reserva" bordered>
          <Descriptions.Item label="Estado" span={3}>
            {getStatusTag(reservation.status)}
          </Descriptions.Item>
          <Descriptions.Item label="Área Común" span={3}>
            {reservation.commonArea.name}
          </Descriptions.Item>
          <Descriptions.Item label="Solicitante" span={3}>
            {`${reservation.user.firstName} ${reservation.user.lastName}`}
          </Descriptions.Item>
          <Descriptions.Item label="Contacto" span={3}>
            Email: {reservation.user.email}
            <br />
            Teléfono: +{reservation.user.phone.countryCode} (
            {reservation.user.phone.areaCode}){" "}
            {reservation.user.phone.phoneNumber}
            {formatPhoneNumber(reservation.user?.phone)}
          </Descriptions.Item>
          <Descriptions.Item label="Fecha de Reserva">
            {dayjs(reservation.reservationDate).format("DD/MM/YYYY")}
          </Descriptions.Item>
          <Descriptions.Item label="Hora de Inicio">
            {reservation.startTime}
          </Descriptions.Item>
          <Descriptions.Item label="Hora de Fin">
            {reservation.endTime}
          </Descriptions.Item>
          <Descriptions.Item label="Propósito" span={3}>
            {getLabelPropuse(reservation.purpose)}
          </Descriptions.Item>
          <Descriptions.Item label="Número de Asistentes">
            {reservation.attendees}
          </Descriptions.Item>
          <Descriptions.Item label="Capacidad del Área">
            {reservation.commonArea.capacity}
          </Descriptions.Item>
          {reservation.additionalNotes && (
            <Descriptions.Item label="Notas Adicionales" span={3}>
              {reservation.additionalNotes}
            </Descriptions.Item>
          )}
          {reservation.adminComments && (
            <Descriptions.Item label="Comentarios del Administrador" span={3}>
              {reservation.adminComments}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="Fecha de Solicitud">
            {dayjs(reservation.createdAt).format("DD/MM/YYYY")}
          </Descriptions.Item>
          {reservation.resolvedAt && (
            <Descriptions.Item label="Fecha de Aporobación/Rechazo">
              {dayjs(reservation.resolvedAt).format("DD/MM/YYYY")}
            </Descriptions.Item>
          )}
        </Descriptions>

        <BaseRow justify="end" style={{ marginTop: 24 }}>
          <BaseCol>
            <Space>
              <Button
                onClick={() =>
                  navigate("/admin-manager/reservas-zonas-comunes")
                }
              >
                Volver
              </Button>
              {reservation.status === "pending" && (
                <>
                  <Button danger onClick={() => showConfirmModal("rejected")}>
                    Rechazar
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => showConfirmModal("confirmed")}
                  >
                    Confirmar
                  </Button>
                </>
              )}
            </Space>
          </BaseCol>
        </BaseRow>
      </BaseCard>
    </>
  );
};
