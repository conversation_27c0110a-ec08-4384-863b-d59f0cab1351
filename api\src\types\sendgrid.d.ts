declare module '@sendgrid/mail' {
  export interface MailDataRequired {
    to: string | { email: string; name?: string };
    from: string | { email: string; name?: string };
    subject: string;
    text?: string;
    html?: string;
    templateId?: string;
    dynamicTemplateData?: Record<string, any>;
    channel?: 'email' | 'whatsapp';
  }

  export interface ClientResponse {
    statusCode: number;
    body: any;
    headers: any;
  }

  export interface ResponseError extends Error {
    code: number;
    response: ClientResponse;
  }

  export type SendGridResponse = [ClientResponse, {}];

  export class MailService {
    setApiKey(apiKey: string): void;
    send(data: MailDataRequired): Promise<SendGridResponse>;
  }

  const mail: MailService;
  export default mail;
}
