import type { BuildContext } from './createBuildContext';
import type { DtsBuildTask } from './tasks/dts/build';
import type { DtsBaseTask } from './tasks/dts/types';
import type { DtsWatchTask } from './tasks/dts/watch';
import type { ViteBuildTask } from './tasks/vite/build';
import type { ViteBaseTask } from './tasks/vite/types';
import type { ViteWatchTask } from './tasks/vite/watch';
type BuildTask = DtsBuildTask | ViteBuildTask;
type WatchTask = ViteWatchTask | DtsWatchTask;
type BaseTask = ViteBaseTask | DtsBaseTask;
declare const createBuildTasks: (ctx: BuildContext) => Promise<BuildTask[]>;
declare const createWatchTasks: (ctx: BuildContext) => Promise<WatchTask[]>;
export { createBuildTasks, createWatchTasks };
export type { BuildTask, WatchTask, BaseTask };
