/**
 * comment controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

interface QueryFilters {
  filters?: {
    article?: {
      id?: {
        $eq?: number;
      };
    };
  };
}

type StrapiContext = Context & {
  state: {
    user: {
      id: number;
      username: string;
      firstName?: string;
      lastName?: string;
      imgUrl?: {
        url: string;
      };
    };
  };
  query: QueryFilters;
};

interface Author {
  id: number;
  username: string;
  firstName?: string;
  lastName?: string;
  imgUrl?: {
    url: string;
    formats?: {
      thumbnail?: any;
    };
  };
}

type StrapiID = number | string;

interface Comment {
  id: StrapiID;
  content: string;
  createdAt: string;
  isDeleted?: boolean;
  author?: Author;
  article?: {
    id: StrapiID;
    title: string;
  };
  replies?: Comment[];
  likes?: { id: StrapiID }[];
  dislikes?: { id: StrapiID }[];
}

type StrapiComment = {
  id: StrapiID;
  content?: string;
  createdAt: string;
  isDeleted?: boolean;
  author?: Author;
  article?: {
    id: StrapiID;
    title: string;
  };
  replies?: StrapiComment[];
  likes?: { id: StrapiID }[];
  dislikes?: { id: StrapiID }[];
};

interface SanitizedComment {
  id: StrapiID;
  content: string;
  createdAt: string;
  isDeleted?: boolean;
  author: {
    id: number;
    username: string;
    firstName: string;
    lastName: string;
    imgUrl: string | null;
  };
  article: {
    id: StrapiID;
    title: string;
  } | null;
  replies: SanitizedComment[];
  likes: StrapiID[];
  dislikes: StrapiID[];
  likeCount: number;
  dislikeCount: number;
  userReaction?: "like" | "dislike" | null;
}

const sanitizeComment = (
  comment: StrapiComment | null,
  userId?: number
): SanitizedComment | null => {
  if (!comment) return null;

  const likes = (comment.likes || []).map((user) => user.id);
  const dislikes = (comment.dislikes || []).map((user) => user.id);

  return {
    id: comment.id,
    content: comment.content,
    createdAt: comment.createdAt,
    isDeleted: comment.isDeleted || false,
    author: comment.author
      ? {
          id: comment.author.id,
          username: comment.author.username || "",
          firstName: comment.author.firstName || "",
          lastName: comment.author.lastName || "",
          imgUrl:
            comment.author.imgUrl?.formats?.thumbnail?.url ||
            comment.author.imgUrl?.url ||
            null,
        }
      : {
          id: 0,
          username: "deleted",
          firstName: "",
          lastName: "",
          imgUrl: null,
        },
    article: comment.article
      ? {
          id: comment.article.id,
          title: comment.article.title,
        }
      : null,
    replies: Array.isArray(comment.replies)
      ? comment.replies.map((c) => sanitizeComment(c, userId)).filter(Boolean)
      : [],
    likes,
    dislikes,
    likeCount: likes.length,
    dislikeCount: dislikes.length,
    userReaction: userId
      ? likes.includes(userId)
        ? "like"
        : dislikes.includes(userId)
        ? "dislike"
        : null
      : null,
  };
};

const defaultPopulate = {
  populate: {
    author: {
      populate: ["imgUrl"],
    },
    article: true,
    threadOf: {
      populate: {
        author: {
          populate: ["imgUrl"],
        },
      },
    },
    replies: {
      populate: {
        author: {
          populate: ["imgUrl"],
        },
      },
    },
    likes: true,
    dislikes: true,
  },
} as any;

type StrapiParams = Record<string, any>;

const validateReplyLevel = async (strapi: any, threadOfId: number) => {
  if (!threadOfId) return true;

  const parentComment = await strapi.entityService.findOne(
    "api::comment.comment",
    threadOfId,
    { populate: ["threadOf"] }
  );

  // Si el comentario padre ya es una respuesta, no permitir otra respuesta
  if (parentComment?.threadOf) {
    throw new Error("No se permiten respuestas a respuestas");
  }

  return true;
};

export default factories.createCoreController(
  "api::comment.comment",
  ({ strapi }) => ({
    async toggleReaction(ctx: StrapiContext) {
      const { id: commentId } = ctx.params;
      const { type } = ctx.request.body as { type: "like" | "dislike" };
      const userId = ctx.state.user.id;

      if (!["like", "dislike"].includes(type)) {
        return ctx.badRequest("Invalid reaction type");
      }

      try {
        const comment = (await strapi.entityService.findOne(
          "api::comment.comment",
          commentId
        )) as StrapiComment | null;

        if (!comment) {
          return ctx.notFound("Comment not found");
        }

        const currentLikes = (comment.likes || []);
        const currentDislikes = (comment.dislikes || []);

        // Get current user reactions
        const hasLiked = currentLikes.some((user) => user.id === userId);
        const hasDisliked = currentDislikes.some((user) => user.id === userId);

        let likesToConnect = [];
        let likesToDisconnect = [];
        let dislikesToConnect = [];
        let dislikesToDisconnect = [];

        if (type === "like") {
          if (hasLiked) {
            // Si ya dio like, quitarlo
            likesToDisconnect.push(userId);
          } else {
            // Si no ha dado like, agregarlo y quitar dislike si existe
            likesToConnect.push(userId);
            if (hasDisliked) {
              dislikesToDisconnect.push(userId);
            }
          }
        } else if (type === "dislike") {
          if (hasDisliked) {
            // Si ya dio dislike, quitarlo
            dislikesToDisconnect.push(userId);
          } else {
            // Si no ha dado dislike, agregarlo y quitar like si existe
            dislikesToConnect.push(userId);
            if (hasLiked) {
              likesToDisconnect.push(userId);
            }
          }
        }

        // Update the comment using raw queries for relations
        if (type === "like") {
          if (hasLiked) {
            // Remove like
            await strapi.db.query("api::comment.comment").update({
              where: { id: commentId },
              data: {
                likes: { disconnect: [{ id: userId }] },
              },
            });
          } else {
            // Add like and remove dislike if exists
            await strapi.db.query("api::comment.comment").update({
              where: { id: commentId },
              data: {
                likes: { connect: [{ id: userId }] },
                dislikes: hasDisliked
                  ? { disconnect: [{ id: userId }] }
                  : undefined,
              },
            });
          }
        } else if (type === "dislike") {
          if (hasDisliked) {
            // Remove dislike
            await strapi.db.query("api::comment.comment").update({
              where: { id: commentId },
              data: {
                dislikes: { disconnect: [{ id: userId }] },
              },
            });
          } else {
            // Add dislike and remove like if exists
            await strapi.db.query("api::comment.comment").update({
              where: { id: commentId },
              data: {
                dislikes: { connect: [{ id: userId }] },
                likes: hasLiked
                  ? { disconnect: [{ id: userId }] }
                  : undefined,
              },
            });
          }
        }

        // Fetch the updated comment with relations
        const updatedComment = (await strapi.entityService.findOne(
          "api::comment.comment",
          commentId,
          defaultPopulate
        )) as StrapiComment;

        return { data: sanitizeComment(updatedComment, userId) };
      } catch (error) {
        console.error("Error updating reaction:", error);
        return ctx.badRequest("Failed to update reaction");
      }
    },
    async find(ctx: StrapiContext) {
      const { query } = ctx;
      const userId = ctx.state.user?.id;

      try {
        // Asegurarnos de que solo obtenemos comentarios principales (no respuestas)
        const filters = {
          ...(query.filters ?? {}),
          threadOf: { $null: true },
        } as Record<string, any>;

        const comments = (await strapi.entityService.findMany(
          "api::comment.comment",
          {
            ...defaultPopulate,
            filters,
          }
        )) as StrapiComment[];

        return {
          data: comments.map((comment) => sanitizeComment(comment, userId)),
        };
      } catch (error) {
        console.error("Error fetching comments:", error);
        return ctx.badRequest("Failed to fetch comments");
      }
    },

    async findOne(ctx: StrapiContext) {
      const { id } = ctx.params;
      const { query } = ctx;
      const comment = await strapi.entityService.findOne(
        "api::comment.comment",
        id
      );

      // Sanitizar los datos antes de enviarlos
      const sanitizedComment = sanitizeComment(comment as any);
      return { data: sanitizedComment };
    },

    async create(ctx: StrapiContext) {
      const { data } = ctx.request.body;
      const userId = ctx.state.user?.id;

      try {
        // Validar nivel de respuesta si es una respuesta
        if (data.threadOf) {
          await validateReplyLevel(strapi, data.threadOf);
        }

        const comment = await strapi.entityService.create(
          "api::comment.comment",
          {
            data: {
              ...data,
              author: userId,
            },
            populate: defaultPopulate.populate,
          }
        );

        return { data: sanitizeComment(comment as any) };
      } catch (error) {
        console.error("Error creating comment:", error);
        return ctx.badRequest("Failed to create comment");
      }
    },

    async update(ctx: StrapiContext) {
      const { id } = ctx.params;
      const { data } = ctx.request.body;

      const comment = await strapi.entityService.findOne(
        "api::comment.comment",
        id,
        {
          populate: ["author"],
        } as StrapiParams
      );

      if (!comment) {
        return ctx.notFound("Comment not found");
      }

      const { user } = ctx.state;

      // Solo el autor puede editar su comentario
      if ((comment as any).author?.id !== user.id) {
        return ctx.forbidden("You are not allowed to perform this action");
      }

      const updatedComment = await strapi.entityService.update(
        "api::comment.comment",
        id,
        {
          ...defaultPopulate,
          data,
        } as StrapiParams
      );

      return { data: sanitizeComment(updatedComment as any) };
    },

    async delete(ctx: StrapiContext) {
      const { id } = ctx.params;
      const { user } = ctx.state;

      if (!user) {
        return ctx.unauthorized("You must be logged in to delete a comment");
      }

      const comment = await strapi.entityService.findOne(
        "api::comment.comment",
        id,
        {
          populate: ["author", "replies"],
        } as StrapiParams
      );

      if (!comment) {
        return ctx.notFound("Comment not found");
      }

      // Solo el autor puede eliminar su comentario
      if ((comment as any).author?.id !== user.id) {
        return ctx.forbidden("You are not allowed to perform this action");
      }

      try {
        // Eliminar el comentario completamente
        const deletedComment = await strapi.entityService.delete(
          "api::comment.comment",
          id
        );

        return sanitizeComment(deletedComment as StrapiComment, user.id);
      } catch (error) {
        return ctx.badRequest("Error al eliminar el comentario");
      }
    },
  })
);
