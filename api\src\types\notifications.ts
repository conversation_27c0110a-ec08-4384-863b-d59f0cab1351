/**
 * Tipos de notificaciones del sistema
 */

export enum NotificationType {
  // Notificaciones generales
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  
  // Notificaciones de huéspedes
  GUEST_ARRIVAL = 'guest_arrival',
  GUEST_CONFIRMATION = 'guest_confirmation',
  GUEST_CHECKOUT = 'guest_checkout',
  GUEST_LATE_ARRIVAL = 'guest_late_arrival',
  
  // Notificaciones de reservas
  NEW_RESERVATION = 'new_reservation',
  RESERVATION_CANCELLED = 'reservation_cancelled',
  RESERVATION_MODIFIED = 'reservation_modified',
  PAYMENT_RECEIVED = 'payment_received',
  
  // Notificaciones del sistema
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SYSTEM_UPDATE = 'system_update',
  BACKUP_COMPLETED = 'backup_completed',
  
  // Notificaciones de staff
  STAFF_MESSAGE = 'staff_message',
  SHIFT_CHANGE = 'shift_change',
  TASK_ASSIGNED = 'task_assigned',
  
  // Alertas críticas
  SECURITY_ALERT = 'security_alert',
  EMERGENCY = 'emergency',
  SYSTEM_ERROR = 'system_error',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface NotificationData {
  id: number;
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  data?: any;
  userId?: number;
  userIds?: number[];
  role?: string;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  actionUrl?: string;
  actionLabel?: string;
}

export interface WebSocketMessage {
  type: 'connection' | 'notification' | 'broadcast' | 'heartbeat' | 'pong' | 'error';
  data?: NotificationData;
  notification?: NotificationData;
  message?: string;
  timestamp: string;
  user?: {
    id: number;
    username: string;
  };
}

// Configuraciones de notificaciones por tipo
export const NotificationConfig = {
  [NotificationType.GUEST_ARRIVAL]: {
    icon: '🏨',
    color: 'blue',
    sound: true,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.GUEST_CONFIRMATION]: {
    icon: '✅',
    color: 'green',
    sound: true,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.GUEST_CHECKOUT]: {
    icon: '🚪',
    color: 'orange',
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.NEW_RESERVATION]: {
    icon: '📅',
    color: 'purple',
    sound: true,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.PAYMENT_RECEIVED]: {
    icon: '💰',
    color: 'green',
    sound: true,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SECURITY_ALERT]: {
    icon: '🚨',
    color: 'red',
    sound: true,
    persistent: true,
    priority: NotificationPriority.CRITICAL,
  },
  [NotificationType.EMERGENCY]: {
    icon: '🆘',
    color: 'red',
    sound: true,
    persistent: true,
    priority: NotificationPriority.CRITICAL,
  },
  [NotificationType.SYSTEM_ERROR]: {
    icon: '⚠️',
    color: 'red',
    sound: false,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.STAFF_MESSAGE]: {
    icon: '💬',
    color: 'blue',
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SYSTEM_MAINTENANCE]: {
    icon: '🔧',
    color: 'yellow',
    sound: false,
    persistent: true,
    priority: NotificationPriority.HIGH,
  },
  [NotificationType.INFO]: {
    icon: 'ℹ️',
    color: 'blue',
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.SUCCESS]: {
    icon: '✅',
    color: 'green',
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.WARNING]: {
    icon: '⚠️',
    color: 'orange',
    sound: false,
    persistent: false,
    priority: NotificationPriority.NORMAL,
  },
  [NotificationType.ERROR]: {
    icon: '❌',
    color: 'red',
    sound: false,
    persistent: false,
    priority: NotificationPriority.HIGH,
  },
};

// Funciones helper
export const getNotificationConfig = (type: NotificationType) => {
  return NotificationConfig[type] || NotificationConfig[NotificationType.INFO];
};

export const createNotification = (
  type: NotificationType,
  title: string,
  message: string,
  options: Partial<NotificationData> = {}
): Partial<NotificationData> => {
  const config = getNotificationConfig(type);
  
  return {
    type,
    priority: config.priority,
    title: `${config.icon} ${title}`,
    message,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
};
