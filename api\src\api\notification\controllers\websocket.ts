/**
 * WebSocket controller para notificaciones
 */

import websocketService from "../../../services/websocket";

export default {
  /**
   * Enviar notificación a un usuario específico vía WebSocket
   */
  async sendToUser(ctx) {
    try {
      const { userId } = ctx.params;
      const { title, message, type = "info", data = {} } = ctx.request.body;

      if (!userId) {
        return ctx.badRequest("Se requiere el ID del usuario");
      }

      if (!title || !message) {
        return ctx.badRequest("Se requieren título y mensaje");
      }

      // Crear la notificación en la base de datos
      const notification = await strapi.entityService.create(
        "api::notification.notification",
        {
          data: {
            title,
            message,
            type,
            data,
            user: parseInt(userId),
            read: false,
          },
          populate: ["user"],
        }
      );

      // Enviar vía WebSocket
      const sent = websocketService.sendNotificationToUser(
        parseInt(userId),
        notification
      );

      if (sent) {
        console.log(`📤 Notificación WebSocket enviada a usuario ${userId}`);
        ctx.send({
          success: true,
          message: "Notificación enviada correctamente",
          notification,
          sentViaWebSocket: true,
        });
      } else {
        console.log(`📭 Usuario ${userId} no está conectado vía WebSocket`);
        ctx.send({
          success: true,
          message: "Notificación creada (usuario no conectado)",
          notification,
          sentViaWebSocket: false,
        });
      }
    } catch (error) {
      console.error("❌ Error enviando notificación WebSocket:", error);
      ctx.internalServerError("Error enviando notificación");
    }
  },

  /**
   * Broadcast a todos los usuarios conectados
   */
  async broadcast(ctx) {
    try {
      const { title, message, type = "info", data = {} } = ctx.request.body;

      if (!title || !message) {
        return ctx.badRequest("Se requieren título y mensaje");
      }

      // Crear notificación para broadcast
      const broadcastData = {
        type: "broadcast",
        notification: {
          title,
          message,
          type,
          data,
          timestamp: new Date().toISOString(),
        },
      };

      // Enviar vía WebSocket
      const sentCount = websocketService.broadcast(broadcastData);

      console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);

      ctx.send({
        success: true,
        message: `Broadcast enviado a ${sentCount} conexión(es)`,
        sentCount,
      });
    } catch (error) {
      console.error("❌ Error enviando broadcast WebSocket:", error);
      ctx.internalServerError("Error enviando broadcast");
    }
  },

  /**
   * Obtener estadísticas de conexiones WebSocket
   */
  async getStats(ctx) {
    try {
      const stats = websocketService.getStats();

      ctx.send({
        success: true,
        stats,
      });
    } catch (error) {
      console.error("❌ Error obteniendo estadísticas WebSocket:", error);
      ctx.internalServerError("Error obteniendo estadísticas");
    }
  },

  /**
   * Enviar notificación de prueba
   */
  async test(ctx) {
    try {
      const { userId } = ctx.params;

      if (!userId) {
        return ctx.badRequest("Se requiere el ID del usuario");
      }

      const testNotification = {
        id: Date.now(),
        title: "🧪 Notificación de Prueba",
        message: "Esta es una notificación de prueba enviada vía WebSocket",
        type: "info",
        data: {
          test: true,
          timestamp: new Date().toISOString(),
        },
        read: false,
        createdAt: new Date().toISOString(),
      };

      // Enviar vía WebSocket
      const sent = websocketService.sendNotificationToUser(
        parseInt(userId),
        testNotification
      );

      if (sent) {
        console.log(`🧪 Notificación de prueba enviada a usuario ${userId}`);
        ctx.send({
          success: true,
          message: "Notificación de prueba enviada correctamente",
          notification: testNotification,
          sentViaWebSocket: true,
        });
      } else {
        console.log(`📭 Usuario ${userId} no está conectado vía WebSocket`);
        ctx.send({
          success: false,
          message: "Usuario no está conectado vía WebSocket",
          notification: testNotification,
          sentViaWebSocket: false,
        });
      }
    } catch (error) {
      console.error("❌ Error enviando notificación de prueba:", error);
      ctx.internalServerError("Error enviando notificación de prueba");
    }
  },

  /**
   * Probar notificación de confirmación de huésped
   */
  async testGuestConfirmation(ctx) {
    try {
      const {
        notificationManager,
      } = require("../../../services/notification-manager");

      const guestData = {
        id: "test-guest-123",
        name: "Juan Pérez",
        reservationId: "test-reservation-456",
        socialArea: "pool",
        date: new Date().toISOString(),
      };

      const confirmedBy = {
        id: ctx.state.user?.id || 1,
        username: ctx.state.user?.username || "admin",
      };

      const result =
        await notificationManager.sendGuestConfirmationNotification(
          guestData,
          confirmedBy
        );

      ctx.send({
        success: true,
        message: "Notificación de confirmación de huésped enviada",
        notification: result,
      });
    } catch (error) {
      console.error("Error enviando notificación de confirmación:", error);
      ctx.internalServerError("Error enviando notificación de confirmación");
    }
  },

  /**
   * Probar notificación de nueva reserva
   */
  async testNewReservation(ctx) {
    try {
      const {
        notificationManager,
      } = require("../../../services/notification-manager");

      const reservationData = {
        id: "test-reservation-789",
        guestName: "María García",
        checkInDate: new Date().toISOString(),
        checkOutDate: new Date(Date.now() + 86400000).toISOString(),
        roomType: "Suite",
        totalAmount: 150.0,
      };

      const result = await notificationManager.sendNewReservationNotification(
        reservationData
      );

      ctx.send({
        success: true,
        message: "Notificación de nueva reserva enviada",
        notification: result,
      });
    } catch (error) {
      console.error("Error enviando notificación de nueva reserva:", error);
      ctx.internalServerError("Error enviando notificación de nueva reserva");
    }
  },

  /**
   * Probar alerta de seguridad
   */
  async testSecurityAlert(ctx) {
    try {
      const {
        notificationManager,
      } = require("../../../services/notification-manager");

      const alertData = {
        id: "alert-123",
        description: "Actividad sospechosa detectada en el área de la piscina",
        location: "Área de Piscina",
        severity: "high",
      };

      const reportedBy = {
        id: ctx.state.user?.id || 1,
        username: ctx.state.user?.username || "security",
      };

      const result = await notificationManager.sendSecurityAlert(
        alertData,
        reportedBy
      );

      ctx.send({
        success: true,
        message: "Alerta de seguridad enviada",
        notification: result,
      });
    } catch (error) {
      console.error("Error enviando alerta de seguridad:", error);
      ctx.internalServerError("Error enviando alerta de seguridad");
    }
  },
};
