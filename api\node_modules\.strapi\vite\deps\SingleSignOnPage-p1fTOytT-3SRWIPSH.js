import {
  useAdminRoles
} from "./chunk-SNQBUIBC.js";
import {
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create2 as create,
  create3 as create2,
  create6 as create3,
  create7 as create4,
  errorsTrads,
  useField
} from "./chunk-M2UTSHEM.js";
import {
  Page,
  useAP<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  useGetProviderOptionsQuery,
  useNotification,
  useRBAC,
  useTypedSelector,
  useUpdateProviderOptionsMutation
} from "./chunk-6SQM3G2Q.js";
import {
  Button,
  Field,
  Flex,
  Grid,
  MultiSelect,
  MultiSelectOption,
  Typography,
  isBaseQueryError,
  useIntl
} from "./chunk-OLXXPFXU.js";
import "./chunk-GGTKPILS.js";
import {
  ForwardRef$4p
} from "./chunk-C3D7G7QO.js";
import "./chunk-JA2NBZIU.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/SingleSignOnPage-p1fTOytT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var SCHEMA = create3().shape({
  autoRegister: create2().required(errorsTrads.required),
  defaultRole: create().when("autoRegister", (value, initSchema) => {
    return value ? initSchema.required(errorsTrads.required) : initSchema.nullable();
  }),
  ssoLockedRoles: create4().nullable().of(
    create().when("ssoLockedRoles", (value, initSchema) => {
      return value ? initSchema.required(errorsTrads.required) : initSchema.nullable();
    })
  )
});
var SingleSignOnPage = () => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const permissions = useTypedSelector((state) => state.admin_app.permissions);
  const { toggleNotification } = useNotification();
  const {
    _unstableFormatAPIError: formatAPIError,
    _unstableFormatValidationErrors: formatValidationErrors
  } = useAPIErrorHandler();
  const { isLoading: isLoadingProviderOptions, data } = useGetProviderOptionsQuery();
  const [updateProviderOptions, { isLoading: isSubmittingForm }] = useUpdateProviderOptionsMutation();
  const {
    isLoading: isLoadingPermissions,
    allowedActions: { canUpdate, canRead: canReadRoles }
  } = useRBAC({
    ...(_a = permissions.settings) == null ? void 0 : _a.sso,
    readRoles: ((_b = permissions.settings) == null ? void 0 : _b.roles.read) ?? []
  });
  const { roles, isLoading: isLoadingRoles } = useAdminRoles(void 0, {
    skip: !canReadRoles
  });
  const handleSubmit = async (body, helpers) => {
    try {
      const res = await updateProviderOptions(body);
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          helpers.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({
            type: "danger",
            message: formatAPIError(res.error)
          });
        }
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({ id: "notification.success.saved" })
      });
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again."
        })
      });
    }
  };
  const isLoadingData = isLoadingRoles || isLoadingPermissions || isLoadingProviderOptions;
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: "SSO"
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Page.Main, { "aria-busy": isSubmittingForm || isLoadingData, tabIndex: -1, children: (0, import_jsx_runtime.jsx)(
      Form,
      {
        method: "PUT",
        onSubmit: handleSubmit,
        validationSchema: SCHEMA,
        disabled: !canUpdate,
        initialValues: data || {
          autoRegister: false,
          defaultRole: null,
          ssoLockedRoles: null
        },
        children: ({ modified, isSubmitting }) => (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
          (0, import_jsx_runtime.jsx)(
            Layouts.Header,
            {
              primaryAction: (0, import_jsx_runtime.jsx)(
                Button,
                {
                  disabled: !modified,
                  loading: isSubmitting,
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                  type: "submit",
                  size: "L",
                  children: formatMessage({
                    id: "global.save",
                    defaultMessage: "Save"
                  })
                }
              ),
              title: formatMessage({
                id: "Settings.sso.title",
                defaultMessage: "Single Sign-On"
              }),
              subtitle: formatMessage({
                id: "Settings.sso.description",
                defaultMessage: "Configure the settings for the Single Sign-On feature."
              })
            }
          ),
          (0, import_jsx_runtime.jsx)(Layouts.Content, { children: isSubmitting || isLoadingData ? (0, import_jsx_runtime.jsx)(Page.Loading, {}) : (0, import_jsx_runtime.jsxs)(
            Flex,
            {
              direction: "column",
              alignItems: "stretch",
              gap: 4,
              background: "neutral0",
              padding: 6,
              shadow: "filterShadow",
              hasRadius: true,
              children: [
                (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                  id: "global.settings",
                  defaultMessage: "Settings"
                }) }),
                (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: [
                  {
                    hint: formatMessage({
                      id: "Settings.sso.form.registration.description",
                      defaultMessage: "Create new user on SSO login if no account exists"
                    }),
                    label: formatMessage({
                      id: "Settings.sso.form.registration.label",
                      defaultMessage: "Auto-registration"
                    }),
                    name: "autoRegister",
                    size: 6,
                    type: "boolean"
                  },
                  {
                    hint: formatMessage({
                      id: "Settings.sso.form.defaultRole.description",
                      defaultMessage: "It will attach the new authenticated user to the selected role"
                    }),
                    label: formatMessage({
                      id: "Settings.sso.form.defaultRole.label",
                      defaultMessage: "Default role"
                    }),
                    name: "defaultRole",
                    options: roles.map(({ id, name }) => ({
                      label: name,
                      value: id.toString()
                    })),
                    placeholder: formatMessage({
                      id: "components.InputSelect.option.placeholder",
                      defaultMessage: "Choose here"
                    }),
                    size: 6,
                    type: "enumeration"
                  },
                  {
                    hint: formatMessage({
                      id: "Settings.sso.form.localAuthenticationLock.description",
                      defaultMessage: "Select the roles for which you want to disable the local authentication"
                    }),
                    label: formatMessage({
                      id: "Settings.sso.form.localAuthenticationLock.label",
                      defaultMessage: "Local authentication lock-out"
                    }),
                    name: "ssoLockedRoles",
                    options: roles.map(({ id, name }) => ({
                      label: name,
                      value: id.toString()
                    })),
                    placeholder: formatMessage({
                      id: "components.InputSelect.option.placeholder",
                      defaultMessage: "Choose here"
                    }),
                    size: 6,
                    type: "multi"
                  }
                ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(
                  Grid.Item,
                  {
                    col: size,
                    direction: "column",
                    alignItems: "stretch",
                    children: (0, import_jsx_runtime.jsx)(FormInputRenderer, { ...field })
                  },
                  field.name
                )) })
              ]
            }
          ) })
        ] })
      }
    ) })
  ] });
};
var FormInputRenderer = (props) => {
  switch (props.type) {
    case "multi":
      return (0, import_jsx_runtime.jsx)(MultiSelectInput, { ...props });
    default:
      return (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...props });
  }
};
var MultiSelectInput = ({ hint, label, name, options, ...props }) => {
  const field = useField(name);
  return (0, import_jsx_runtime.jsxs)(Field.Root, { name, hint, error: field.error, children: [
    (0, import_jsx_runtime.jsx)(Field.Label, { children: label }),
    (0, import_jsx_runtime.jsx)(
      MultiSelect,
      {
        onChange: (value) => field.onChange("ssoLockedRoles", value),
        onClear: () => field.onChange("ssoLockedRoles", []),
        value: field.value ?? [],
        withTags: true,
        ...props,
        children: options.map(({ label: label2, value }) => (0, import_jsx_runtime.jsx)(MultiSelectOption, { value, children: label2 }, value))
      }
    ),
    (0, import_jsx_runtime.jsx)(Field.Hint, {}),
    (0, import_jsx_runtime.jsx)(Field.Error, {})
  ] });
};
var ProtectedSSO = () => {
  const permissions = useTypedSelector((state) => {
    var _a, _b;
    return (_b = (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.sso) == null ? void 0 : _b.main;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(SingleSignOnPage, {}) });
};
export {
  ProtectedSSO,
  SingleSignOnPage
};
//# sourceMappingURL=SingleSignOnPage-p1fTOytT-3SRWIPSH.js.map
