
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

/*
 * Copyright (C) 2013  Google, Inc.
 *
 *  This is part of HarfBuzz, a text shaping library.
 *
 * Permission is hereby granted, without written agreement and without
 * license or royalty fees, to use, copy, modify, and distribute this
 * software and its documentation for any purpose, provided that the
 * above copyright notice and the following two paragraphs appear in
 * all copies of this software.
 *
 * IN NO EVENT SHALL THE COPYRIGHT HOLDER BE LIABLE TO ANY PARTY FOR
 * DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES
 * ARISING OUT OF THE USE OF THIS SOFTWARE AND ITS DOCUMENTATION, EVEN
 * IF THE COPYRIGHT HOLDER HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGE.
 *
 * THE COPYRIGHT HOLDER SPECIFICALLY DISCLAIMS ANY WARRANTIES, INCLUDING,
 * BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE.  THE SOFTWARE PROVIDED HEREUNDER IS
 * ON AN "AS IS" BASIS, AND THE COPYRIGHT HOLDER HAS NO OBLIGATION TO
 * PROVIDE MAINTENANCE, SUPPORT, UPDATES, ENHANCEMENTS, OR MODIFICATIONS.
 *
 * Google Author(s): Behdad Esfahbod
 */

#if !defined(HB_GOBJECT_H_IN) && !defined(HB_NO_SINGLE_HEADER_ERROR)
#error "Include <hb-gobject.h> instead."
#endif

#ifndef HB_GOBJECT_ENUMS_H
#define HB_GOBJECT_ENUMS_H

#include "hb.h"

#include <glib-object.h>

HB_BEGIN_DECLS
HB_EXTERN GType
hb_gobject_aat_layout_feature_type_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_AAT_LAYOUT_FEATURE_TYPE (hb_gobject_aat_layout_feature_type_get_type ())
HB_EXTERN GType
hb_gobject_aat_layout_feature_selector_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_AAT_LAYOUT_FEATURE_SELECTOR (hb_gobject_aat_layout_feature_selector_get_type ())
HB_EXTERN GType
hb_gobject_memory_mode_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_MEMORY_MODE (hb_gobject_memory_mode_get_type ())
HB_EXTERN GType
hb_gobject_glyph_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_GLYPH_FLAGS (hb_gobject_glyph_flags_get_type ())
HB_EXTERN GType
hb_gobject_buffer_content_type_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_CONTENT_TYPE (hb_gobject_buffer_content_type_get_type ())
HB_EXTERN GType
hb_gobject_buffer_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_FLAGS (hb_gobject_buffer_flags_get_type ())
HB_EXTERN GType
hb_gobject_buffer_cluster_level_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_CLUSTER_LEVEL (hb_gobject_buffer_cluster_level_get_type ())
HB_EXTERN GType
hb_gobject_buffer_serialize_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_SERIALIZE_FLAGS (hb_gobject_buffer_serialize_flags_get_type ())
HB_EXTERN GType
hb_gobject_buffer_serialize_format_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_SERIALIZE_FORMAT (hb_gobject_buffer_serialize_format_get_type ())
HB_EXTERN GType
hb_gobject_buffer_diff_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_BUFFER_DIFF_FLAGS (hb_gobject_buffer_diff_flags_get_type ())
HB_EXTERN GType
hb_gobject_direction_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_DIRECTION (hb_gobject_direction_get_type ())
HB_EXTERN GType
hb_gobject_script_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_SCRIPT (hb_gobject_script_get_type ())
HB_EXTERN GType
hb_gobject_ot_color_palette_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_COLOR_PALETTE_FLAGS (hb_gobject_ot_color_palette_flags_get_type ())
HB_EXTERN GType
hb_gobject_ot_layout_glyph_class_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_LAYOUT_GLYPH_CLASS (hb_gobject_ot_layout_glyph_class_get_type ())
HB_EXTERN GType
hb_gobject_ot_layout_baseline_tag_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_LAYOUT_BASELINE_TAG (hb_gobject_ot_layout_baseline_tag_get_type ())
HB_EXTERN GType
hb_gobject_ot_math_constant_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_MATH_CONSTANT (hb_gobject_ot_math_constant_get_type ())
HB_EXTERN GType
hb_gobject_ot_math_kern_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_MATH_KERN (hb_gobject_ot_math_kern_get_type ())
HB_EXTERN GType
hb_gobject_ot_math_glyph_part_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_MATH_GLYPH_PART_FLAGS (hb_gobject_ot_math_glyph_part_flags_get_type ())
HB_EXTERN GType
hb_gobject_ot_meta_tag_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_META_TAG (hb_gobject_ot_meta_tag_get_type ())
HB_EXTERN GType
hb_gobject_ot_metrics_tag_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_METRICS_TAG (hb_gobject_ot_metrics_tag_get_type ())
HB_EXTERN GType
hb_gobject_ot_name_id_predefined_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_NAME_ID_PREDEFINED (hb_gobject_ot_name_id_predefined_get_type ())
HB_EXTERN GType
hb_gobject_ot_var_axis_flags_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_OT_VAR_AXIS_FLAGS (hb_gobject_ot_var_axis_flags_get_type ())
HB_EXTERN GType
hb_gobject_paint_extend_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_PAINT_EXTEND (hb_gobject_paint_extend_get_type ())
HB_EXTERN GType
hb_gobject_paint_composite_mode_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_PAINT_COMPOSITE_MODE (hb_gobject_paint_composite_mode_get_type ())
HB_EXTERN GType
hb_gobject_style_tag_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_STYLE_TAG (hb_gobject_style_tag_get_type ())
HB_EXTERN GType
hb_gobject_unicode_general_category_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_UNICODE_GENERAL_CATEGORY (hb_gobject_unicode_general_category_get_type ())
HB_EXTERN GType
hb_gobject_unicode_combining_class_get_type (void) G_GNUC_CONST;
#define HB_GOBJECT_TYPE_UNICODE_COMBINING_CLASS (hb_gobject_unicode_combining_class_get_type ())

HB_END_DECLS

#endif /* HB_GOBJECT_ENUMS_H */

/* Generated data ends here */

