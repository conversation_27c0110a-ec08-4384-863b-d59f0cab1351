{"kind": "collectionType", "collectionName": "pool_accesses", "info": {"singularName": "pool-access", "pluralName": "pool-accesses", "displayName": "Pool Access", "description": "Records of pool access by residents and their guests"}, "options": {"draftAndPublish": false}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "pool_accesses"}, "guestCount": {"type": "integer", "min": 0, "default": 0}, "isAlone": {"type": "boolean", "default": true}, "entryTime": {"type": "datetime"}, "exitTime": {"type": "datetime"}, "status": {"type": "enumeration", "enum": ["active", "completed"], "default": "active"}, "dependents": {"type": "relation", "relation": "manyToMany", "target": "api::dependent.dependent", "inversedBy": "pool_accesses"}}}