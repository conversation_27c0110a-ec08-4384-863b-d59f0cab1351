import {
  CheckOutlined,
  CloseOutlined,
  DownloadOutlined,
  SearchOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { BaseCard } from "@app/components/common/BaseCard/BaseCard";
import { PageTitle } from "@app/components/common/PageTitle/PageTitle";
import { useResponsive } from "@app/hooks/useResponsive";
import { getLabelPropuse } from "@app/pages/CommonAreasPage/components/ReservationDetails/components/ReservationInfo";
import {
  Avatar,
  Badge,
  Button,
  Descriptions,
  Empty,
  Flex,
  List,
  Modal,
  Switch,
  Tag,
  Space,
  Input,
} from "antd";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import dayjs from "dayjs";
import { block } from "million/react";
import { useMemo, useState } from "react";
import { useDeleteConfirmModal } from "./components/DeleteConfirmModal";
import GenerateReservationPDF from "./components/GenerateReservationPDF";
import { RejectReservationModal } from "./components/RejectReservationModal";
import { ReservationCardView } from "./components/ReservationCardView";
import { ReservationTable } from "./components/ReservationTable";
import { ViewModeToggle } from "./components/ViewModeToggle";
import { Reservation, useListReservations } from "./hooks/useListReservations";
import { useUpdateReservationStatus } from "./hooks/useUpdateReservationStatus";
import { useConfirmGuest } from "@app/hooks/reservations/useConfirmGuest";

// Función auxiliar para obtener el nombre del área
const getAreaName = (area: string): string => {
  const areas: Record<string, string> = {
    communalHall: "Salón comunal",
    pool: "Piscina",
    bbq: "BBQ",
    terrace: "Terraza",
  };
  return areas[area] || area;
};

const getStatuName = (value: string): string => {
  const status: Record<string, string> = {
    pending: "Pendiente",
    approved: "Aprobada",
    rejected: "Rechazada",
  };
  return status[value] || value;
};

// Función auxiliar para obtener el tag de estado
const getStatusTag = (status: string): JSX.Element => {
  const statusConfig: Record<string, { color: string; text: string }> = {
    pending: { color: "warning", text: "Pendiente" },
    approved: { color: "success", text: "Aprobada" },
    rejected: { color: "error", text: "Rechazada" },
  };

  const config = statusConfig[status] || { color: "default", text: status };
  return <Tag color={config.color}>{config.text}</Tag>;
};

// Componente principal optimizado con Million.js
const ReservationsAdminPage = block(() => {
  const { isTablet } = useResponsive();

  const [isTableMode, setViewMode] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [selectedReservation, setSelectedReservation] =
    useState<Reservation | null>(null);

  // Custom hooks
  const { reservations, isLoading, isError } = useListReservations();
  const { updateReservationStatus } = useUpdateReservationStatus();
  const { showDeleteConfirm } = useDeleteConfirmModal();

  // Función para actualizar el estado local del guest
  const updateLocalGuestState = (
    reservationId: string | number,
    guestIndex: number,
    isConfirmed: boolean,
  ) => {
    if (
      selectedReservation &&
      selectedReservation.id === Number(reservationId)
    ) {
      const updatedGuests = [...(selectedReservation.guests || [])];
      if (updatedGuests[guestIndex]) {
        updatedGuests[guestIndex] = {
          ...updatedGuests[guestIndex],
          isConfirmed: isConfirmed,
        };
        setSelectedReservation({
          ...selectedReservation,
          guests: updatedGuests,
        });
      }
    }
  };

  const confirmGuestMutation = useConfirmGuest({
    onLocalUpdate: updateLocalGuestState,
  });

  const handleRejectOpen = (reservation: Reservation) => {
    setSelectedReservation(reservation);
    setIsRejectModalVisible(true);
  };

  const handleRejectClose = () => {
    setIsRejectModalVisible(false);
    setSelectedReservation(null);
  };

  const handleStatusUpdate = (
    id: number,
    status: string,
    rejectionReason?: string,
  ) => {
    updateReservationStatus({ id, status, rejectionReason });
    setIsModalVisible(false);
    setSelectedReservation(null);
  };

  const handleViewDetails = (reservation: Reservation) => {
    setSelectedReservation(reservation);
    setIsModalVisible(true);
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setSelectedReservation(null);
    setGuestSearchText("");
  };

  if (isError) {
    return (
      <div style={{ padding: "24px", textAlign: "center" }}>
        <Empty description="Error al cargar las reservaciones" />
      </div>
    );
  }

  // Renderizado del contenido principal
  const renderContent = () => {
    if (isTableMode) {
      return (
        <ReservationTable
          reservations={reservations}
          isLoading={isLoading}
          onReject={handleRejectOpen}
          onApprove={(id) => handleStatusUpdate(id, "approved")}
          onDelete={showDeleteConfirm}
          onViewDetails={handleViewDetails}
        />
      );
    }

    return (
      <ReservationCardView
        reservations={reservations}
        onReject={handleRejectOpen}
        onApprove={(id) => handleStatusUpdate(id, "approved")}
        onDelete={showDeleteConfirm}
        onViewDetails={handleViewDetails}
      />
    );
  };

  const handleDownloadPDF = () => {
    if (!selectedReservation) return;
    const doc = GenerateReservationPDF(selectedReservation);
    const filename = `reservacion-${selectedReservation.socialArea}-${dayjs(
      selectedReservation.date,
    ).format("YYYY-MM-DD")}.pdf`;
    doc.save(filename);
  };

  // Versión inline para usar directamente en tu modal
  const [guestSearchText, setGuestSearchText] = useState("");

  // Agregar esto en tu componente principal
  const filteredGuests = useMemo(() => {
    if (!selectedReservation?.guests || !guestSearchText.trim()) {
      return selectedReservation?.guests || [];
    }

    const searchLower = guestSearchText.toLowerCase().trim();
    return selectedReservation.guests.filter((guest) => {
      const fullName =
        `${guest.name || ""} ${guest.lastName || ""}`.toLowerCase();
      return fullName.includes(searchLower);
    });
  }, [selectedReservation?.guests, guestSearchText]);

  // Renderizado del modal de detalles
  const renderDetailsModal = () => (
    <Modal
      destroyOnHidden
      centered
      width={800}
      title="Detalles de la Reservación Para Administradores"
      open={isModalVisible}
      onCancel={handleModalClose}
      footer={[
        <Button
          size="small"
          key="pdf"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleDownloadPDF}
        >
          Descargar PDF
        </Button>,
        <Button
          key="close"
          onClick={handleModalClose}
          disabled={isLoading}
          size="small"
        >
          Cerrar
        </Button>,
      ]}
    >
      {selectedReservation && (
        <>
          <Descriptions
            bordered
            column={{ xxl: 2, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }}
          >
            <Descriptions.Item label="Residente" span={isTablet ? 3 : 1}>
              <Flex gap={12} align="center">
                <Badge
                  dot
                  status={
                    selectedReservation.user?.status ? "success" : "error"
                  }
                  offset={[-2, 28]}
                >
                  <Avatar
                    size={32}
                    src={selectedReservation.user?.imgUrl}
                    icon={<UserOutlined />}
                  />
                </Badge>
                <Flex vertical>
                  <span>
                    {selectedReservation.user
                      ? `${selectedReservation.user.firstName || ""} ${selectedReservation.user.lastName || ""}`.trim()
                      : "Usuario no disponible"}
                  </span>
                  <Tag
                    color={
                      selectedReservation.user?.status ? "success" : "error"
                    }
                  >
                    {selectedReservation.user?.status ? "Al dia" : "Moroso"}
                  </Tag>
                </Flex>
              </Flex>
            </Descriptions.Item>
            <Descriptions.Item label="Casa">
              {selectedReservation.user?.address}
            </Descriptions.Item>
            <Descriptions.Item label="Estado">
              {getStatusTag(selectedReservation.status)}
            </Descriptions.Item>
            {selectedReservation.rejectionReason ? (
              <Descriptions.Item label="Motivo de Rechazo" span={2}>
                {selectedReservation.rejectionReason}
              </Descriptions.Item>
            ) : (
              <>
                <Descriptions.Item label="Lugar">
                  {getAreaName(selectedReservation.socialArea)}
                </Descriptions.Item>
                <Descriptions.Item label="Evento">
                  {getLabelPropuse(selectedReservation.eventType) ||
                    "No especificado"}
                </Descriptions.Item>

                <Descriptions.Item label="Solicitud">
                  {format(
                    new Date(selectedReservation.createdAt),
                    "dd/MM/yyyy",
                    {
                      locale: es,
                    },
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="Reservación">
                  {format(new Date(selectedReservation.date), "dd/MM/yyyy", {
                    locale: es,
                  })}
                </Descriptions.Item>
                <Descriptions.Item label="Cantidad">
                  {selectedReservation.guests?.length ||
                    selectedReservation.attendees ||
                    0}
                </Descriptions.Item>
                <Descriptions.Item label="Horario">
                  {`${dayjs(`${selectedReservation.date} ${selectedReservation.startTime}`).format("hh:mm A")} - ${dayjs(`${selectedReservation.date} ${selectedReservation.endTime}`).format("hh:mm A")}`}
                </Descriptions.Item>
              </>
            )}
          </Descriptions>

          <BaseCard
            size="small"
            padding="small"
            title={
              <Input.Search
                placeholder="Buscar invitados"
                allowClear
                size="small"
                value={guestSearchText}
                onChange={(e) => setGuestSearchText(e.target.value)}
                onSearch={setGuestSearchText}
                prefix={<UsergroupAddOutlined />}
              />
            }
          >
            <List
              split
              size="small"
              style={{ maxHeight: 400, overflow: "auto" }}
              dataSource={filteredGuests}
              locale={{
                emptyText: guestSearchText
                  ? `No se encontraron invitados que coincidan con "${guestSearchText}"`
                  : "Sin invitados registrados",
              }}
              renderItem={(guest, filteredIndex) => {
                // Encontrar el índice original
                const originalIndex =
                  selectedReservation?.guests?.findIndex(
                    (g) =>
                      g.name === guest.name && g.lastName === guest.lastName,
                  ) ?? filteredIndex;

                return (
                  <List.Item
                    key={`guest-${originalIndex}-${guest.name}`}
                    actions={[
                      <Switch
                        key={`switch-${originalIndex}`}
                        size="small"
                        checked={guest.isConfirmed || false}
                        checkedChildren={<CheckOutlined />}
                        unCheckedChildren={<CloseOutlined />}
                        loading={confirmGuestMutation.isPending}
                        onChange={(checked) => {
                          if (selectedReservation?.id) {
                            confirmGuestMutation.mutate({
                              reservationId: selectedReservation.id,
                              guestIndex: originalIndex,
                              isConfirmed: checked,
                            });
                          }
                        }}
                      />,
                    ]}
                  >
                    <small>
                      {guest.name} {guest.lastName}
                    </small>
                  </List.Item>
                );
              }}
            />
          </BaseCard>
        </>
      )}
    </Modal>
  );

  return (
    <BaseCard
      title="Gestión de Reservaciones"
      extra={
        <ViewModeToggle
          isTableMode={isTableMode}
          onTableMode={() => setViewMode(true)}
          onCardMode={() => setViewMode(false)}
        />
      }
    >
      <PageTitle>Reservaciones</PageTitle>

      {renderContent()}
      {renderDetailsModal()}

      {/* Modal de rechazo */}
      <RejectReservationModal
        visible={isRejectModalVisible}
        reservation={selectedReservation}
        onCancel={handleRejectClose}
        onReject={(id, rejectionReason) => {
          handleStatusUpdate(id, "rejected", rejectionReason);
          handleRejectClose();
        }}
      />
    </BaseCard>
  );
});

export default ReservationsAdminPage;
