import {
  PERMISSIONS,
  getTranslation,
  useCreateLocaleMutation,
  useDeleteLocaleMutation,
  useGetDefaultLocalesQuery,
  useGetLocalesQuery,
  useUpdateLocaleMutation
} from "./chunk-RZNXC7D6.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-JFNDUCW5.js";
import "./chunk-RPX6VIML.js";
import "./chunk-RPZ2IAY7.js";
import "./chunk-7NY4WJ74.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  ConfirmDialog,
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create3 as create,
  create4 as create2,
  create6 as create3,
  useField,
  useForm
} from "./chunk-M2UTSHEM.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler,
  useAuth,
  useNotification,
  useRBAC
} from "./chunk-6SQM3G2Q.js";
import {
  Box,
  Button,
  Dialog,
  Divider,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  IconButton,
  Modal,
  SingleSelect,
  SingleSelectOption,
  Table,
  Tabs,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useId,
  useIntl
} from "./chunk-OLXXPFXU.js";
import "./chunk-GGTKPILS.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$4p,
  ForwardRef$j
} from "./chunk-C3D7G7QO.js";
import "./chunk-JA2NBZIU.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-IYHTQODQ.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/i18n/dist/_chunks/SettingsPage-DbRFoF_H.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var isBaseQueryError = (error) => {
  return error.name !== void 0;
};
var CreateLocale = ({ disabled, variant = "default" }) => {
  const { formatMessage } = useIntl();
  const [visible, setVisible] = React.useState(false);
  return (0, import_jsx_runtime.jsxs)(Modal.Root, { open: visible, onOpenChange: setVisible, children: [
    (0, import_jsx_runtime.jsx)(Modal.Trigger, { children: (0, import_jsx_runtime.jsx)(
      Button,
      {
        variant,
        disabled,
        startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
        onClick: () => setVisible(true),
        size: "S",
        children: formatMessage({
          id: getTranslation("Settings.list.actions.add"),
          defaultMessage: "Add new locale"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(CreateModal, { onClose: () => setVisible(false) })
  ] });
};
var LOCALE_SCHEMA = create3().shape({
  code: create2().nullable().required({
    id: "Settings.locales.modal.create.code.error",
    defaultMessage: "Please select a locale"
  }),
  name: create2().nullable().max(50, {
    id: "Settings.locales.modal.create.name.error.min",
    defaultMessage: "The locale display name can only be less than 50 characters."
  }).required({
    id: "Settings.locales.modal.create.name.error.required",
    defaultMessage: "Please give the locale a display name"
  }),
  isDefault: create()
});
var initialFormValues = {
  code: "",
  name: "",
  isDefault: false
};
var CreateModal = ({ onClose }) => {
  const titleId = useId();
  const { toggleNotification } = useNotification();
  const {
    _unstableFormatAPIError: formatAPIError,
    _unstableFormatValidationErrors: formatValidationErrors
  } = useAPIErrorHandler();
  const [createLocale] = useCreateLocaleMutation();
  const { formatMessage } = useIntl();
  const refetchPermissions = useAuth("CreateModal", (state) => state.refetchPermissions);
  const handleSubmit = async (values, helpers) => {
    try {
      const res = await createLocale(values);
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          helpers.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({ type: "danger", message: formatAPIError(res.error) });
        }
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.create.success"),
          defaultMessage: "Created locale"
        })
      });
      refetchPermissions();
      onClose();
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(Modal.Content, { children: (0, import_jsx_runtime.jsxs)(
    Form,
    {
      method: "POST",
      initialValues: initialFormValues,
      validationSchema: LOCALE_SCHEMA,
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage({
          id: getTranslation("Settings.list.actions.add"),
          defaultMessage: "Add new locale"
        }) }) }),
        (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsxs)(Tabs.Root, { variant: "simple", defaultValue: "basic", children: [
          (0, import_jsx_runtime.jsxs)(Flex, { justifyContent: "space-between", children: [
            (0, import_jsx_runtime.jsx)(Typography, { tag: "h2", variant: "beta", id: titleId, children: formatMessage({
              id: getTranslation("Settings.locales.modal.title"),
              defaultMessage: "Configuration"
            }) }),
            (0, import_jsx_runtime.jsxs)(Tabs.List, { "aria-labelledby": titleId, children: [
              (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "basic", children: formatMessage({
                id: getTranslation("Settings.locales.modal.base"),
                defaultMessage: "Basic settings"
              }) }),
              (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "advanced", children: formatMessage({
                id: getTranslation("Settings.locales.modal.advanced"),
                defaultMessage: "Advanced settings"
              }) })
            ] })
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)(Box, { paddingTop: 7, paddingBottom: 7, children: [
            (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "basic", children: (0, import_jsx_runtime.jsx)(BaseForm, {}) }),
            (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "advanced", children: (0, import_jsx_runtime.jsx)(AdvancedForm, {}) })
          ] })
        ] }) }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", children: formatMessage({ id: "app.components.Button.cancel", defaultMessage: "Cancel" }) }) }),
          (0, import_jsx_runtime.jsx)(SubmitButton, {})
        ] })
      ]
    }
  ) });
};
var SubmitButton = () => {
  const { formatMessage } = useIntl();
  const isSubmitting = useForm("SubmitButton", (state) => state.isSubmitting);
  const modified = useForm("SubmitButton", (state) => state.modified);
  return (0, import_jsx_runtime.jsx)(Button, { type: "submit", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}), disabled: isSubmitting || !modified, children: formatMessage({ id: "global.save", defaultMessage: "Save" }) });
};
var BaseForm = ({ mode = "create" }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data: defaultLocales, error } = useGetDefaultLocalesQuery();
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [error, formatAPIError, toggleNotification]);
  if (!Array.isArray(defaultLocales)) {
    return null;
  }
  const options = defaultLocales.map((locale) => ({
    label: locale.name,
    value: locale.code
  }));
  const translatedForm = [
    {
      disabled: mode !== "create",
      label: {
        id: getTranslation("Settings.locales.modal.create.code.label"),
        defaultMessage: "Locales"
      },
      name: "code",
      options,
      placeholder: {
        id: "components.placeholder.select",
        defaultMessage: "Select"
      },
      required: true,
      size: 6,
      type: "enumeration"
    },
    {
      hint: {
        id: getTranslation("Settings.locales.modal.create.name.label.description"),
        defaultMessage: "Locale will be displayed under that name in the administration panel"
      },
      label: {
        id: getTranslation("Settings.locales.modal.create.name.label"),
        defaultMessage: "Locale display name"
      },
      name: "name",
      required: true,
      size: 6,
      type: "string"
    }
  ].map((field) => ({
    ...field,
    hint: field.hint ? formatMessage(field.hint) : void 0,
    label: formatMessage(field.label),
    placeholder: field.placeholder ? formatMessage(field.placeholder) : void 0
  }));
  return (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: translatedForm.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(FormRenderer, { ...field }) }, field.name)) });
};
var AdvancedForm = ({ isDefaultLocale }) => {
  const { formatMessage } = useIntl();
  const form = [
    {
      disabled: isDefaultLocale,
      hint: {
        id: getTranslation("Settings.locales.modal.advanced.setAsDefault.hint"),
        defaultMessage: "One default locale is required, change it by selecting another one"
      },
      label: {
        id: getTranslation("Settings.locales.modal.advanced.setAsDefault"),
        defaultMessage: "Set as default locale"
      },
      name: "isDefault",
      size: 6,
      type: "boolean"
    }
  ].map((field) => ({
    ...field,
    hint: field.hint ? formatMessage(field.hint) : void 0,
    label: formatMessage(field.label)
  }));
  return (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: form.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(FormRenderer, { ...field }) }, field.name)) });
};
var FormRenderer = (field) => {
  switch (field.type) {
    case "enumeration":
      return (0, import_jsx_runtime.jsx)(EnumerationInput, { ...field });
    default:
      return (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field });
  }
};
var EnumerationInput = ({
  disabled,
  hint,
  label,
  name,
  options,
  placeholder,
  required
}) => {
  const { value, error, onChange } = useField(name);
  const { data: defaultLocales = [] } = useGetDefaultLocalesQuery();
  const handleChange = (value2) => {
    if (Array.isArray(defaultLocales)) {
      const locale = defaultLocales.find((locale2) => locale2.code === value2);
      onChange(name, value2);
      onChange("name", locale.name);
    } else {
      onChange(name, value2);
    }
  };
  return (0, import_jsx_runtime.jsxs)(Field.Root, { error, hint, name, required, children: [
    (0, import_jsx_runtime.jsx)(Field.Label, { children: label }),
    (0, import_jsx_runtime.jsx)(
      SingleSelect,
      {
        disabled,
        onChange: handleChange,
        placeholder,
        value,
        children: options.map((option) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: option.value, children: option.label }, option.value))
      }
    ),
    (0, import_jsx_runtime.jsx)(Field.Error, {}),
    (0, import_jsx_runtime.jsx)(Field.Hint, {})
  ] });
};
var DeleteLocale = ({ id, name }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [visible, setVisible] = React.useState(false);
  const [deleteLocale] = useDeleteLocaleMutation();
  const handleConfirm = async () => {
    try {
      const res = await deleteLocale(id);
      if ("error" in res) {
        toggleNotification({ type: "danger", message: formatAPIError(res.error) });
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.delete.success"),
          defaultMessage: "Deleted locale"
        })
      });
      setVisible(false);
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(Dialog.Root, { open: visible, onOpenChange: setVisible, children: [
    (0, import_jsx_runtime.jsx)(Dialog.Trigger, { children: (0, import_jsx_runtime.jsx)(
      IconButton,
      {
        onClick: () => setVisible(true),
        label: formatMessage(
          {
            id: getTranslation("Settings.list.actions.delete"),
            defaultMessage: "Delete {name} locale"
          },
          {
            name
          }
        ),
        variant: "ghost",
        children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
      }
    ) }),
    (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleConfirm })
  ] });
};
var EditLocale = (props) => {
  const { formatMessage } = useIntl();
  const [visible, setVisible] = React.useState(false);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      IconButton,
      {
        onClick: () => setVisible(true),
        label: formatMessage(
          {
            id: getTranslation("Settings.list.actions.edit"),
            defaultMessage: "Edit {name} locale"
          },
          {
            name: props.name
          }
        ),
        variant: "ghost",
        children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
      }
    ),
    (0, import_jsx_runtime.jsx)(EditModal, { ...props, open: visible, onOpenChange: setVisible })
  ] });
};
var EditModal = ({ id, code, isDefault, name, open, onOpenChange }) => {
  const { toggleNotification } = useNotification();
  const {
    _unstableFormatAPIError: formatAPIError,
    _unstableFormatValidationErrors: formatValidationErrors
  } = useAPIErrorHandler();
  const refetchPermissions = useAuth("EditModal", (state) => state.refetchPermissions);
  const { formatMessage } = useIntl();
  const titleId = useId();
  const [updateLocale] = useUpdateLocaleMutation();
  const handleSubmit = async ({ code: _code, ...data }, helpers) => {
    try {
      const res = await updateLocale({
        id,
        ...data
      });
      if ("error" in res) {
        if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
          helpers.setErrors(formatValidationErrors(res.error));
        } else {
          toggleNotification({ type: "danger", message: formatAPIError(res.error) });
        }
        return;
      }
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTranslation("Settings.locales.modal.edit.success"),
          defaultMessage: "Updated locale"
        })
      });
      refetchPermissions();
      onOpenChange(false);
    } catch (err) {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred, please try again"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsx)(Modal.Root, { open, onOpenChange, children: (0, import_jsx_runtime.jsx)(Modal.Content, { children: (0, import_jsx_runtime.jsxs)(
    Form,
    {
      method: "PUT",
      onSubmit: handleSubmit,
      initialValues: {
        code,
        name,
        isDefault
      },
      validationSchema: LOCALE_SCHEMA,
      children: [
        (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage(
          {
            id: getTranslation("Settings.list.actions.edit"),
            defaultMessage: "Edit a locale"
          },
          {
            name
          }
        ) }) }),
        (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsxs)(Tabs.Root, { variant: "simple", defaultValue: "basic", children: [
          (0, import_jsx_runtime.jsxs)(Flex, { justifyContent: "space-between", children: [
            (0, import_jsx_runtime.jsx)(Typography, { tag: "h2", variant: "beta", id: titleId, children: formatMessage({
              id: getTranslation("Settings.locales.modal.title"),
              defaultMessage: "Configuration"
            }) }),
            (0, import_jsx_runtime.jsxs)(Tabs.List, { "aria-labelledby": titleId, children: [
              (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "basic", children: formatMessage({
                id: getTranslation("Settings.locales.modal.base"),
                defaultMessage: "Basic settings"
              }) }),
              (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "advanced", children: formatMessage({
                id: getTranslation("Settings.locales.modal.advanced"),
                defaultMessage: "Advanced settings"
              }) })
            ] })
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)(Box, { paddingTop: 7, paddingBottom: 7, children: [
            (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "basic", children: (0, import_jsx_runtime.jsx)(BaseForm, { mode: "edit" }) }),
            (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "advanced", children: (0, import_jsx_runtime.jsx)(AdvancedForm, { isDefaultLocale: isDefault }) })
          ] })
        ] }) }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", children: formatMessage({ id: "app.components.Button.cancel", defaultMessage: "Cancel" }) }) }),
          (0, import_jsx_runtime.jsx)(SubmitButton, {})
        ] })
      ]
    }
  ) }) });
};
var LocaleTable = ({ locales = [], canDelete, canUpdate }) => {
  const [editLocaleId, setEditLocaleId] = React.useState();
  const { formatMessage } = useIntl();
  const handleClick = (localeId) => () => {
    if (canUpdate) {
      setEditLocaleId(localeId);
    }
  };
  return (0, import_jsx_runtime.jsxs)(Table, { colCount: 4, rowCount: locales.length + 1, children: [
    (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
      (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
        id: getTranslation("Settings.locales.row.id"),
        defaultMessage: "ID"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
        id: getTranslation("Settings.locales.row.displayName"),
        defaultMessage: "Display name"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
        id: getTranslation("Settings.locales.row.default-locale"),
        defaultMessage: "Default locale"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: "Actions" }) })
    ] }) }),
    (0, import_jsx_runtime.jsx)(Tbody, { children: locales.map((locale) => (0, import_jsx_runtime.jsxs)(React.Fragment, { children: [
      (0, import_jsx_runtime.jsxs)(
        Tr,
        {
          onClick: handleClick(locale.id),
          style: { cursor: canUpdate ? "pointer" : "default" },
          children: [
            (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: locale.id }) }),
            (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: locale.name }) }),
            (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: locale.isDefault ? formatMessage({
              id: getTranslation("Settings.locales.default"),
              defaultMessage: "Default"
            }) : null }) }),
            (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsxs)(Flex, { gap: 1, justifyContent: "flex-end", onClick: (e) => e.stopPropagation(), children: [
              canUpdate && (0, import_jsx_runtime.jsx)(EditLocale, { ...locale }),
              canDelete && !locale.isDefault && (0, import_jsx_runtime.jsx)(DeleteLocale, { ...locale })
            ] }) })
          ]
        }
      ),
      (0, import_jsx_runtime.jsx)(
        EditModal,
        {
          ...locale,
          onOpenChange: () => setEditLocaleId(void 0),
          open: editLocaleId === locale.id
        }
      )
    ] }, locale.id)) })
  ] });
};
var SettingsPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data: locales, isLoading: isLoadingLocales, error } = useGetLocalesQuery();
  const {
    isLoading: isLoadingRBAC,
    allowedActions: { canUpdate, canCreate, canDelete }
  } = useRBAC(PERMISSIONS);
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [error, formatAPIError, toggleNotification]);
  const isLoading = isLoadingLocales || isLoadingRBAC;
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (error || !Array.isArray(locales)) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, { tabIndex: -1, children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        primaryAction: (0, import_jsx_runtime.jsx)(CreateLocale, { disabled: !canCreate }),
        title: formatMessage({
          id: getTranslation("plugin.name"),
          defaultMessage: "Internationalization"
        }),
        subtitle: formatMessage({
          id: getTranslation("Settings.list.description"),
          defaultMessage: "Configure the settings"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: locales.length > 0 ? (0, import_jsx_runtime.jsx)(LocaleTable, { locales, canDelete, canUpdate }) : (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: void 0, height: void 0 }),
        content: formatMessage({
          id: getTranslation("Settings.list.empty.title"),
          defaultMessage: "There are no locales"
        }),
        action: (0, import_jsx_runtime.jsx)(CreateLocale, { disabled: !canCreate, variant: "secondary" })
      }
    ) })
  ] });
};
var ProtectedSettingsPage = () => {
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.read, children: (0, import_jsx_runtime.jsx)(SettingsPage, {}) });
};
export {
  ProtectedSettingsPage,
  SettingsPage
};
//# sourceMappingURL=SettingsPage-DbRFoF_H-C3RTK6LN.js.map
