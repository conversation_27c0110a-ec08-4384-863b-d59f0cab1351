{"version": 3, "sources": ["../../../@strapi/upload/dist/_chunks/tr--GzWXE_A.mjs"], "sourcesContent": ["const tr = {\n  \"apiError.FileTooBig\": \"Yüklenen dosya azami dosya boyutunu aşıyor.\",\n  \"upload.generic-error\": \"<PERSON>syayı yüklerken bir hata oluştu.\",\n  \"bulk.select.label\": \"Tüm dosyaları seçin\",\n  \"button.next\": \"İleri\",\n  \"checkControl.crop-duplicate\": \"Dosyayı yinele ve kırp\",\n  \"checkControl.crop-original\": \"Orijinal dosyayı kırp\",\n  \"content.isLoading\": \"İçerik yükleniyor.\",\n  \"control-card.add\": \"Ekle\",\n  \"control-card.cancel\": \"İptal\",\n  \"control-card.copy-link\": \"Bağlantıyı kopyala\",\n  \"control-card.crop\": \"Kırp\",\n  \"control-card.download\": \"İndir\",\n  \"control-card.edit\": \"Düzenle\",\n  \"control-card.replace-media\": \"Ortamı Değiştir\",\n  \"control-card.save\": \"Kay<PERSON>\",\n  \"control-card.stop-crop\": \"<PERSON>ırpmayı durdur\",\n  \"filter.add\": \"Filtre ekle\",\n  \"form.button.replace-media\": \"Ortamı değiştir\",\n  \"form.input.decription.file-alt\": \"Dosya gösterilemediğinde bu yazı görünecek.\",\n  \"form.input.label.file-alt\": \"Alternatif metin\",\n  \"form.input.label.file-caption\": \"Başlık\",\n  \"form.input.label.file-name\": \"Dosya adı\",\n  \"form.upload-url.error.url.invalid\": \"Bir URL geçersiz\",\n  \"form.upload-url.error.url.invalids\": \"{number} URL geçersiz\",\n  \"header.actions.add-assets\": \"Yeni dosya ekle\",\n  \"header.actions.add-folder\": \"Yeni klasör ekle\",\n  \"header.actions.add-assets.folder\": \"klasör\",\n  \"header.actions.upload-assets\": \"Dosya yükle\",\n  \"header.actions.upload-new-asset\": \"Yeni dosya yükle\",\n  \"header.content.assets-empty\": \"Dosya yok\",\n  \"header.content.assets\": \"{numberFolders} klasör - {numberAssets} dosya\",\n  \"input.button.label\": \"Dosyalara gözat\",\n  \"input.label\": \"Buraya sürükle ve bırak ya da\",\n  \"input.label-bold\": \"Sürükle ve Bırak\",\n  \"input.label-normal\": \"yüklemek için ya da\",\n  \"input.placeholder\": \"Dosya yüklemek için tıkla ya da bu alana sürükle ve bırak\",\n  \"input.placeholder.icon\": \"Dosyayı bu bölgeye bırak\",\n  \"input.url.description\": \"URL bağlantıları arasına yeni bir satır ekle.\",\n  \"input.url.label\": \"URL\",\n  \"list.assets.title\": \"Dosyalar\",\n  \"list.asset.at.finished\": \"Dosyaların yüklenmesi tamamlandı.\",\n  \"list.assets-empty.search\": \"Sonuç bulunamadı\",\n  \"list.assets-empty.subtitle\": \"Listeye bir tane ekle.\",\n  \"list.assets-empty.title\": \"Henüz bir dosya yok\",\n  \"list.assets-empty.title-withSearch\": \"Uygulanan filtrelere uygun sonuç yok.\",\n  \"list.assets.empty\": \"Ortam Kütüphanesi boş\",\n  \"list.assets.empty-upload\": \"İlk dosyalarını yükle...\",\n  \"list.assets.empty.no-permissions\": \"Görüntüleme izni yok\",\n  \"list.assets.loading-asset\": \"Ortamın ön izlemesi yükleniyor: {path}\",\n  \"list.assets.not-supported-content\": \"Ön izleme bulunmuyor\",\n  \"list.assets.preview-asset\": \"{path} yolundaki vidyonun önizlemesi\",\n  \"list.assets.selected\": \"{numberFolders} klasör - {numberAssets} dosya\",\n  \"list.assets.type-not-allowed\": \"Bu biçimdeki dosyalar yüklenemez.\",\n  \"list.assets.to-upload\": \"{number} yüklenmeye hazır\",\n  \"list.folder.edit\": \"Klasörü düzenle\",\n  \"list.folder.subtitle\": \"{folderCount} klasör, {filesCount} dosya\",\n  \"list.folders.title\": \"Klasörler\",\n  \"mediaLibraryInput.actions.nextSlide\": \"Sonraki slayt\",\n  \"mediaLibraryInput.actions.previousSlide\": \"Önceki slayt\",\n  \"mediaLibraryInput.placeholder\": \"Dosya yüklemek için tıkla ya da bu alana sürükle ve bırak\",\n  \"mediaLibraryInput.slideCount\": \"{n} / {m}\",\n  \"modal.file-details.date\": \"Tarih\",\n  \"modal.file-details.dimensions\": \"Ölçüler\",\n  \"modal.file-details.extension\": \"Uzantı\",\n  \"modal.file-details.size\": \"Boyut\",\n  \"modal.file-details.id\": \"Dosya Kimlik No\",\n  \"modal.folder.elements.count\": \"{folderCount} klasör, {assetCount} dosya\",\n  \"modal.header.browse\": \"Dosya yükle\",\n  \"modal.header.file-detail\": \"Detaylar\",\n  \"modal.header.pending-assets\": \"Bekleyen dosyalar\",\n  \"modal.header.select-files\": \"Seçili dosyalar\",\n  \"modal.header.go-back\": \"Geri git\",\n  \"modal.folder.move.title\": \"Öğeleri şuraya taşı\",\n  \"modal.nav.browse\": \"gözat\",\n  \"modal.nav.computer\": \"Bilgisayarımdan\",\n  \"modal.nav.selected\": \"seçilmiş\",\n  \"modal.nav.url\": \"URL'den\",\n  \"modal.remove.success-label\": \"Öğeler başarıyla silindi.\",\n  \"modal.move.success-label\": \"Öğeler başarıyla taşındı.\",\n  \"modal.selected-list.sub-header-subtitle\": \"Alandaki dosyaların sırasını değiştirmek için sürükle ve bırak\",\n  \"modal.upload-list.footer.button\": \"Kütüphaneye {number} dosya yükle\",\n  \"modal.upload-list.sub-header-subtitle\": \"Ortam Kütüphanesine yüklemeden önce dosyaları yönet\",\n  \"modal.upload-list.sub-header.button\": \"Daha fazla dosya yükle\",\n  \"modal.upload.cancelled\": \"Yükleme iptal edildi.\",\n  \"page.title\": \"Ayarlar - Ortam Kütüphanesi\",\n  \"permissions.not-allowed.update\": \"Bu dosyayı düzenlemeye yetkin yok.\",\n  \"plugin.description.long\": \"Ortam dosyası yönetimi.\",\n  \"plugin.description.short\": \"Ortam dosyası yönetimi.\",\n  \"plugin.name\": \"Ortam kütüphanesi\",\n  \"search.clear.label\": \"Aramayı temizle\",\n  \"search.label\": \"Dosya ara\",\n  \"search.placeholder\": \"ör: aydaki ilk köpek\",\n  \"settings.blockTitle\": \"Dosya Yönetimi\",\n  \"settings.form.autoOrientation.description\": \"Bu seçeneği aktifleştirdiğinde görsel EXIF oryantasyon etiketine göre otomatik olarak döndürülecek.\",\n  \"settings.form.autoOrientation.label\": \"Oto oryantasyon\",\n  \"settings.form.responsiveDimensions.description\": \"Bu seçeneği aktifleştirdiğinde yüklenen dosyanın birden çok ölçüsü (küçük, orta, büyük) oluşturalacak.\",\n  \"settings.form.responsiveDimensions.label\": \"Duyarlı tasarım dostu yükleme\",\n  \"settings.form.sizeOptimization.description\": \"Bu seçeneği aktifleştirdiğinizde görsel boyutu ufalacak ve kalitesi biraz azaltılacak.\",\n  \"settings.form.sizeOptimization.label\": \"Boyut iyileştirmesi\",\n  \"settings.form.videoPreview.description\": \"Bu seçeneği aktifleştirdiğinizde vidyonun 6 saniyelik GIF biçiminde bir ön izlemesi oluşturulacak.\",\n  \"settings.form.videoPreview.label\": \"Ön izleme\",\n  \"settings.header.label\": \"Ortam Kütüphanesi\",\n  \"settings.section.doc.label\": \"Belge\",\n  \"settings.section.image.label\": \"Görsel\",\n  \"settings.section.video.label\": \"Vidyo\",\n  \"settings.sub-header.label\": \"Ortam Kütüphanesi ayarlarını düzenleyin\",\n  \"sort.created_at_asc\": \"En eski yüklemeler\",\n  \"sort.created_at_desc\": \"En yeni yüklemeler\",\n  \"sort.label\": \"Diz\",\n  \"sort.name_asc\": \"Alfabetik Sıra (A to Z)\",\n  \"sort.name_desc\": \"Ters Alfabetik Sıra (Z to A)\",\n  \"sort.updated_at_asc\": \"En eski yüklemeler\",\n  \"sort.updated_at_desc\": \"En yeni yüklemeler\",\n  \"tabs.title\": \"Dosyalarını nasıl yüklemek istersin?\",\n  \"window.confirm.close-modal.file\": \"Emin misin? Değişikliklerin kaybolacak.\",\n  \"window.confirm.close-modal.files\": \"Emin misin? Henüz yüklenmemiş dosyaların var.\"\n};\nexport {\n  tr as default\n};\n//# sourceMappingURL=tr--GzWXE_A.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,gCAAgC;AAAA,EAChC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AACtC;", "names": []}