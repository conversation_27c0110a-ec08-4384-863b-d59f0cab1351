import { createContext, ReactNode, useContext } from "react";
import type { Notification } from "@app/types/notifications";
import { useQueryWebSocketNotifications } from "@app/hooks/notifications/use-query-websocket-notifications";

interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string | number) => void;
  markAllAsRead: () => void;
  loadNotifications: () => Promise<any>;
  showNotificationDetail: (notification: Notification) => void;
  deleteNotification: (id: string | number) => void;
  filterByType: (type: string | null) => void;
  getNotificationTypes: () => string[];
  activeFilter: string | null;
}

const NotificationsContext = createContext<
  NotificationsContextType | undefined
>(undefined);

export const useNotificationsContext = () => {
  const context = useContext(NotificationsContext);
  if (!context) {
    throw new Error(
      "useNotificationsContext debe ser usado dentro de un NotificationsProvider",
    );
  }
  return context;
};

interface NotificationsProviderProps {
  children: ReactNode;
}

export const NotificationsProvider = ({
  children,
}: NotificationsProviderProps) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    loadNotifications,
    showNotificationDetail,
    deleteNotification,
    isConnected,
    connectionStatus,
    connectionError,
  } = useQueryWebSocketNotifications();

  // Funciones de filtrado (mantenemos compatibilidad)
  const activeFilter = null; // Por ahora no implementamos filtrado

  const filterByType = (type: string | null) => {
    // Esta funcionalidad se puede implementar en el futuro si es necesaria
    console.log("Filtro por tipo:", type);
  };

  const getNotificationTypes = () => {
    const types = [
      ...new Set(notifications.map((n) => n.type).filter(Boolean)),
    ];
    return ["Todos los tipos", ...types];
  };

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        loadNotifications,
        showNotificationDetail,
        deleteNotification,
        filterByType,
        getNotificationTypes: () =>
          getNotificationTypes().filter(
            (type): type is string => type !== undefined,
          ),
        activeFilter,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
};
