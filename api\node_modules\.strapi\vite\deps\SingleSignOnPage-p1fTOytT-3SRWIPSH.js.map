{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/SingleSignOnPage.tsx"], "sourcesContent": ["import {\n  Button,\n  Flex,\n  Grid,\n  MultiSelect,\n  MultiSelectOption,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport {\n  Form,\n  FormHelpers,\n  InputProps,\n  useField,\n} from '../../../../../../admin/src/components/Form';\nimport { InputRenderer } from '../../../../../../admin/src/components/FormInputs/Renderer';\nimport { Layouts } from '../../../../../../admin/src/components/Layouts/Layout';\nimport { Page } from '../../../../../../admin/src/components/PageHelpers';\nimport { useTypedSelector } from '../../../../../../admin/src/core/store/hooks';\nimport { useNotification } from '../../../../../../admin/src/features/Notifications';\nimport { useAdminRoles } from '../../../../../../admin/src/hooks/useAdminRoles';\nimport { useAPIErrorHandler } from '../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../../../admin/src/hooks/useRBAC';\nimport {\n  useGetProviderOptionsQuery,\n  useUpdateProviderOptionsMutation,\n} from '../../../../../../admin/src/services/auth';\nimport { isBaseQueryError } from '../../../../../../admin/src/utils/baseQuery';\nimport { translatedErrors } from '../../../../../../admin/src/utils/translatedErrors';\nimport { ProvidersOptions } from '../../../../../../shared/contracts/admin';\n\nconst SCHEMA = yup.object().shape({\n  autoRegister: yup.bool().required(translatedErrors.required),\n  defaultRole: yup.mixed().when('autoRegister', (value, initSchema) => {\n    return value ? initSchema.required(translatedErrors.required) : initSchema.nullable();\n  }),\n  ssoLockedRoles: yup\n    .array()\n    .nullable()\n    .of(\n      yup.mixed().when('ssoLockedRoles', (value, initSchema) => {\n        return value ? initSchema.required(translatedErrors.required) : initSchema.nullable();\n      })\n    ),\n});\n\nexport const SingleSignOnPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  const { isLoading: isLoadingProviderOptions, data } = useGetProviderOptionsQuery();\n\n  const [updateProviderOptions, { isLoading: isSubmittingForm }] =\n    useUpdateProviderOptionsMutation();\n\n  const {\n    isLoading: isLoadingPermissions,\n    allowedActions: { canUpdate, canRead: canReadRoles },\n  } = useRBAC({\n    ...permissions.settings?.sso,\n    readRoles: permissions.settings?.roles.read ?? [],\n  });\n\n  const { roles, isLoading: isLoadingRoles } = useAdminRoles(undefined, {\n    skip: !canReadRoles,\n  });\n\n  const handleSubmit = async (\n    body: ProvidersOptions.Request['body'],\n    helpers: FormHelpers<ProvidersOptions.Request['body']>\n  ) => {\n    try {\n      const res = await updateProviderOptions(body);\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved' }),\n      });\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again.',\n        }),\n      });\n    }\n  };\n\n  const isLoadingData = isLoadingRoles || isLoadingPermissions || isLoadingProviderOptions;\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'SSO',\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isSubmittingForm || isLoadingData} tabIndex={-1}>\n        <Form\n          method=\"PUT\"\n          onSubmit={handleSubmit}\n          validationSchema={SCHEMA}\n          disabled={!canUpdate}\n          initialValues={\n            data || {\n              autoRegister: false,\n              defaultRole: null,\n              ssoLockedRoles: null,\n            }\n          }\n        >\n          {({ modified, isSubmitting }) => (\n            <>\n              <Layouts.Header\n                primaryAction={\n                  <Button\n                    disabled={!modified}\n                    loading={isSubmitting}\n                    startIcon={<Check />}\n                    type=\"submit\"\n                    size=\"L\"\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                }\n                title={formatMessage({\n                  id: 'Settings.sso.title',\n                  defaultMessage: 'Single Sign-On',\n                })}\n                subtitle={formatMessage({\n                  id: 'Settings.sso.description',\n                  defaultMessage: 'Configure the settings for the Single Sign-On feature.',\n                })}\n              />\n              <Layouts.Content>\n                {isSubmitting || isLoadingData ? (\n                  <Page.Loading />\n                ) : (\n                  <Flex\n                    direction=\"column\"\n                    alignItems=\"stretch\"\n                    gap={4}\n                    background=\"neutral0\"\n                    padding={6}\n                    shadow=\"filterShadow\"\n                    hasRadius\n                  >\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'global.settings',\n                        defaultMessage: 'Settings',\n                      })}\n                    </Typography>\n                    <Grid.Root gap={4}>\n                      {[\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.registration.description',\n                            defaultMessage: 'Create new user on SSO login if no account exists',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.registration.label',\n                            defaultMessage: 'Auto-registration',\n                          }),\n                          name: 'autoRegister',\n                          size: 6,\n                          type: 'boolean' as const,\n                        },\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.defaultRole.description',\n                            defaultMessage:\n                              'It will attach the new authenticated user to the selected role',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.defaultRole.label',\n                            defaultMessage: 'Default role',\n                          }),\n                          name: 'defaultRole',\n                          options: roles.map(({ id, name }) => ({\n                            label: name,\n                            value: id.toString(),\n                          })),\n                          placeholder: formatMessage({\n                            id: 'components.InputSelect.option.placeholder',\n                            defaultMessage: 'Choose here',\n                          }),\n                          size: 6,\n                          type: 'enumeration' as const,\n                        },\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.localAuthenticationLock.description',\n                            defaultMessage:\n                              'Select the roles for which you want to disable the local authentication',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.localAuthenticationLock.label',\n                            defaultMessage: 'Local authentication lock-out',\n                          }),\n                          name: 'ssoLockedRoles',\n                          options: roles.map(({ id, name }) => ({\n                            label: name,\n                            value: id.toString(),\n                          })),\n                          placeholder: formatMessage({\n                            id: 'components.InputSelect.option.placeholder',\n                            defaultMessage: 'Choose here',\n                          }),\n                          size: 6,\n                          type: 'multi' as const,\n                        },\n                      ].map(({ size, ...field }) => (\n                        <Grid.Item\n                          key={field.name}\n                          col={size}\n                          direction=\"column\"\n                          alignItems=\"stretch\"\n                        >\n                          <FormInputRenderer {...field} />\n                        </Grid.Item>\n                      ))}\n                    </Grid.Root>\n                  </Flex>\n                )}\n              </Layouts.Content>\n            </>\n          )}\n        </Form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\ntype FormInputProps = InputProps | MultiSelectInputProps;\n\nconst FormInputRenderer = (props: FormInputProps) => {\n  switch (props.type) {\n    case 'multi':\n      return <MultiSelectInput {...props} />;\n    default:\n      return <InputRenderer {...props} />;\n  }\n};\n\ntype MultiSelectInputProps = Omit<Extract<InputProps, { type: 'enumeration' }>, 'type'> & {\n  type: 'multi';\n};\n\nconst MultiSelectInput = ({ hint, label, name, options, ...props }: MultiSelectInputProps) => {\n  const field = useField(name);\n\n  return (\n    <Field.Root name={name} hint={hint} error={field.error}>\n      <Field.Label>{label}</Field.Label>\n      <MultiSelect\n        onChange={(value) => field.onChange('ssoLockedRoles', value)}\n        onClear={() => field.onChange('ssoLockedRoles', [])}\n        value={field.value ?? []}\n        withTags\n        {...props}\n      >\n        {options.map(({ label, value }) => (\n          <MultiSelectOption key={value} value={value}>\n            {label}\n          </MultiSelectOption>\n        ))}\n      </MultiSelect>\n      <Field.Hint />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nexport const ProtectedSSO = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.sso?.main);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <SingleSignOnPage />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,IAAM,SAAaA,QAAO,EAAE,MAAM;EAChC,cAAkBA,QAAA,EAAO,SAASC,YAAiB,QAAQ;EAC3D,aAAiB,OAAM,EAAE,KAAK,gBAAgB,CAAC,OAAO,eAAe;AACnE,WAAO,QAAQ,WAAW,SAASA,YAAiB,QAAQ,IAAI,WAAW,SAAS;EAAA,CACrF;EACD,gBACGD,QAAM,EACN,SAAA,EACA;IACK,OAAM,EAAE,KAAK,kBAAkB,CAAC,OAAO,eAAe;AACxD,aAAO,QAAQ,WAAW,SAASC,YAAiB,QAAQ,IAAI,WAAW,SAAS;IAAA,CACrF;EAAA;AAEP,CAAC;AAEM,IAAM,mBAAmB,MAAM;;AAC9B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,WAAW;AACrE,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AACzC,QAAA;IACJ,yBAAyB;IACzB,iCAAiC;EAAA,IAC/B,mBAAmB;AAEvB,QAAM,EAAE,WAAW,0BAA0B,KAAA,IAAS,2BAA2B;AAEjF,QAAM,CAAC,uBAAuB,EAAE,WAAW,iBAAiB,CAAC,IAC3D,iCAAiC;AAE7B,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,WAAW,SAAS,aAAa;EAAA,IACjD,QAAQ;IACV,IAAG,iBAAY,aAAZ,mBAAsB;IACzB,aAAW,iBAAY,aAAZ,mBAAsB,MAAM,SAAQ,CAAA;EAAC,CACjD;AAED,QAAM,EAAE,OAAO,WAAW,eAAe,IAAI,cAAc,QAAW;IACpE,MAAM,CAAC;EAAA,CACR;AAEK,QAAA,eAAe,OACnB,MACA,YACG;AACC,QAAA;AACI,YAAA,MAAM,MAAM,sBAAsB,IAAI;AAE5C,UAAI,WAAW,KAAK;AAClB,YAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACvE,kBAAQ,UAAU,uBAAuB,IAAI,KAAK,CAAC;QAAA,OAC9C;AACc,6BAAA;YACjB,MAAM;YACN,SAAS,eAAe,IAAI,KAAK;UAAA,CAClC;QAAA;AAGH;MAAA;AAGiB,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,6BAAA,CAA8B;MAAA,CAC5D;IAAA,SACM,KAAK;AACO,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH;AAGI,QAAA,gBAAgB,kBAAkB,wBAAwB;AAG9D,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM;MAAA;IACR,EAEJ,CAAA;QACA,wBAAC,KAAK,MAAL,EAAU,aAAW,oBAAoB,eAAe,UAAU,IACjE,cAAA;MAAC;MAAA;QACC,QAAO;QACP,UAAU;QACV,kBAAkB;QAClB,UAAU,CAAC;QACX,eACE,QAAQ;UACN,cAAc;UACd,aAAa;UACb,gBAAgB;QAAA;QAInB,UAAC,CAAA,EAAE,UAAU,aAAA,UAEV,yBAAA,6BAAA,EAAA,UAAA;cAAA;YAAC,QAAQ;YAAR;cACC,mBACE;gBAAC;gBAAA;kBACC,UAAU,CAAC;kBACX,SAAS;kBACT,eAAA,wBAAY,eAAM,CAAA,CAAA;kBAClB,MAAK;kBACL,MAAK;kBAEJ,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;gBAAA;cAAA;cAGL,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU,cAAc;gBACtB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UAAA;cAEH,wBAAC,QAAQ,SAAR,EACE,UAAA,gBAAgB,oBACd,wBAAA,KAAK,SAAL,CAAA,CAAa,QAEd;YAAC;YAAA;cACC,WAAU;cACV,YAAW;cACX,KAAK;cACL,YAAW;cACX,SAAS;cACT,QAAO;cACP,WAAS;cAET,UAAA;oBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBAAA,CACjB,EACH,CAAA;oBACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA;kBACC;oBACE,MAAM,cAAc;sBAClB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,MAAM;oBACN,MAAM;oBACN,MAAM;kBAAA;kBAER;oBACE,MAAM,cAAc;sBAClB,IAAI;sBACJ,gBACE;oBAAA,CACH;oBACD,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,MAAM;oBACN,SAAS,MAAM,IAAI,CAAC,EAAE,IAAI,KAAA,OAAY;sBACpC,OAAO;sBACP,OAAO,GAAG,SAAS;oBAAA,EACnB;oBACF,aAAa,cAAc;sBACzB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,MAAM;oBACN,MAAM;kBAAA;kBAER;oBACE,MAAM,cAAc;sBAClB,IAAI;sBACJ,gBACE;oBAAA,CACH;oBACD,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,MAAM;oBACN,SAAS,MAAM,IAAI,CAAC,EAAE,IAAI,KAAA,OAAY;sBACpC,OAAO;sBACP,OAAO,GAAG,SAAS;oBAAA,EACnB;oBACF,aAAa,cAAc;sBACzB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,MAAM;oBACN,MAAM;kBAAA;gBACR,EACA,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,UAChB;kBAAC,KAAK;kBAAL;oBAEC,KAAK;oBACL,WAAU;oBACV,YAAW;oBAEX,cAAA,wBAAC,mBAAmB,EAAA,GAAG,MAAO,CAAA;kBAAA;kBALzB,MAAM;gBAAA,CAOd,EACH,CAAA;cAAA;YAAA;UAAA,EAGN,CAAA;QAAA,EACF,CAAA;MAAA;IAAA,EAGN,CAAA;EAAA,EACF,CAAA;AAEJ;AAIA,IAAM,oBAAoB,CAAC,UAA0B;AACnD,UAAQ,MAAM,MAAM;IAClB,KAAK;AACI,iBAAA,wBAAC,kBAAkB,EAAA,GAAG,MAAO,CAAA;IACtC;AACS,iBAAA,wBAACC,uBAAe,EAAA,GAAG,MAAO,CAAA;EAAA;AAEvC;AAMA,IAAM,mBAAmB,CAAC,EAAE,MAAM,OAAO,MAAM,SAAS,GAAG,MAAA,MAAmC;AACtF,QAAA,QAAQ,SAAS,IAAI;AAGzB,aAAA,yBAAC,MAAM,MAAN,EAAW,MAAY,MAAY,OAAO,MAAM,OAC/C,UAAA;QAAC,wBAAA,MAAM,OAAN,EAAa,UAAM,MAAA,CAAA;QACpB;MAAC;MAAA;QACC,UAAU,CAAC,UAAU,MAAM,SAAS,kBAAkB,KAAK;QAC3D,SAAS,MAAM,MAAM,SAAS,kBAAkB,CAAA,CAAE;QAClD,OAAO,MAAM,SAAS,CAAA;QACtB,UAAQ;QACP,GAAG;QAEH,UAAQ,QAAA,IAAI,CAAC,EAAE,OAAAC,QAAO,MAAA,UACrB,wBAAC,mBAA8B,EAAA,OAC5B,UAAAA,OAAAA,GADqB,KAExB,CACD;MAAA;IAAA;QAEH,wBAAC,MAAM,MAAN,CAAA,CAAW;QACZ,wBAAC,MAAM,OAAN,CAAA,CAAY;EAAA,EACf,CAAA;AAEJ;AAEO,IAAM,eAAe,MAAM;AAC1B,QAAA,cAAc,iBAAiB,CAAC,UAAA;;AAAU,6BAAM,UAAU,YAAY,aAA5B,mBAAsC,QAAtC,mBAA2C;GAAI;AAE/F,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,kBAAA,CAAA,CAAiB,EACpB,CAAA;AAEJ;", "names": ["create", "translatedErrors", "InputR<PERSON><PERSON>", "label"]}