export default {
  routes: [
    // Ruta personalizada para el asistente legal
    {
      method: 'POST',
      path: '/legal-assistant/query',
      handler: 'legal-assistant.query',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    // Rutas CRUD estándar
    {
      method: 'GET',
      path: '/legal-assistants',
      handler: 'legal-assistant.find',
      config: { auth: false }
    },
    {
      method: 'GET',
      path: '/legal-assistants/:id',
      handler: 'legal-assistant.findOne',
      config: { auth: false }
    },
    {
      method: 'POST',
      path: '/legal-assistants',
      handler: 'legal-assistant.create',
      config: { auth: false }
    },
    {
      method: 'PUT',
      path: '/legal-assistants/:id',
      handler: 'legal-assistant.update',
      config: { auth: false }
    },
    {
      method: 'DELETE',
      path: '/legal-assistants/:id',
      handler: 'legal-assistant.delete',
      config: { auth: false }
    }
  ]
};
