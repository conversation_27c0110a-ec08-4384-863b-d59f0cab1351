{"version": 3, "sources": ["../../../@strapi/i18n/admin/src/utils/baseQuery.ts", "../../../@strapi/i18n/admin/src/components/CreateLocale.tsx", "../../../@strapi/i18n/admin/src/components/DeleteLocale.tsx", "../../../@strapi/i18n/admin/src/components/EditLocale.tsx", "../../../@strapi/i18n/admin/src/components/LocaleTable.tsx", "../../../@strapi/i18n/admin/src/pages/SettingsPage.tsx"], "sourcesContent": ["import { SerializedError } from '@reduxjs/toolkit';\nimport { type ApiError, type UnknownApiError } from '@strapi/admin/strapi-admin';\n\ntype BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\nconst isBaseQueryError = (error: BaseQueryError): error is ApiError | UnknownApiError => {\n  return error.name !== undefined;\n};\n\nexport { isBaseQueryError };\n", "import * as React from 'react';\n\nimport {\n  Form,\n  type InputProps,\n  Input<PERSON>enderer,\n  useField,\n  type FormHelpers,\n  useForm,\n  useAP<PERSON><PERSON>r<PERSON>and<PERSON>,\n  useNotification,\n  useAuth,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  ButtonProps,\n  Divider,\n  Field,\n  Flex,\n  Grid,\n  Modal,\n  SingleSelect,\n  SingleSelectOption,\n  Tabs,\n  Typography,\n  useId,\n} from '@strapi/design-system';\nimport { Check, Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { CreateLocale } from '../../../shared/contracts/locales';\nimport { useCreateLocaleMutation, useGetDefaultLocalesQuery } from '../services/locales';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { getTranslation } from '../utils/getTranslation';\n\n/* -------------------------------------------------------------------------------------------------\n * CreateLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CreateLocaleProps extends Pick<ButtonProps, 'disabled' | 'variant'> {}\n\nconst CreateLocale = ({ disabled, variant = 'default' }: CreateLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const [visible, setVisible] = React.useState(false);\n\n  return (\n    <Modal.Root open={visible} onOpenChange={setVisible}>\n      <Modal.Trigger>\n        <Button\n          variant={variant}\n          disabled={disabled}\n          startIcon={<Plus />}\n          onClick={() => setVisible(true)}\n          size=\"S\"\n        >\n          {formatMessage({\n            id: getTranslation('Settings.list.actions.add'),\n            defaultMessage: 'Add new locale',\n          })}\n        </Button>\n      </Modal.Trigger>\n      <CreateModal onClose={() => setVisible(false)} />\n    </Modal.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CreateModal\n * -----------------------------------------------------------------------------------------------*/\n\nconst LOCALE_SCHEMA = yup.object().shape({\n  code: yup.string().nullable().required({\n    id: 'Settings.locales.modal.create.code.error',\n    defaultMessage: 'Please select a locale',\n  }),\n  name: yup\n    .string()\n    .nullable()\n    .max(50, {\n      id: 'Settings.locales.modal.create.name.error.min',\n      defaultMessage: 'The locale display name can only be less than 50 characters.',\n    })\n    .required({\n      id: 'Settings.locales.modal.create.name.error.required',\n      defaultMessage: 'Please give the locale a display name',\n    }),\n  isDefault: yup.boolean(),\n});\n\ntype FormValues = CreateLocale.Request['body'];\n\nconst initialFormValues = {\n  code: '',\n  name: '',\n  isDefault: false,\n} satisfies FormValues;\n\ntype ModalCreateProps = {\n  onClose: () => void;\n};\n\nconst CreateModal = ({ onClose }: ModalCreateProps) => {\n  const titleId = useId();\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const [createLocale] = useCreateLocaleMutation();\n  const { formatMessage } = useIntl();\n  const refetchPermissions = useAuth('CreateModal', (state) => state.refetchPermissions);\n\n  const handleSubmit = async (values: FormValues, helpers: FormHelpers<FormValues>) => {\n    try {\n      const res = await createLocale(values);\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.create.success'),\n          defaultMessage: 'Created locale',\n        }),\n      });\n\n      refetchPermissions();\n      onClose();\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Modal.Content>\n      <Form\n        method=\"POST\"\n        initialValues={initialFormValues}\n        validationSchema={LOCALE_SCHEMA}\n        onSubmit={handleSubmit}\n      >\n        <Modal.Header>\n          <Modal.Title>\n            {formatMessage({\n              id: getTranslation('Settings.list.actions.add'),\n              defaultMessage: 'Add new locale',\n            })}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Tabs.Root variant=\"simple\" defaultValue=\"basic\">\n            <Flex justifyContent=\"space-between\">\n              <Typography tag=\"h2\" variant=\"beta\" id={titleId}>\n                {formatMessage({\n                  id: getTranslation('Settings.locales.modal.title'),\n                  defaultMessage: 'Configuration',\n                })}\n              </Typography>\n              <Tabs.List aria-labelledby={titleId}>\n                <Tabs.Trigger value=\"basic\">\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.base'),\n                    defaultMessage: 'Basic settings',\n                  })}\n                </Tabs.Trigger>\n                <Tabs.Trigger value=\"advanced\">\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.advanced'),\n                    defaultMessage: 'Advanced settings',\n                  })}\n                </Tabs.Trigger>\n              </Tabs.List>\n            </Flex>\n\n            <Divider />\n\n            <Box paddingTop={7} paddingBottom={7}>\n              <Tabs.Content value=\"basic\">\n                <BaseForm />\n              </Tabs.Content>\n              <Tabs.Content value=\"advanced\">\n                <AdvancedForm />\n              </Tabs.Content>\n            </Box>\n          </Tabs.Root>\n        </Modal.Body>\n        <Modal.Footer>\n          <Modal.Close>\n            <Button variant=\"tertiary\">\n              {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n            </Button>\n          </Modal.Close>\n          <SubmitButton />\n        </Modal.Footer>\n      </Form>\n    </Modal.Content>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * SubmitButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SubmitButton = () => {\n  const { formatMessage } = useIntl();\n  const isSubmitting = useForm('SubmitButton', (state) => state.isSubmitting);\n  const modified = useForm('SubmitButton', (state) => state.modified);\n\n  return (\n    <Button type=\"submit\" startIcon={<Check />} disabled={isSubmitting || !modified}>\n      {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n    </Button>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BaseForm\n * -----------------------------------------------------------------------------------------------*/\n\ninterface BaseFormProps {\n  mode?: 'create' | 'edit';\n}\n\nconst BaseForm = ({ mode = 'create' }: BaseFormProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { data: defaultLocales, error } = useGetDefaultLocalesQuery();\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  if (!Array.isArray(defaultLocales)) {\n    return null;\n  }\n\n  const options = defaultLocales.map((locale) => ({\n    label: locale.name,\n    value: locale.code,\n  }));\n\n  const translatedForm = [\n    {\n      disabled: mode !== 'create',\n      label: {\n        id: getTranslation('Settings.locales.modal.create.code.label'),\n        defaultMessage: 'Locales',\n      },\n      name: 'code',\n      options,\n      placeholder: {\n        id: 'components.placeholder.select',\n        defaultMessage: 'Select',\n      },\n      required: true,\n      size: 6,\n      type: 'enumeration' as const,\n    },\n    {\n      hint: {\n        id: getTranslation('Settings.locales.modal.create.name.label.description'),\n        defaultMessage: 'Locale will be displayed under that name in the administration panel',\n      },\n      label: {\n        id: getTranslation('Settings.locales.modal.create.name.label'),\n        defaultMessage: 'Locale display name',\n      },\n      name: 'name',\n      required: true,\n      size: 6,\n      type: 'string' as const,\n    },\n  ].map((field) => ({\n    ...field,\n    hint: field.hint ? formatMessage(field.hint) : undefined,\n    label: formatMessage(field.label),\n    placeholder: field.placeholder ? formatMessage(field.placeholder) : undefined,\n  }));\n\n  return (\n    <Grid.Root gap={4}>\n      {translatedForm.map(({ size, ...field }) => (\n        <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n          <FormRenderer {...field} />\n        </Grid.Item>\n      ))}\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AdvancedForm\n * -----------------------------------------------------------------------------------------------*/\n\ntype AdvancedFormProps = {\n  isDefaultLocale?: boolean;\n};\n\nconst AdvancedForm = ({ isDefaultLocale }: AdvancedFormProps) => {\n  const { formatMessage } = useIntl();\n\n  const form = [\n    {\n      disabled: isDefaultLocale,\n      hint: {\n        id: getTranslation('Settings.locales.modal.advanced.setAsDefault.hint'),\n        defaultMessage: 'One default locale is required, change it by selecting another one',\n      },\n      label: {\n        id: getTranslation('Settings.locales.modal.advanced.setAsDefault'),\n        defaultMessage: 'Set as default locale',\n      },\n      name: 'isDefault',\n      size: 6,\n      type: 'boolean' as const,\n    },\n  ].map((field) => ({\n    ...field,\n    hint: field.hint ? formatMessage(field.hint) : undefined,\n    label: formatMessage(field.label),\n  })) satisfies InputProps[];\n\n  return (\n    <Grid.Root gap={4}>\n      {form.map(({ size, ...field }) => (\n        <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n          <FormRenderer {...field} />\n        </Grid.Item>\n      ))}\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * FormRenderer\n * -----------------------------------------------------------------------------------------------*/\n\nconst FormRenderer = (field: InputProps) => {\n  switch (field.type) {\n    /**\n     * This will override the default input renderer\n     * choice for `enumeration`.\n     */\n    case 'enumeration':\n      return <EnumerationInput {...field} />;\n    default:\n      return <InputRenderer {...field} />;\n  }\n};\n\nconst EnumerationInput = ({\n  disabled,\n  hint,\n  label,\n  name,\n  options,\n  placeholder,\n  required,\n}: Extract<InputProps, { type: 'enumeration' }>) => {\n  const { value, error, onChange } = useField(name);\n  const { data: defaultLocales = [] } = useGetDefaultLocalesQuery();\n\n  const handleChange = (value: string) => {\n    if (Array.isArray(defaultLocales)) {\n      // We know it exists because the options are created from the list of default locales\n      const locale = defaultLocales.find((locale) => locale.code === value)!;\n\n      onChange(name, value);\n      // This lets us automatically fill the name field with the locale name\n      onChange('name', locale.name);\n    } else {\n      onChange(name, value);\n    }\n  };\n\n  return (\n    <Field.Root error={error} hint={hint} name={name} required={required}>\n      <Field.Label>{label}</Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        // @ts-expect-error – This will dissapear when the DS removes support for numbers to be returned by SingleSelect.\n        onChange={handleChange}\n        placeholder={placeholder}\n        value={value}\n      >\n        {options.map((option) => (\n          <SingleSelectOption value={option.value} key={option.value}>\n            {option.label}\n          </SingleSelectOption>\n        ))}\n      </SingleSelect>\n      <Field.Error />\n      <Field.Hint />\n    </Field.Root>\n  );\n};\n\nexport { CreateLocale, BaseForm, AdvancedForm, SubmitButton, LOCALE_SCHEMA };\n", "import * as React from 'react';\n\nimport { Confirm<PERSON>ial<PERSON>, useAPIErrorHand<PERSON>, useNotification } from '@strapi/admin/strapi-admin';\nimport { Dialog, IconButton } from '@strapi/design-system';\nimport { Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDeleteLocaleMutation } from '../services/locales';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport type { Locale } from '../../../shared/contracts/locales';\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DeleteLocaleProps extends Locale {}\n\nconst DeleteLocale = ({ id, name }: DeleteLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const [visible, setVisible] = React.useState(false);\n\n  const [deleteLocale] = useDeleteLocaleMutation();\n  const handleConfirm = async () => {\n    try {\n      const res = await deleteLocale(id);\n\n      if ('error' in res) {\n        toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.delete.success'),\n          defaultMessage: 'Deleted locale',\n        }),\n      });\n\n      setVisible(false);\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Dialog.Root open={visible} onOpenChange={setVisible}>\n      <Dialog.Trigger>\n        <IconButton\n          onClick={() => setVisible(true)}\n          label={formatMessage(\n            {\n              id: getTranslation('Settings.list.actions.delete'),\n              defaultMessage: 'Delete {name} locale',\n            },\n            {\n              name,\n            }\n          )}\n          variant=\"ghost\"\n        >\n          <Trash />\n        </IconButton>\n      </Dialog.Trigger>\n      <ConfirmDialog onConfirm={handleConfirm} />\n    </Dialog.Root>\n  );\n};\n\nexport { DeleteLocale };\n", "import * as React from 'react';\n\nimport {\n  useNotification,\n  useAP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  useAuth,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  Divider,\n  Flex,\n  IconButton,\n  Modal,\n  Tabs,\n  Typography,\n  useId,\n} from '@strapi/design-system';\nimport { Pencil } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Locale, UpdateLocale } from '../../../shared/contracts/locales';\nimport { useUpdateLocaleMutation } from '../services/locales';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport { AdvancedForm, BaseForm, LOCALE_SCHEMA, SubmitButton } from './CreateLocale';\n\n/* -------------------------------------------------------------------------------------------------\n * EditLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EditLocaleProps extends Omit<EditModalProps, 'open' | 'onOpenChange'> {}\n\nconst EditLocale = (props: EditLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const [visible, setVisible] = React.useState(false);\n\n  return (\n    <>\n      <IconButton\n        onClick={() => setVisible(true)}\n        label={formatMessage(\n          {\n            id: getTranslation('Settings.list.actions.edit'),\n            defaultMessage: 'Edit {name} locale',\n          },\n          {\n            name: props.name,\n          }\n        )}\n        variant=\"ghost\"\n      >\n        <Pencil />\n      </IconButton>\n      <EditModal {...props} open={visible} onOpenChange={setVisible} />\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EditModal\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EditModalProps extends Pick<Locale, 'id' | 'isDefault' | 'name' | 'code'> {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\ntype FormValues = UpdateLocale.Request['body'] & { code: string };\n\n/**\n * @internal\n * @description Exported to be used when someone clicks on a table row.\n */\nconst EditModal = ({ id, code, isDefault, name, open, onOpenChange }: EditModalProps) => {\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const refetchPermissions = useAuth('EditModal', (state) => state.refetchPermissions);\n  const { formatMessage } = useIntl();\n  const titleId = useId();\n\n  const [updateLocale] = useUpdateLocaleMutation();\n  const handleSubmit = async (\n    { code: _code, ...data }: FormValues,\n    helpers: FormHelpers<FormValues>\n  ) => {\n    try {\n      /**\n       * We don't need to send the code, because the\n       * code can never be changed.\n       */\n      const res = await updateLocale({\n        id,\n        ...data,\n      });\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.edit.success'),\n          defaultMessage: 'Updated locale',\n        }),\n      });\n\n      refetchPermissions();\n      onOpenChange(false);\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Modal.Root open={open} onOpenChange={onOpenChange}>\n      <Modal.Content>\n        <Form\n          method=\"PUT\"\n          onSubmit={handleSubmit}\n          initialValues={{\n            code,\n            name,\n            isDefault,\n          }}\n          validationSchema={LOCALE_SCHEMA}\n        >\n          <Modal.Header>\n            <Modal.Title>\n              {formatMessage(\n                {\n                  id: getTranslation('Settings.list.actions.edit'),\n                  defaultMessage: 'Edit a locale',\n                },\n                {\n                  name,\n                }\n              )}\n            </Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            <Tabs.Root variant=\"simple\" defaultValue=\"basic\">\n              <Flex justifyContent=\"space-between\">\n                <Typography tag=\"h2\" variant=\"beta\" id={titleId}>\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.title'),\n                    defaultMessage: 'Configuration',\n                  })}\n                </Typography>\n                <Tabs.List aria-labelledby={titleId}>\n                  <Tabs.Trigger value=\"basic\">\n                    {formatMessage({\n                      id: getTranslation('Settings.locales.modal.base'),\n                      defaultMessage: 'Basic settings',\n                    })}\n                  </Tabs.Trigger>\n                  <Tabs.Trigger value=\"advanced\">\n                    {formatMessage({\n                      id: getTranslation('Settings.locales.modal.advanced'),\n                      defaultMessage: 'Advanced settings',\n                    })}\n                  </Tabs.Trigger>\n                </Tabs.List>\n              </Flex>\n              <Divider />\n              <Box paddingTop={7} paddingBottom={7}>\n                <Tabs.Content value=\"basic\">\n                  <BaseForm mode=\"edit\" />\n                </Tabs.Content>\n                <Tabs.Content value=\"advanced\">\n                  <AdvancedForm isDefaultLocale={isDefault} />\n                </Tabs.Content>\n              </Box>\n            </Tabs.Root>\n          </Modal.Body>\n          <Modal.Footer>\n            <Modal.Close>\n              <Button variant=\"tertiary\">\n                {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n              </Button>\n            </Modal.Close>\n            <SubmitButton />\n          </Modal.Footer>\n        </Form>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nexport { EditLocale, EditModal };\n", "import * as React from 'react';\n\nimport {\n  Flex,\n  Table,\n  Tbody,\n  Td,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  VisuallyH<PERSON><PERSON>,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { getTranslation } from '../utils/getTranslation';\n\nimport { DeleteLocale } from './DeleteLocale';\nimport { EditLocale, EditModal } from './EditLocale';\n\nimport type { Locale } from '../../../shared/contracts/locales';\n\n/* -------------------------------------------------------------------------------------------------\n * LocaleTable\n * -----------------------------------------------------------------------------------------------*/\n\ntype LocaleTableProps = {\n  locales?: Locale[];\n  canDelete?: boolean;\n  canUpdate?: boolean;\n  onDeleteLocale?: (locale: Locale) => void;\n  onEditLocale?: (locale: Locale) => void;\n};\n\nconst LocaleTable = ({ locales = [], canDelete, canUpdate }: LocaleTableProps) => {\n  const [editLocaleId, setEditLocaleId] = React.useState<Locale['id']>();\n  const { formatMessage } = useIntl();\n\n  const handleClick = (localeId: Locale['id']) => () => {\n    if (canUpdate) {\n      setEditLocaleId(localeId);\n    }\n  };\n\n  return (\n    <Table colCount={4} rowCount={locales.length + 1}>\n      <Thead>\n        <Tr>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.id'),\n                defaultMessage: 'ID',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.displayName'),\n                defaultMessage: 'Display name',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.default-locale'),\n                defaultMessage: 'Default locale',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <VisuallyHidden>Actions</VisuallyHidden>\n          </Th>\n        </Tr>\n      </Thead>\n      <Tbody>\n        {locales.map((locale) => (\n          <React.Fragment key={locale.id}>\n            <Tr\n              onClick={handleClick(locale.id)}\n              style={{ cursor: canUpdate ? 'pointer' : 'default' }}\n            >\n              <Td>\n                <Typography textColor=\"neutral800\">{locale.id}</Typography>\n              </Td>\n              <Td>\n                <Typography textColor=\"neutral800\">{locale.name}</Typography>\n              </Td>\n              <Td>\n                <Typography textColor=\"neutral800\">\n                  {locale.isDefault\n                    ? formatMessage({\n                        id: getTranslation('Settings.locales.default'),\n                        defaultMessage: 'Default',\n                      })\n                    : null}\n                </Typography>\n              </Td>\n              <Td>\n                <Flex gap={1} justifyContent=\"flex-end\" onClick={(e) => e.stopPropagation()}>\n                  {canUpdate && <EditLocale {...locale} />}\n                  {canDelete && !locale.isDefault && <DeleteLocale {...locale} />}\n                </Flex>\n              </Td>\n            </Tr>\n            <EditModal\n              {...locale}\n              onOpenChange={() => setEditLocaleId(undefined)}\n              open={editLocaleId === locale.id}\n            />\n          </React.Fragment>\n        ))}\n      </Tbody>\n    </Table>\n  );\n};\n\nexport { LocaleTable };\nexport type { LocaleTableProps };\n", "import * as React from 'react';\n\nimport {\n  Page,\n  useAPIErrorHandler,\n  useNotification,\n  useRBAC,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { EmptyStateLayout } from '@strapi/design-system';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { CreateLocale } from '../components/CreateLocale';\nimport { LocaleTable } from '../components/LocaleTable';\nimport { PERMISSIONS } from '../constants';\nimport { useGetLocalesQuery } from '../services/locales';\nimport { getTranslation } from '../utils/getTranslation';\n\nconst SettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { data: locales, isLoading: isLoadingLocales, error } = useGetLocalesQuery();\n  const {\n    isLoading: isLoadingRBAC,\n    allowedActions: { canUpdate, canCreate, canDelete },\n  } = useRBAC(PERMISSIONS);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const isLoading = isLoadingLocales || isLoadingRBAC;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !Array.isArray(locales)) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Page.Main tabIndex={-1}>\n      <Layouts.Header\n        primaryAction={<CreateLocale disabled={!canCreate} />}\n        title={formatMessage({\n          id: getTranslation('plugin.name'),\n          defaultMessage: 'Internationalization',\n        })}\n        subtitle={formatMessage({\n          id: getTranslation('Settings.list.description'),\n          defaultMessage: 'Configure the settings',\n        })}\n      />\n      <Layouts.Content>\n        {locales.length > 0 ? (\n          <LocaleTable locales={locales} canDelete={canDelete} canUpdate={canUpdate} />\n        ) : (\n          <EmptyStateLayout\n            icon={<EmptyDocuments width={undefined} height={undefined} />}\n            content={formatMessage({\n              id: getTranslation('Settings.list.empty.title'),\n              defaultMessage: 'There are no locales',\n            })}\n            action={<CreateLocale disabled={!canCreate} variant=\"secondary\" />}\n          />\n        )}\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nconst ProtectedSettingsPage = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.read}>\n      <SettingsPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedSettingsPage, SettingsPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,mBAAmB,CAAC,UAA+D;AACvF,SAAO,MAAM,SAAS;AACxB;ACoCA,IAAM,eAAe,CAAC,EAAE,UAAU,UAAU,UAAA,MAAmC;AACvE,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAElD,aAAA,yBACG,MAAM,MAAN,EAAW,MAAM,SAAS,cAAc,YACvC,UAAA;QAAC,wBAAA,MAAM,SAAN,EACC,cAAA;MAAC;MAAA;QACC;QACA;QACA,eAAA,wBAAY,eAAK,CAAA,CAAA;QACjB,SAAS,MAAM,WAAW,IAAI;QAC9B,MAAK;QAEJ,UAAc,cAAA;UACb,IAAI,eAAe,2BAA2B;UAC9C,gBAAgB;QAAA,CACjB;MAAA;IAAA,EAAA,CAEL;QAAA,wBACC,aAAY,EAAA,SAAS,MAAM,WAAW,KAAK,EAAA,CAAG;EACjD,EAAA,CAAA;AAEJ;AAMA,IAAM,gBAAoBA,QAAO,EAAE,MAAM;EACvC,MAAUA,QAAS,EAAA,SAAA,EAAW,SAAS;IACrC,IAAI;IACJ,gBAAgB;EAAA,CACjB;EACD,MACGA,QAAA,EACA,SAAS,EACT,IAAI,IAAI;IACP,IAAI;IACJ,gBAAgB;EACjB,CAAA,EACA,SAAS;IACR,IAAI;IACJ,gBAAgB;EAAA,CACjB;EACH,WAAe,OAAQ;AACzB,CAAC;AAID,IAAM,oBAAoB;EACxB,MAAM;EACN,MAAM;EACN,WAAW;AACb;AAMA,IAAM,cAAc,CAAC,EAAE,QAAA,MAAgC;AACrD,QAAM,UAAU,MAAA;AACV,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA;IACJ,yBAAyB;IACzB,iCAAiC;EAAA,IAC/B,mBAAmB;AACjB,QAAA,CAAC,YAAY,IAAI,wBAAA;AACjB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,qBAAqB,QAAQ,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAE/E,QAAA,eAAe,OAAO,QAAoB,YAAqC;AAC/E,QAAA;AACI,YAAA,MAAM,MAAM,aAAa,MAAM;AAErC,UAAI,WAAW,KAAK;AAClB,YAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACvE,kBAAQ,UAAU,uBAAuB,IAAI,KAAK,CAAC;QAAA,OAC9C;AACc,6BAAA,EAAE,MAAM,UAAU,SAAS,eAAe,IAAI,KAAK,EAAA,CAAG;QAC3E;AAEA;MACF;AAEmB,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,eAAe,uCAAuC;UAC1D,gBAAgB;QAAA,CACjB;MAAA,CACF;AAEkB,yBAAA;AACX,cAAA;aACD,KAAK;AACO,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;EAAA;AAIA,aAAA,wBAAC,MAAM,SAAN,EACC,cAAA;IAAC;IAAA;MACC,QAAO;MACP,eAAe;MACf,kBAAkB;MAClB,UAAU;MAEV,UAAA;YAAA,wBAAC,MAAM,QAAN,EACC,cAAA,wBAAC,MAAM,OAAN,EACE,UAAc,cAAA;UACb,IAAI,eAAe,2BAA2B;UAC9C,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;YACA,wBAAC,MAAM,MAAN,EACC,cAAA,yBAAC,KAAK,MAAL,EAAU,SAAQ,UAAS,cAAa,SACvC,UAAA;cAAC,yBAAA,MAAA,EAAK,gBAAe,iBACnB,UAAA;gBAAA,wBAAC,YAAA,EAAW,KAAI,MAAK,SAAQ,QAAO,IAAI,SACrC,UAAc,cAAA;cACb,IAAI,eAAe,8BAA8B;cACjD,gBAAgB;YACjB,CAAA,EAAA,CACH;gBACC,yBAAA,KAAK,MAAL,EAAU,mBAAiB,SAC1B,UAAA;kBAAA,wBAAC,KAAK,SAAL,EAAa,OAAM,SACjB,UAAc,cAAA;gBACb,IAAI,eAAe,6BAA6B;gBAChD,gBAAgB;cACjB,CAAA,EAAA,CACH;kBAAA,wBACC,KAAK,SAAL,EAAa,OAAM,YACjB,UAAc,cAAA;gBACb,IAAI,eAAe,iCAAiC;gBACpD,gBAAgB;cACjB,CAAA,EAAA,CACH;YAAA,EAAA,CACF;UAAA,EAAA,CACF;cAAA,wBAEC,SAAQ,CAAA,CAAA;cAER,yBAAA,KAAA,EAAI,YAAY,GAAG,eAAe,GACjC,UAAA;gBAAA,wBAAC,KAAK,SAAL,EAAa,OAAM,SAClB,cAAA,wBAAC,UAAA,CAAS,CAAA,EAAA,CACZ;gBACA,wBAAC,KAAK,SAAL,EAAa,OAAM,YAClB,cAAA,wBAAC,cAAA,CAAa,CAAA,EAAA,CAChB;UAAA,EAAA,CACF;QAAA,EAAA,CACF,EACF,CAAA;YACA,yBAAC,MAAM,QAAN,EACC,UAAA;cAAA,wBAAC,MAAM,OAAN,EACC,cAAA,wBAAC,QAAA,EAAO,SAAQ,YACb,UAAc,cAAA,EAAE,IAAI,gCAAgC,gBAAgB,SAAU,CAAA,EACjF,CAAA,EAAA,CACF;cAAA,wBACC,cAAa,CAAA,CAAA;QAAA,EAAA,CAChB;MAAA;IAAA;EAEJ,EAAA,CAAA;AAEJ;AAMA,IAAM,eAAe,MAAM;AACnB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,eAAe,QAAQ,gBAAgB,CAAC,UAAU,MAAM,YAAY;AAC1E,QAAM,WAAW,QAAQ,gBAAgB,CAAC,UAAU,MAAM,QAAQ;AAElE,aAAA,wBACG,QAAO,EAAA,MAAK,UAAS,eAAW,wBAAC,eAAA,CAAM,CAAA,GAAI,UAAU,gBAAgB,CAAC,UACpE,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ,EAC9D,CAAA;AAEJ;AAUA,IAAM,WAAW,CAAC,EAAE,OAAO,SAAA,MAA8B;AACjD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,EAAE,MAAM,gBAAgB,MAAA,IAAU,0BAA0B;AAElE,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;EACC,GAAA,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAE9C,MAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAC3B,WAAA;EACT;AAEA,QAAM,UAAU,eAAe,IAAI,CAAC,YAAY;IAC9C,OAAO,OAAO;IACd,OAAO,OAAO;EACd,EAAA;AAEF,QAAM,iBAAiB;IACrB;MACE,UAAU,SAAS;MACnB,OAAO;QACL,IAAI,eAAe,0CAA0C;QAC7D,gBAAgB;MAClB;MACA,MAAM;MACN;MACA,aAAa;QACX,IAAI;QACJ,gBAAgB;MAClB;MACA,UAAU;MACV,MAAM;MACN,MAAM;IACR;IACA;MACE,MAAM;QACJ,IAAI,eAAe,sDAAsD;QACzE,gBAAgB;MAClB;MACA,OAAO;QACL,IAAI,eAAe,0CAA0C;QAC7D,gBAAgB;MAClB;MACA,MAAM;MACN,UAAU;MACV,MAAM;MACN,MAAM;IACR;EAAA,EACA,IAAI,CAAC,WAAW;IAChB,GAAG;IACH,MAAM,MAAM,OAAO,cAAc,MAAM,IAAI,IAAI;IAC/C,OAAO,cAAc,MAAM,KAAK;IAChC,aAAa,MAAM,cAAc,cAAc,MAAM,WAAW,IAAI;EACpE,EAAA;AAEF,aACG,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA,eAAe,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,UAC7B,wBAAA,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAA,wBAAC,cAAc,EAAA,GAAG,MAAO,CAAA,EAAA,GADX,MAAM,IAEtB,CACD,EACH,CAAA;AAEJ;AAUA,IAAM,eAAe,CAAC,EAAE,gBAAA,MAAyC;AACzD,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,OAAO;IACX;MACE,UAAU;MACV,MAAM;QACJ,IAAI,eAAe,mDAAmD;QACtE,gBAAgB;MAClB;MACA,OAAO;QACL,IAAI,eAAe,8CAA8C;QACjE,gBAAgB;MAClB;MACA,MAAM;MACN,MAAM;MACN,MAAM;IACR;EAAA,EACA,IAAI,CAAC,WAAW;IAChB,GAAG;IACH,MAAM,MAAM,OAAO,cAAc,MAAM,IAAI,IAAI;IAC/C,OAAO,cAAc,MAAM,KAAK;EAChC,EAAA;AAEF,aACG,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,UACnB,wBAAA,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAA,wBAAC,cAAc,EAAA,GAAG,MAAO,CAAA,EAAA,GADX,MAAM,IAEtB,CACD,EACH,CAAA;AAEJ;AAMA,IAAM,eAAe,CAAC,UAAsB;AAC1C,UAAQ,MAAM,MAAM;IAKlB,KAAK;AACI,iBAAA,wBAAC,kBAAkB,EAAA,GAAG,MAAO,CAAA;IACtC;AACS,iBAAA,wBAAC,uBAAe,EAAA,GAAG,MAAO,CAAA;EACrC;AACF;AAEA,IAAM,mBAAmB,CAAC;EACxB;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAoD;AAClD,QAAM,EAAE,OAAO,OAAO,SAAS,IAAI,SAAS,IAAI;AAChD,QAAM,EAAE,MAAM,iBAAiB,CAAA,EAAA,IAAO,0BAA0B;AAE1D,QAAA,eAAe,CAACC,WAAkB;AAClC,QAAA,MAAM,QAAQ,cAAc,GAAG;AAEjC,YAAM,SAAS,eAAe,KAAK,CAACC,YAAWA,QAAO,SAASD,MAAK;AAEpE,eAAS,MAAMA,MAAK;AAEX,eAAA,QAAQ,OAAO,IAAI;IAAA,OACvB;AACL,eAAS,MAAMA,MAAK;IACtB;EAAA;AAGF,aAAA,yBACG,MAAM,MAAN,EAAW,OAAc,MAAY,MAAY,UAChD,UAAA;QAAC,wBAAA,MAAM,OAAN,EAAa,UAAM,MAAA,CAAA;QACpB;MAAC;MAAA;QACC;QAEA,UAAU;QACV;QACA;QAEC,UAAQ,QAAA,IAAI,CAAC,eACX,wBAAA,oBAAA,EAAmB,OAAO,OAAO,OAC/B,UAAA,OAAO,MADoC,GAAA,OAAO,KAErD,CACD;MAAA;IACH;QACA,wBAAC,MAAM,OAAN,CAAA,CAAY;QACb,wBAAC,MAAM,MAAN,CAAA,CAAW;EACd,EAAA,CAAA;AAEJ;AChZA,IAAM,eAAe,CAAC,EAAE,IAAI,KAAA,MAA8B;AAClD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAE5C,QAAA,CAAC,YAAY,IAAI,wBAAA;AACvB,QAAM,gBAAgB,YAAY;AAC5B,QAAA;AACI,YAAA,MAAM,MAAM,aAAa,EAAE;AAEjC,UAAI,WAAW,KAAK;AACC,2BAAA,EAAE,MAAM,UAAU,SAAS,eAAe,IAAI,KAAK,EAAA,CAAG;AAEzE;MACF;AAEmB,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,eAAe,uCAAuC;UAC1D,gBAAgB;QAAA,CACjB;MAAA,CACF;AAED,iBAAW,KAAK;IAAA,SACT,KAAK;AACO,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;EAAA;AAGF,aAAA,yBACG,OAAO,MAAP,EAAY,MAAM,SAAS,cAAc,YACxC,UAAA;QAAC,wBAAA,OAAO,SAAP,EACC,cAAA;MAAC;MAAA;QACC,SAAS,MAAM,WAAW,IAAI;QAC9B,OAAO;UACL;YACE,IAAI,eAAe,8BAA8B;YACjD,gBAAgB;UAClB;UACA;YACE;UACF;QACF;QACA,SAAQ;QAER,cAAA,wBAAC,cAAM,CAAA,CAAA;MAAA;IAAA,EAAA,CAEX;QACA,wBAAC,eAAc,EAAA,WAAW,cAAe,CAAA;EAC3C,EAAA,CAAA;AAEJ;AC1CA,IAAM,aAAa,CAAC,UAA2B;AACvC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAElD,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,SAAS,MAAM,WAAW,IAAI;QAC9B,OAAO;UACL;YACE,IAAI,eAAe,4BAA4B;YAC/C,gBAAgB;UAClB;UACA;YACE,MAAM,MAAM;UACd;QACF;QACA,SAAQ;QAER,cAAA,wBAAC,eAAO,CAAA,CAAA;MAAA;IACV;QAAA,wBACC,WAAW,EAAA,GAAG,OAAO,MAAM,SAAS,cAAc,WAAA,CAAY;EACjE,EAAA,CAAA;AAEJ;AAiBA,IAAM,YAAY,CAAC,EAAE,IAAI,MAAM,WAAW,MAAM,MAAM,aAAA,MAAmC;AACjF,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA;IACJ,yBAAyB;IACzB,iCAAiC;EAAA,IAC/B,mBAAmB;AACvB,QAAM,qBAAqB,QAAQ,aAAa,CAAC,UAAU,MAAM,kBAAkB;AAC7E,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,UAAU,MAAA;AAEV,QAAA,CAAC,YAAY,IAAI,wBAAA;AACjB,QAAA,eAAe,OACnB,EAAE,MAAM,OAAO,GAAG,KAAA,GAClB,YACG;AACC,QAAA;AAKI,YAAA,MAAM,MAAM,aAAa;QAC7B;QACA,GAAG;MAAA,CACJ;AAED,UAAI,WAAW,KAAK;AAClB,YAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACvE,kBAAQ,UAAU,uBAAuB,IAAI,KAAK,CAAC;QAAA,OAC9C;AACc,6BAAA,EAAE,MAAM,UAAU,SAAS,eAAe,IAAI,KAAK,EAAA,CAAG;QAC3E;AAEA;MACF;AAEmB,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,eAAe,qCAAqC;UACxD,gBAAgB;QAAA,CACjB;MAAA,CACF;AAEkB,yBAAA;AACnB,mBAAa,KAAK;IAAA,SACX,KAAK;AACO,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;EAAA;AAIA,aAAA,wBAAC,MAAM,MAAN,EAAW,MAAY,cACtB,cAAA,wBAAC,MAAM,SAAN,EACC,cAAA;IAAC;IAAA;MACC,QAAO;MACP,UAAU;MACV,eAAe;QACb;QACA;QACA;MACF;MACA,kBAAkB;MAElB,UAAA;YAAA,wBAAC,MAAM,QAAN,EACC,cAAC,wBAAA,MAAM,OAAN,EACE,UAAA;UACC;YACE,IAAI,eAAe,4BAA4B;YAC/C,gBAAgB;UAClB;UACA;YACE;UACF;QAAA,EAAA,CAEJ,EACF,CAAA;YACA,wBAAC,MAAM,MAAN,EACC,cAAA,yBAAC,KAAK,MAAL,EAAU,SAAQ,UAAS,cAAa,SACvC,UAAA;cAAC,yBAAA,MAAA,EAAK,gBAAe,iBACnB,UAAA;gBAAA,wBAAC,YAAA,EAAW,KAAI,MAAK,SAAQ,QAAO,IAAI,SACrC,UAAc,cAAA;cACb,IAAI,eAAe,8BAA8B;cACjD,gBAAgB;YACjB,CAAA,EAAA,CACH;gBACC,yBAAA,KAAK,MAAL,EAAU,mBAAiB,SAC1B,UAAA;kBAAA,wBAAC,KAAK,SAAL,EAAa,OAAM,SACjB,UAAc,cAAA;gBACb,IAAI,eAAe,6BAA6B;gBAChD,gBAAgB;cACjB,CAAA,EAAA,CACH;kBAAA,wBACC,KAAK,SAAL,EAAa,OAAM,YACjB,UAAc,cAAA;gBACb,IAAI,eAAe,iCAAiC;gBACpD,gBAAgB;cACjB,CAAA,EAAA,CACH;YAAA,EAAA,CACF;UAAA,EAAA,CACF;cAAA,wBACC,SAAQ,CAAA,CAAA;cACR,yBAAA,KAAA,EAAI,YAAY,GAAG,eAAe,GACjC,UAAA;gBAAC,wBAAA,KAAK,SAAL,EAAa,OAAM,SAClB,cAAC,wBAAA,UAAA,EAAS,MAAK,OAAA,CAAO,EACxB,CAAA;gBACA,wBAAC,KAAK,SAAL,EAAa,OAAM,YAClB,cAAC,wBAAA,cAAA,EAAa,iBAAiB,UAAA,CAAW,EAC5C,CAAA;UAAA,EAAA,CACF;QAAA,EAAA,CACF,EACF,CAAA;YACA,yBAAC,MAAM,QAAN,EACC,UAAA;cAAA,wBAAC,MAAM,OAAN,EACC,cAAA,wBAAC,QAAA,EAAO,SAAQ,YACb,UAAc,cAAA,EAAE,IAAI,gCAAgC,gBAAgB,SAAU,CAAA,EACjF,CAAA,EAAA,CACF;cAAA,wBACC,cAAa,CAAA,CAAA;QAAA,EAAA,CAChB;MAAA;IAAA;EAAA,EAEJ,CAAA,EACF,CAAA;AAEJ;AC5KA,IAAM,cAAc,CAAC,EAAE,UAAU,CAAI,GAAA,WAAW,UAAA,MAAkC;AAChF,QAAM,CAAC,cAAc,eAAe,IAAU,eAAuB;AAC/D,QAAA,EAAE,cAAA,IAAkB,QAAA;AAEpB,QAAA,cAAc,CAAC,aAA2B,MAAM;AACpD,QAAI,WAAW;AACb,sBAAgB,QAAQ;IAC1B;EAAA;AAGF,aAAA,yBACG,OAAM,EAAA,UAAU,GAAG,UAAU,QAAQ,SAAS,GAC7C,UAAA;QAAC,wBAAA,OAAA,EACC,cAAA,yBAAC,IACC,EAAA,UAAA;UAAA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;QACb,IAAI,eAAe,yBAAyB;QAC5C,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;UACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;QACb,IAAI,eAAe,kCAAkC;QACrD,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;UACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;QACb,IAAI,eAAe,qCAAqC;QACxD,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;UACC,wBAAA,IAAA,EACC,cAAC,wBAAA,gBAAA,EAAe,UAAA,UAAO,CAAA,EAAA,CACzB;IAAA,EAAA,CACF,EACF,CAAA;QACA,wBAAC,OAAA,EACE,UAAQ,QAAA,IAAI,CAAC,eACZ,yBAAO,gBAAN,EACC,UAAA;UAAA;QAAC;QAAA;UACC,SAAS,YAAY,OAAO,EAAE;UAC9B,OAAO,EAAE,QAAQ,YAAY,YAAY,UAAU;UAEnD,UAAA;gBAAA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cAAc,UAAA,OAAO,GAAA,CAAG,EAChD,CAAA;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cAAc,UAAA,OAAO,KAAA,CAAK,EAClD,CAAA;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cACnB,UAAA,OAAO,YACJ,cAAc;cACZ,IAAI,eAAe,0BAA0B;cAC7C,gBAAgB;YAAA,CACjB,IACD,KACN,CAAA,EAAA,CACF;gBACC,wBAAA,IAAA,EACC,cAAC,yBAAA,MAAA,EAAK,KAAK,GAAG,gBAAe,YAAW,SAAS,CAAC,MAAM,EAAE,gBACvD,GAAA,UAAA;cAAa,iBAAA,wBAAC,YAAY,EAAA,GAAG,OAAQ,CAAA;cACrC,aAAa,CAAC,OAAO,iBAAc,wBAAA,cAAA,EAAc,GAAG,OAAA,CAAQ;YAAA,EAAA,CAC/D,EACF,CAAA;UAAA;QAAA;MACF;UACA;QAAC;QAAA;UACE,GAAG;UACJ,cAAc,MAAM,gBAAgB,MAAS;UAC7C,MAAM,iBAAiB,OAAO;QAAA;MAChC;IAhCmB,EAAA,GAAA,OAAO,EAiC5B,CACD,EAAA,CACH;EACF,EAAA,CAAA;AAEJ;AClGA,IAAM,eAAe,MAAM;AACnB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AACvE,QAAM,EAAE,MAAM,SAAS,WAAW,kBAAkB,MAAA,IAAU,mBAAA;AACxD,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,WAAW,WAAW,UAAU;EAAA,IAChD,QAAQ,WAAW;AAEvB,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;EACC,GAAA,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAE9C,QAAM,YAAY,oBAAoB;AAEtC,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MAAI,SAAS,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC7B,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,aACG,yBAAA,KAAK,MAAL,EAAU,UAAU,IACnB,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,mBAAe,wBAAC,cAAa,EAAA,UAAU,CAAC,UAAW,CAAA;QACnD,OAAO,cAAc;UACnB,IAAI,eAAe,aAAa;UAChC,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI,eAAe,2BAA2B;UAC9C,gBAAgB;QAAA,CACjB;MAAA;IACH;QACC,wBAAA,QAAQ,SAAR,EACE,UAAQ,QAAA,SAAS,QAChB,wBAAC,aAAY,EAAA,SAAkB,WAAsB,UAAA,CAAsB,QAE3E;MAAC;MAAA;QACC,UAAO,wBAAA,cAAA,EAAe,OAAO,QAAW,QAAQ,OAAA,CAAW;QAC3D,SAAS,cAAc;UACrB,IAAI,eAAe,2BAA2B;UAC9C,gBAAgB;QAAA,CACjB;QACD,YAAS,wBAAA,cAAA,EAAa,UAAU,CAAC,WAAW,SAAQ,YAAA,CAAY;MAAA;IAAA,EAAA,CAGtE;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,wBAAwB,MAAM;AAEhC,aAAA,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,MACrC,cAAC,wBAAA,cAAA,CAAa,CAAA,EAChB,CAAA;AAEJ;", "names": ["create", "value", "locale"]}