{"name": "ccpalcan-api", "version": "2.0.0", "private": true, "description": "api para la app, ccpalcan", "scripts": {"db:seed:complaint-templates": "node ./scripts/seed-complaint-templates.js", "db:seed:warning-rules": "node ./scripts/seed-warning-rules-db.js", "db:seed:blocks": "node ./scripts/seed-blocks-db.js", "db:seed:users": "node ./scripts/seed-users-db.js", "db:seed:roles": "node ./scripts/seed-roles-db.js", "db:seed": "npm run db:seed:roles && npm run db:seed:users && npm run db:seed:blocks && npm run db:seed:warning-rules && npm run db:seed:complaint-templates", "db:clean": "node ./scripts/clean-database.js", "db:seed:email-templates": "npx tsc && node ./dist/src/scripts/setup-email-templates.js", "db:reset": "npm run db:clean && npm run db:seed && npm run db:seed:email-templates", "test:email": "node ./src/scripts/test/send-test-email.js", "deploy": "strapi deploy", "develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@sendgrid/mail": "^7.7.0", "@strapi/email": "^5.13.1", "@strapi/plugin-cloud": "5.0.0", "@strapi/plugin-documentation": "5.0.0", "@strapi/plugin-users-permissions": "5.0.0", "@strapi/provider-email-sendgrid": "5.0.0", "@strapi/provider-upload-cloudinary": "5.0.0", "@strapi/strapi": "5.0.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "fs-extra": "^10.0.0", "mime-types": "^2.1.27", "mysql2": "^3.14.1", "openai": "^4.103.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0", "web-push": "^3.6.7"}, "devDependencies": {"@types/node": "^20.17.57", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}