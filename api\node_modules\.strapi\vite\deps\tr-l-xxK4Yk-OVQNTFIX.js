import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/tr-l-xxK4Yk.mjs
var tr = {
  "coming-soon": "Bu içerik şuanda düzenleniyor. Bir kaç hafta sonra yayında olacak!",
  "components.Row.open": "Aç",
  "components.Row.regenerate": "Yeniden üret",
  "containers.HomePage.Block.title": "Versiyonlar",
  "containers.HomePage.Button.update": "Güncelle",
  "containers.HomePage.PluginHeader.title": "Dokümantasyon — Ayarlar",
  "containers.HomePage.PopUpWarning.confirm": "Anladım",
  "containers.HomePage.PopUpWarning.message": "Bu versiyonu silmek istediğinden emin misin?",
  "containers.HomePage.copied": "Token panoya kopyalandı",
  "containers.HomePage.form.jwtToken": "JWT tokenını al",
  "containers.HomePage.form.jwtToken.description": "İstek atmak için bu tokenı kopyala ve swaggerda kullan",
  "containers.HomePage.form.password": "Şifre",
  "containers.HomePage.form.password.inputDescription": "Dokümantasyona erişmek için şifreyi belirle",
  "containers.HomePage.form.restrictedAccess": "Kısıtlı erişim",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Dokümantasyon uç noktasını gizle. Varsayılan olarak erişim herkese açıktır",
  "containers.HomePage.form.showGeneratedFiles": "Üretilen dosyaları göster",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Üretilen dokümantasyonun üzerine yazmak istediğinde kullanışlıdır. \nEklenti dosyaları model ve eklentilere göre ayrı olarak üretecektir. \nBu seçeneği etkinleştirerek dokümantasyonu özelleştirmen kolaylaşacaktır.",
  "error.deleteDoc.versionMissing": "Silmek istediğin versiyon bulunmuyor.",
  "error.noVersion": "Bir versiyon gerekli.",
  "error.regenerateDoc": "Dokümanı yeniden üretirken bir hata oluştu",
  "error.regenerateDoc.versionMissing": "Üretmeye çalıştığın versiyon bulunmuyor",
  "notification.delete.success": "Doküman silindi",
  "notification.generate.success": "Doküman üretildi",
  "notification.update.success": "Ayarlar başarıyla güncellendi",
  "pages.PluginPage.Button.open": "Dokümanı aç",
  "pages.PluginPage.header.description": "Dokümantasyon eklentisini ayarla",
  "pages.PluginPage.table.generated": "Son üretilme",
  "pages.PluginPage.table.icon.regenerate": "Yeniden üret: {target}",
  "pages.PluginPage.table.icon.show": "Aç: {target}",
  "pages.PluginPage.table.version": "Versiyon",
  "pages.SettingsPage.header.description": "Dokümantasyon eklentisini ayarla",
  "pages.SettingsPage.header.save": "Kaydet",
  "pages.SettingsPage.toggle.hint": "Dokümantasyon uç noktasını gizli yap",
  "pages.SettingsPage.toggle.label": "Kısıtlı Erişim",
  "plugin.description.long": "Bir OpenAPI Dokümanı oluştur ve Swagger UI ile APIni görselleştir.",
  "plugin.description.short": "Bir OpenAPI Dokümanı oluştur ve Swagger UI ile APIni görselleştir.",
  "plugin.name": "Dokümantasyon"
};
export {
  tr as default
};
//# sourceMappingURL=tr-l-xxK4Yk-OVQNTFIX.js.map
