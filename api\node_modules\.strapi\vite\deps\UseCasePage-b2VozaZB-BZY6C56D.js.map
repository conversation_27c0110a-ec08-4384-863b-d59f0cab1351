{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/UseCasePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  Main,\n  SingleSelectOption,\n  SingleSelect,\n  TextButton,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { parse } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\nimport { PrivateRoute } from '../components/PrivateRoute';\nimport { Logo } from '../components/UnauthenticatedLogo';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { LayoutContent, UnauthenticatedLayout } from '../layouts/UnauthenticatedLayout';\n\nexport const options = [\n  {\n    intlLabel: {\n      id: 'Usecase.front-end',\n      defaultMessage: 'Front-end developer',\n    },\n    value: 'front_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.back-end',\n      defaultMessage: 'Back-end developer',\n    },\n    value: 'back_end_developer',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.full-stack',\n      defaultMessage: 'Full-stack developer',\n    },\n    value: 'full_stack_developer',\n  },\n  {\n    intlLabel: {\n      id: 'global.content-manager',\n      defaultMessage: 'Content Manager',\n    },\n    value: 'content_manager',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.content-creator',\n      defaultMessage: 'Content Creator',\n    },\n    value: 'content_creator',\n  },\n  {\n    intlLabel: {\n      id: 'Usecase.other',\n      defaultMessage: 'Other',\n    },\n    value: 'other',\n  },\n];\n\nconst UseCasePage = () => {\n  const { toggleNotification } = useNotification();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const [role, setRole] = React.useState<string | number | null>(null);\n  const [otherRole, setOtherRole] = React.useState('');\n\n  const { firstname, email } = useAuth('UseCasePage', (state) => state.user) ?? {};\n  const { hasAdmin } = parse(location.search, { ignoreQueryPrefix: true });\n  const isOther = role === 'other';\n\n  const handleSubmit = async (event: React.FormEvent, skipPersona: boolean) => {\n    event.preventDefault();\n    try {\n      await fetch('https://analytics.strapi.io/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          username: firstname,\n          firstAdmin: Boolean(!hasAdmin),\n          persona: {\n            role: skipPersona ? undefined : role,\n            otherRole: skipPersona ? undefined : otherRole,\n          },\n        }),\n      });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'Usecase.notification.success.project-created',\n          defaultMessage: 'Project has been successfully created',\n        }),\n      });\n      navigate('/');\n    } catch (err) {\n      // Silent\n    }\n  };\n\n  return (\n    <UnauthenticatedLayout>\n      <Main labelledBy=\"usecase-title\">\n        <LayoutContent>\n          <form onSubmit={(e) => handleSubmit(e, false)}>\n            <Flex direction=\"column\" paddingBottom={7}>\n              <Logo />\n              <Box paddingTop={6} paddingBottom={1} width={`25rem`}>\n                <Typography textAlign=\"center\" variant=\"alpha\" tag=\"h1\" id=\"usecase-title\">\n                  {formatMessage({\n                    id: 'Usecase.title',\n                    defaultMessage: 'Tell us a bit more about yourself',\n                  })}\n                </Typography>\n              </Box>\n            </Flex>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              <Field.Root name=\"usecase\">\n                <Field.Label>\n                  {formatMessage({\n                    id: 'Usecase.input.work-type',\n                    defaultMessage: 'What type of work do you do?',\n                  })}\n                </Field.Label>\n                <SingleSelect onChange={(value) => setRole(value)} value={role}>\n                  {options.map(({ intlLabel, value }) => (\n                    <SingleSelectOption key={value} value={value}>\n                      {formatMessage(intlLabel)}\n                    </SingleSelectOption>\n                  ))}\n                </SingleSelect>\n              </Field.Root>\n              {isOther && (\n                <Field.Root name=\"other\">\n                  <Field.Label>\n                    {formatMessage({ id: 'Usecase.other', defaultMessage: 'Other' })}\n                  </Field.Label>\n                  <TextInput value={otherRole} onChange={(e) => setOtherRole(e.target.value)} />\n                </Field.Root>\n              )}\n              <Button type=\"submit\" size=\"L\" fullWidth disabled={!role}>\n                {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n              </Button>\n            </Flex>\n          </form>\n        </LayoutContent>\n        <Flex justifyContent=\"center\">\n          <Box paddingTop={4}>\n            <TextButton\n              onClick={(event: React.MouseEvent<HTMLButtonElement>) => handleSubmit(event, true)}\n            >\n              {formatMessage({\n                id: 'Usecase.button.skip',\n                defaultMessage: 'Skip this question',\n              })}\n            </TextButton>\n          </Box>\n        </Flex>\n      </Main>\n    </UnauthenticatedLayout>\n  );\n};\n\nconst PrivateUseCasePage = () => {\n  return (\n    <PrivateRoute>\n      <UseCasePage />\n    </PrivateRoute>\n  );\n};\n\nexport { PrivateUseCasePage, UseCasePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBO,IAAM,UAAU;EACrB;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;EAET;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;EAET;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;EAET;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;EAET;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;EAET;IACE,WAAW;MACT,IAAI;MACJ,gBAAgB;IAAA;IAElB,OAAO;EAAA;AAEX;AAEA,IAAM,cAAc,MAAM;AAClB,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,WAAW,YAAY;AAC7B,QAAM,WAAW,YAAY;AACvB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,CAAC,MAAM,OAAO,IAAU,eAAiC,IAAI;AACnE,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,EAAE;AAE7C,QAAA,EAAE,WAAW,MAAA,IAAU,QAAQ,eAAe,CAAC,UAAU,MAAM,IAAI,KAAK,CAAA;AACxE,QAAA,EAAE,SAAA,QAAa,iBAAM,SAAS,QAAQ,EAAE,mBAAmB,KAAA,CAAM;AACvE,QAAM,UAAU,SAAS;AAEnB,QAAA,eAAe,OAAO,OAAwB,gBAAyB;AAC3E,UAAM,eAAe;AACjB,QAAA;AACF,YAAM,MAAM,wCAAwC;QAClD,QAAQ;QACR,SAAS;UACP,gBAAgB;QAAA;QAElB,MAAM,KAAK,UAAU;UACnB;UACA,UAAU;UACV,YAAY,QAAQ,CAAC,QAAQ;UAC7B,SAAS;YACP,MAAM,cAAc,SAAY;YAChC,WAAW,cAAc,SAAY;UAAA;QACvC,CACD;MAAA,CACF;AAEkB,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;AACD,eAAS,GAAG;IAAA,SACL,KAAK;IAAA;EAEd;AAGF,aACG,wBAAA,uBAAA,EACC,cAAC,yBAAA,MAAA,EAAK,YAAW,iBACf,UAAA;QAAC,wBAAA,eAAA,EACC,cAAA,yBAAC,QAAK,EAAA,UAAU,CAAC,MAAM,aAAa,GAAG,KAAK,GAC1C,UAAA;UAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,eAAe,GACtC,UAAA;YAAA,wBAAC,MAAK,CAAA,CAAA;YAAA,wBACL,KAAI,EAAA,YAAY,GAAG,eAAe,GAAG,OAAO,SAC3C,cAAA,wBAAC,YAAW,EAAA,WAAU,UAAS,SAAQ,SAAQ,KAAI,MAAK,IAAG,iBACxD,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;MAAA,EACF,CAAA;UAAA,yBACC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,yBAAC,MAAM,MAAN,EAAW,MAAK,WACf,UAAA;cAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;cACA,wBAAC,cAAa,EAAA,UAAU,CAAC,UAAU,QAAQ,KAAK,GAAG,OAAO,MACvD,UAAQ,QAAA,IAAI,CAAC,EAAE,WAAW,MAAM,UAC9B,wBAAA,oBAAA,EAA+B,OAC7B,UAAA,cAAc,SAAS,EAAA,GADD,KAEzB,CACD,EACH,CAAA;QAAA,EACF,CAAA;QACC,eACE,yBAAA,MAAM,MAAN,EAAW,MAAK,SACf,UAAA;cAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA,EAAE,IAAI,iBAAiB,gBAAgB,QAAQ,CAAC,EACjE,CAAA;cACA,wBAAC,WAAU,EAAA,OAAO,WAAW,UAAU,CAAC,MAAM,aAAa,EAAE,OAAO,KAAK,EAAG,CAAA;QAAA,EAC9E,CAAA;YAAA,wBAED,QAAO,EAAA,MAAK,UAAS,MAAK,KAAI,WAAS,MAAC,UAAU,CAAC,MACjD,UAAA,cAAc,EAAE,IAAI,iBAAiB,gBAAgB,SAAA,CAAU,EAClE,CAAA;MAAA,EACF,CAAA;IAAA,EAAA,CACF,EACF,CAAA;QAAA,wBACC,MAAK,EAAA,gBAAe,UACnB,cAAC,wBAAA,KAAA,EAAI,YAAY,GACf,cAAA;MAAC;MAAA;QACC,SAAS,CAAC,UAA+C,aAAa,OAAO,IAAI;QAEhF,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA,EAAA,CAEL,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AAEA,IAAM,qBAAqB,MAAM;AAC/B,aACG,wBAAA,cAAA,EACC,cAAC,wBAAA,aAAA,CAAA,CAAY,EACf,CAAA;AAEJ;", "names": []}