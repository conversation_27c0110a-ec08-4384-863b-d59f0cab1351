{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/sv-BGb12eW3.mjs"], "sourcesContent": ["const configurations = \"Konfigurationer\";\nconst from = \"från\";\nconst sv = {\n  \"attribute.boolean\": \"Booleskt värde\",\n  \"attribute.boolean.description\": \"<PERSON>a eller nej, 1 eller 0, sant eller falskt\",\n  \"attribute.component\": \"Komponent\",\n  \"attribute.component.description\": \"Grupp av fält du kan återanvända och repetera\",\n  \"attribute.customField\": \"Anpassat fält\",\n  \"attribute.date\": \"Datum\",\n  \"attribute.date.description\": \"En datumväljare med sekunder, minuter och timmar\",\n  \"attribute.datetime\": \"Datum och tid\",\n  \"attribute.dynamiczone\": \"Dynamisk zon\",\n  \"attribute.dynamiczone.description\": \"Dynamiskt välj mellan förutbestämda komponenter när du skapar innehåll\",\n  \"attribute.email\": \"E-post\",\n  \"attribute.email.description\": \"E-postadressfält med validering\",\n  \"attribute.enumeration\": \"Enumeration\",\n  \"attribute.enumeration.description\": \"Förutbestämda värden som man kan välja mellan\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"Data i JSON-format\",\n  \"attribute.media\": \"Media\",\n  \"attribute.media.description\": \"Filer såsom bilder, videos, m.m\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Nummer\",\n  \"attribute.number.description\": \"Nummer (heltal och decimaltal)\",\n  \"attribute.password\": \"Lösenord\",\n  \"attribute.password.description\": \"Lösenordsfält med kryptering\",\n  \"attribute.relation\": \"Relation\",\n  \"attribute.relation.description\": \"Refererar till en samlingstyp\",\n  \"attribute.richtext\": \"Formaterad text\",\n  \"attribute.richtext.description\": \"En textredigerare med formatalternativ\",\n  \"attribute.text\": \"Text\",\n  \"attribute.text.description\": \"Kort och lång text såsom titel eller beskrivning\",\n  \"attribute.time\": \"Tid\",\n  \"attribute.timestamp\": \"Tidstämpel\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Unik identifierare\",\n  \"button.attributes.add.another\": \"Lägg till nytt fält\",\n  \"button.component.add\": \"Lägg till en komponent\",\n  \"button.component.create\": \"Skapa komponent\",\n  \"button.model.create\": \"Skapa samlingstyp\",\n  \"button.single-types.create\": \"Skapa engångstyp\",\n  \"component.repeatable\": \"(repeterbar)\",\n  \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# komponenter} one {# komponent} other {# komponenter}} valda\",\n  \"components.componentSelect.no-component-available\": \"Du har redan lagt till alla dina komponenter\",\n  \"components.componentSelect.no-component-available.with-search\": \"Det finns inga komponenter matchande sökningen\",\n  \"components.componentSelect.value-component\": \"{number} komponenter valda (skriv för att söka efter en komponent)\",\n  \"components.componentSelect.value-components\": \"{number} komponenter valda\",\n  configurations,\n  \"contentType.apiId-plural.description\": \"Pluraliserat API-ID\",\n  \"contentType.apiId-plural.label\": \"API ID (Plural)\",\n  \"contentType.apiId-singular.description\": \"UID:t används för att generera API-rutterna och databastabellerna\",\n  \"contentType.apiId-singular.label\": \"API ID (Singular)\",\n  \"contentType.collectionName.description\": \"Användbart när namnet på din innehållstyp och din tabell är olika\",\n  \"contentType.collectionName.label\": \"Samlingsnamn\",\n  \"contentType.displayName.label\": \"Visningsnamn\",\n  \"contentType.kind.change.warning\": \"Du ändrade typen på en innehållstyp: API:n kommer återskapas (rutter, kontroller, och tjänster kommer bli överskrivna).\",\n  \"error.attributeName.reserved-name\": \"Namnet kan inte användas för att det kan påverka annan funktionalitet\",\n  \"error.contentType.pluralName-used\": \"Värdet kan inte vara samma som namnet i singular\",\n  \"error.contentType.singularName-used\": \"Värdet kan inte vara samma som det pluraliserade namnet\",\n  \"error.contentTypeName.reserved-name\": \"Namnet kan inte användas i ditt projekt för att det kan påverka annan funktionalitet\",\n  \"error.validation.enum-duplicate\": \"Dubblettvärden är inte tillåtna (endast alfanumeriska tecken kontrolleras).\",\n  \"error.validation.enum-empty-string\": \"Tomma strängar är inte tillåtna\",\n  \"error.validation.enum-regex\": \"Minst ett värde är ogiltigt. Värden ska ha minst ett alfabetiskt tecken före det första talet.\",\n  \"error.validation.minSupMax\": \"Can't be superior\",\n  \"error.validation.positive\": \"Måste vara ett positivt tal\",\n  \"error.validation.regex\": \"Regex-mönstret är ogiltigt\",\n  \"error.validation.relation.targetAttribute-taken\": \"Detta namn existerar i målet\",\n  \"form.attribute.component.option.add\": \"Lägg till en komponent\",\n  \"form.attribute.component.option.create\": \"Skapa en komponent\",\n  \"form.attribute.component.option.create.description\": \"En komponent delas mellan innehållstyper och komponenter, och är tillgänglig överallt.\",\n  \"form.attribute.component.option.repeatable\": \"Repeterbar komponent\",\n  \"form.attribute.component.option.repeatable.description\": \"Bäst för flera t.ex ingredienser, metataggar m.m. (listor)\",\n  \"form.attribute.component.option.reuse-existing\": \"Använd en befintlig komponent\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Återanvänd en komponent som redan har skapats för att hålla datastrukturen samma över flera innehållstyper.\",\n  \"form.attribute.component.option.single\": \"Engångskomponent\",\n  \"form.attribute.component.option.single.description\": \"Bäst för att gruppera fält som adress, huvudinnehåll, etc...\",\n  \"form.attribute.item.customColumnName\": \"Anpassade kolumnnamn\",\n  \"form.attribute.item.customColumnName.description\": \"Kan användas för att anpassa hur datan ser ut i API-svar\",\n  \"form.attribute.item.date.type.date\": \"datum (t.ex: 01/01/{currentYear})\",\n  \"form.attribute.item.date.type.datetime\": \"datum och tid (t.ex: 01/01/{currentYear} 00:00)\",\n  \"form.attribute.item.date.type.time\": \"tid (t.ex: 00:00)\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Fältnamn\",\n  \"form.attribute.item.enumeration.graphql\": \"Namnändring för GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"Låter dig ändra det genererade namnet i GraphQL\",\n  \"form.attribute.item.enumeration.placeholder\": \"Ex:\\nmorgon\\nmiddag\\nkväll\",\n  \"form.attribute.item.enumeration.rules\": \"Värden (ett värde per rad)\",\n  \"form.attribute.item.maximum\": \"Största värde\",\n  \"form.attribute.item.maximumLength\": \"Längsta längd\",\n  \"form.attribute.item.minimum\": \"Minsta värde\",\n  \"form.attribute.item.minimumLength\": \"Kortaste längd\",\n  \"form.attribute.item.number.type\": \"Nummerformat\",\n  \"form.attribute.item.number.type.biginteger\": \"stort heltal (t.ex: 123456789)\",\n  \"form.attribute.item.number.type.decimal\": \"decimaltal (t.ex: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"flyttal (t.ex: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"heltal (t.ex: 10)\",\n  \"form.attribute.item.privateField\": \"Privat fält\",\n  \"form.attribute.item.privateField.description\": \"Detta fält kommer inte att visas i API-svar\",\n  \"form.attribute.item.requiredField\": \"Obligatoriskt fält\",\n  \"form.attribute.item.requiredField.description\": \"Du kommer inte kunna skapa posten om detta fältet är tomt\",\n  \"form.attribute.item.text.regex\": \"RegEx-mönster\",\n  \"form.attribute.item.text.regex.description\": \"Texten för RegEx-mönstret\",\n  \"form.attribute.item.uniqueField\": \"Unikt fält\",\n  \"form.attribute.item.uniqueField.description\": \"Du kommer inte kunna skapa posten om det redan finns en annan post med samma värde\",\n  \"form.attribute.media.allowed-types\": \"Välj tillåtna typer av medier\",\n  \"form.attribute.media.allowed-types.option-files\": \"Filer\",\n  \"form.attribute.media.allowed-types.option-images\": \"Bilder\",\n  \"form.attribute.media.allowed-types.option-videos\": \"Videos\",\n  \"form.attribute.media.option.multiple\": \"Flera medier\",\n  \"form.attribute.media.option.multiple.description\": \"Tillåter flera medier att väljas i samma fält, passar bra för t.ex bildgallerier\",\n  \"form.attribute.media.option.single\": \"En media\",\n  \"form.attribute.media.option.single.description\": \"Bäst för profilbilder eller andra bilder som bara förekommer en gång per post\",\n  \"form.attribute.settings.default\": \"Standardvärde\",\n  \"form.attribute.text.option.long-text\": \"Lång text\",\n  \"form.attribute.text.option.long-text.description\": \"Bäst för beskrivningar eller andra längre texter. Fulltextsökning är inaktiverat.\",\n  \"form.attribute.text.option.short-text\": \"Kort text\",\n  \"form.attribute.text.option.short-text.description\": \"Bäst för titlar, namn, länkar (URL:er). Aktiverar fulltextsökning sökning för fältet.\",\n  \"form.button.add-components-to-dynamiczone\": \"Lägg till komponenter till dynamiska zonen\",\n  \"form.button.add-field\": \"Lägg till nytt fält\",\n  \"form.button.add-first-field-to-created-component\": \"Lägg till första fältet i komponenten\",\n  \"form.button.add.field.to.collectionType\": \"Lägg till ett nytt fält i samlingstypen\",\n  \"form.button.add.field.to.component\": \"Lägg till ett nytt fält i komponenten\",\n  \"form.button.add.field.to.contentType\": \"Lägg till ett nytt fält i innehållstypen\",\n  \"form.button.add.field.to.singleType\": \"Lägg till ett nytt fält i engångstypen\",\n  \"form.button.cancel\": \"Avbryt\",\n  \"form.button.collection-type.description\": \"Bäst för flera poster såsom artiklar, produkter och kommentarer.\",\n  \"form.button.collection-type.name\": \"Samlingstyper\",\n  \"form.button.configure-component\": \"Konfigurera komponenten\",\n  \"form.button.configure-view\": \"Konfigurera visningen\",\n  \"form.button.select-component\": \"Välj en komponent\",\n  \"form.button.single-type.description\": \"Bäst för poster som bara förekommer en gång såsom startsida, om oss, m.m.\",\n  \"form.button.single-type.name\": \"Engångstyper\",\n  from,\n  \"listView.headerLayout.description\": \"Skapa datastrukturen för ditt innehåll\",\n  \"menu.section.components.name\": \"Komponenter\",\n  \"menu.section.models.name\": \"Samlingstyper\",\n  \"menu.section.single-types.name\": \"Engångstyper\",\n  \"modalForm.attribute.form.base.name.description\": \"Mellanslag tillåts inte i namnet på attributet\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"t.ex titel, slug, canonicalUrl\",\n  \"modalForm.attribute.target-field\": \"Kopplat fält\",\n  \"modalForm.attributes.select-component\": \"Välj komponent\",\n  \"modalForm.attributes.select-components\": \"Välj komponenter\",\n  \"modalForm.collectionType.header-create\": \"Skapa samlingstyp\",\n  \"modalForm.component.header-create\": \"Skapa komponent\",\n  \"modalForm.components.create-component.category.label\": \"Välj kategori eller ange namn på ny kategori\",\n  \"modalForm.components.icon.label\": \"Ikon\",\n  \"modalForm.empty.button\": \"Lägg till anpassade fält\",\n  \"modalForm.empty.heading\": \"Inget här ännu.\",\n  \"modalForm.empty.sub-heading\": \"Hitta det du letar efter genom ett brett utbud av tillägg.\",\n  \"modalForm.editCategory.base.name.description\": \"Inget mellanslag får finnas i namnet på kategorin\",\n  \"modalForm.header-edit\": \"Redigera {name}\",\n  \"modalForm.header.categories\": \"Kategorier\",\n  \"modalForm.header.back\": \"Tillbaka\",\n  \"modalForm.singleType.header-create\": \"Skapa engångstyp\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Lägg till ny komponent till dynamisk zon\",\n  \"modalForm.sub-header.attribute.create\": \"Lägg till {type}-fält\",\n  \"modalForm.sub-header.attribute.create.step\": \"Lägg till ny komponent ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Redigera {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Välj ett fält för samlingstypen\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Välj ett fält för komponenten\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Välj ett fält för engångstypen\",\n  \"modalForm.custom-fields.advanced.settings.extended\": \"Utökade inställningar\",\n  \"modalForm.tabs.custom\": \"Anpassat\",\n  \"modalForm.tabs.custom.howToLink\": \"Hur du lägger till anpassade fält\",\n  \"modalForm.tabs.default\": \"Standard\",\n  \"modalForm.tabs.label\": \"Standard/anpassade-flikar\",\n  \"modelPage.attribute.relation-polymorphic\": \"Relation (polymorfisk)\",\n  \"modelPage.attribute.relationWith\": \"Relation med\",\n  \"notification.error.dynamiczone-min.validation\": \"Minst en komponent måste vara vald för en dynamisk zon för att kunna spara innehållstypen\",\n  \"notification.info.autoreaload-disable\": \"autoReload-funktionen krävs för att använda detta plugin. Starta servern med `strapi develop`\",\n  \"notification.info.creating.notSaved\": \"Spara ditt arbete innan du skapar en ny samlingstyp eller komponent\",\n  \"plugin.description.long\": \"Modellera datastrukturen för ditt API. Skapa nya fält och relationer på bara en minut. Filerna skapas och uppdateras automatiskt i ditt projekt.\",\n  \"plugin.description.short\": \"Modellera datastrukturen för ditt API.\",\n  \"plugin.name\": \"Innehållstyps-skapare\",\n  \"popUpForm.navContainer.advanced\": \"Avancerade inställningar\",\n  \"popUpForm.navContainer.base\": \"Grundläggande inställningar\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Är du säker på att du vill avbryta dina ändringar?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Är du säker på att du vill avbryta dina ändringar? Vissa komponenter har skapats eller modifierats...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Är du säker på att du vill ta bort den här kategorin? Alla komponenter i den kommer också att raderas.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Är du säker på att du vill ta bort den här komponenten?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Är du säker på att du vill ta bort den här samlingstypen?\",\n  \"popUpWarning.draft-publish.button.confirm\": \"Ja, inaktiverar\",\n  \"popUpWarning.draft-publish.message\": \"Om du inaktiverar Utkast/Publicera-systemet kommer dina utkast att raderas.\",\n  \"popUpWarning.draft-publish.second-message\": \"Är du säker på att du vill inaktivera det?\",\n  \"prompt.unsaved\": \"Är du säker på att du vill lämna? Dina ändringar kommer att försvinna.\",\n  \"relation.attributeName.placeholder\": \"Ex: författare, kategori, tagg\",\n  \"relation.manyToMany\": \"har och tillhör många\",\n  \"relation.manyToOne\": \"har många\",\n  \"relation.manyWay\": \"har många\",\n  \"relation.oneToMany\": \"tillhör många\",\n  \"relation.oneToOne\": \"har och tillhör en\",\n  \"relation.oneWay\": \"har en\",\n  \"table.button.no-fields\": \"Lägg till nytt fält\",\n  \"table.content.create-first-content-type\": \"Skapa din första samlingstyp\",\n  \"table.content.no-fields.collection-type\": \"Lägg till ett första fält till samlingstypen\",\n  \"table.content.no-fields.component\": \"Lägg till ett första fält i komponenten\"\n};\nexport {\n  configurations,\n  sv as default,\n  from\n};\n//# sourceMappingURL=sv-BGb12eW3.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,qCAAqC;AAAA,EACrC,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACvC;", "names": []}