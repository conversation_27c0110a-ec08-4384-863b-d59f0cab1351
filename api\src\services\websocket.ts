/**
 * WebSocket Service para notificaciones en tiempo real - Versión Simplificada
 */

import { WebSocketServer, WebSocket } from "ws";

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private connections: Set<WebSocket> = new Set();

  /**
   * Inicializar el servidor WebSocket
   */
  initialize(server: any) {
    console.log("🚀 Inicializando servidor WebSocket simplificado...");

    this.wss = new WebSocketServer({
      server,
      path: "/ws/notifications",
    });

    this.wss.on("connection", this.handleConnection.bind(this));

    console.log("✅ Servidor WebSocket inicializado en /ws/notifications");
  }

  /**
   * Manejar nueva conexión WebSocket
   */
  private handleConnection(ws: WebSocket) {
    console.log("🔗 Nueva conexión WebSocket");

    // Agregar a la lista de conexiones
    this.connections.add(ws);

    // Enviar mensaje de bienvenida
    this.sendToClient(ws, {
      type: "connection",
      message: "Conectado al servidor WebSocket",
      timestamp: new Date().toISOString(),
    });

    // Manejar mensajes del cliente
    ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log("📨 Mensaje recibido:", message);

        // Responder con pong si es ping
        if (message.type === "ping") {
          this.sendToClient(ws, {
            type: "pong",
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        console.error("❌ Error procesando mensaje:", error);
      }
    });

    // Manejar desconexión
    ws.on("close", () => {
      console.log("🔌 Conexión WebSocket cerrada");
      this.connections.delete(ws);
    });

    // Manejar errores
    ws.on("error", (error) => {
      console.error("❌ Error WebSocket:", error);
      this.connections.delete(ws);
    });
  }

  /**
   * Enviar mensaje a un cliente específico
   */
  private sendToClient(ws: WebSocket, data: any): boolean {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast a todas las conexiones
   */
  broadcast(data: any): number {
    let sentCount = 0;

    this.connections.forEach((ws) => {
      if (this.sendToClient(ws, data)) {
        sentCount++;
      }
    });

    console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
    return sentCount;
  }

  /**
   * Enviar notificación (simplificado)
   */
  sendNotificationToUser(userId: number, notification: any): boolean {
    return (
      this.broadcast({
        type: "notification",
        data: notification,
        timestamp: new Date().toISOString(),
      }) > 0
    );
  }

  /**
   * Obtener estadísticas
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      isActive: this.wss !== null,
    };
  }

  /**
   * Cerrar servidor WebSocket
   */
  close() {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.connections.clear();
    console.log("🔌 Servidor WebSocket cerrado");
  }
}

// Exportar instancia singleton
export const websocketService = new WebSocketService();
export default websocketService;
