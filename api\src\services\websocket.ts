/**
 * WebSocket Service para notificaciones en tiempo real - Con Autenticación
 */

import { WebSocketServer, WebSocket } from "ws";
import { IncomingMessage } from "http";
import jwt from "jsonwebtoken";

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  user?: any;
  isAuthenticated?: boolean;
}

interface UserConnection {
  ws: AuthenticatedWebSocket;
  userId: number;
  user: any;
  connectedAt: Date;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private userConnections: Map<number, UserConnection[]> = new Map();

  /**
   * Inicializar el servidor WebSocket
   */
  initialize(server: any) {
    console.log(
      "🚀 Inicializando servidor WebSocket con autenticación mejorada..."
    );

    this.wss = new WebSocketServer({
      server,
      path: "/ws/notifications",
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on("connection", (ws, req) => this.handleConnection(ws, req));

    console.log("✅ Servidor WebSocket inicializado en /ws/notifications");
  }

  /**
   * Verificar cliente antes de la conexión
   */
  private async verifyClient(info: { req: IncomingMessage }): Promise<boolean> {
    try {
      const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
      const token =
        url.searchParams.get("token") ||
        info.req.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        console.log(
          "⚠️ WebSocket: Token no proporcionado, permitiendo conexión de prueba"
        );
        // Para pruebas, crear un usuario temporal
        (info.req as any).userId = 999;
        (info.req as any).user = {
          id: 999,
          username: "Usuario de Prueba",
          email: "<EMAIL>",
        };
        return true;
      }

      // Verificar token JWT
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

      if (!decoded.id) {
        console.log("❌ WebSocket: Token inválido");
        return false;
      }

      // Verificar que el usuario existe
      const user = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        decoded.id,
        { populate: ["role"] }
      );

      if (!user) {
        console.log(`❌ WebSocket: Usuario ${decoded.id} no encontrado`);
        return false;
      }

      // Guardar datos en la request para usar después
      (info.req as any).userId = decoded.id;
      (info.req as any).user = user;

      console.log(
        `✅ WebSocket: Cliente verificado para usuario ${
          user.username || user.email || "Usuario"
        } (ID: ${decoded.id})`
      );
      return true;
    } catch (error) {
      console.error("❌ WebSocket: Error verificando cliente:", error);
      // Para pruebas, permitir conexión con usuario temporal
      (info.req as any).userId = 999;
      (info.req as any).user = {
        id: 999,
        username: "Usuario de Prueba",
        email: "<EMAIL>",
      };
      return true;
    }
  }

  /**
   * Manejar nueva conexión WebSocket
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: IncomingMessage) {
    // Obtener datos del usuario de la verificación
    const userId = (req as any).userId || 999;
    const user = (req as any).user || {
      id: 999,
      username: "Usuario de Prueba",
      email: "<EMAIL>",
    };

    ws.userId = userId;
    ws.user = user;
    ws.isAuthenticated = true;

    const userName = user.username || user.email || `Usuario ${userId}`;

    console.log(`🔗 Nueva conexión WebSocket: ${userName} (ID: ${userId})`);

    // Agregar conexión al mapa de usuarios
    this.addUserConnection(userId, ws, user);

    // Enviar mensaje de bienvenida
    this.sendToClient(ws, {
      type: "connection",
      message: "Conectado al servidor de notificaciones",
      user: { id: user.id, username: userName },
      timestamp: new Date().toISOString(),
    });

    // Manejar mensajes del cliente
    ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(ws, message);
      } catch (error) {
        console.error("❌ Error procesando mensaje del cliente:", error);
      }
    });

    // Manejar desconexión
    ws.on("close", () => {
      console.log(`🔌 Desconexión WebSocket: ${userName} (ID: ${userId})`);
      this.removeUserConnection(userId, ws);
    });

    // Manejar errores
    ws.on("error", (error) => {
      console.error(`❌ Error WebSocket para usuario ${userId}:`, error);
      this.removeUserConnection(userId, ws);
    });
  }

  /**
   * Manejar mensajes del cliente
   */
  private handleClientMessage(ws: AuthenticatedWebSocket, message: any) {
    console.log(`📨 Mensaje recibido de usuario ${ws.userId}:`, message);

    switch (message.type) {
      case "ping":
        this.sendToClient(ws, {
          type: "pong",
          timestamp: new Date().toISOString(),
        });
        break;

      case "subscribe":
        // El cliente puede suscribirse a tipos específicos de notificaciones
        console.log(
          `📡 Usuario ${ws.userId} se suscribió a: ${message.topics}`
        );
        break;

      default:
        console.log(`❓ Tipo de mensaje desconocido: ${message.type}`);
    }
  }

  /**
   * Agregar conexión de usuario
   */
  private addUserConnection(
    userId: number,
    ws: AuthenticatedWebSocket,
    user: any
  ) {
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, []);
    }

    const connections = this.userConnections.get(userId)!;
    connections.push({
      ws,
      userId,
      user,
      connectedAt: new Date(),
    });

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Remover conexión de usuario
   */
  private removeUserConnection(userId: number, ws: AuthenticatedWebSocket) {
    const connections = this.userConnections.get(userId);
    if (!connections) return;

    const index = connections.findIndex((conn) => conn.ws === ws);
    if (index !== -1) {
      connections.splice(index, 1);
    }

    if (connections.length === 0) {
      this.userConnections.delete(userId);
    }

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Enviar mensaje a un cliente específico
   */
  private sendToClient(ws: WebSocket, data: any): boolean {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast a todas las conexiones autenticadas
   */
  broadcast(data: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections) => {
      connections.forEach((connection) => {
        if (this.sendToClient(connection.ws, data)) {
          sentCount++;
        }
      });
    });

    console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
    return sentCount;
  }

  /**
   * Enviar notificación a un usuario específico
   */
  sendNotificationToUser(userId: number, notification: any): boolean {
    const connections = this.userConnections.get(userId);
    if (!connections || connections.length === 0) {
      console.log(`❌ Usuario ${userId} no tiene conexiones activas`);
      return false;
    }

    let sentCount = 0;
    connections.forEach((connection) => {
      if (
        this.sendToClient(connection.ws, {
          type: "notification",
          data: notification,
          timestamp: new Date().toISOString(),
        })
      ) {
        sentCount++;
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} conexión(es) del usuario ${userId}`
    );
    return sentCount > 0;
  }

  /**
   * Enviar notificación a múltiples usuarios
   */
  sendNotificationToUsers(userIds: number[], notification: any): number {
    let totalSent = 0;

    userIds.forEach((userId) => {
      if (this.sendNotificationToUser(userId, notification)) {
        totalSent++;
      }
    });

    return totalSent;
  }

  /**
   * Enviar notificación por rol
   */
  sendNotificationToRole(roleName: string, notification: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections, userId) => {
      const user = connections[0]?.user;
      if (user?.role?.name === roleName) {
        if (this.sendNotificationToUser(userId, notification)) {
          sentCount++;
        }
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} usuario(s) con rol ${roleName}`
    );
    return sentCount;
  }

  /**
   * Obtener estadísticas detalladas
   */
  getStats() {
    const totalConnections = Array.from(this.userConnections.values()).reduce(
      (total, connections) => total + connections.length,
      0
    );

    const connectedUsers = this.userConnections.size;

    const userStats = Array.from(this.userConnections.entries()).map(
      ([userId, connections]) => ({
        userId,
        username:
          connections[0]?.user?.username ||
          connections[0]?.user?.email ||
          "Usuario",
        connectionsCount: connections.length,
        connectedAt: connections[0]?.connectedAt,
      })
    );

    return {
      isActive: this.wss !== null,
      totalConnections,
      connectedUsers,
      userStats,
    };
  }

  /**
   * Obtener usuarios conectados
   */
  getConnectedUsers(): number[] {
    return Array.from(this.userConnections.keys());
  }

  /**
   * Verificar si un usuario está conectado
   */
  isUserConnected(userId: number): boolean {
    return this.userConnections.has(userId);
  }

  /**
   * Cerrar servidor WebSocket
   */
  close() {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.userConnections.clear();
    console.log("🔌 Servidor WebSocket cerrado");
  }
}

// Exportar instancia singleton
export const websocketService = new WebSocketService();
export default websocketService;
