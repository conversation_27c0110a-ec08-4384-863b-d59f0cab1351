/**
 * WebSocket Service para notificaciones en tiempo real - Con Autenticación
 */

import { WebSocketServer, WebSocket } from "ws";
import { IncomingMessage } from "http";
import jwt from "jsonwebtoken";

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  user?: any;
  isAuthenticated?: boolean;
}

interface UserConnection {
  ws: AuthenticatedWebSocket;
  userId: number;
  user: any;
  connectedAt: Date;
}

class WebSocketService {
  private wss: WebSocketServer | null = null;
  private userConnections: Map<number, UserConnection[]> = new Map();
  private tokenToUserMap: Map<string, { userId: number; user: any }> =
    new Map();

  /**
   * Inicializar el servidor WebSocket
   */
  initialize(server: any) {
    console.log(
      "🚀 Inicializando servidor WebSocket con autenticación mejorada..."
    );

    this.wss = new WebSocketServer({
      server,
      path: "/ws/notifications",
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on("connection", (ws, req) => this.handleConnection(ws, req));

    console.log("✅ Servidor WebSocket inicializado en /ws/notifications");
  }

  /**
   * Verificar cliente antes de la conexión
   */
  private async verifyClient(info: { req: IncomingMessage }): Promise<boolean> {
    try {
      const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
      const token =
        url.searchParams.get("token") ||
        info.req.headers.authorization?.replace("Bearer ", "");

      if (!token) {
        console.log(
          "❌ WebSocket: Token no proporcionado, rechazando conexión"
        );
        return false;
      }

      // Verificar token JWT
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

      if (!decoded.id) {
        console.log("❌ WebSocket: Token inválido");
        return false;
      }

      // Verificar que el usuario existe
      const user = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        decoded.id,
        { populate: ["role"] }
      );

      if (!user) {
        console.log(`❌ WebSocket: Usuario ${decoded.id} no encontrado`);
        return false;
      }

      // Generar un ID único para esta conexión
      const connectionKey = `conn_${decoded.id}_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;

      // Guardar datos en la request Y en almacenamiento temporal
      (info.req as any).userId = decoded.id;
      (info.req as any).user = user;
      (info.req as any).connectionKey = connectionKey;

      // Almacenar por token para recuperar en handleConnection
      this.tokenToUserMap.set(token, {
        userId: decoded.id,
        user: user,
      });

      console.log(
        `✅ WebSocket: Cliente verificado para usuario ${
          user.username || user.email || "Usuario"
        } (ID: ${decoded.id}) - Rol: ${
          (user as any).role?.name || "sin rol"
        } - Key: ${connectionKey}`
      );
      return true;
    } catch (error) {
      console.error("❌ WebSocket: Error verificando cliente:", error);
      return false;
    }
  }

  /**
   * Manejar nueva conexión WebSocket
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: IncomingMessage) {
    // Extraer token de la URL
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const token = url.searchParams.get("token");

    if (!token) {
      console.error("❌ WebSocket: Token no encontrado en la URL");
      ws.close(1008, "Token requerido");
      return;
    }

    // Buscar datos del usuario por token
    const userData = this.tokenToUserMap.get(token);
    if (!userData) {
      console.error(
        "❌ WebSocket: Datos de usuario no encontrados para el token"
      );
      ws.close(1008, "Usuario no autenticado");
      return;
    }

    const { userId, user } = userData;

    // Limpiar datos temporales
    this.tokenToUserMap.delete(token);

    ws.userId = userId;
    ws.user = user;
    ws.isAuthenticated = true;

    const userName = user.username || user.email || `Usuario ${userId}`;
    console.log(
      `🔗 Nueva conexión WebSocket AUTENTICADA: ${userName} (ID: ${userId})`
    );

    // Agregar conexión al mapa de usuarios
    this.addUserConnection(ws.userId, ws, ws.user);

    // Enviar mensaje de bienvenida
    this.sendToClient(ws, {
      type: "connection",
      message: "Conectado al servidor de notificaciones",
      user: { id: ws.user.id, username: userName },
      timestamp: new Date().toISOString(),
    });

    // Manejar mensajes del cliente
    ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(ws, message);
      } catch (error) {
        console.error("❌ Error procesando mensaje del cliente:", error);
      }
    });

    // Manejar desconexión
    ws.on("close", () => {
      const userName =
        ws.user.username || ws.user.email || `Usuario ${ws.userId}`;
      console.log(`🔌 Desconexión WebSocket: ${userName} (ID: ${ws.userId})`);
      this.removeUserConnection(ws.userId, ws);
    });

    // Manejar errores
    ws.on("error", (error) => {
      console.error(`❌ Error WebSocket para usuario ${ws.userId}:`, error);
      this.removeUserConnection(ws.userId, ws);
    });
  }

  /**
   * Manejar mensajes del cliente
   */
  private handleClientMessage(ws: AuthenticatedWebSocket, message: any) {
    console.log(`📨 Mensaje recibido de usuario ${ws.userId}:`, message);

    switch (message.type) {
      case "ping":
        this.sendToClient(ws, {
          type: "pong",
          timestamp: new Date().toISOString(),
        });
        break;

      case "subscribe":
        // El cliente puede suscribirse a tipos específicos de notificaciones
        console.log(
          `📡 Usuario ${ws.userId} se suscribió a: ${message.topics}`
        );
        break;

      default:
        console.log(`❓ Tipo de mensaje desconocido: ${message.type}`);
    }
  }

  /**
   * Agregar conexión de usuario
   */
  private addUserConnection(
    userId: number,
    ws: AuthenticatedWebSocket,
    user: any
  ) {
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, []);
    }

    const connections = this.userConnections.get(userId)!;
    connections.push({
      ws,
      userId,
      user,
      connectedAt: new Date(),
    });

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Remover conexión de usuario
   */
  private removeUserConnection(userId: number, ws: AuthenticatedWebSocket) {
    const connections = this.userConnections.get(userId);
    if (!connections) return;

    const index = connections.findIndex((conn) => conn.ws === ws);
    if (index !== -1) {
      connections.splice(index, 1);
    }

    if (connections.length === 0) {
      this.userConnections.delete(userId);
    }

    console.log(
      `📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`
    );
  }

  /**
   * Enviar mensaje a un cliente específico
   */
  private sendToClient(ws: WebSocket, data: any): boolean {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error("❌ Error enviando mensaje:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Broadcast a todas las conexiones autenticadas
   */
  broadcast(data: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections) => {
      connections.forEach((connection) => {
        if (this.sendToClient(connection.ws, data)) {
          sentCount++;
        }
      });
    });

    console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
    return sentCount;
  }

  /**
   * Enviar notificación a un usuario específico
   */
  sendNotificationToUser(userId: number, notification: any): boolean {
    // Verificar si userId es NaN
    if (isNaN(userId)) {
      console.log(`❌ ERROR: userId es NaN. Valor original: ${userId}`);
      return false;
    }

    const connections = this.userConnections.get(userId);
    if (!connections || connections.length === 0) {
      console.log(`❌ Usuario ${userId} no tiene conexiones activas`);
      return false;
    }

    let sentCount = 0;
    connections.forEach((connection) => {
      if (
        this.sendToClient(connection.ws, {
          type: "notification",
          data: notification,
          timestamp: new Date().toISOString(),
        })
      ) {
        sentCount++;
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} conexión(es) del usuario ${userId}`
    );
    return sentCount > 0;
  }

  /**
   * Enviar notificación a múltiples usuarios
   */
  sendNotificationToUsers(userIds: number[], notification: any): number {
    let totalSent = 0;

    userIds.forEach((userId) => {
      if (this.sendNotificationToUser(userId, notification)) {
        totalSent++;
      }
    });

    return totalSent;
  }

  /**
   * Enviar notificación por rol
   */
  sendNotificationToRole(roleName: string, notification: any): number {
    let sentCount = 0;

    this.userConnections.forEach((connections, userId) => {
      const user = connections[0]?.user;

      if (user?.role?.name === roleName) {
        if (this.sendNotificationToUser(userId, notification)) {
          sentCount++;
        }
      }
    });

    console.log(
      `📤 Notificación enviada a ${sentCount} usuario(s) con rol ${roleName}`
    );
    return sentCount;
  }

  /**
   * Obtener estadísticas detalladas
   */
  getStats() {
    const totalConnections = Array.from(this.userConnections.values()).reduce(
      (total, connections) => total + connections.length,
      0
    );

    const connectedUsers = this.userConnections.size;

    const userStats = Array.from(this.userConnections.entries()).map(
      ([userId, connections]) => ({
        userId,
        username:
          connections[0]?.user?.username ||
          connections[0]?.user?.email ||
          "Usuario",
        connectionsCount: connections.length,
        connectedAt: connections[0]?.connectedAt,
      })
    );

    return {
      isActive: this.wss !== null,
      totalConnections,
      connectedUsers,
      userStats,
    };
  }

  /**
   * Obtener usuarios conectados
   */
  getConnectedUsers(): number[] {
    return Array.from(this.userConnections.keys());
  }

  /**
   * Verificar si un usuario está conectado
   */
  isUserConnected(userId: number): boolean {
    return this.userConnections.has(userId);
  }

  /**
   * Cerrar servidor WebSocket
   */
  close() {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.userConnections.clear();
    this.tokenToUserMap.clear();
    console.log("🔌 Servidor WebSocket cerrado");
  }
}

// Exportar instancia singleton
export const websocketService = new WebSocketService();
export default websocketService;
