"use strict";
/**
 * notification service
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ strapi }) => ({
    async sendWarningNotification(warning, resident) {
        var _a;
        try {
            // Verificar si las notificaciones están habilitadas
            if (!((_a = warning.notifications) === null || _a === void 0 ? void 0 : _a.enabled)) {
                return;
            }
            const { channels } = warning.notifications;
            // Preparar la notificación
            const notification = {
                type: "warning",
                title: `Nuevo ${this.getWarningTypeLabel(warning.type)}`,
                message: warning.description ||
                    warning.content ||
                    "Se ha registrado una nueva advertencia",
                data: {
                    warningId: warning.id,
                    warningType: warning.type,
                    date: warning.date,
                    time: warning.time,
                },
            };
            // 1. Crear notificación interna en Strapi
            await strapi.service("api::notification.notification").create({
                data: {
                    title: notification.title,
                    type: `warning_${warning.type}`,
                    message: notification.message,
                    user: resident.id,
                    data: notification.data,
                },
            });
            // 2. Enviar notificación por WebSocket
            if (strapi.websocket) {
                strapi.websocket.sendNotificationToUser(resident.id, notification);
            }
            // 3. Si el email está habilitado, enviar correo
            if (channels.email && resident.email) {
                await strapi.plugins["email"].services.email.send({
                    to: resident.email,
                    subject: notification.title,
                    text: notification.message,
                    html: this.getEmailTemplate(warning, resident),
                });
            }
            // 4. Enviar WhatsApp si está habilitado
            if (channels.whatsapp && resident.phone) {
                try {
                    await strapi
                        .service("api::warning.whatsapp")
                        .sendWhatsApp(resident.phone, `${notification.title}\n\n${notification.message}`);
                }
                catch (error) {
                    // Log el error pero no interrumpir el flujo
                    strapi.log.error("Error sending WhatsApp notification:", error);
                }
            }
        }
        catch (error) {
            strapi.log.error("Error sending warning notification:", error);
        }
    },
    getWarningTypeLabel(type) {
        const labels = {
            attention_call_1: "Llamado de Atención",
            attention_call_2: "Segundo Llamado de Atención",
            sanction: "Sanción",
        };
        return labels[type] || "Advertencia";
    },
    getEmailTemplate(warning, resident) {
        return `
      <h2>Estimado(a) ${resident.firstName} ${resident.lastName}</h2>
      <p>Se ha registrado un ${this.getWarningTypeLabel(warning.type)} a su nombre.</p>
      <p><strong>Fecha:</strong> ${warning.date}</p>
      <p><strong>Hora:</strong> ${warning.time}</p>
      ${warning.description ? `<p><strong>Descripción:</strong> ${warning.description}</p>` : ""}
      ${warning.content ? `<p><strong>Contenido:</strong> ${warning.content}</p>` : ""}
      <p>Por favor, ingrese a la plataforma para más detalles.</p>
    `;
    },
});
