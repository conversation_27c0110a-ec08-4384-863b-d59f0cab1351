import { Button, Toolt<PERSON> } from "antd";
import { BellOutlined, BellFilled, LoadingOutlined } from "@ant-design/icons";
import { usePushNotifications } from "@app/hooks/notifications/usePushNotifications";

export const PushNotificationButton = () => {
  const { isSupported, isSubscribed, isLoading, subscribe, unsubscribe } =
    usePushNotifications();

  if (!isSupported) {
    return null;
  }

  const handleClick = async () => {
    if (isSubscribed) {
      await unsubscribe();
    } else {
      await subscribe();
    }
  };

  const tooltipTitle = isSubscribed
    ? "Desactivar notificaciones push"
    : "Activar notificaciones push";

  return (
    <Tooltip title={tooltipTitle}>
      <Button
        size="small"
        type={isSubscribed ? "primary" : "default"}
        icon={
          isLoading ? (
            <LoadingOutlined />
          ) : isSubscribed ? (
            <BellFilled />
          ) : (
            <BellOutlined />
          )
        }
        onClick={handleClick}
        disabled={isLoading}
      />
    </Tooltip>
  );
};
