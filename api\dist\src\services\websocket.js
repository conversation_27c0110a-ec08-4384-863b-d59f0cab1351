"use strict";
/**
 * WebSocket Service para notificaciones en tiempo real - Con Autenticación
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = void 0;
const ws_1 = require("ws");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class WebSocketService {
    constructor() {
        this.wss = null;
        this.userConnections = new Map();
        this.pendingConnections = new Map();
    }
    /**
     * Inicializar el servidor WebSocket
     */
    initialize(server) {
        console.log("🚀 Inicializando servidor WebSocket con autenticación mejorada...");
        this.wss = new ws_1.WebSocketServer({
            server,
            path: "/ws/notifications",
            verifyClient: this.verifyClient.bind(this),
        });
        this.wss.on("connection", (ws, req) => this.handleConnection(ws, req));
        console.log("✅ Servidor WebSocket inicializado en /ws/notifications");
    }
    /**
     * Verificar cliente antes de la conexión
     */
    async verifyClient(info) {
        var _a;
        try {
            const url = new URL(info.req.url, `http://${info.req.headers.host}`);
            const token = url.searchParams.get("token") ||
                ((_a = info.req.headers.authorization) === null || _a === void 0 ? void 0 : _a.replace("Bearer ", ""));
            if (!token) {
                console.log("❌ WebSocket: Token no proporcionado, rechazando conexión");
                return false;
            }
            // Verificar token JWT
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            if (!decoded.id) {
                console.log("❌ WebSocket: Token inválido");
                return false;
            }
            // Verificar que el usuario existe
            const user = await strapi.entityService.findOne("plugin::users-permissions.user", decoded.id, { populate: ["role"] });
            if (!user) {
                console.log(`❌ WebSocket: Usuario ${decoded.id} no encontrado`);
                return false;
            }
            // Guardar datos en la request Y en almacenamiento temporal
            const connectionKey = `${info.req.socket.remoteAddress}:${info.req.socket.remotePort}:${Date.now()}`;
            info.req.userId = decoded.id;
            info.req.user = user;
            info.req.connectionKey = connectionKey;
            // Almacenar temporalmente para recuperar en handleConnection
            this.pendingConnections.set(connectionKey, {
                userId: decoded.id,
                user: user,
            });
            console.log(`✅ WebSocket: Cliente verificado para usuario ${user.username || user.email || "Usuario"} (ID: ${decoded.id}) - Key: ${connectionKey}`);
            return true;
        }
        catch (error) {
            console.error("❌ WebSocket: Error verificando cliente:", error);
            return false;
        }
    }
    /**
     * Manejar nueva conexión WebSocket
     */
    handleConnection(ws, req) {
        // Intentar obtener datos del usuario de múltiples fuentes
        let userId = req.userId;
        let user = req.user;
        const connectionKey = req.connectionKey;
        // Si no tenemos datos directos, intentar recuperar del almacenamiento temporal
        if ((!userId || !user) && connectionKey) {
            const pendingData = this.pendingConnections.get(connectionKey);
            if (pendingData) {
                userId = pendingData.userId;
                user = pendingData.user;
                // Limpiar datos temporales
                this.pendingConnections.delete(connectionKey);
                console.log(`🔄 Datos recuperados del almacenamiento temporal para ${userId}`);
            }
        }
        // Si aún no tenemos datos, buscar en todas las conexiones pendientes (fallback)
        if (!userId || !user) {
            const remoteAddress = req.socket.remoteAddress;
            for (const [key, data] of this.pendingConnections.entries()) {
                if (key.includes(remoteAddress || "")) {
                    userId = data.userId;
                    user = data.user;
                    this.pendingConnections.delete(key);
                    console.log(`🔄 Datos recuperados por IP para ${userId}`);
                    break;
                }
            }
        }
        if (!userId || !user) {
            console.error("❌ WebSocket: Datos de usuario no encontrados en la conexión");
            console.error("❌ Debug - req keys:", Object.keys(req));
            console.error("❌ Debug - pending connections:", this.pendingConnections.size);
            ws.close(1008, "Usuario no autenticado");
            return;
        }
        ws.userId = userId;
        ws.user = user;
        ws.isAuthenticated = true;
        const userName = user.username || user.email || `Usuario ${userId}`;
        console.log(`🔗 Nueva conexión WebSocket AUTENTICADA: ${userName} (ID: ${userId})`);
        // Agregar conexión al mapa de usuarios
        this.addUserConnection(ws.userId, ws, ws.user);
        // Enviar mensaje de bienvenida
        this.sendToClient(ws, {
            type: "connection",
            message: "Conectado al servidor de notificaciones",
            user: { id: ws.user.id, username: userName },
            timestamp: new Date().toISOString(),
        });
        // Manejar mensajes del cliente
        ws.on("message", (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleClientMessage(ws, message);
            }
            catch (error) {
                console.error("❌ Error procesando mensaje del cliente:", error);
            }
        });
        // Manejar desconexión
        ws.on("close", () => {
            const userName = ws.user.username || ws.user.email || `Usuario ${ws.userId}`;
            console.log(`🔌 Desconexión WebSocket: ${userName} (ID: ${ws.userId})`);
            this.removeUserConnection(ws.userId, ws);
        });
        // Manejar errores
        ws.on("error", (error) => {
            console.error(`❌ Error WebSocket para usuario ${ws.userId}:`, error);
            this.removeUserConnection(ws.userId, ws);
        });
    }
    /**
     * Manejar mensajes del cliente
     */
    handleClientMessage(ws, message) {
        console.log(`📨 Mensaje recibido de usuario ${ws.userId}:`, message);
        switch (message.type) {
            case "ping":
                this.sendToClient(ws, {
                    type: "pong",
                    timestamp: new Date().toISOString(),
                });
                break;
            case "subscribe":
                // El cliente puede suscribirse a tipos específicos de notificaciones
                console.log(`📡 Usuario ${ws.userId} se suscribió a: ${message.topics}`);
                break;
            default:
                console.log(`❓ Tipo de mensaje desconocido: ${message.type}`);
        }
    }
    /**
     * Agregar conexión de usuario
     */
    addUserConnection(userId, ws, user) {
        if (!this.userConnections.has(userId)) {
            this.userConnections.set(userId, []);
        }
        const connections = this.userConnections.get(userId);
        connections.push({
            ws,
            userId,
            user,
            connectedAt: new Date(),
        });
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Remover conexión de usuario
     */
    removeUserConnection(userId, ws) {
        const connections = this.userConnections.get(userId);
        if (!connections)
            return;
        const index = connections.findIndex((conn) => conn.ws === ws);
        if (index !== -1) {
            connections.splice(index, 1);
        }
        if (connections.length === 0) {
            this.userConnections.delete(userId);
        }
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Enviar mensaje a un cliente específico
     */
    sendToClient(ws, data) {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify(data));
                return true;
            }
            catch (error) {
                console.error("❌ Error enviando mensaje:", error);
                return false;
            }
        }
        return false;
    }
    /**
     * Broadcast a todas las conexiones autenticadas
     */
    broadcast(data) {
        let sentCount = 0;
        this.userConnections.forEach((connections) => {
            connections.forEach((connection) => {
                if (this.sendToClient(connection.ws, data)) {
                    sentCount++;
                }
            });
        });
        console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
        return sentCount;
    }
    /**
     * Enviar notificación a un usuario específico
     */
    sendNotificationToUser(userId, notification) {
        const connections = this.userConnections.get(userId);
        if (!connections || connections.length === 0) {
            console.log(`❌ Usuario ${userId} no tiene conexiones activas`);
            return false;
        }
        let sentCount = 0;
        connections.forEach((connection) => {
            if (this.sendToClient(connection.ws, {
                type: "notification",
                data: notification,
                timestamp: new Date().toISOString(),
            })) {
                sentCount++;
            }
        });
        console.log(`📤 Notificación enviada a ${sentCount} conexión(es) del usuario ${userId}`);
        return sentCount > 0;
    }
    /**
     * Enviar notificación a múltiples usuarios
     */
    sendNotificationToUsers(userIds, notification) {
        let totalSent = 0;
        userIds.forEach((userId) => {
            if (this.sendNotificationToUser(userId, notification)) {
                totalSent++;
            }
        });
        return totalSent;
    }
    /**
     * Enviar notificación por rol
     */
    sendNotificationToRole(roleName, notification) {
        let sentCount = 0;
        this.userConnections.forEach((connections, userId) => {
            var _a, _b;
            const user = (_a = connections[0]) === null || _a === void 0 ? void 0 : _a.user;
            if (((_b = user === null || user === void 0 ? void 0 : user.role) === null || _b === void 0 ? void 0 : _b.name) === roleName) {
                if (this.sendNotificationToUser(userId, notification)) {
                    sentCount++;
                }
            }
        });
        console.log(`📤 Notificación enviada a ${sentCount} usuario(s) con rol ${roleName}`);
        return sentCount;
    }
    /**
     * Obtener estadísticas detalladas
     */
    getStats() {
        const totalConnections = Array.from(this.userConnections.values()).reduce((total, connections) => total + connections.length, 0);
        const connectedUsers = this.userConnections.size;
        const userStats = Array.from(this.userConnections.entries()).map(([userId, connections]) => {
            var _a, _b, _c, _d, _e;
            return ({
                userId,
                username: ((_b = (_a = connections[0]) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.username) ||
                    ((_d = (_c = connections[0]) === null || _c === void 0 ? void 0 : _c.user) === null || _d === void 0 ? void 0 : _d.email) ||
                    "Usuario",
                connectionsCount: connections.length,
                connectedAt: (_e = connections[0]) === null || _e === void 0 ? void 0 : _e.connectedAt,
            });
        });
        return {
            isActive: this.wss !== null,
            totalConnections,
            connectedUsers,
            userStats,
        };
    }
    /**
     * Obtener usuarios conectados
     */
    getConnectedUsers() {
        return Array.from(this.userConnections.keys());
    }
    /**
     * Verificar si un usuario está conectado
     */
    isUserConnected(userId) {
        return this.userConnections.has(userId);
    }
    /**
     * Cerrar servidor WebSocket
     */
    close() {
        if (this.wss) {
            this.wss.close();
            this.wss = null;
        }
        this.userConnections.clear();
        console.log("🔌 Servidor WebSocket cerrado");
    }
}
// Exportar instancia singleton
exports.websocketService = new WebSocketService();
exports.default = exports.websocketService;
