"use strict";
/**
 * WebSocket Service para notificaciones en tiempo real
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = void 0;
const ws_1 = require("ws");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
class WebSocketService {
    constructor() {
        this.wss = null;
        this.userConnections = new Map();
        this.heartbeatInterval = null;
    }
    /**
     * Inicializar el servidor WebSocket
     */
    initialize(server) {
        console.log('🚀 Inicializando servidor WebSocket...');
        this.wss = new ws_1.WebSocketServer({
            server,
            path: '/ws/notifications',
            verifyClient: this.verifyClient.bind(this),
        });
        this.wss.on('connection', this.handleConnection.bind(this));
        this.startHeartbeat();
        console.log('✅ Servidor WebSocket inicializado en /ws/notifications');
    }
    /**
     * Verificar cliente antes de la conexión
     */
    async verifyClient(info) {
        var _a;
        try {
            const url = new URL(info.req.url, `http://${info.req.headers.host}`);
            const token = url.searchParams.get('token') ||
                ((_a = info.req.headers.authorization) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', ''));
            if (!token) {
                console.log('❌ WebSocket: Token no proporcionado');
                return false;
            }
            // Verificar token JWT
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            if (!decoded.id) {
                console.log('❌ WebSocket: Token inválido');
                return false;
            }
            // Verificar que el usuario existe
            const user = await strapi.entityService.findOne('plugin::users-permissions.user', decoded.id);
            if (!user) {
                console.log(`❌ WebSocket: Usuario ${decoded.id} no encontrado`);
                return false;
            }
            // Guardar datos en la request para usar después
            info.req.userId = decoded.id;
            info.req.user = user;
            console.log(`✅ WebSocket: Cliente verificado para usuario ${user.username} (ID: ${decoded.id})`);
            return true;
        }
        catch (error) {
            console.error('❌ WebSocket: Error verificando cliente:', error);
            return false;
        }
    }
    /**
     * Manejar nueva conexión WebSocket
     */
    handleConnection(ws, req) {
        const userId = req.userId;
        const user = req.user;
        ws.userId = userId;
        ws.isAuthenticated = true;
        console.log(`🔗 Nueva conexión WebSocket: ${user.username} (ID: ${userId})`);
        // Agregar conexión al mapa de usuarios
        this.addUserConnection(userId, ws);
        // Enviar mensaje de bienvenida
        this.sendToClient(ws, {
            type: 'connection',
            message: 'Conectado al servidor de notificaciones',
            timestamp: new Date().toISOString(),
        });
        // Manejar mensajes del cliente
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleClientMessage(ws, message);
            }
            catch (error) {
                console.error('❌ Error procesando mensaje del cliente:', error);
            }
        });
        // Manejar desconexión
        ws.on('close', () => {
            console.log(`🔌 Desconexión WebSocket: ${user.username} (ID: ${userId})`);
            this.removeUserConnection(userId, ws);
        });
        // Manejar errores
        ws.on('error', (error) => {
            console.error(`❌ Error WebSocket para usuario ${userId}:`, error);
            this.removeUserConnection(userId, ws);
        });
    }
    /**
     * Manejar mensajes del cliente
     */
    handleClientMessage(ws, message) {
        console.log(`📨 Mensaje recibido de usuario ${ws.userId}:`, message);
        switch (message.type) {
            case 'ping':
                this.sendToClient(ws, { type: 'pong', timestamp: new Date().toISOString() });
                break;
            case 'subscribe':
                // El cliente puede suscribirse a tipos específicos de notificaciones
                console.log(`📡 Usuario ${ws.userId} se suscribió a: ${message.topics}`);
                break;
            default:
                console.log(`❓ Tipo de mensaje desconocido: ${message.type}`);
        }
    }
    /**
     * Agregar conexión de usuario
     */
    addUserConnection(userId, ws) {
        if (!this.userConnections.has(userId)) {
            this.userConnections.set(userId, []);
        }
        const connections = this.userConnections.get(userId);
        connections.push({
            ws,
            userId,
            connectedAt: new Date(),
        });
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Remover conexión de usuario
     */
    removeUserConnection(userId, ws) {
        const connections = this.userConnections.get(userId);
        if (!connections)
            return;
        const index = connections.findIndex(conn => conn.ws === ws);
        if (index !== -1) {
            connections.splice(index, 1);
        }
        if (connections.length === 0) {
            this.userConnections.delete(userId);
        }
        console.log(`📊 Usuario ${userId} tiene ${connections.length} conexión(es) activa(s)`);
    }
    /**
     * Enviar mensaje a un cliente específico
     */
    sendToClient(ws, data) {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify(data));
                return true;
            }
            catch (error) {
                console.error('❌ Error enviando mensaje a cliente:', error);
                return false;
            }
        }
        return false;
    }
    /**
     * Enviar notificación a un usuario específico
     */
    sendNotificationToUser(userId, notification) {
        const connections = this.userConnections.get(userId);
        if (!connections || connections.length === 0) {
            console.log(`📭 No hay conexiones activas para usuario ${userId}`);
            return false;
        }
        let sent = false;
        connections.forEach(({ ws }) => {
            const success = this.sendToClient(ws, {
                type: 'notification',
                data: notification,
                timestamp: new Date().toISOString(),
            });
            if (success)
                sent = true;
        });
        if (sent) {
            console.log(`📤 Notificación enviada a usuario ${userId} (${connections.length} conexión(es))`);
        }
        return sent;
    }
    /**
     * Broadcast a todos los usuarios conectados
     */
    broadcast(data) {
        let sentCount = 0;
        this.userConnections.forEach((connections, userId) => {
            connections.forEach(({ ws }) => {
                if (this.sendToClient(ws, data)) {
                    sentCount++;
                }
            });
        });
        console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
        return sentCount;
    }
    /**
     * Obtener estadísticas de conexiones
     */
    getStats() {
        const totalConnections = Array.from(this.userConnections.values())
            .reduce((total, connections) => total + connections.length, 0);
        return {
            totalUsers: this.userConnections.size,
            totalConnections,
            userConnections: Array.from(this.userConnections.entries()).map(([userId, connections]) => ({
                userId,
                connections: connections.length,
                connectedAt: connections.map(c => c.connectedAt),
            })),
        };
    }
    /**
     * Heartbeat para mantener conexiones vivas
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.broadcast({
                type: 'heartbeat',
                timestamp: new Date().toISOString(),
            });
        }, 30000); // Cada 30 segundos
        console.log('💓 Heartbeat iniciado (cada 30 segundos)');
    }
    /**
     * Cerrar servidor WebSocket
     */
    close() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        if (this.wss) {
            this.wss.close();
            this.wss = null;
        }
        this.userConnections.clear();
        console.log('🔌 Servidor WebSocket cerrado');
    }
}
// Exportar instancia singleton
exports.websocketService = new WebSocketService();
exports.default = exports.websocketService;
