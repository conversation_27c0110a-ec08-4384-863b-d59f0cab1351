"use strict";
/**
 * WebSocket Service para notificaciones en tiempo real - Versión Simplificada
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketService = void 0;
const ws_1 = require("ws");
class WebSocketService {
    constructor() {
        this.wss = null;
        this.connections = new Set();
    }
    /**
     * Inicializar el servidor WebSocket
     */
    initialize(server) {
        console.log("🚀 Inicializando servidor WebSocket simplificado...");
        this.wss = new ws_1.WebSocketServer({
            server,
            path: "/ws/notifications",
        });
        this.wss.on("connection", this.handleConnection.bind(this));
        console.log("✅ Servidor WebSocket inicializado en /ws/notifications");
    }
    /**
     * Manejar nueva conexión WebSocket
     */
    handleConnection(ws) {
        console.log("🔗 Nueva conexión WebSocket");
        // Agregar a la lista de conexiones
        this.connections.add(ws);
        // Enviar mensaje de bienvenida
        this.sendToClient(ws, {
            type: "connection",
            message: "Conectado al servidor WebSocket",
            timestamp: new Date().toISOString(),
        });
        // Manejar mensajes del cliente
        ws.on("message", (data) => {
            try {
                const message = JSON.parse(data.toString());
                console.log("📨 Mensaje recibido:", message);
                // Responder con pong si es ping
                if (message.type === "ping") {
                    this.sendToClient(ws, {
                        type: "pong",
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            catch (error) {
                console.error("❌ Error procesando mensaje:", error);
            }
        });
        // Manejar desconexión
        ws.on("close", () => {
            console.log("🔌 Conexión WebSocket cerrada");
            this.connections.delete(ws);
        });
        // Manejar errores
        ws.on("error", (error) => {
            console.error("❌ Error WebSocket:", error);
            this.connections.delete(ws);
        });
    }
    /**
     * Enviar mensaje a un cliente específico
     */
    sendToClient(ws, data) {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            try {
                ws.send(JSON.stringify(data));
                return true;
            }
            catch (error) {
                console.error("❌ Error enviando mensaje:", error);
                return false;
            }
        }
        return false;
    }
    /**
     * Broadcast a todas las conexiones
     */
    broadcast(data) {
        let sentCount = 0;
        this.connections.forEach((ws) => {
            if (this.sendToClient(ws, data)) {
                sentCount++;
            }
        });
        console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
        return sentCount;
    }
    /**
     * Enviar notificación (simplificado)
     */
    sendNotificationToUser(userId, notification) {
        return (this.broadcast({
            type: "notification",
            data: notification,
            timestamp: new Date().toISOString(),
        }) > 0);
    }
    /**
     * Obtener estadísticas
     */
    getStats() {
        return {
            totalConnections: this.connections.size,
            isActive: this.wss !== null,
        };
    }
    /**
     * Cerrar servidor WebSocket
     */
    close() {
        if (this.wss) {
            this.wss.close();
            this.wss = null;
        }
        this.connections.clear();
        console.log("🔌 Servidor WebSocket cerrado");
    }
}
// Exportar instancia singleton
exports.websocketService = new WebSocketService();
exports.default = exports.websocketService;
