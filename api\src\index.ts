import pushSubscriptionPermissions from "./bootstrap/push-subscription-permissions";
import sseService from "./bootstrap/sse";
import "./types/sse";
import setupEmailTemplates from "./scripts/setup-email-templates";

declare global {
  namespace NodeJS {
    interface Global {
      sseConnections: Map<string, Set<Function>>;
    }
  }
}

// Inicializar el mapa de conexiones global con manejo de errores
try {
  if (!global.sseConnections) {
    global.sseConnections = new Map();
  }
} catch (error) {
  console.error("Error initializing SSE connections map:", error);
  throw new Error("Failed to initialize SSE connections");
}

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register({ strapi }) {
    // Registrar el servicio SSE
    strapi.sse = sseService.initialize();
  },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }) {
    // Configurar permisos para push-subscription
    await pushSubscriptionPermissions({ strapi });

    // Configurar plantillas de correo
    await setupEmailTemplates({ strapi });

    // Configurar middleware personalizado para SSE
    strapi.server.use(async (ctx, next) => {
      // Solo procesar rutas SSE específicas
      if (ctx.path.startsWith("/api/notifications/sse/")) {
        console.log(`Interceptando solicitud SSE: ${ctx.path}`);
        try {
          // Extraer el ID de usuario de la URL
          const userId = ctx.path.split("/").pop();

          if (!userId) {
            ctx.status = 400;
            ctx.body = { error: "Se requiere el ID del usuario" };
            return;
          }

          // Extraer el token de la URL
          const token = ctx.query.token;
          if (!token) {
            ctx.status = 401;
            ctx.body = { error: "Token no proporcionado" };
            return;
          }

          // Verificar el token
          try {
            const decoded = await strapi.plugins[
              "users-permissions"
            ].services.jwt.verify(token);
            if (decoded.id !== parseInt(userId)) {
              ctx.status = 403;
              ctx.body = { error: "Token inválido para este usuario" };
              return;
            }
          } catch (error) {
            console.error("Error al verificar token SSE:", error);
            ctx.status = 401;
            ctx.body = { error: "Token inválido" };
            return;
          }

          // Verificar que el usuario existe
          let userExists;
          try {
            userExists = await strapi.entityService.findOne(
              "plugin::users-permissions.user",
              parseInt(userId),
              {
                populate: ["role"],
              }
            );
          } catch (error) {
            console.error(`Error al buscar usuario ${userId}:`, error);
          }

          if (!userExists) {
            console.error(`SSE: Usuario con ID ${userId} no encontrado`);
            ctx.status = 404;
            ctx.body = { error: "Usuario no encontrado" };
            return;
          }

          // Configurar la respuesta para SSE
          ctx.set({
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
            "X-Accel-Buffering": "no",
          });

          // Mantener la conexión abierta
          ctx.req.socket.setTimeout(0);
          ctx.req.socket.setNoDelay(true);
          ctx.req.socket.setKeepAlive(true);

          // Función para enviar eventos al cliente
          const send = (data: string) => {
            try {
              if (ctx.res.writable) {
                ctx.res.write(`data: ${data}\n\n`);
              }
            } catch (error) {
              console.error(
                `Error al enviar datos SSE a usuario ${userId}:`,
                error
              );
              // Limpiar la conexión si hay un error
              if (global.sseConnections.has(userId)) {
                const connections = global.sseConnections.get(userId);
                connections?.delete(send);
                if (connections?.size === 0) {
                  global.sseConnections.delete(userId);
                }
              }
            }
          };

          // Enviar un evento inicial para confirmar la conexión
          send(
            JSON.stringify({
              type: "connection",
              message: "Conexión SSE establecida",
              userId: userId,
            })
          );

          // Registrar la conexión
          if (!global.sseConnections.has(userId)) {
            global.sseConnections.set(userId, new Set());
          }
          global.sseConnections.get(userId)?.add(send);

          // Limpiar la conexión cuando se cierre
          ctx.req.on("close", () => {
            const connections = global.sseConnections.get(userId);
            if (connections) {
              connections.delete(send);
              if (connections.size === 0) {
                global.sseConnections.delete(userId);
              }
            }
          });

          // Mantener la conexión abierta
          await new Promise((resolve) => ctx.req.on("close", resolve));
        } catch (error) {
          console.error("Error en el middleware SSE:", error);
          ctx.status = 500;
          ctx.body = { error: "Error en el servidor de eventos" };
        }
      } else {
        // No es una ruta SSE, continuar con el siguiente middleware
        await next();
      }
    });
  },
};
