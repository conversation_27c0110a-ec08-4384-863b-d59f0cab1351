import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/sv-lzyY1dsz.mjs
var Analytics = "Analytics";
var Documentation = "Dokumentation";
var Email = "E-post";
var Password = "Lösenord";
var Provider = "Tjänst";
var ResetPasswordToken = "Återställ lösenord";
var Role = "Roll";
var light = "Ljust";
var dark = "Mörkt";
var Username = "Användarnamn";
var Users = "Användare";
var anErrorOccurred = "Hoppsan! Något gick fel. Försök igen.";
var clearLabel = "Rensa";
var or = "ELLER";
var skipToContent = "Hoppa till innehållet";
var submit = "Skicka";
var sv = {
  Analytics,
  "Auth.components.Oops.text": "Ditt konto har blivit avstängt.",
  "Auth.components.Oops.text.admin": "Kontakta din administratör om du tror detta är av misstag.",
  "Auth.components.Oops.title": "Oj då...",
  "Auth.form.active.label": "Aktiv",
  "Auth.form.button.forgot-password": "Skicka e-post",
  "Auth.form.button.go-home": "GÅ TILLBAKA HEM",
  "Auth.form.button.login": "Logga in",
  "Auth.form.button.login.providers.error": "Vi kan inte logga in dig med den valda tjänsten.",
  "Auth.form.button.login.strapi": "Logga in via Strapi",
  "Auth.form.button.password-recovery": "Återställning av lösenord",
  "Auth.form.button.register": "Låt oss börja",
  "Auth.form.confirmPassword.label": "Bekräfta lösenord",
  "Auth.form.currentPassword.label": "Nuvarande lösenord",
  "Auth.form.email.label": "E-post",
  "Auth.form.email.placeholder": "t.ex. <EMAIL>",
  "Auth.form.error.blocked": "Ditt konto har blockerats en administratör.",
  "Auth.form.error.code.provide": "Felaktig kod angiven.",
  "Auth.form.error.confirmed": "E-postadressen för ditt konto är inte verifierad.",
  "Auth.form.error.email.invalid": "Denna e-postadress är ogiltig.",
  "Auth.form.error.email.provide": "Ange användarnamn eller e-postadress.",
  "Auth.form.error.email.taken": "E-postadressen är upptagen.",
  "Auth.form.error.invalid": "Användarnamn/e-postadress eller lösenordet är felaktigt.",
  "Auth.form.error.params.provide": "Felaktiga parametrar angivna.",
  "Auth.form.error.password.format": "Ditt lösenord får inte innehålla symbolen `$` mer än tre gånger.",
  "Auth.form.error.password.local": "Denna användare har inte ett lokalt lösenord, logga in via den tjänsten som användes när kontot skapades.",
  "Auth.form.error.password.matching": "Lösenorden stämmer inte överens.",
  "Auth.form.error.password.provide": "Ange ditt lösenord.",
  "Auth.form.error.ratelimit": "För många försök, försök igen om en minut.",
  "Auth.form.error.user.not-exist": "Det här e-postadress finns inte.",
  "Auth.form.error.username.taken": "Användarnamnet är redan upptaget.",
  "Auth.form.firstname.label": "Förnamn",
  "Auth.form.firstname.placeholder": "t.ex. Kai",
  "Auth.form.forgot-password.email.label": "Ange din e-postadress",
  "Auth.form.forgot-password.email.label.success": "E-post har skickats till",
  "Auth.form.lastname.label": "Efternamn",
  "Auth.form.lastname.placeholder": "t.ex. Doe",
  "Auth.form.password.hide-password": "Dölj lösenord",
  "Auth.form.password.hint": "Måste bestå av minst 8 tecken, en stor bokstav, en liten bokstav och en siffra",
  "Auth.form.password.show-password": "Visa lösenord",
  "Auth.form.register.news.label": "Håll mig uppdaterad om nya funktioner och förbättringar (genom att göra detta godkänner du {terms} och {policy}).",
  "Auth.form.register.subtitle": "Inloggningsuppgifter används bara för att logga in i Strapi. All data kommer att sparas i din databas.",
  "Auth.form.rememberMe.label": "Kom ihåg mig",
  "Auth.form.username.label": "Användarnamn",
  "Auth.form.username.placeholder": "t.ex. Kai_Doe",
  "Auth.form.welcome.subtitle": "Logga in till ett Strapi-konto",
  "Auth.form.welcome.title": "Välkommen till Strapi!",
  "Auth.link.forgot-password": "Glömt ditt lösenord?",
  "Auth.link.ready": "Redo att logga in?",
  "Auth.link.signin": "Logga in",
  "Auth.link.signin.account": "Har du redan ett konto?",
  "Auth.login.sso.divider": "Eller logga in med",
  "Auth.login.sso.loading": "Laddar inloggningstjänster...",
  "Auth.login.sso.subtitle": "Logga in med SSO",
  "Auth.privacy-policy-agreement.policy": "integritetspolicyn",
  "Auth.privacy-policy-agreement.terms": "villkoren",
  "Auth.reset-password.title": "Återställ lösenord",
  "Content Manager": "Innehållshantering",
  "Content Type Builder": "Innehållstypsbyggare",
  Documentation,
  Email,
  "Files Upload": "Filuppladdning",
  "HomePage.head.title": "Hemsida",
  "HomePage.roadmap": "Se vår roadmap",
  "HomePage.welcome.congrats": "Grattis!",
  "HomePage.welcome.congrats.content": "Du är inloggad som administratör. För att upptäcka de kraftfulla funktionerna från Strapi,",
  "HomePage.welcome.congrats.content.bold": "vi rekommenderar dig att skapa din första innehållstyp.",
  "Media Library": "Mediebibliotek",
  "New entry": "Nytt inlägg",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Roller & Behörigheter",
  "Roles.ListPage.notification.delete-all-not-allowed": "Några roller kunde inte tas bort eftersom de är kopplade till minst en användare",
  "Roles.ListPage.notification.delete-not-allowed": "En roll kan inte tas bort om den är kopplad till någon användare",
  "Roles.RoleRow.select-all": "Välj {name} för massåtgärder",
  "Roles.RoleRow.user-count": "användare",
  "Roles.components.List.empty.withSearch": "Det finns inga roller som matchar sökningen ({search})...",
  "Settings.PageTitle": "Inställningar - {name}",
  "Settings.apiTokens.addFirstToken": "Lägg till din första API-token",
  "Settings.apiTokens.addNewToken": "Lägg till en ny API-token",
  "Settings.tokens.copy.editMessage": "Av säkerhetsskäl kan du bara se din token en gång.",
  "Settings.tokens.copy.editTitle": "Denna token är inte längre tillgänglig.",
  "Settings.tokens.copy.lastWarning": "Kopiera denna token nu, den kommer inte visas igen!",
  "Settings.apiTokens.create": "Skapa en ny API-token",
  "Settings.apiTokens.description": "Lista över genererade tokens för att använda API:t",
  "Settings.apiTokens.emptyStateLayout": "Det finns inget innehåll ännu...",
  "Settings.apiTokens.ListView.headers.name": "Name",
  "Settings.apiTokens.ListView.headers.description": "Description",
  "Settings.apiTokens.ListView.headers.type": "Token type",
  "Settings.apiTokens.ListView.headers.createdAt": "Created at",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "Last used",
  "Settings.tokens.notification.copied": "Token har kopierats.",
  "Settings.apiTokens.title": "API-tokens",
  "Settings.tokens.types.full-access": "Full åtkomst",
  "Settings.tokens.types.read-only": "Skrivskyddad",
  "Settings.tokens.duration.7-days": "7 dagar",
  "Settings.tokens.duration.30-days": "30 dagar",
  "Settings.tokens.duration.90-days": "90 dagar",
  "Settings.tokens.duration.unlimited": "Evig",
  "Settings.tokens.form.duration": "Giltighetstid för token",
  "Settings.tokens.form.type": "Token-typ",
  "Settings.tokens.duration.expiration-date": "Utgångsdatum",
  "Settings.apiTokens.createPage.permissions.title": "Behörigheter",
  "Settings.apiTokens.createPage.permissions.description": "Endast åtgärder som är kopplade till en rutt listas nedan.",
  "Settings.tokens.RegenerateDialog.title": "Återställ token",
  "Settings.tokens.popUpWarning.message": "Är du säker på att du vill återställa denna token?",
  "Settings.tokens.Button.cancel": "Avbryt",
  "Settings.tokens.Button.regenerate": "Återställ",
  "Settings.application.description": "Adminpanelens globala information",
  "Settings.application.edition-title": "aktiv version",
  "Settings.application.get-help": "Få hjälp",
  "Settings.application.link-pricing": "Se alla prisplaner",
  "Settings.application.link-upgrade": "Uppgradera din adminpanel",
  "Settings.application.node-version": "node-version",
  "Settings.application.strapi-version": "strapi-version",
  "Settings.application.strapiVersion": "strapi-version",
  "Settings.application.title": "Översikt",
  "Settings.application.customization": "Anpassning",
  "Settings.application.customization.carousel.title": "Logga",
  "Settings.application.customization.carousel.change-action": "Ändra logga",
  "Settings.application.customization.carousel.reset-action": "Återställ logga",
  "Settings.application.customization.carousel-slide.label": "Logotyphjul",
  "Settings.application.customization.carousel-hint": "Ändra adminpanelens logotyp (Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB)",
  "Settings.application.customization.modal.cancel": "Avbryt",
  "Settings.application.customization.modal.upload": "Ladda upp logga",
  "Settings.application.customization.modal.tab.label": "Hur vill du ladda dina mediefiler?",
  "Settings.application.customization.modal.upload.from-computer": "Från datorn",
  "Settings.application.customization.modal.upload.file-validation": "Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB",
  "Settings.application.customization.modal.upload.error-format": "Fel format uppladdat (tillåtna format: jpeg, jpg, png, svg).",
  "Settings.application.customization.modal.upload.error-size": "Den uppladdade filen är för stor (Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB)",
  "Settings.application.customization.modal.upload.error-network": "Nätverksfel",
  "Settings.application.customization.modal.upload.cta.browse": "Bläddra bland filer",
  "Settings.application.customization.modal.upload.drag-drop": "Dra och släpp här eller",
  "Settings.application.customization.modal.upload.from-url": "Från URL",
  "Settings.application.customization.modal.upload.from-url.input-label": "URL",
  "Settings.application.customization.modal.upload.next": "Nästa",
  "Settings.application.customization.modal.pending": "Ändra logga",
  "Settings.application.customization.modal.pending.choose-another": "Välj ny logga",
  "Settings.application.customization.modal.pending.title": "Loggan är redo att laddas upp",
  "Settings.application.customization.modal.pending.subtitle": "Ändra den valda loggan innan du laddar upp den",
  "Settings.application.customization.modal.pending.upload": "Ladda upp ny logga",
  "Settings.application.customization.modal.pending.card-badge": "bild",
  "Settings.error": "Fel",
  "Settings.global": "Globala inställningar",
  "Settings.permissions": "Administrationspanel",
  "Settings.permissions.category": "Behörighetsinställningar för {category}",
  "Settings.permissions.category.plugins": "Behörighetsinställningar för {category}-plugin-programmet",
  "Settings.permissions.conditions.anytime": "När som helst",
  "Settings.permissions.conditions.apply": "Använd",
  "Settings.permissions.conditions.can": "Kan",
  "Settings.permissions.conditions.conditions": "Definiera behörigheter",
  "Settings.permissions.conditions.links": "Länkar",
  "Settings.permissions.conditions.no-actions": "Du måste först välja åtgärder (skapa, läsa, uppdatera, ...) innan du definierar behörigheter för dem.",
  "Settings.permissions.conditions.none-selected": "När som helst",
  "Settings.permissions.conditions.or": "ELLER",
  "Settings.permissions.conditions.when": "När",
  "Settings.permissions.select-all-by-permission": "Välj alla behörighet för {label}",
  "Settings.permissions.select-by-permission": "Välj behörighet för {label}",
  "Settings.permissions.users.create": "Bjud in användare",
  "Settings.permissions.users.email": "Email",
  "Settings.permissions.users.firstname": "Firstname",
  "Settings.permissions.users.lastname": "Lastname",
  "Settings.permissions.users.user-status": "User status",
  "Settings.permissions.users.roles": "Roller",
  "Settings.permissions.users.username": "Användarnamn",
  "Settings.permissions.users.active": "Aktiv",
  "Settings.permissions.users.inactive": "Inaktiv",
  "Settings.permissions.users.form.sso": "Anslut med SSO",
  "Settings.permissions.users.form.sso.description": "När aktivt (PÅ) kan användare logga in med SSO",
  "Settings.permissions.users.listview.header.subtitle": "Alla användare som har tillgång till Strapi-adminpanel",
  "Settings.permissions.users.tabs.label": "Behörighetsfliken",
  "Settings.permissions.users.strapi-super-admin": "Super Admin",
  "Settings.permissions.users.strapi-editor": "Redigerare",
  "Settings.permissions.users.strapi-author": "Författare",
  "Settings.profile.form.notify.data.loaded": "Din profildata har laddats",
  "Settings.profile.form.section.experience.clear.select": "Återställ det valda gränssnittsspråket",
  "Settings.profile.form.section.experience.here": "här",
  "Settings.profile.form.section.experience.interfaceLanguage": "Gränssnittsspråk",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "Detta kommer bara att visa ditt egna gränssnitt på det valda språket.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "Ändringar av inställningar kommer bara att gälla dig. Mer information finns {here}.",
  "Settings.profile.form.section.experience.mode.label": "Gränssnittsläge",
  "Settings.profile.form.section.experience.mode.hint": "Visar ditt gränssnitt i det valda läget.",
  "Settings.profile.form.section.experience.mode.option-label": "{name}-läge",
  light,
  dark,
  "Settings.profile.form.section.experience.title": "Upplevelse",
  "Settings.profile.form.section.head.title": "Användarprofil",
  "Settings.profile.form.section.profile.page.title": "Profilsida",
  "Settings.roles.create.description": "Definiera de rättigheter som ges till rollen",
  "Settings.roles.create.title": "Skapa en roll",
  "Settings.roles.created": "Roll skapad",
  "Settings.roles.edit.title": "Redigera en roll",
  "Settings.roles.form.button.users-with-role": "användare med denna rollen",
  "Settings.roles.form.created": "Skapad",
  "Settings.roles.form.description": "Namn och beskrivning av rollen",
  "Settings.roles.form.permission.property-label": "{label}-behörigheter",
  "Settings.roles.form.permissions.attributesPermissions": "Fältbehörigheter",
  "Settings.roles.form.permissions.create": "Skapa",
  "Settings.roles.form.permissions.delete": "Ta bort",
  "Settings.roles.form.permissions.publish": "Publicera",
  "Settings.roles.form.permissions.read": "Läsa",
  "Settings.roles.form.permissions.update": "Uppdatera",
  "Settings.roles.list.button.add": "Lägg till ny roll",
  "Settings.roles.list.description": "Lista över roller",
  "Settings.roles.title.singular": "roll",
  "Settings.sso.description": "Konfigurera inställningarna för Single Sign-On.",
  "Settings.sso.form.defaultRole.description": "Den kommer att koppla den nya inloggade användaren till den valda rollen",
  "Settings.sso.form.defaultRole.description-not-allowed": "Du måste ha behörighet att läsa administratörsrollerna",
  "Settings.sso.form.defaultRole.label": "Standardroll",
  "Settings.sso.form.registration.description": "Skapa ny användare med SSO-inloggning om kontot saknas",
  "Settings.sso.form.registration.label": "Auto-registrering",
  "Settings.sso.title": "Single Sign-On",
  "Settings.webhooks.create": "Skapa en webhook",
  "Settings.webhooks.create.header": "Skapa ny header",
  "Settings.webhooks.created": "Webhook skapad",
  "Settings.webhooks.event.publish-tooltip": "Den här händelsen finns endast för innehåll med funktionen Utkast/Publicera aktiverat",
  "Settings.webhooks.events.create": "Skapa",
  "Settings.webhooks.events.update": "Uppdatera",
  "Settings.webhooks.form.events": "Händelser",
  "Settings.webhooks.form.headers": "Headers",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.headers.remove": "Ta bort header-rad {number}",
  "Settings.webhooks.key": "Nyckel",
  "Settings.webhooks.list.button.add": "Skapa ny webhook",
  "Settings.webhooks.list.description": "Få notiser för POST-ändringar",
  "Settings.webhooks.list.empty.description": "Inga webhooks hittades",
  "Settings.webhooks.list.empty.link": "Se vår dokumentation",
  "Settings.webhooks.list.empty.title": "Det finns inga webhooks än",
  "Settings.webhooks.list.th.actions": "åtgärder",
  "Settings.webhooks.list.th.status": "status",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} valda",
  "Settings.webhooks.trigger": "Utlösare",
  "Settings.webhooks.trigger.cancel": "Avbryt utlösaren",
  "Settings.webhooks.trigger.pending": "Pågår…",
  "Settings.webhooks.trigger.save": "Spara innan du utlöser",
  "Settings.webhooks.trigger.success": "Klart!",
  "Settings.webhooks.trigger.success.label": "Utlösare lyckades",
  "Settings.webhooks.trigger.test": "Testutlösare",
  "Settings.webhooks.trigger.title": "Spara före utlösning",
  "Settings.webhooks.value": "Värde",
  "Usecase.back-end": "Back-end utvecklare",
  "Usecase.button.skip": "Hoppa över denna frågan",
  "Usecase.content-creator": "Innehållsskapare",
  "Usecase.front-end": "Front-end utvecklare",
  "Usecase.full-stack": "Full-stack utvecklare",
  "Usecase.input.work-type": "Vad jobbar du som?",
  "Usecase.notification.success.project-created": "Projektet har skapats",
  "Usecase.other": "Annat",
  "Usecase.title": "Berätta lite mer om dig själv",
  Username,
  Users,
  "Users & Permissions": "Användare och behörigheter",
  "Users.components.List.empty": "Det finns inga användare...",
  "Users.components.List.empty.withFilters": "Det finns inga användare som matchar filtreringen...",
  "Users.components.List.empty.withSearch": "Det finns inga användare som matchar sökningen ({search})...",
  "admin.pages.MarketPlacePage.head": "Plugin-marknad",
  "admin.pages.MarketPlacePage.offline.title": "Du är offline",
  "admin.pages.MarketPlacePage.offline.subtitle": "Du måste vara ansluten till Internet för att komma åt Strapi-marknaden.",
  "admin.pages.MarketPlacePage.plugins": "Plugins",
  "admin.pages.MarketPlacePage.plugin.copy": "Kopiera installationskommando",
  "admin.pages.MarketPlacePage.plugin.copy.success": "Installationskommandot redo att klistras in i din terminal",
  "admin.pages.MarketPlacePage.plugin.info": "Läs mer",
  "admin.pages.MarketPlacePage.plugin.info.label": "Läs mer om {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "Läs mer",
  "admin.pages.MarketPlacePage.plugin.installed": "Installerat",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Skapat av Strapi",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Plugin verifierat av Strapi",
  "admin.pages.MarketPlacePage.plugin.version": 'Uppdatera din strapi-version: "{strapiAppVersion}" till: "{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": 'Det går inte att verifiera kompatibiliteten med din Strapi-version: "{strapiAppVersion}"',
  "admin.pages.MarketPlacePage.plugin.githubStars": "Detta plugin stjärnmarkerades {starsCount} på GitHub",
  "admin.pages.MarketPlacePage.plugin.downloads": "Detta plugin har {downloadsCount} nedladdningar per vecka",
  "admin.pages.MarketPlacePage.providers": "Tjänster",
  "admin.pages.MarketPlacePage.provider.githubStars": "Den här leverantören stjärnmärktes med {starsCount} på GitHub",
  "admin.pages.MarketPlacePage.provider.downloads": "Denna tjänsten har {downloadsCount} nedladdningar per vecka",
  "admin.pages.MarketPlacePage.search.clear": "Rensa sökningen",
  "admin.pages.MarketPlacePage.search.empty": 'Inget resultat för "{target}"',
  "admin.pages.MarketPlacePage.search.placeholder": "Sök",
  "admin.pages.MarketPlacePage.submit.plugin.link": "Skicka plugin",
  "admin.pages.MarketPlacePage.submit.provider.link": "Skicka tjänst",
  "admin.pages.MarketPlacePage.subtitle": "Få ut mer av Strapi",
  "admin.pages.MarketPlacePage.tab-group.label": "Plugins och leverantörer för Strapi",
  "admin.pages.MarketPlacePage.missingPlugin.title": "Saknar du ett plugin?",
  "admin.pages.MarketPlacePage.missingPlugin.description": "Berätta vilket plugin du letar efter så kan vi meddela våra plugin-utvecklare om de letar efter inspiration!",
  "admin.pages.MarketPlacePage.sort.alphabetical": "Alfabetisk ordning",
  "admin.pages.MarketPlacePage.sort.newest": "Nyast",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "Sortera i alfabetisk ordning",
  "admin.pages.MarketPlacePage.sort.newest.selected": "Sortera efter ålder",
  "admin.pages.MarketPlacePage.filters.collections": "Samlingar",
  "admin.pages.MarketPlacePage.filters.collectionsSelected": "{count, plural, =0 {Inga samlingar valda} one {# samling vald} other {# samlingar valda}}",
  "admin.pages.MarketPlacePage.filters.categories": "Kategorier",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {Inga kategorier valda} one {# kategori vald} other {# kategorier valda}}",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Kopiera till urklipp",
  "app.component.search.label": "Söka efter {target}",
  "app.component.table.duplicate": "Duplicera {target}",
  "app.component.table.edit": "Redigera {target}",
  "app.component.table.select.one-entry": "Välj {target}",
  "app.components.BlockLink.blog": "Blogg",
  "app.components.BlockLink.blog.content": "Läs de senaste nyheterna om Strapi och Strapi-ekosystemet.",
  "app.components.BlockLink.code": "Kodexempel",
  "app.components.BlockLink.code.content": "Lär dig genom att testa riktiga projekt som utvecklade av Strapi-communityn.",
  "app.components.BlockLink.documentation.content": "Upptäck de viktigaste koncepten, guiderna och instruktionerna.",
  "app.components.BlockLink.tutorial": "Guider",
  "app.components.BlockLink.tutorial.content": "Följ steg-för-steg-instruktionerna för att använda och anpassa Strapi.",
  "app.components.Button.cancel": "Avbryt",
  "app.components.Button.confirm": "Bekräfta",
  "app.components.Button.reset": "Återställ",
  "app.components.ComingSoonPage.comingSoon": "Kommer snart",
  "app.components.ConfirmDialog.title": "Bekräftelse",
  "app.components.DownloadInfo.download": "Nedladdning pågår...",
  "app.components.DownloadInfo.text": "Detta kan ta någon minut. Tack för ditt tålamod.",
  "app.components.EmptyAttributes.title": "Det finns inga fält än",
  "app.components.EmptyStateLayout.content-document": "Inget innehåll hittades",
  "app.components.EmptyStateLayout.content-permissions": "Du har inte behörighet att komma åt detta innehållet",
  "app.components.GuidedTour.CM.create.content": "<p>Skapa och hantera allt innehåll här i innehållshanteraren.</p><p>För att ta exemplet bloggwebbplats vidare kan man skriva en artikel, spara och publicera den som de vill.</p><p>💡 Tips - Glöm inte att trycka på publicera på det innehåll du skapar.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ Skapa innehåll",
  "app.components.GuidedTour.CM.success.content": "<p>Perfekt, ett sista steg kvar!</p><b>🚀  Se ditt innehåll live</b>",
  "app.components.GuidedTour.CM.success.cta.title": "Testa API:t",
  "app.components.GuidedTour.CM.success.title": "Steg 2: Avklarat ✅",
  "app.components.GuidedTour.CTB.create.content": '<p>Samlingstyper hjälper dig att hantera flera poster, Engångstyper är lämpliga för att hantera bara en post.</p> <p>För en bloggwebbplats skulle "Artiklar" vara en samlingstyp medan en "Startsida" skulle vara en engångstyp.</p>',
  "app.components.GuidedTour.CTB.create.cta.title": "Skapa en samlingstyp",
  "app.components.GuidedTour.CTB.create.title": "🧠 Skapa din första samlingstyp",
  "app.components.GuidedTour.CTB.success.content": "<p>Bra jobbat!</p><b>⚡️ Vad skulle du vilja dela med dig till världen?</b>",
  "app.components.GuidedTour.CTB.success.title": "Steg 1: Avklarat ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>Skapa en autentiseringstoken här och hämta innehållet du just skapat.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "Skapa en API-token",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 Se innehåll live",
  "app.components.GuidedTour.apiTokens.success.content": "<p>Se innehåll live genom att göra en HTTP-förfrågan:</p><ul><li><p>Till denna URLen: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Med headern: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>För fler sätt att interagera med innehållet, se <documentationLink>dokumentationen</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "Gå tillbaka till startsidan",
  "app.components.GuidedTour.apiTokens.success.title": "Steg 3: Avklarat ✅",
  "app.components.GuidedTour.create-content": "Skapa innehåll",
  "app.components.GuidedTour.home.CM.title": "⚡️ Vad skulle du vilja dela med dig till världen?",
  "app.components.GuidedTour.home.CTB.cta.title": "Gå till innehållstypsskaparen",
  "app.components.GuidedTour.home.CTB.title": "🧠 Bygg en innehållsstruktur",
  "app.components.GuidedTour.home.apiTokens.cta.title": "Testa API:t",
  "app.components.GuidedTour.skip": "Hoppa över guiden",
  "app.components.GuidedTour.title": "3 steg för att komma igång",
  "app.components.HomePage.button.blog": "SE MER PÅ BLOGGEN",
  "app.components.HomePage.community": "Hitta gemenskapen på webben",
  "app.components.HomePage.community.content": "Diskutera med teammedlemmar, bidragsgivare och utvecklare på olika kanaler.",
  "app.components.HomePage.create": "Skapa din första innehållstyp",
  "app.components.HomePage.roadmap": "Se vår roadmap",
  "app.components.HomePage.welcome": "Välkommen ombord!",
  "app.components.HomePage.welcome.again": "Välkommen ",
  "app.components.HomePage.welcomeBlock.content": "Vi är glada över att ha dig som en del av communityn. Vi letar ständigt efter feedback så skicka oss DM på ",
  "app.components.HomePage.welcomeBlock.content.again": "Vi hoppas att kommer framåt med ditt projekt... Läs gärna de senaste nyheterna om Strapi. Vi gör vårt bästa för att förbättra produkten baserat på din feedback.",
  "app.components.HomePage.welcomeBlock.content.issues": "problem.",
  "app.components.HomePage.welcomeBlock.content.raise": " eller skapa ",
  "app.components.ImgPreview.hint": "Dra och släpp din fil i det här området eller {bläddra} efter en fil att ladda upp",
  "app.components.ImgPreview.hint.browse": "bläddra",
  "app.components.InputFile.newFile": "Lägg till ny fil",
  "app.components.InputFileDetails.open": "Öppna i en ny flik",
  "app.components.InputFileDetails.originalName": "Originalnamn:",
  "app.components.InputFileDetails.remove": "Ta bort den här filen",
  "app.components.InputFileDetails.size": "Storlek:",
  "app.components.InstallPluginPage.Download.description": "Det kan ta några sekunder att ladda ner och installera pluginet",
  "app.components.InstallPluginPage.Download.title": "Laddar ner...",
  "app.components.InstallPluginPage.description": "Utöka din app enkelt.",
  "app.components.LeftMenu.collapse": "Dölj navigeringsfältet",
  "app.components.LeftMenu.expand": "Expandera navigeringsfältet",
  "app.components.LeftMenu.general": "Allmänt",
  "app.components.LeftMenu.logout": "Logga ut",
  "app.components.LeftMenu.logo.alt": "Applikationslogga",
  "app.components.LeftMenu.plugins": "Plugins",
  "app.components.LeftMenu.navbrand.title": "Strapi Dashboard",
  "app.components.LeftMenu.navbrand.workplace": "Arbetsplats",
  "app.components.LeftMenuFooter.help": "Hjälp",
  "app.components.LeftMenuFooter.poweredBy": "Drivs av ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Samlingstyper",
  "app.components.LeftMenuLinkContainer.configuration": "Konfigurationer",
  "app.components.LeftMenuLinkContainer.general": "Allmänt",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Inga plugins installerade än",
  "app.components.LeftMenuLinkContainer.plugins": "Plugins",
  "app.components.LeftMenuLinkContainer.singleTypes": "Engångstyper",
  "app.components.ListPluginsPage.deletePlugin.description": "Det kan ta några sekunder att avinstallera plugin-programmet.",
  "app.components.ListPluginsPage.deletePlugin.title": "Avinstallerar",
  "app.components.ListPluginsPage.description": "Lista över de installerade plugin-programmen i projektet.",
  "app.components.ListPluginsPage.head.title": "Lista plugins",
  "app.components.Logout.logout": "Logga ut",
  "app.components.Logout.profile": "Profil",
  "app.components.MarketplaceBanner": "Upptäck plugins som skapats av Strapi-communityn, och många fler fantastiska saker för att rivstarta ditt projekt, på Strapi Awesome.",
  "app.components.MarketplaceBanner.image.alt": "en strapi-raket-logga",
  "app.components.MarketplaceBanner.link": "Besök nu",
  "app.components.NotFoundPage.back": "Tillbaka till startsidan",
  "app.components.NotFoundPage.description": "Hittades inte",
  "app.components.Official": "Officiell",
  "app.components.Onboarding.help.button": "Hjälpknapp",
  "app.components.Onboarding.label.completed": "% klar",
  "app.components.Onboarding.title": "Kom igång-videos",
  "app.components.PluginCard.Button.label.download": "Ladda ner",
  "app.components.PluginCard.Button.label.install": "Redan installerad",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "AutoReload-funktionen måste vara inaktiverad. Starta din app med `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Jag förstår!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Av säkerhetsskäl kan ett plugin bara laddas ner i en utvecklingsmiljö.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Nedladdning är inte möjlig",
  "app.components.PluginCard.compatible": "Kompatibel med din app",
  "app.components.PluginCard.compatibleCommunity": "Kompatibel med communityn",
  "app.components.PluginCard.more-details": "Mer detaljer",
  "app.components.ToggleCheckbox.off-label": "Av",
  "app.components.ToggleCheckbox.on-label": "På",
  "app.components.Users.MagicLink.connect": "Kopiera och dela denna länk för att ge åtkomst till denna användare",
  "app.components.Users.MagicLink.connect.sso": "Skicka denna länk till användaren, den första inloggningen kan göras via en SSO-tjänst",
  "app.components.Users.ModalCreateBody.block-title.details": "Användarinformation",
  "app.components.Users.ModalCreateBody.block-title.roles": "Användarens roller",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "En användare kan ha en eller flera roller",
  "app.components.Users.SortPicker.button-label": "Sortera efter",
  "app.components.Users.SortPicker.sortby.email_asc": "E-post (A till Ö)",
  "app.components.Users.SortPicker.sortby.email_desc": "E-post (Ö till A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Förnamn (A till Ö)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Förnamn (Ö till A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Efternamn (A till Ö)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Efternamn (Ö till A)",
  "app.components.Users.SortPicker.sortby.username_asc": "Användarnamn (A till Ö)",
  "app.components.Users.SortPicker.sortby.username_desc": "Användarnamn (Ö till A)",
  "app.components.listPlugins.button": "Lägg till nytt plugin",
  "app.components.listPlugins.title.none": "Inga plugins installerade",
  "app.components.listPluginsPage.deletePlugin.error": "Ett fel inträffade vid avinstallation av pluginet",
  "app.containers.App.notification.error.init": "Ett fel uppstod när API:t begärdes",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Om du inte får den här länken, kontakta din administratör.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Det kan ta några minuter att få lösenordsåterställningslänken.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email skickat",
  "app.containers.Users.EditPage.form.active.label": "Aktiv",
  "app.containers.Users.EditPage.header.label": "Redigera {name}",
  "app.containers.Users.EditPage.header.label-loading": "Redigera användare",
  "app.containers.Users.EditPage.roles-bloc-title": "Tilldelade roller",
  "app.containers.Users.ModalForm.footer.button-success": "Bjud in användare",
  "app.links.configure-view": "Konfigurera vyn",
  "app.page.not.found": "Hoppsan! Vi kan inte hitta sidan du letar efter...",
  "app.static.links.cheatsheet": "CheatSheet",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Lägg till filter",
  "app.utils.close-label": "Stäng",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "Duplicera",
  "app.utils.edit": "Redigera",
  "app.utils.delete": "Delete",
  "app.utils.errors.file-too-big.message": "Filen är för stor",
  "app.utils.filter-value": "Filtervärde",
  "app.utils.filters": "Filter",
  "app.utils.notify.data-loaded": "{target} har laddats",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Publicera",
  "app.utils.select-all": "Välj alla",
  "app.utils.select-field": "Välj fält",
  "app.utils.select-filter": "Välj filter",
  "app.utils.unpublish": "Avpublicera",
  clearLabel,
  "coming.soon": "Det här innehållet är för närvarande under uppbyggnad och kommer tillbaka om några veckor!",
  "component.Input.error.validation.integer": "Värdet måste vara ett heltal",
  "components.AutoReloadBlocker.description": "Kör Strapi med ett av de följande kommandon:",
  "components.AutoReloadBlocker.header": "Reload-funktionen krävs för detta plugin.",
  "components.ErrorBoundary.title": "Någonting gick fel...",
  "components.FilterOptions.FILTER_TYPES.$contains": "innehåller",
  "components.FilterOptions.FILTER_TYPES.$containsi": "innehåller (okänslig för skiftläge)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "slutar med",
  "components.FilterOptions.FILTER_TYPES.$eq": "är",
  "components.FilterOptions.FILTER_TYPES.$eqi": "är (okänslig för skiftläge)",
  "components.FilterOptions.FILTER_TYPES.$gt": "är större än",
  "components.FilterOptions.FILTER_TYPES.$gte": "är större än eller lika med",
  "components.FilterOptions.FILTER_TYPES.$lt": "är lägre än",
  "components.FilterOptions.FILTER_TYPES.$lte": "är lägre än eller lika med",
  "components.FilterOptions.FILTER_TYPES.$ne": "är inte",
  "components.FilterOptions.FILTER_TYPES.$nei": "är inte (okänslig för skiftläge)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "innehåller inte",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "innehåller inte (okänslig för skiftläge)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "är inte null",
  "components.FilterOptions.FILTER_TYPES.$null": "är null",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "börjar med",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "börjar med (okänslig för skiftläge)",
  "components.Input.error.attribute.key.taken": "Detta värde finns redan",
  "components.Input.error.attribute.sameKeyAndName": "Kan inte vara lika",
  "components.Input.error.attribute.taken": "Detta fältnamn finns redan",
  "components.Input.error.contain.lowercase": "Lösenordet måste innehålla minst en liten bokstav",
  "components.Input.error.contain.number": "lösenordet måste innehålla minst en siffra",
  "components.Input.error.contain.uppercase": "Lösenordet måste innehålla minst en stor bokstav",
  "components.Input.error.contentTypeName.taken": "Detta namn finns redan",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Lösenorden stämmer inte överens",
  "components.Input.error.validation.email": "Detta är inte en e-post",
  "components.Input.error.validation.json": "Detta stämmer inte med JSON-formatet",
  "components.Input.error.validation.lowercase": "Värdet får bara bestå av små bokstäver",
  "components.Input.error.validation.max": "Värdet är för högt {max}.",
  "components.Input.error.validation.maxLength": "Värdet är för långt {max}.",
  "components.Input.error.validation.min": "Värdet är för lågt {min}.",
  "components.Input.error.validation.minLength": "Värdet är för kort {min}.",
  "components.Input.error.validation.minSupMax": "Kan inte vara överlägsen",
  "components.Input.error.validation.regex": "Värdet matchar inte regexet.",
  "components.Input.error.validation.required": "Detta värde krävs.",
  "components.Input.error.validation.unique": "Detta värde används redan.",
  "components.InputSelect.option.placeholder": "Välj här",
  "components.ListRow.empty": "Det finns inga data som ska visas.",
  "components.NotAllowedInput.text": "Ingen behörighet att se detta fält",
  "components.OverlayBlocker.description": "Du använder en funktion som behöver servern att starta om. Vänta tills servern är igång.",
  "components.OverlayBlocker.description.serverError": "Servern borde ha startat om, kontrollera dina loggar i terminalen.",
  "components.OverlayBlocker.title": "Väntar på omstart...",
  "components.OverlayBlocker.title.serverError": "Omstarten tar längre tid än väntat",
  "components.PageFooter.select": "poster per sida",
  "components.ProductionBlocker.description": "För säkerhetsskäl måste vi inaktivera detta plugin i andra miljöer.",
  "components.ProductionBlocker.header": "Denna plugin är endast tillgänglig under utveckling.",
  "components.Search.placeholder": "Sök...",
  "components.TableHeader.sort": "Sortera på {label}",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Markdown-läge",
  "components.Wysiwyg.ToggleMode.preview-mode": "Visuellt läge",
  "components.Wysiwyg.collapse": "Fäll ihop",
  "components.Wysiwyg.selectOptions.H1": "Titel H1",
  "components.Wysiwyg.selectOptions.H2": "Titel H2",
  "components.Wysiwyg.selectOptions.H3": "Titel H3",
  "components.Wysiwyg.selectOptions.H4": "Titel H4",
  "components.Wysiwyg.selectOptions.H5": "Titel H5",
  "components.Wysiwyg.selectOptions.H6": "Titel H6",
  "components.Wysiwyg.selectOptions.title": "Lägg till en titel",
  "components.WysiwygBottomControls.charactersIndicators": "Tecken",
  "components.WysiwygBottomControls.fullscreen": "Expandera",
  "components.WysiwygBottomControls.uploadFiles": "Dra och släpp filer, klistra in från urklipp eller {bläddra}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "välj dem",
  "components.pagination.go-to": "Gå till sidan {page}",
  "components.pagination.go-to-next": "Gå till nästa sida",
  "components.pagination.go-to-previous": "Gå till förra sidan",
  "components.pagination.remaining-links": "Och {number} andra länkar",
  "components.popUpWarning.button.cancel": "Nej, avbryt",
  "components.popUpWarning.button.confirm": "Ja, bekräfta",
  "components.popUpWarning.message": "Är du säker på att du vill radera detta?",
  "components.popUpWarning.title": "Var god bekräfta",
  "form.button.continue": "Fortsätt",
  "form.button.done": "Klar",
  "global.search": "Sök",
  "global.actions": "Åtgärder",
  "global.back": "Backa",
  "global.cancel": "Avbryt",
  "global.change-password": "Ändra lösenord",
  "global.content-manager": "Innehållshanterare",
  "global.continue": "Fortsätt",
  "global.delete": "Ta bort",
  "global.delete-target": "Ta bort {target}",
  "global.description": "Beskrivning",
  "global.details": "Detaljer",
  "global.disabled": "Inaktiverad",
  "global.documentation": "Dokumentation",
  "global.enabled": "Aktiv",
  "global.finish": "Slutför",
  "global.marketplace": "Marketplace",
  "global.name": "Namn",
  "global.none": "Ingen",
  "global.password": "Lösenord",
  "global.plugins": "Plugins",
  "global.plugins.content-manager": "Innehållshanterare",
  "global.plugins.content-manager.description": "Snabbt sätt att se, redigera och ta bort data i din databas.",
  "global.plugins.content-type-builder": "Innehållstypsskapare",
  "global.plugins.content-type-builder.description": "Modellera datastrukturen för ditt API. Skapa nya fält och relationer snabbt. Filerna skapas och uppdateras automatiskt i ditt projekt.",
  "global.plugins.email": "Email",
  "global.plugins.email.description": "Konfigurera din applikation för att skicka e-post.",
  "global.plugins.upload": "Mediebibliotek",
  "global.plugins.upload.description": "Mediafilhantering.",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "Lägger till GraphQL-endpoint med standard-API-metoder.",
  "global.plugins.documentation": "Dokumentation",
  "global.plugins.documentation.description": "Skapa ett OpenAPI-dokument och visualisera ditt API med SWAGGER UI.",
  "global.plugins.i18n": "Internationalisering",
  "global.plugins.i18n.description": "Denna plugin gör det möjligt att skapa, läsa och uppdatera innehåll på olika språk, både från adminpanelen och från API:et.",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "Skicka Strapi-fel till Sentry.",
  "global.plugins.users-permissions": "Roller och behörigheter",
  "global.plugins.users-permissions.description": "Skydda ditt API med en autentiseringsprocess baserad på JWT. Detta plugin kommer också med ACL som låter dig hantera behörigheterna mellan användargrupperna.",
  "global.profile": "Profil",
  "global.prompt.unsaved": "Är du säker på att du vill lämna den här sidan? Alla dina ändringar kommer att gå förlorade",
  "global.reset-password": "Återställ lösenord",
  "global.roles": "Roller",
  "global.save": "Spara",
  "global.see-more": "Se mer",
  "global.select": "Välj",
  "global.select-all-entries": "Välj alla poster",
  "global.settings": "Inställningar",
  "global.type": "Typ",
  "global.users": "Användare",
  "notification.contentType.relations.conflict": "Innehållstypen har inkompatibla relationer",
  "notification.default.title": "Information:",
  "notification.error": "Ett fel uppstod",
  "notification.error.layout": "Det gick inte att hämta layouten",
  "notification.form.error.fields": "Formuläret innehåller fel",
  "notification.form.success.fields": "Ändringar sparade",
  "notification.link-copied": "Länken har kopierats till urklipp",
  "notification.permission.not-allowed-read": "Du får inte se detta dokument",
  "notification.success.delete": "Objektet har tagits bort",
  "notification.success.saved": "Sparat",
  "notification.success.title": "Lyckat:",
  "notification.success.apitokencreated": "API-token skapad",
  "notification.success.apitokenedited": "API-token uppdaterad",
  "notification.error.tokennamenotunique": "Namnet används redan av en annan token",
  "notification.version.update.message": "En ny version av Strapi är tillgänglig!",
  "notification.warning.title": "Varning:",
  "notification.warning.404": "404 - sidan finns inte",
  or,
  "request.error.model.unknown": "Denna modellen finns inte",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  sv as default,
  light,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=sv-lzyY1dsz-E6T56D2E.js.map
