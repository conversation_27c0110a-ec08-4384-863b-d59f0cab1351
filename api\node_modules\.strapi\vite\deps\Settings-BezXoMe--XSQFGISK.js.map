{"version": 3, "sources": ["../../../@strapi/plugin-documentation/admin/src/utils/baseQuery.ts", "../../../@strapi/plugin-documentation/admin/src/components/SettingsForm.tsx", "../../../@strapi/plugin-documentation/admin/src/pages/Settings.tsx"], "sourcesContent": ["import { SerializedError } from '@reduxjs/toolkit';\nimport { type UnknownApiError, type ApiError } from '@strapi/strapi/admin';\n\ntype BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\nconst isBaseQueryError = (error: BaseQueryError): error is ApiError | UnknownApiError => {\n  return error.name !== undefined;\n};\n\nexport { isBaseQueryError };\n", "import * as React from 'react';\n\nimport {\n  <PERSON>,\n  Button,\n  Flex,\n  Grid,\n  TextInput,\n  Toggle,\n  Typography,\n  Field,\n} from '@strapi/design-system';\n// Strapi Icons\nimport { Check, Eye as Show, EyeStriked as Hide } from '@strapi/icons';\nimport { translatedErrors, useRBAC, Layouts } from '@strapi/strapi/admin';\nimport { Form, Formik, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\n\nimport { PERMISSIONS } from '../constants';\nimport { DocumentInfos, SettingsInput } from '../types';\nimport { getTrad } from '../utils';\n\nconst schema = yup.object().shape({\n  restrictedAccess: yup.boolean(),\n  password: yup.string().when('restrictedAccess', (value, initSchema) => {\n    return value\n      ? initSchema\n          .required(translatedErrors.required.id)\n          .min(8)\n          .matches(/[a-z]/, 'components.Input.error.contain.lowercase')\n          .matches(/[A-Z]/, 'components.Input.error.contain.uppercase')\n          .matches(/\\d/, 'components.Input.error.contain.number')\n      : initSchema;\n  }),\n});\n\nconst FieldActionWrapper = styled(Field.Action)`\n  svg {\n    height: 1.6rem;\n    width: 1.6rem;\n    path {\n      fill: ${({ theme }) => theme.colors.neutral600};\n    }\n  }\n`;\n\ntype SettingsFormProps = {\n  data?: DocumentInfos;\n  onSubmit: (body: SettingsInput, formik: FormikHelpers<SettingsInput>) => Promise<void>;\n};\n\nexport const SettingsForm = ({ data, onSubmit }: SettingsFormProps) => {\n  const { formatMessage } = useIntl();\n  const [passwordShown, setPasswordShown] = React.useState(false);\n  const { allowedActions } = useRBAC(PERMISSIONS);\n\n  return (\n    <Formik\n      enableReinitialize\n      initialValues={{\n        restrictedAccess: data?.documentationAccess.restrictedAccess || false,\n        password: '',\n      }}\n      onSubmit={onSubmit}\n      validationSchema={schema}\n    >\n      {({\n        handleSubmit,\n        values,\n        handleChange,\n        errors,\n        setFieldTouched,\n        setFieldValue,\n        setFieldError,\n        dirty,\n      }) => {\n        return (\n          <Form noValidate onSubmit={handleSubmit}>\n            <Layouts.Header\n              title={formatMessage({\n                id: getTrad('plugin.name'),\n                defaultMessage: 'Documentation',\n              })}\n              subtitle={formatMessage({\n                id: getTrad('pages.SettingsPage.header.description'),\n                defaultMessage: 'Configure the documentation plugin',\n              })}\n              primaryAction={\n                <Button\n                  type=\"submit\"\n                  startIcon={<Check />}\n                  disabled={!dirty && allowedActions.canUpdate}\n                >\n                  {formatMessage({\n                    id: getTrad('pages.SettingsPage.Button.save'),\n                    defaultMessage: 'Save',\n                  })}\n                </Button>\n              }\n            />\n            <Layouts.Content>\n              <Box\n                background=\"neutral0\"\n                hasRadius\n                shadow=\"filterShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Typography variant=\"delta\" tag=\"h2\">\n                    {formatMessage({\n                      id: 'global.settings',\n                      defaultMessage: 'Settings',\n                    })}\n                  </Typography>\n                  <Grid.Root gap={4}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"restrictedAccess\"\n                        hint={formatMessage({\n                          id: getTrad('pages.SettingsPage.toggle.hint'),\n                          defaultMessage: 'Make the documentation endpoint private',\n                        })}\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('pages.SettingsPage.toggle.label'),\n                            defaultMessage: 'Restricted Access',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={values.restrictedAccess}\n                          onChange={() => {\n                            if (values.restrictedAccess === true) {\n                              setFieldValue('password', '', false);\n                              setFieldTouched('password', false, false);\n                              setFieldError('password', undefined);\n                            }\n\n                            setFieldValue('restrictedAccess', !values.restrictedAccess, false);\n                          }}\n                          onLabel=\"On\"\n                          offLabel=\"Off\"\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                    {values.restrictedAccess && (\n                      <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                        <Field.Root\n                          name=\"password\"\n                          error={\n                            errors.password\n                              ? formatMessage({\n                                  id: errors.password,\n                                  defaultMessage: errors.password,\n                                })\n                              : undefined\n                          }\n                        >\n                          <Field.Label>\n                            {formatMessage({\n                              id: 'global.password',\n                              defaultMessage: 'Password',\n                            })}\n                          </Field.Label>\n                          <TextInput\n                            placeholder=\"**********\"\n                            type={passwordShown ? 'text' : 'password'}\n                            value={values.password}\n                            onChange={handleChange}\n                            endAction={\n                              <FieldActionWrapper\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  setPasswordShown((prev) => !prev);\n                                }}\n                                label={formatMessage(\n                                  passwordShown\n                                    ? {\n                                        id: 'Auth.form.password.show-password',\n                                        defaultMessage: 'Show password',\n                                      }\n                                    : {\n                                        id: 'Auth.form.password.hide-password',\n                                        defaultMessage: 'Hide password',\n                                      }\n                                )}\n                              >\n                                {passwordShown ? <Show /> : <Hide />}\n                              </FieldActionWrapper>\n                            }\n                          />\n                          <Field.Error />\n                        </Field.Root>\n                      </Grid.Item>\n                    )}\n                  </Grid.Root>\n                </Flex>\n              </Box>\n            </Layouts.Content>\n          </Form>\n        );\n      }}\n    </Formik>\n  );\n};\n", "import * as React from 'react';\n\nimport { Main } from '@strapi/design-system';\nimport { useAPIError<PERSON><PERSON><PERSON>, Page, useNotification } from '@strapi/strapi/admin';\nimport { FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\n\nimport { SettingsForm } from '../components/SettingsForm';\nimport { useGetInfoQuery, useUpdateSettingsMutation } from '../services/api';\nimport { getTrad, isBaseQueryError } from '../utils';\n\nimport type { SettingsInput } from '../types';\n\nconst SettingsPage = () => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const { data, isError, isLoading, isFetching } = useGetInfoQuery();\n  const [updateSettings] = useUpdateSettingsMutation();\n\n  const onUpdateSettings = async (body: SettingsInput, formik: FormikHelpers<SettingsInput>) => {\n    return updateSettings({ body })\n      .unwrap()\n      .then(() => {\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: getTrad('notification.update.success'),\n            defaultMessage: 'Successfully updated settings',\n          }),\n        });\n      })\n      .catch((err) => {\n        if (isBaseQueryError(err) && err.name === 'ValidationError') {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(err),\n          });\n        }\n      });\n  };\n\n  if (isLoading || isFetching) {\n    return <Page.Loading />;\n  }\n\n  if (isError) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Main>\n      <SettingsForm data={data} onSubmit={onUpdateSettings} />\n    </Main>\n  );\n};\n\nexport { SettingsPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,mBAAmB,CAAC,UAA+D;AACvF,SAAO,MAAM,SAAS;AACxB;ACiBA,IAAM,SAAaA,QAAO,EAAE,MAAM;EAChC,kBAAsB,OAAQ;EAC9B,UAAcA,QAAO,EAAE,KAAK,oBAAoB,CAAC,OAAO,eAAe;AAC9D,WAAA,QACH,WACG,SAAS,YAAiB,SAAS,EAAE,EACrC,IAAI,CAAC,EACL,QAAQ,SAAS,0CAA0C,EAC3D,QAAQ,SAAS,0CAA0C,EAC3D,QAAQ,MAAM,uCAAuC,IACxD;EAAA,CACL;AACH,CAAC;AAED,IAAM,qBAAqB,GAAO,MAAM,MAAM;;;;;cAKhC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAU7C,IAAM,eAAe,CAAC,EAAE,MAAM,SAAA,MAAkC;AAC/D,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS,KAAK;AAC9D,QAAM,EAAE,eAAA,IAAmB,QAAQ,WAAW;AAG5C,aAAA;IAAC;IAAA;MACC,oBAAkB;MAClB,eAAe;QACb,mBAAkB,6BAAM,oBAAoB,qBAAoB;QAChE,UAAU;MACZ;MACA;MACA,kBAAkB;MAEjB,UAAC,CAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,MACI;AACJ,mBACG,yBAAA,MAAA,EAAK,YAAU,MAAC,UAAU,cACzB,UAAA;cAAA;YAAC,QAAQ;YAAR;cACC,OAAO,cAAc;gBACnB,IAAI,QAAQ,aAAa;gBACzB,gBAAgB;cAAA,CACjB;cACD,UAAU,cAAc;gBACtB,IAAI,QAAQ,uCAAuC;gBACnD,gBAAgB;cAAA,CACjB;cACD,mBACE;gBAAC;gBAAA;kBACC,MAAK;kBACL,eAAA,wBAAY,eAAM,CAAA,CAAA;kBAClB,UAAU,CAAC,SAAS,eAAe;kBAElC,UAAc,cAAA;oBACb,IAAI,QAAQ,gCAAgC;oBAC5C,gBAAgB;kBAAA,CACjB;gBAAA;cACH;YAAA;UAEJ;cACA,wBAAC,QAAQ,SAAR,EACC,cAAA;YAAC;YAAA;cACC,YAAW;cACX,WAAS;cACT,QAAO;cACP,YAAY;cACZ,eAAe;cACf,aAAa;cACb,cAAc;cAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;oBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;sBAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;oBAAC,MAAM;oBAAN;sBACC,MAAK;sBACL,MAAM,cAAc;wBAClB,IAAI,QAAQ,gCAAgC;wBAC5C,gBAAgB;sBAAA,CACjB;sBAED,UAAA;4BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;0BACb,IAAI,QAAQ,iCAAiC;0BAC7C,gBAAgB;wBACjB,CAAA,EAAA,CACH;4BACA;0BAAC;0BAAA;4BACC,SAAS,OAAO;4BAChB,UAAU,MAAM;AACV,kCAAA,OAAO,qBAAqB,MAAM;AACtB,8CAAA,YAAY,IAAI,KAAK;AACnB,gDAAA,YAAY,OAAO,KAAK;AACxC,8CAAc,YAAY,MAAS;8BACrC;AAEA,4CAAc,oBAAoB,CAAC,OAAO,kBAAkB,KAAK;4BACnE;4BACA,SAAQ;4BACR,UAAS;0BAAA;wBACX;4BACA,wBAAC,MAAM,MAAN,CAAA,CAAW;sBAAA;oBAAA;kBAAA,EAAA,CAEhB;kBACC,OAAO,wBACL,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;oBAAC,MAAM;oBAAN;sBACC,MAAK;sBACL,OACE,OAAO,WACH,cAAc;wBACZ,IAAI,OAAO;wBACX,gBAAgB,OAAO;sBACxB,CAAA,IACD;sBAGN,UAAA;4BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;0BACb,IAAI;0BACJ,gBAAgB;wBACjB,CAAA,EAAA,CACH;4BACA;0BAAC;0BAAA;4BACC,aAAY;4BACZ,MAAM,gBAAgB,SAAS;4BAC/B,OAAO,OAAO;4BACd,UAAU;4BACV,eACE;8BAAC;8BAAA;gCACC,SAAS,CAAC,MAAM;AACd,oCAAE,gBAAgB;AACD,mDAAA,CAAC,SAAS,CAAC,IAAI;gCAClC;gCACA,OAAO;kCACL,gBACI;oCACE,IAAI;oCACJ,gBAAgB;kCAAA,IAElB;oCACE,IAAI;oCACJ,gBAAgB;kCAClB;gCACN;gCAEC,UAAgB,oBAAA,wBAACC,eAAK,CAAA,CAAA,QAAA,wBAAMC,eAAK,CAAA,CAAA;8BAAA;4BACpC;0BAAA;wBAEJ;4BACA,wBAAC,MAAM,OAAN,CAAA,CAAY;sBAAA;oBAAA;kBAAA,EAAA,CAEjB;gBAAA,EAAA,CAEJ;cAAA,EAAA,CACF;YAAA;UAAA,EAAA,CAEJ;QACF,EAAA,CAAA;MAEJ;IAAA;EAAA;AAGN;ACrMA,IAAM,eAAe,MAAM;AACnB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA;IACJ,yBAAyB;IACzB,iCAAiC;EAAA,IAC/B,mBAAmB;AACvB,QAAM,EAAE,MAAM,SAAS,WAAW,WAAA,IAAe,gBAAA;AAC3C,QAAA,CAAC,cAAc,IAAI,0BAAA;AAEnB,QAAA,mBAAmB,OAAO,MAAqB,WAAyC;AACrF,WAAA,eAAe,EAAE,KAAK,CAAC,EAC3B,OAAO,EACP,KAAK,MAAM;AACS,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,QAAQ,6BAA6B;UACzC,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA,CACF,EACA,MAAM,CAAC,QAAQ;AACd,UAAI,iBAAiB,GAAG,KAAK,IAAI,SAAS,mBAAmB;AACxC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,GAAG;QAAA,CAC5B;MACH;IAAA,CACD;EAAA;AAGL,MAAI,aAAa,YAAY;AACpB,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MAAI,SAAS;AACJ,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,aAAA,wBACG,MACC,EAAA,cAAA,wBAAC,cAAA,EAAa,MAAY,UAAU,iBAAkB,CAAA,EACxD,CAAA;AAEJ;", "names": ["create", "Show", "<PERSON>de"]}