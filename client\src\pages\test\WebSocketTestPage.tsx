import { useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  List,
  Badge,
  Typography,
  Space,
  Divider,
  Tag,
  Row,
  Col,
  Input,
  Form,
  Select,
  message,
  Alert,
} from "antd";
import { useWebSocketNotifications } from "@app/hooks/notifications/use-websocket-notifications";
import { useQueryWebSocketNotifications } from "@app/hooks/notifications/use-query-websocket-notifications";
import { useAuthContext } from "@app/context/auth/AuthContext";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

const { Title, Text } = Typography;
const { TextArea } = Input;

export const WebSocketTestPage = () => {
  const { user } = useAuthContext();
  const [testMessage, setTestMessage] = useState("");

  // Hook WebSocket básico
  const {
    connectionStatus: wsStatus,
    connectionError: wsError,
    isConnected: wsConnected,
    sendMessage,
    lastMessage,
    connect: wsConnect,
    disconnect: wsDisconnect,
  } = useWebSocketNotifications();

  // Hook híbrido (React Query + WebSocket)
  const {
    notifications,
    unreadCount,
    isConnected: hybridConnected,
    connectionStatus: hybridStatus,
    connectionError: hybridError,
    markAsRead,
    markAllAsRead,
    loadNotifications,
  } = useQueryWebSocketNotifications();

  // Enviar mensaje de prueba
  const handleSendTestMessage = () => {
    if (!testMessage.trim()) {
      message.warning("Escribe un mensaje de prueba");
      return;
    }

    const success = sendMessage({
      type: "test",
      message: testMessage,
      timestamp: new Date().toISOString(),
    });

    if (success) {
      message.success("Mensaje enviado");
      setTestMessage("");
    } else {
      message.error("Error enviando mensaje");
    }
  };

  // Enviar ping
  const handleSendPing = () => {
    const success = sendMessage({
      type: "ping",
      timestamp: new Date().toISOString(),
    });

    if (success) {
      message.info("Ping enviado");
    } else {
      message.error("Error enviando ping");
    }
  };

  // Suscribirse a temas
  const handleSubscribe = () => {
    const success = sendMessage({
      type: "subscribe",
      topics: ["notifications", "broadcasts", "alerts"],
    });

    if (success) {
      message.success("Suscripción enviada");
    } else {
      message.error("Error enviando suscripción");
    }
  };

  // Enviar notificación de prueba desde el backend
  const handleSendBackendTest = async () => {
    if (!user?.id) {
      message.error("Usuario no autenticado");
      return;
    }

    try {
      const response = await fetch(
        `http://localhost:1337/api/notifications/websocket/test/${user.id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (data.success) {
        message.success("Notificación de prueba enviada desde el backend");
      } else {
        message.warning(data.message || "No se pudo enviar la notificación");
      }
    } catch (error) {
      console.error("Error enviando notificación de prueba:", error);
      message.error("Error enviando notificación de prueba");
    }
  };

  // Enviar notificación de confirmación de huésped
  const handleGuestConfirmationTest = async () => {
    try {
      const response = await fetch(
        "http://localhost:1337/api/notifications/websocket/test/guest-confirmation",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (data.success) {
        message.success("🏨 Notificación de confirmación de huésped enviada");
      } else {
        message.warning(data.message || "No se pudo enviar la notificación");
      }
    } catch (error) {
      console.error("Error enviando notificación de confirmación:", error);
      message.error("Error enviando notificación de confirmación");
    }
  };

  // Enviar notificación de nueva reserva
  const handleNewReservationTest = async () => {
    try {
      const response = await fetch(
        "http://localhost:1337/api/notifications/websocket/test/new-reservation",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (data.success) {
        message.success("📅 Notificación de nueva reserva enviada");
      } else {
        message.warning(data.message || "No se pudo enviar la notificación");
      }
    } catch (error) {
      console.error("Error enviando notificación de nueva reserva:", error);
      message.error("Error enviando notificación de nueva reserva");
    }
  };

  // Enviar alerta de seguridad
  const handleSecurityAlertTest = async () => {
    try {
      const response = await fetch(
        "http://localhost:1337/api/notifications/websocket/test/security-alert",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      const data = await response.json();

      if (data.success) {
        message.error("🚨 Alerta de seguridad enviada");
      } else {
        message.warning(data.message || "No se pudo enviar la alerta");
      }
    } catch (error) {
      console.error("Error enviando alerta de seguridad:", error);
      message.error("Error enviando alerta de seguridad");
    }
  };

  // Obtener color del estado de conexión
  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "green";
      case "CONNECTING":
        return "orange";
      case "ERROR":
        return "red";
      case "CLOSED":
        return "default";
      default:
        return "default";
    }
  };

  return (
    <div style={{ padding: "24px" }}>
      <Title level={2}>🚀 Prueba de WebSockets</Title>
      <Text type="secondary">
        Página de prueba para verificar la funcionalidad de WebSockets en tiempo
        real
      </Text>

      <Divider />

      <Row gutter={[16, 16]}>
        {/* Estado de Conexiones */}
        <Col xs={24} lg={12}>
          <Card title="📊 Estado de Conexiones" size="small">
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text strong>WebSocket Básico:</Text>
                <Tag color={getStatusColor(wsStatus)} style={{ marginLeft: 8 }}>
                  {wsStatus}
                </Tag>
                {wsConnected && <Badge status="success" text="Conectado" />}
              </div>

              <div>
                <Text strong>Hook Híbrido:</Text>
                <Tag
                  color={getStatusColor(hybridStatus)}
                  style={{ marginLeft: 8 }}
                >
                  {hybridStatus}
                </Tag>
                {hybridConnected && <Badge status="success" text="Conectado" />}
              </div>

              {(wsError || hybridError) && (
                <Alert
                  message="Error de Conexión"
                  description={wsError || hybridError}
                  type="error"
                  showIcon
                />
              )}

              <Space>
                <Button onClick={wsConnect} disabled={wsConnected}>
                  Conectar WS
                </Button>
                <Button onClick={wsDisconnect} disabled={!wsConnected}>
                  Desconectar WS
                </Button>
                <Button onClick={() => loadNotifications()}>
                  Recargar Notificaciones
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* Controles de Prueba */}
        <Col xs={24} lg={12}>
          <Card title="🎮 Controles de Prueba" size="small">
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text strong>Mensaje de Prueba:</Text>
                <Space.Compact style={{ marginTop: 8, width: "100%" }}>
                  <Input
                    style={{ width: "70%" }}
                    placeholder="Escribe un mensaje..."
                    value={testMessage}
                    onChange={(e) => setTestMessage(e.target.value)}
                    onPressEnter={handleSendTestMessage}
                  />
                  <Button
                    type="primary"
                    style={{ width: "30%" }}
                    onClick={handleSendTestMessage}
                    disabled={!wsConnected}
                  >
                    Enviar
                  </Button>
                </Space.Compact>
              </div>

              <Space wrap>
                <Button onClick={handleSendPing} disabled={!wsConnected}>
                  📡 Ping
                </Button>
                <Button onClick={handleSubscribe} disabled={!wsConnected}>
                  📺 Suscribirse
                </Button>
                <Button onClick={handleSendBackendTest} type="dashed">
                  🧪 Prueba Backend
                </Button>
              </Space>

              <Divider style={{ margin: "12px 0" }} />

              <Space wrap>
                <Button
                  onClick={handleGuestConfirmationTest}
                  type="primary"
                  ghost
                >
                  🏨 Confirmación Huésped
                </Button>
                <Button onClick={handleNewReservationTest} type="primary" ghost>
                  📅 Nueva Reserva
                </Button>
                <Button onClick={handleSecurityAlertTest} danger ghost>
                  🚨 Alerta Seguridad
                </Button>
              </Space>

              {user && (
                <div>
                  <Text strong>Usuario Actual:</Text>
                  <Tag color="blue" style={{ marginLeft: 8 }}>
                    {user.username} (ID: {user.id})
                  </Tag>
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* Último Mensaje WebSocket */}
        <Col xs={24} lg={12}>
          <Card title="📨 Último Mensaje WebSocket" size="small">
            {lastMessage ? (
              <div>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <div>
                    <Text strong>Tipo:</Text>
                    <Tag color="blue" style={{ marginLeft: 8 }}>
                      {lastMessage.type}
                    </Tag>
                  </div>

                  {lastMessage.message && (
                    <div>
                      <Text strong>Mensaje:</Text>
                      <Text style={{ marginLeft: 8 }}>
                        {lastMessage.message}
                      </Text>
                    </div>
                  )}

                  {lastMessage.timestamp && (
                    <div>
                      <Text strong>Timestamp:</Text>
                      <Text style={{ marginLeft: 8 }}>
                        {formatDistanceToNow(new Date(lastMessage.timestamp), {
                          addSuffix: true,
                          locale: es,
                        })}
                      </Text>
                    </div>
                  )}

                  <details>
                    <summary>Ver datos completos</summary>
                    <pre style={{ fontSize: "12px", marginTop: 8 }}>
                      {JSON.stringify(lastMessage, null, 2)}
                    </pre>
                  </details>
                </Space>
              </div>
            ) : (
              <Text type="secondary">No hay mensajes recibidos</Text>
            )}
          </Card>
        </Col>

        {/* Notificaciones */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <span>🔔 Notificaciones</span>
                <Badge count={unreadCount} overflowCount={99} />
              </Space>
            }
            size="small"
            extra={
              <Space>
                <Button
                  size="small"
                  onClick={markAllAsRead}
                  disabled={unreadCount === 0}
                >
                  Marcar todas como leídas
                </Button>
              </Space>
            }
          >
            {notifications.length > 0 ? (
              <List
                size="small"
                dataSource={notifications.slice(0, 5)}
                renderItem={(notification) => (
                  <List.Item
                    style={{
                      opacity: notification.read ? 0.6 : 1,
                      cursor: "pointer",
                    }}
                    onClick={() => markAsRead(notification.id)}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          {notification.title}
                          {!notification.read && <Badge status="processing" />}
                        </Space>
                      }
                      description={
                        <div>
                          <div>{notification.message}</div>
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            {formatDistanceToNow(
                              new Date(notification.createdAt),
                              {
                                addSuffix: true,
                                locale: es,
                              },
                            )}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Text type="secondary">No hay notificaciones</Text>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WebSocketTestPage;
