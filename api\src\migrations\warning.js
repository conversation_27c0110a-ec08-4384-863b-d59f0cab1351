async function up(knex) {
  // Primero eliminamos la restricción existente
  await knex.schema.table('warnings', (table) => {
    table.dropForeign(['created_by_id']);
  });

  // Luego creamos la nueva restricción que apunta a la tabla users
  await knex.schema.table('warnings', (table) => {
    table.foreign('created_by_id')
      .references('id')
      .inTable('up_users')
      .onDelete('SET NULL');
  });
}

async function down(knex) {
  // Revertimos los cambios
  await knex.schema.table('warnings', (table) => {
    table.dropForeign(['created_by_id']);
    table.foreign('created_by_id')
      .references('id')
      .inTable('admin_users')
      .onDelete('SET NULL');
  });
}

module.exports = { up, down };
