import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/uk-CR-zDhAY.mjs
var groups = "Групи";
var models = "Collection Types";
var pageNotFound = "Сторінка не знайдена";
var uk = {
  "EditRelations.title": "Зв'язок",
  "api.id": "API ID",
  "components.AddFilterCTA.add": "Фільтри",
  "components.AddFilterCTA.hide": "Фільтри",
  "components.DraggableAttr.edit": "Натисніть щоб змінити",
  "components.DynamicZone.pick-compo": "Виберіть один компонент",
  "components.EmptyAttributesBlock.button": "Перейти до налаштувань",
  "components.EmptyAttributesBlock.description": "Ви можете змінити свої налаштування",
  "components.FieldItem.linkToComponentLayout": "Встановити макет компоненту",
  "components.FilterOptions.button.apply": "Застосувати",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Застосувати",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Очистити все",
  "components.FiltersPickWrapper.PluginHeader.description": "Вкажіть умови фільтрації записів",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Фільтри",
  "components.FiltersPickWrapper.hide": "Сховати",
  "components.LimitSelect.itemsPerPage": "Елементів на сторінку",
  "components.Search.placeholder": "Пошук записів...",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Налаштуйте, як буде виглядати екран редагування.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Визначте параметри вигяду списку.",
  "components.SettingsViewWrapper.pluginHeader.title": "Налаштуйте вигляд - {name}",
  "components.TableDelete.delete": "Видалити все",
  "components.TableDelete.deleteSelected": "Видалити обране",
  "components.TableEmpty.withFilters": "Немає {contentType} з обраними фільтрами...",
  "components.TableEmpty.withSearch": 'Немає {contentType}, які відповідають пошуку "{search}"...',
  "components.TableEmpty.withoutFilter": "Немає {contentType}...",
  "components.empty-repeatable": "Немає записів. Натисніть кнопку нижче, щоб додати нову.",
  "components.notification.info.maximum-requirement": "Ви досягли максимальної кількості полей",
  "components.notification.info.minimum-requirement": "Поле було додано, щоб відповідати мінімальним вимогам",
  "components.reset-entry": "Скинути запис",
  "components.uid.apply": "Вибрати",
  "components.uid.available": "Доступний",
  "components.uid.regenerate": "Згенерувати",
  "components.uid.suggested": "Рекомендоване",
  "components.uid.unavailable": "Недоступний",
  "containers.Edit.Link.Layout": "Налаштувати компонування",
  "containers.Edit.Link.Model": "Змінити Collection Type",
  "containers.Edit.addAnItem": "Додати елемент...",
  "containers.Edit.clickToJump": "Натисніть щоб перейти до запису",
  "containers.Edit.delete": "Видалити",
  "containers.Edit.editing": "Редагування...",
  "containers.Edit.pluginHeader.title.new": "Створити запис",
  "containers.Edit.reset": "Скинути",
  "containers.Edit.returnList": "Повернутися до списку",
  "containers.Edit.seeDetails": "Докладніше",
  "containers.Edit.submit": "Зберегти",
  "containers.EditSettingsView.modal-form.edit-field": "Налаштуйте поле",
  "containers.EditView.notification.errors": "Форма містить деякі помилки",
  "containers.Home.introduction": "Щоб редагувати ваші записи, перейдіть за посиланням в лівому меню. Цей плагін не має належного способу редагування налаштувань і все ще активно розробляється.",
  "containers.Home.pluginHeaderDescription": "Керуйте своїми записами за допомогою потужного та красивого інтерфейсу.",
  "containers.Home.pluginHeaderTitle": "Контент-менеджер",
  "containers.List.errorFetchRecords": "Помилка",
  "containers.list.displayedFields": "Показувати поля",
  "containers.ListSettingsView.modal-form.edit-label": "Налаштуйте підпис",
  "containers.SettingPage.add.field": "Додати ще одне поле",
  "containers.SettingPage.attributes": "Поля атрибутів",
  "containers.SettingPage.attributes.description": "Визначте порядок атрибутів",
  "containers.SettingPage.editSettings.description": "Перетягніть поля щоб налаштувати вигляд.",
  "containers.SettingPage.editSettings.entry.title": "Заголовок запису",
  "containers.SettingPage.editSettings.entry.title.description": "Встановіть поле, яке буде відображати запис.",
  "containers.SettingPage.editSettings.title": "Змінити вигляд (налаштування)",
  "containers.SettingPage.layout": "Компонування",
  "containers.SettingPage.listSettings.description": "Налаштуйте параметри для цього Collection Type",
  "containers.SettingPage.listSettings.title": "Список (налаштування)",
  "containers.SettingPage.pluginHeaderDescription": "Налаштуйте конкретні параметри для цього Collection Type",
  "containers.SettingPage.settings": "Налаштування",
  "containers.SettingPage.view": "Вигляд",
  "containers.SettingViewModel.pluginHeader.title": "Контент-менеджер - {name}",
  "containers.SettingsPage.Block.contentType.description": "Налаштуйте конкретні параметри",
  "containers.SettingsPage.Block.contentType.title": "Collection Types",
  "containers.SettingsPage.Block.generalSettings.description": "Налаштуйте параметри за замовчуванням для свого Collection Types",
  "containers.SettingsPage.Block.generalSettings.title": "Загальне",
  "containers.SettingsPage.pluginHeaderDescription": "Налаштуйте параметри для всіх ваших Collection Types та груп",
  "containers.SettingsView.list.subtitle": "Налаштуйте компонування та відображення ваших Collection Types та груп",
  "containers.SettingsView.list.title": "Налаштування відображення",
  "emptyAttributes.button": "Перейдіть до конструктора Collection Type",
  "emptyAttributes.description": "Додайте перше поле в ваш Collection Type",
  "emptyAttributes.title": "Поки що немає полей",
  "error.attribute.key.taken": "Значення вже існує",
  "error.attribute.sameKeyAndName": "Не може співпадати",
  "error.attribute.taken": "Це поле вже існує",
  "error.contentTypeName.taken": "Це ім'я вже існує",
  "error.model.fetch": "Під час завантаження конфігурації моделей сталася помилка.",
  "error.record.create": "Під час створення запису сталася помилка.",
  "error.record.delete": "Під час видалення запису сталася помилка.",
  "error.record.fetch": "Під час завантаження запису сталася помилка.",
  "error.record.update": "Під час оновлення запису сталася помилка.",
  "error.records.count": "Під час завантаження кількості записів сталася помилка.",
  "error.records.fetch": "Під час завантаження записів сталася помилка.",
  "error.schema.generation": "Під час створення схеми сталася помилка.",
  "error.validation.json": "Це не відпоідає формату JSON",
  "error.validation.max": "Значення занадто велике.",
  "error.validation.maxLength": "Значення занадто довге.",
  "error.validation.min": "Значення занадто мале.",
  "error.validation.minLength": "Значення занадто коротке.",
  "error.validation.minSupMax": "Не може бути більше",
  "error.validation.regex": "Значення не відповідає регулярному виразу.",
  "error.validation.required": "Це обов'язкове поле.",
  "form.Input.bulkActions": "Дозволити масові дії",
  "form.Input.defaultSort": "Сортування за замовчуванням",
  "form.Input.description": "Опис",
  "form.Input.description.placeholder": "Ім'я, для відображення в профілі",
  "form.Input.editable": "Редагуєме поле",
  "form.Input.filters": "Увімкнути фільтри",
  "form.Input.label": "Підпис",
  "form.Input.label.inputDescription": "Це значення змінить підпис, який відображується у заголовку таблиці",
  "form.Input.pageEntries": "записів на сторінці",
  "form.Input.pageEntries.inputDescription": "Зауважте, що ви можете змінити це значення на сторінці налаштувань Collection Type.",
  "form.Input.placeholder": "Плейсхолдер",
  "form.Input.placeholder.placeholder": "Моє значення",
  "form.Input.search": "Увімкнути пошук",
  "form.Input.search.field": "Дозволити шукати за цим полем",
  "form.Input.sort.field": "Дозволити сортувати за цим полем",
  "form.Input.wysiwyg": "Відображувати як WYSIWYG",
  "global.displayedFields": "Відображені поля",
  groups,
  "groups.numbered": "Групи ({number})",
  models,
  "models.numbered": "Collection Types ({number})",
  "notification.error.displayedFields": "Потрібне хоча б одне поле для відображення.",
  "notification.error.relationship.fetch": "Під час завантаження зв'язків сталася помилка.",
  "notification.info.SettingPage.disableSort": "Потріен хоча б один атрібут дозволений для сортування.",
  "notification.info.minimumFields": "Необхідно відобразити хоча б одне поле.",
  "notification.upload.error": "Під час завантаження файлів сталася помилка.",
  pageNotFound,
  "plugin.description.long": "Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.",
  "plugin.description.short": "Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.",
  "popUpWarning.bodyMessage.contentType.delete": "Ви впевнені, що хочете видалити цей запис?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Ви впевнені, що хочете видалити ці записи?",
  "popUpWarning.warning.cancelAllSettings": "Ви впевнені, що хочете скасувати свої зміни?",
  "popUpWarning.warning.updateAllSettings": "Це змінить всі ваші налаштування",
  "success.record.delete": "Видалено",
  "success.record.save": "Збережено"
};
export {
  uk as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=uk-CR-zDhAY-C24WQSUA.js.map
