import { PageTitle } from "@app/components/common/PageTitle/PageTitle";
import { Icon } from "@ant-design/compatible";
import {
  CheckOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { BaseCard } from "@app/components/common/BaseCard/BaseCard";
import { BaseTypography } from "@app/components/common/BaseTypography/BaseTypography";
import { Animation } from "@app/components/lotties/Animation";
import { useNotificationsContext } from "@app/context/notifications/NotificationsContext";
import { Notification } from "@app/hooks/notifications/use-sse-notifications";
import { useResponsive } from "@app/hooks/useResponsive";
import {
  BackTop,
  Button,
  Flex,
  Result,
  Space,
  Timeline,
  Typography,
  theme,
} from "antd";

import dayjs from "dayjs";
import "dayjs/locale/es";
import { useMemo } from "react";
import styled from "styled-components";
import { PushNotificationButton } from "@app/components/notifications/PushNotificationButton";

dayjs.locale("es");

const { Title } = BaseTypography;

const StyledTimeline = styled(Timeline)`
  margin-top: 1.5rem;

  .ant-timeline-item-content {
    width: 100%;
  }
`;

export const NotificationsHistoryPage = () => {
  const { token } = theme.useToken();
  const { isDesktop } = useResponsive();

  const { notifications, deleteNotification, loadNotifications, markAsRead } =
    useNotificationsContext();

  // Agrupar notificaciones por fecha
  const groupedNotifications = useMemo(() => {
    const groups: { [key: string]: Notification[] } = {};

    notifications.forEach((notification) => {
      const date = dayjs(notification.createdAt).format("YYYY-MM-DD");
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(notification);
    });

    return Object.entries(groups)
      .sort(([dateA], [dateB]) => dateB.localeCompare(dateA))
      .map(([date, items]) => ({
        date,
        formattedDate: dayjs(date).format("DD [de] MMMM [de] YYYY"),
        items: items.sort(
          (a, b) =>
            dayjs(b.createdAt || 0).valueOf() -
            dayjs(a.createdAt || 0).valueOf(),
        ),
      }));
  }, [notifications]);

  const handleDelete = async (id: string | number) => {
    await deleteNotification(id);
  };

  const handleRefresh = () => {
    loadNotifications();
  };

  if (notifications.length === 0) {
    return (
      <BaseCard
        title="Historial de Notificaciones"
        extra={
          <Button
            size="small"
            type="primary"
            onClick={handleRefresh}
            icon={<SyncOutlined />}
          />
        }
      >
        <Flex wrap="wrap" justify="center" style={{ width: "100%" }}>
          <Result
            icon={
              <Animation
                src="/animations/notifications.lottie"
                width={isDesktop ? 800 : 400}
                height={isDesktop ? 800 : 400}
              />
            }
            title="No hay notificaciones"
            subTitle="Se mostrarán tus notificaciones aquí"
          />
        </Flex>
      </BaseCard>
    );
  }

  return (
    <BaseCard
      title="Historial de Notificaciones"
      extra={
        <Space.Compact>
          <PushNotificationButton key="push" />
          <Button
            key="refresh"
            size="small"
            type="primary"
            onClick={handleRefresh}
            icon={<SyncOutlined />}
          />
        </Space.Compact>
      }
    >
      <PageTitle>Historial de Notificaciones</PageTitle>
      <StyledTimeline mode="alternate">
        <Title level={2}>
          <Icon type="calendar" />
          Inicio de tus notificaciones
        </Title>

        {groupedNotifications.map((group) => (
          <Timeline.Item
            key={group.date}
            label={group.formattedDate}
            dot={<ClockCircleOutlined />}
          >
            {group.items.map((notification) => (
              <Flex
                key={notification.id}
                justify="center"
                align="center"
                gap="small"
                wrap="wrap"
              >
                <BaseCard
                  size="small"
                  style={{
                    backgroundColor: notification.read
                      ? token.colorBgLayout
                      : "#f5f5f5",
                    opacity: notification.read ? 0.8 : 1,
                    borderRadius: "8px",
                    margin: "0.5rem",
                    width: "100%",
                    maxWidth: "24rem",
                    maxHeight: "24rem",
                    overflow: "hidden",
                  }}
                  title={notification.title}
                  className={notification.read ? "notification-read" : ""}
                  actions={[
                    <Button
                      key="markAsRead"
                      type="link"
                      ghost
                      block
                      onClick={() => markAsRead(notification.id)}
                      icon={<CheckOutlined />}
                      disabled={notification.read}
                      size="small"
                    />,

                    <Button
                      size="small"
                      key="delete"
                      type="link"
                      danger
                      ghost
                      block
                      icon={<DeleteOutlined />}
                      onClick={() => handleDelete(notification.id)}
                    />,
                  ]}
                >
                  <Typography.Text>{notification.message}</Typography.Text>
                </BaseCard>
              </Flex>
            ))}
          </Timeline.Item>
        ))}
        <Title level={2}>
          <Icon type="calendar" />
          Fin de tus notificaciones
        </Title>
      </StyledTimeline>
      <BackTop />
    </BaseCard>
  );
};

export default NotificationsHistoryPage;
