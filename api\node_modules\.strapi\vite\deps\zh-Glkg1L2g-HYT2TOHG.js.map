{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/zh-Glkg1L2g.mjs"], "sourcesContent": ["const zh = {\n  \"coming-soon\": \"此內容目前正在構建，將在幾周後回歸！\",\n  \"components.Row.open\": \"開啟\",\n  \"components.Row.regenerate\": \"重新產生\",\n  \"containers.HomePage.Block.title\": \"版本\",\n  \"containers.HomePage.Button.update\": \"更新\",\n  \"containers.HomePage.PluginHeader.title\": \"文件資料 - 設定\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"我了解了\",\n  \"containers.HomePage.PopUpWarning.message\": \"你確定要刪除這個版本嗎？\",\n  \"containers.HomePage.copied\": \"權杖已經複製到剪貼簿\",\n  \"containers.HomePage.form.jwtToken\": \"存取你的 jwt 權杖\",\n  \"containers.HomePage.form.jwtToken.description\": \"複製這個權杖並於 swagger 中使用於發出請求\",\n  \"containers.HomePage.form.password\": \"密碼\",\n  \"containers.HomePage.form.password.inputDescription\": \"設定密碼存取權限\",\n  \"containers.HomePage.form.restrictedAccess\": \"存取受到限制\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"將 endpoints 設定為私密的。預設是對外公開的\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"顯示產生的檔案\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"用於覆寫產生的文件檔案時。\\n擴充套件會依照model及plugin分別產生檔案。\\n啟用這個選項將會方便你客製化文件資料\",\n  \"error.deleteDoc.versionMissing\": \"你想刪除的版本不存在\",\n  \"error.noVersion\": \"版本是必填欄位\",\n  \"error.regenerateDoc\": \"重新產生文件資料時發生了錯誤\",\n  \"error.regenerateDoc.versionMissing\": \"你想產生的版本不存在\",\n  \"notification.delete.success\": \"文件已刪除\",\n  \"notification.generate.success\": \"文件已重新產生\",\n  \"notification.update.success\": \"更新設定成功\",\n  \"pages.PluginPage.Button.open\": \"開啟說明文件\",\n  \"pages.PluginPage.header.description\": \"設定說明文件外掛程式\",\n  \"pages.PluginPage.table.generated\": \"最後產生時間\",\n  \"pages.PluginPage.table.icon.regenerate\": \"重新產生 {target}\",\n  \"pages.PluginPage.table.icon.show\": \"開啟 {target}\",\n  \"pages.PluginPage.table.version\": \"版本\",\n  \"pages.SettingsPage.header.description\": \"設定說明文件外掛程式\",\n  \"pages.SettingsPage.header.save\": \"儲存\",\n  \"pages.SettingsPage.toggle.hint\": \"將說明文件端點設為私人\",\n  \"pages.SettingsPage.toggle.label\": \"受限存取\",\n  \"plugin.description.long\": \"建立 OpenAPI 文件，並透過 SWAGGER UI 可視化您的 API。\",\n  \"plugin.description.short\": \"建立 OpenAPI 文件，並透過 SWAGGER UI 可視化您的 API。\",\n  \"plugin.name\": \"文件資料\"\n};\nexport {\n  zh as default\n};\n//# sourceMappingURL=zh-Glkg1L2g.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}