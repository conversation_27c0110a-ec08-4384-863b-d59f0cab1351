{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/uk-Cpmsxyku.mjs"], "sourcesContent": ["const uk = {\n  \"components.Row.open\": \"Відкрити\",\n  \"components.Row.regenerate\": \"Згенерувати\",\n  \"containers.HomePage.Block.title\": \"Версії\",\n  \"containers.HomePage.Button.update\": \"Оновити\",\n  \"containers.HomePage.PluginHeader.title\": \"Документація — Налаштування\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"Я усвідомлюю\",\n  \"containers.HomePage.PopUpWarning.message\": \"Ви впевнені, що хочете видалити цю версію?\",\n  \"containers.HomePage.copied\": \"Токен скопійовано у буфер обміну\",\n  \"containers.HomePage.form.jwtToken\": \"Отримайте ваш JWT токен\",\n  \"containers.HomePage.form.jwtToken.description\": \"Скопіюйте цей токен та застосуйте його в swagger, щоб відправляти запити\",\n  \"containers.HomePage.form.password\": \"Пароль\",\n  \"containers.HomePage.form.password.inputDescription\": \"Встановіть пароль доступу до документації\",\n  \"containers.HomePage.form.restrictedAccess\": \"Обмежений доступ\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"Зробіть документацію приватною. За замовченням, документація загальнодоступна\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"Показати згенеровані файли\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"Корисно, якщо ви хочете переписати згенеровані документи \\nПлаґін згенерує файли окремо для моделі та плаґіну. \\nУвімкнувши цю опцію, буде легше налаштувати вашу документацію\",\n  \"error.deleteDoc.versionMissing\": \"Версія, яку ви намагаєтесь видалити не існує.\",\n  \"error.noVersion\": \"Необхідна версія\",\n  \"error.regenerateDoc\": \"Під час генерування документації сталася помилка\",\n  \"error.regenerateDoc.versionMissing\": \"Версія, яку ви намагаєтесь згенерувати не існує.\",\n  \"notification.update.success\": \"Налаштування оновлено\"\n};\nexport {\n  uk as default\n};\n//# sourceMappingURL=uk-Cpmsxyku.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AACjC;", "names": []}