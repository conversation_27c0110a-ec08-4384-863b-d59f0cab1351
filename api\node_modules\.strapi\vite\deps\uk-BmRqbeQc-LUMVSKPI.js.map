{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/uk-BmRqbeQc.mjs"], "sourcesContent": ["const uk = {\n  \"BoundRoute.title\": \"Пов'язано з\",\n  \"EditForm.inputSelect.description.role\": \"Підключає нового автентифікованого користувача до вибраної ролі.\",\n  \"EditForm.inputSelect.label.role\": \"Роль за замовчуванням для автентифікованих користувачів\",\n  \"EditForm.inputToggle.description.email\": \"Не дозволяти користувачам створювати кілька аккаунтів з тим самим email, але різним провайдером автентифікації.\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"Якщо увімкнено (ON), щойно зареєстровані користувачі отримують листа для підтверждення.\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Куди ви будете перенаправлені після підтвердження свого email.\",\n  \"EditForm.inputToggle.description.email-reset-password\": \"URL-адреса сторінки скидання пароля вашого додатку\",\n  \"EditForm.inputToggle.description.sign-up\": \"Якщо вимкнено (OFF), реєстрація заборонена. Незалежно від використовуваного провайдера більше ніхто не зможе приєднатись.\",\n  \"EditForm.inputToggle.label.email\": \"Один аккаунт на email\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"Увімкнути підтверження email\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL для перенаправлення\",\n  \"EditForm.inputToggle.label.email-reset-password\": \"Сторінка скидання пароля\",\n  \"EditForm.inputToggle.label.sign-up\": \"Увімкнути реєстрацію\",\n  \"HeaderNav.link.advancedSettings\": \"Розширені налаштування\",\n  \"HeaderNav.link.emailTemplates\": \"Шаблони листів\",\n  \"HeaderNav.link.providers\": \"Провайдери\",\n  \"Plugin.permissions.plugins.description\": \"Встановіть всі дозволені дії у плаґіні {name}.\",\n  \"Plugins.header.description\": \"Нижче перераховано лише дії, пов'язані з маршрутом.\",\n  \"Plugins.header.title\": \"Дозволи\",\n  \"Policies.header.hint\": \"Виберіть дії вашого додатку або дії плаґіну та натисніть на значок шестірні, щоб відобразити пов'язаний маршрут\",\n  \"Policies.header.title\": \"Розширені налашрування\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"Якщо ви не впевнені, як використовувати змінні, {link}\",\n  \"PopUpForm.Email.options.from.email.label\": \"Email відправника\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"Ім'я відправника\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"Повідомлення\",\n  \"PopUpForm.Email.options.object.label\": \"Тема\",\n  \"PopUpForm.Email.options.response_email.label\": \"Email для відповіді\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"Якщо вимкнуто, користувачі не зможуть вікористати цей провайдер.\",\n  \"PopUpForm.Providers.enabled.label\": \"Увімкнути\",\n  \"PopUpForm.Providers.key.label\": \"Client ID\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL переадресації для вашего front-end додатку\",\n  \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"Редагування шаблони листів\",\n  \"notification.success.submit\": \"Налаштування оновлено\",\n  \"plugin.description.long\": \"Захистіть API за допомогою процесу аутентифікації на основі JWT. Цей плагін також включає можливості ACL, які дозволяють керувати дозволами між групами користувачів.\",\n  \"plugin.description.short\": \"Захистіть API за допомогою процесу аутентифікації на основі JWT\"\n};\nexport {\n  uk as default\n};\n//# sourceMappingURL=uk-BmRqbeQc.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAC9B;", "names": []}