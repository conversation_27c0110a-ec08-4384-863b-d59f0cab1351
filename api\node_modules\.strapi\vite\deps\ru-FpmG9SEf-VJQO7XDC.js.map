{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/ru-FpmG9SEf.mjs"], "sourcesContent": ["const Analytics = \"Аналитика\";\nconst Documentation = \"Документация\";\nconst Email = \"Email\";\nconst Password = \"Пароль\";\nconst Provider = \"Провайдер\";\nconst ResetPasswordToken = \"Сброс токена пароля\";\nconst Role = \"Роль\";\nconst Username = \"Имя пользователя\";\nconst Users = \"Пользователи\";\nconst anErrorOccurred = \"Упс! Что-то пошло не так. Пожалуйста, попробуйте ещё раз.\";\nconst clearLabel = \"Очистить\";\nconst dark = \"Тёмная\";\nconst light = \"Светлая\";\nconst or = \"ИЛИ\";\nconst selectButtonTitle = \"Выбрать\";\nconst skipToContent = \"Перейти к содержимому\";\nconst submit = \"Отправить\";\nconst ru = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Ваш аккаунт был заморожен.\",\n\t\"Auth.components.Oops.text.admin\": \"Если это ошибка, пожалуйста, обратитесь к администратору.\",\n\t\"Auth.components.Oops.title\": \"Упс...\",\n\t\"Auth.form.active.label\": \"Активно\",\n\t\"Auth.form.button.forgot-password\": \"Отправить письмо\",\n\t\"Auth.form.button.go-home\": \"ВЕРНУТЬСЯ НА ГЛАВНУЮ\",\n\t\"Auth.form.button.login\": \"Войти\",\n\t\"Auth.form.button.login.providers.error\": \"Мы не можем подключить вас через выбранного провайдера.\",\n\t\"Auth.form.button.login.strapi\": \"Войти через Strapi\",\n\t\"Auth.form.button.password-recovery\": \"Восстановление пароля\",\n\t\"Auth.form.button.register\": \"Давайте начнём\",\n\t\"Auth.form.confirmPassword.label\": \"Подтверждение пароля\",\n\t\"Auth.form.currentPassword.label\": \"Текущий пароль\",\n\t\"Auth.form.email.label\": \"Email\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Ваш аккаунт был заблокирован администратором.\",\n\t\"Auth.form.error.code.provide\": \"Указан неверный код.\",\n\t\"Auth.form.error.confirmed\": \"Ваш email не подтверждён.\",\n\t\"Auth.form.error.email.invalid\": \"Неправильный email.\",\n\t\"Auth.form.error.email.provide\": \"Укажите своё имя пользователя или email.\",\n\t\"Auth.form.error.email.taken\": \"Данный email уже используется.\",\n\t\"Auth.form.error.invalid\": \"Неверный логин или пароль.\",\n\t\"Auth.form.error.params.provide\": \"Предоставлены неверные параметры.\",\n\t\"Auth.form.error.password.format\": \"Пароль не может содержать символ `$` больше трёх раз.\",\n\t\"Auth.form.error.password.local\": \"Этот пользователь никогда не задавал пароль, пожалуйста, войдите в систему через провайдера, используемого при создании учётной записи.\",\n\t\"Auth.form.error.password.matching\": \"Пароли не совпадают.\",\n\t\"Auth.form.error.password.provide\": \"Укажите свой пароль.\",\n\t\"Auth.form.error.ratelimit\": \"Слишком много попыток, повторите через минуту.\",\n\t\"Auth.form.error.user.not-exist\": \"Этот email не существует.\",\n\t\"Auth.form.error.username.taken\": \"Имя пользователя уже используется.\",\n\t\"Auth.form.firstname.label\": \"Имя\",\n\t\"Auth.form.firstname.placeholder\": \"Иван\",\n\t\"Auth.form.forgot-password.email.label\": \"Введите ваш email\",\n\t\"Auth.form.forgot-password.email.label.success\": \"Письмо успешно отправлено на email\",\n\t\"Auth.form.lastname.label\": \"Фамилия\",\n\t\"Auth.form.lastname.placeholder\": \"Иванов\",\n\t\"Auth.form.password.hide-password\": \"Скрыть пароль\",\n\t\"Auth.form.password.hint\": \"Должно быть не менее 8 символов, минимум 1 прописная буква, 1 строчная буква и 1 цифра\",\n\t\"Auth.form.password.show-password\": \"Показать пароль\",\n\t\"Auth.form.register.news.label\": \"Хочу быть в курсе новых функций и предстоящих улучшений (делая это, вы принимаете {terms} и {policy}).\",\n\t\"Auth.form.register.subtitle\": \"Учётные данные используются только для аутентификации в Strapi. Все сохраненные данные будут храниться в вашей базе данных.\",\n\t\"Auth.form.rememberMe.label\": \"Запомнить меня\",\n\t\"Auth.form.username.label\": \"Имя пользователя\",\n\t\"Auth.form.username.placeholder\": \"Ivan_Ivanov\",\n\t\"Auth.form.welcome.subtitle\": \"Войдите в свою учётную запись Strapi\",\n\t\"Auth.form.welcome.title\": \"Добро пожаловать в Strapi!\",\n\t\"Auth.link.forgot-password\": \"Забыли пароль?\",\n\t\"Auth.link.ready\": \"Готовы войти?\",\n\t\"Auth.link.signin\": \"Войти\",\n\t\"Auth.link.signin.account\": \"Уже есть аккаунт?\",\n\t\"Auth.login.sso.divider\": \"Или войдите в систему с помощью\",\n\t\"Auth.login.sso.loading\": \"Загрузка провайдеров...\",\n\t\"Auth.login.sso.subtitle\": \"Войдите в свою учётную запись через SSO\",\n\t\"Auth.privacy-policy-agreement.policy\": \"Политику конфиденциальности\",\n\t\"Auth.privacy-policy-agreement.terms\": \"Условия использования\",\n\t\"Auth.reset-password.title\": \"Сброс пароля\",\n\t\"Content Manager\": \"Редактор контента\",\n\t\"Content Type Builder\": \"Конструктор типов содержимого\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Загрузка файлов\",\n\t\"HomePage.head.title\": \"Домашняя страница\",\n\t\"HomePage.roadmap\": \"Смотрите нашу дорожную карту\",\n\t\"HomePage.welcome.congrats\": \"Поздравляем!\",\n\t\"HomePage.welcome.congrats.content\": \"Вы вошли как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"мы рекомендуем вам создать свой первый тип коллекции.\",\n\t\"Media Library\": \"Библиотека медиа\",\n\t\"New entry\": \"Новая запись\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Роли и Разрешения\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Некоторые роли нельзя было удалить, так как они связаны с пользователями\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Невозможно удалить роль, если она связана с пользователями\",\n\t\"Roles.RoleRow.select-all\": \"Выберите {name} для групповых действий\",\n\t\"Roles.RoleRow.user-count\": \"{number, plural, =0{# пользователей} one{# пользователь} few{# пользователя} many {# пользователей}}\",\n\t\"Roles.components.List.empty.withSearch\": \"Нет роли, соответствующей поиску ({search})...\",\n\t\"Settings.PageTitle\": \"Настройки — {name}\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"Cоздан в\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"Описание\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Последнее использование\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"Название\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"Тип токена\",\n\t\"Settings.apiTokens.addFirstToken\": \"Добавьте свой первый API-токен\",\n\t\"Settings.apiTokens.addNewToken\": \"Добавить новый API-токен\",\n\t\"Settings.apiTokens.create\": \"Создайте новый API-токен\",\n\t\"Settings.apiTokens.createPage.BoundRoute.title\": \"Связанный маршрут к\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"Ниже перечислены только действия связанные с маршрутом.\",\n\t\"Settings.apiTokens.createPage.permissions.header.hint\": \"Выберите действия для приложения или плагина и нажмите на значок шестеренки, чтобы отобразить связанный маршрут\",\n\t\"Settings.apiTokens.createPage.permissions.header.title\": \"Дополнительные настройки\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"Разрешения\",\n\t\"Settings.apiTokens.createPage.title\": \"Создать API-токен\",\n\t\"Settings.apiTokens.description\": \"Список сгенерированных токенов для использования API\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"У вас ещё нет контента...\",\n\t\"Settings.apiTokens.lastHour\": \"последний час\",\n\t\"Settings.apiTokens.regenerate\": \"Перегенерировать\",\n\t\"Settings.apiTokens.title\": \"API-токены\",\n\t\"Settings.application.customization\": \"Персонализация\",\n\t\"Settings.application.customization.auth-logo.carousel-hint\": \"Заменить логотип на страницах аутентификации\",\n\t\"Settings.application.customization.carousel-hint\": \"Изменить логотип панели администратора (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)\",\n\t\"Settings.application.customization.carousel-slide.label\": \"Слайд с логотипом\",\n\t\"Settings.application.customization.carousel.auth-logo.title\": \"Логотип на странице авторизации\",\n\t\"Settings.application.customization.carousel.change-action\": \"Изменить логотип\",\n\t\"Settings.application.customization.carousel.menu-logo.title\": \"Логотип в меню\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Сброс логотипа\",\n\t\"Settings.application.customization.carousel.title\": \"Логотип\",\n\t\"Settings.application.customization.menu-logo.carousel-hint\": \"Замените логотип в главном меню\",\n\t\"Settings.application.customization.modal.cancel\": \"Отмена\",\n\t\"Settings.application.customization.modal.pending\": \"Ожидание логотипа\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"изображение\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Выберите другой логотип\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Управление выбранным логотипом перед его загрузкой\",\n\t\"Settings.application.customization.modal.pending.title\": \"Логотип готов к загрузке\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Загрузить логотип\",\n\t\"Settings.application.customization.modal.tab.label\": \"Как вы хотите загрузить свои ассеты?\",\n\t\"Settings.application.customization.modal.upload\": \"Загрузить логотип\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Просмотр файлов\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Перетащите сюда или\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Загружен неправильный формат (разрешены только форматы: jpeg, jpg, png, svg).\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Ошибка сети\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"Загруженный файл слишком большой (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"Максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"С компьютера\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"По ссылке\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"Далее\",\n\t\"Settings.application.customization.size-details\": \"Максимальное разрешение: {dimension}×{dimension}, максимальный размер файла: {size}KB\",\n\t\"Settings.application.description\": \"Глобальная информация панели администратора\",\n\t\"Settings.application.edition-title\": \"Текущий план\",\n\t\"Settings.application.ee-or-ce\": \"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}\",\n\t\"Settings.application.ee.admin-seats.add-seats\": \"{isHostedOnStrapiCloud, select, true {Добавить места} other {Обратитесь в отдел продаж}}\",\n\t\"Settings.application.ee.admin-seats.at-limit-tooltip\": \"При исчерпании лимита: добавьте места, чтобы пригласить больше пользователей\",\n\t\"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n\t\"Settings.application.get-help\": \"Получить помощь\",\n\t\"Settings.application.link-pricing\": \"Посмотреть все тарифы\",\n\t\"Settings.application.link-upgrade\": \"Обновить ваше приложение\",\n\t\"Settings.application.node-version\": \"Версия Node\",\n\t\"Settings.application.strapi-version\": \"Версия Strapi\",\n\t\"Settings.application.strapiVersion\": \"Версия Strapi\",\n\t\"Settings.application.title\": \"Обзор\",\n\t\"Settings.error\": \"Ошибка\",\n\t\"Settings.global\": \"Глобальные Настройки\",\n\t\"Settings.permissions\": \"Панель администратора\",\n\t\"Settings.permissions.auditLogs.action\": \"Действие\",\n\t\"Settings.permissions.auditLogs.admin.auth.success\": \"Вход администратора\",\n\t\"Settings.permissions.auditLogs.admin.logout\": \"Выход администратора\",\n\t\"Settings.permissions.auditLogs.component.create\": \"Создать компонент\",\n\t\"Settings.permissions.auditLogs.component.delete\": \"Удалить компонент\",\n\t\"Settings.permissions.auditLogs.component.update\": \"Обновить компонент\",\n\t\"Settings.permissions.auditLogs.content-type.create\": \"Создать тип контента\",\n\t\"Settings.permissions.auditLogs.content-type.delete\": \"Удалить тип контента\",\n\t\"Settings.permissions.auditLogs.content-type.update\": \"Обновить тип контента\",\n\t\"Settings.permissions.auditLogs.date\": \"Дата\",\n\t\"Settings.permissions.auditLogs.details\": \"Детали лога\",\n\t\"Settings.permissions.auditLogs.entry.create\": \"Создать {model, select, undefined {} other { ({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.delete\": \"Удалить {model, select, undefined {} other { ({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.publish\": \"Опубликовать {model, select, undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.unpublish\": \"Сделать непубличным {model, select, undefined {} other { ({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.update\": \"Обновить {model, select, undefined {} other { ({model})}}\",\n\t\"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"Выполните поиск и выберите вариант для фильтрации\",\n\t\"Settings.permissions.auditLogs.listview.header.subtitle\": \"Журналы всех действий, которые произошли в вашей среде\",\n\t\"Settings.permissions.auditLogs.media.create\": \"Создать медиафайл\",\n\t\"Settings.permissions.auditLogs.media.delete\": \"Удалить медиафайл\",\n\t\"Settings.permissions.auditLogs.media.update\": \"Обновить медиафайл\",\n\t\"Settings.permissions.auditLogs.payload\": \"Полезная нагрузка\",\n\t\"Settings.permissions.auditLogs.permission.create\": \"Создать разрешение\",\n\t\"Settings.permissions.auditLogs.permission.delete\": \"Удалить разрешение\",\n\t\"Settings.permissions.auditLogs.permission.update\": \"Обновить разрешение\",\n\t\"Settings.permissions.auditLogs.role.create\": \"Создать роль\",\n\t\"Settings.permissions.auditLogs.role.delete\": \"Удалить роль\",\n\t\"Settings.permissions.auditLogs.role.update\": \"Обновить роль\",\n\t\"Settings.permissions.auditLogs.user\": \"Пользователь\",\n\t\"Settings.permissions.auditLogs.user.create\": \"Создать пользователя\",\n\t\"Settings.permissions.auditLogs.user.delete\": \"Удалить пользователя\",\n\t\"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n\t\"Settings.permissions.auditLogs.user.update\": \"Обновить пользователя\",\n\t\"Settings.permissions.auditLogs.userId\": \"ID пользователя\",\n\t\"Settings.permissions.category\": \"Настройки разрешений для {category}\",\n\t\"Settings.permissions.category.plugins\": \"Настройки разрешений для плагина {category}\",\n\t\"Settings.permissions.conditions.anytime\": \"Всегда\",\n\t\"Settings.permissions.conditions.apply\": \"Применить\",\n\t\"Settings.permissions.conditions.can\": \"Можно\",\n\t\"Settings.permissions.conditions.conditions\": \"Условия\",\n\t\"Settings.permissions.conditions.define-conditions\": \"Определить условия\",\n\t\"Settings.permissions.conditions.links\": \"Ссылки\",\n\t\"Settings.permissions.conditions.no-actions\": \"Сначала вам нужно выбрать действия (создать, прочитать, обновить, ...), прежде чем определять условия для них.\",\n\t\"Settings.permissions.conditions.none-selected\": \"Любое время\",\n\t\"Settings.permissions.conditions.or\": \"ИЛИ\",\n\t\"Settings.permissions.conditions.when\": \"Когда\",\n\t\"Settings.permissions.select-all-by-permission\": \"Выбрать все разрешения {label}\",\n\t\"Settings.permissions.select-by-permission\": \"Выберите разрешения {label}\",\n\t\"Settings.permissions.users.active\": \"Активен\",\n\t\"Settings.permissions.users.create\": \"Создать нового пользователя\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Имя\",\n\t\"Settings.permissions.users.form.sso\": \"Соединить с SSO\",\n\t\"Settings.permissions.users.form.sso.description\": \"Когда включено, пользователи смогут входить через SSO\",\n\t\"Settings.permissions.users.inactive\": \"Неактивен\",\n\t\"Settings.permissions.users.lastname\": \"Фамилия\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Все пользователи, имеющие доступ к панели администратора Strapi\",\n\t\"Settings.permissions.users.roles\": \"Роли\",\n\t\"Settings.permissions.users.sso.provider.error\": \"Произошла ошибка при запросе настроек единого входа (SSO)\",\n\t\"Settings.permissions.users.strapi-author\": \"Автор\",\n\t\"Settings.permissions.users.strapi-editor\": \"Редактор\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Супер-администратор\",\n\t\"Settings.permissions.users.tabs.label\": \"Вкладки Разрешения\",\n\t\"Settings.permissions.users.user-status\": \"Статус пользователя\",\n\t\"Settings.permissions.users.username\": \"Имя пользователя\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Данные вашего профиля загружены\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Очистить выбранный язык интерфейса\",\n\t\"Settings.profile.form.section.experience.here\": \"здесь\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Язык интерфейса\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Только ваш интерфейс будет отображаться на выбранном языке.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Изменения предпочтений будут касаться только вас. Дополнительная информация доступна {here}.\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Отображает ваш интерфейс в выбранной теме.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Тема интерфейса\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} тема\",\n\t\"Settings.profile.form.section.experience.title\": \"Опыт\",\n\t\"Settings.profile.form.section.head.title\": \"Профиль пользователя\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Страница профиля\",\n\t\"Settings.roles.create.description\": \"Определите права, предоставленные ролью\",\n\t\"Settings.roles.create.title\": \"Создать роль\",\n\t\"Settings.roles.created\": \"Роль создана\",\n\t\"Settings.roles.edit.title\": \"Изменить роль\",\n\t\"Settings.roles.form.button.users-with-role\": \"Пользователи с этой ролью\",\n\t\"Settings.roles.form.created\": \"Создано\",\n\t\"Settings.roles.form.description\": \"Название и описание роли\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} разрешения\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Разрешения полей\",\n\t\"Settings.roles.form.permissions.create\": \"Создать\",\n\t\"Settings.roles.form.permissions.delete\": \"Удалить\",\n\t\"Settings.roles.form.permissions.publish\": \"Опубликовать\",\n\t\"Settings.roles.form.permissions.read\": \"Прочесть\",\n\t\"Settings.roles.form.permissions.update\": \"Обновить\",\n\t\"Settings.roles.list.button.add\": \"Добавить новую роль\",\n\t\"Settings.roles.list.description\": \"Список ролей\",\n\t\"Settings.roles.title.singular\": \"роль\",\n\t\"Settings.sso.description\": \"Настройте параметры для функции единого входа.\",\n\t\"Settings.sso.form.defaultRole.description\": \"Присоединит нового аутентифицированного пользователя к выбранной роли\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"У вас должно быть разрешение на чтение ролей администратора\",\n\t\"Settings.sso.form.defaultRole.label\": \"Роль по умолчанию\",\n\t\"Settings.sso.form.localAuthenticationLock.description\": \"Выберите роли, для которых вы хотите отключить локальную проверку аутентификацию\",\n\t\"Settings.sso.form.localAuthenticationLock.label\": \"Блокировать локальную аутентификацию\",\n\t\"Settings.sso.form.registration.description\": \"Создать нового пользователя при входе через SSO, если учётной записи нет\",\n\t\"Settings.sso.form.registration.label\": \"Авто-регистрация\",\n\t\"Settings.sso.title\": \"Функция единого входа\",\n\t\"Settings.tokens.Button.cancel\": \"Отмена\",\n\t\"Settings.tokens.Button.regenerate\": \"Восстановить\",\n\t\"Settings.tokens.ListView.headers.createdAt\": \"Создан\",\n\t\"Settings.tokens.ListView.headers.description\": \"Описание\",\n\t\"Settings.tokens.ListView.headers.lastUsedAt\": \"Последний раз использовался\",\n\t\"Settings.tokens.ListView.headers.name\": \"Название\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"Восстановить токен\",\n\t\"Settings.tokens.copy.editMessage\": \"Из соображений безопасности вы можете увидеть свой токен только один раз.\",\n\t\"Settings.tokens.copy.editTitle\": \"Этот токен больше не доступен.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Обязательно скопируйте этот токен, вы больше не сможете его увидеть!\",\n\t\"Settings.tokens.duration.30-days\": \"30 дней\",\n\t\"Settings.tokens.duration.7-days\": \"7 дней\",\n\t\"Settings.tokens.duration.90-days\": \"90 дней\",\n\t\"Settings.tokens.duration.expiration-date\": \"Срок действия\",\n\t\"Settings.tokens.duration.unlimited\": \"Неограниченный\",\n\t\"Settings.tokens.form.description\": \"Описание\",\n\t\"Settings.tokens.form.duration\": \"Срок действия токена\",\n\t\"Settings.tokens.form.name\": \"Название\",\n\t\"Settings.tokens.form.type\": \"Тип токена\",\n\t\"Settings.tokens.notification.copied\": \"Токен скопирован в буфер обмена.\",\n\t\"Settings.tokens.popUpWarning.message\": \"Вы уверены, что хотите восстановить этот токен?\",\n\t\"Settings.tokens.regenerate\": \"Перегенерировать\",\n\t\"Settings.tokens.types.custom\": \"Пользовательский тип\",\n\t\"Settings.tokens.types.full-access\": \"Полный доступ\",\n\t\"Settings.tokens.types.read-only\": \"Только для чтения\",\n\t\"Settings.transferTokens.ListView.headers.type\": \"Тип токена\",\n\t\"Settings.transferTokens.addFirstToken\": \"Добавьте свой первый токен для передачи\",\n\t\"Settings.transferTokens.addNewToken\": \"Добавить новый токен для передачи\",\n\t\"Settings.transferTokens.create\": \"Создать новый токен для передачи\",\n\t\"Settings.transferTokens.createPage.title\": \"Создать передачу токена\",\n\t\"Settings.transferTokens.description\": \"Список сгенерированных токенов для передачи\",\n\t\"Settings.transferTokens.emptyStateLayout\": \"У вас ещё нет никакого контента...\",\n\t\"Settings.transferTokens.title\": \"Передача токенов\",\n\t\"Settings.webhooks.create\": \"Создание webhook`а\",\n\t\"Settings.webhooks.create.header\": \"Создание нового заголовка\",\n\t\"Settings.webhooks.created\": \"Webhook создан\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Это событие существует только для содержимого с включенной системой Черновиков/Публикаций\",\n\t\"Settings.webhooks.event.select\": \"Выберите событие\",\n\t\"Settings.webhooks.events.create\": \"Создание\",\n\t\"Settings.webhooks.events.delete\": \"Удалить вебхук\",\n\t\"Settings.webhooks.events.isLoading\": \"Загрузка событий\",\n\t\"Settings.webhooks.events.update\": \"Обновление\",\n\t\"Settings.webhooks.form.events\": \"События\",\n\t\"Settings.webhooks.form.headers\": \"Заголовки\",\n\t\"Settings.webhooks.form.url\": \"URL\",\n\t\"Settings.webhooks.headers.remove\": \"Удалить строку заголовка {number}\",\n\t\"Settings.webhooks.key\": \"Ключ\",\n\t\"Settings.webhooks.list.button.add\": \"Добавить новый webhook\",\n\t\"Settings.webhooks.list.description\": \"Уведомления с помощью POST событий\",\n\t\"Settings.webhooks.list.empty.description\": \"Добавить первый в этот список\",\n\t\"Settings.webhooks.list.empty.link\": \"Просмотреть документацию\",\n\t\"Settings.webhooks.list.empty.title\": \"Пока ещё нет ни одного webhook'а\",\n\t\"Settings.webhooks.list.loading.success\": \"Вебхуки были загружены\",\n\t\"Settings.webhooks.list.th.actions\": \"действия\",\n\t\"Settings.webhooks.list.th.status\": \"статус\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhook'и\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, =0{# ассетов} one{# ассет} few{# ассета} many {# ассетов}} выбраны\",\n\t\"Settings.webhooks.trigger\": \"Триггер\",\n\t\"Settings.webhooks.trigger.cancel\": \"Отмена триггера\",\n\t\"Settings.webhooks.trigger.pending\": \"Ожидание…\",\n\t\"Settings.webhooks.trigger.save\": \"Пожалуйста сохраните триггер\",\n\t\"Settings.webhooks.trigger.success\": \"Успех!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Триггер выполнен\",\n\t\"Settings.webhooks.trigger.test\": \"Тест триггер\",\n\t\"Settings.webhooks.trigger.title\": \"Сохранить перед триггером\",\n\t\"Settings.webhooks.validation.key\": \"Ключ обязателен\",\n\t\"Settings.webhooks.validation.name.regex\": \"Имя должно начинаться с буквы и содержать только буквы, цифры, пробелы и подчеркивания\",\n\t\"Settings.webhooks.validation.name.required\": \"Имя обязательно\",\n\t\"Settings.webhooks.validation.url.regex\": \"Значение должно быть допустимым URL-адресом\",\n\t\"Settings.webhooks.validation.url.required\": \"URL-адрес обязателен\",\n\t\"Settings.webhooks.validation.value\": \"Значение обязательно\",\n\t\"Settings.webhooks.value\": \"Значение\",\n\t\"Usecase.back-end\": \"Back-end разработчик\",\n\t\"Usecase.button.skip\": \"Пропустить этот вопрос\",\n\t\"Usecase.content-creator\": \"Создатель контента\",\n\t\"Usecase.front-end\": \"Front-end разработчик\",\n\t\"Usecase.full-stack\": \"Full-stack разработчик\",\n\t\"Usecase.input.work-type\": \"Какой тип работы вы выполняете?\",\n\t\"Usecase.notification.success.project-created\": \"Проект успешно создан\",\n\t\"Usecase.other\": \"Другое\",\n\t\"Usecase.title\": \"Расскажите нам немного больше о себе\",\n\tUsername: Username,\n\t\"Users & Permissions\": \"Пользователи и Разрешения\",\n\tUsers: Users,\n\t\"Users.components.List.empty\": \"Нет пользователей...\",\n\t\"Users.components.List.empty.withFilters\": \"Нет пользователей с применёнными фильтрами...\",\n\t\"Users.components.List.empty.withSearch\": \"Нет пользователей, соответствующих запросу ({search})...\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"Категории\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"Выбрано {count, plural, =0{# категорий} one{# категория} few{# категории} many {# категорий}}\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"Коллекции\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"Выбрано {count, plural, =0{# Коллекций} one{# Коллекция} few{# Коллекции} many {# Коллекций}}\",\n\t\"admin.pages.MarketPlacePage.head\": \"Маркет плагинов — Плагины\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Расскажите нам, какой плагин вы ищете, и мы сообщим об этом разработчикам плагинов нашего сообщества на случай, если они ищут вдохновение!\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Вам не хватает плагина?\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Для доступа к Маркету Strapi необходимо подключение к Интернету.\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Вы не в сети\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Скопируйте команду установки\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"Команда установки готова к вставке в терминал\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"Этот плагин был загружен {downloadsCount} раз(-а) за неделю\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"Этот плагин имеет {starsCount} звёзд на GitHub\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"Узнать больше\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"Узнать больше про {pluginName}\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"Узнать больше\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Установлено\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Сделано Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Плагин проверен командой Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"Обновите вашу версию Strapi: \\\"{strapiAppVersion}\\\" до: \\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"Невозможно проверить совместимость с вашей версией Strapi: \\\"{strapiAppVersion}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"Плагины\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"Этот провайдер был загружен {downloadsCount} раз(-а) за неделю\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"Этот провайдер имеет {starsCount} звёзд на GitHub\",\n\t\"admin.pages.MarketPlacePage.providers\": \"Провайдеры\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Очистить поиск\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"Нет результатов для \\\"{target}\\\"\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Найти плагин\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"По алфавиту\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Сортировать по алфавиту\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars\": \"По звёздам на GitHub\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Сортировать по звёздам на GitHub\",\n\t\"admin.pages.MarketPlacePage.sort.label\": \"Сортировка\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"Новые\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"Сортировать по новейшим\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Количество загрузок\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Сортировать по npm загрузкам\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Отправить плагин\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"Отправить провайдера\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Получите больше от Strapi\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Плагины и провайдеры для Strapi\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Копировать в буфер обмена\",\n\t\"app.component.search.label\": \"Искать {target}\",\n\t\"app.component.table.duplicate\": \"Дубликат {target}\",\n\t\"app.component.table.edit\": \"Редактировать {target}\",\n\t\"app.component.table.read\": \"Читать {target}\",\n\t\"app.component.table.select.one-entry\": \"Выбрать {target}\",\n\t\"app.component.table.view\": \"{target} детали\",\n\t\"app.components.BlockLink.blog\": \"Блог\",\n\t\"app.components.BlockLink.blog.content\": \"Читайте последние новости о Strapi и экосистеме.\",\n\t\"app.components.BlockLink.cloud\": \"Strapi Cloud\",\n\t\"app.components.BlockLink.cloud.content\": \"Полностью настраиваемая платформа для совместной работы, повышающая скорость работы вашей команды.\",\n\t\"app.components.BlockLink.code\": \"Примеры кода\",\n\t\"app.components.BlockLink.code.content\": \"Учитесь, тестируя реальные проекты, разработанные сообществом.\",\n\t\"app.components.BlockLink.documentation.content\": \"Откройте для себя основные понятия, руководства и инструкции.\",\n\t\"app.components.BlockLink.tutorial\": \"Учебные материалы\",\n\t\"app.components.BlockLink.tutorial.content\": \"Следуйте пошаговым инструкциям по использованию и настройке Strapi.\",\n\t\"app.components.Button.cancel\": \"Отменить\",\n\t\"app.components.Button.confirm\": \"Подтвердить\",\n\t\"app.components.Button.reset\": \"Сброс\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Скоро\",\n\t\"app.components.ConfirmDialog.title\": \"Подтверждение\",\n\t\"app.components.DownloadInfo.download\": \"Выполняется загрузка...\",\n\t\"app.components.DownloadInfo.text\": \"Это может занять около минуты. Спасибо за ваше терпение.\",\n\t\"app.components.EmptyAttributes.title\": \"Пока нет полей\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Данные не найдены\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"У вас нет прав доступа к этому содержимому\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Создавайте и управляйте всем содержимым здесь, в Редакторе контента.</p><p>Например, если взять пример с сайтом-блогом, можно написать статью, сохранить и опубликовать её по своему усмотрению.</p><p>💡 Краткий совет — не забудьте нажать опубликовать для публикации созданного вами контента.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ Создавайте контент\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Потрясающе, остался последний шаг!</p><b>🚀 Посмотрите как в итоге выглядит контент</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"Протестируйте API\",\n\t\"app.components.GuidedTour.CM.success.title\": \"Шаг 2: Завершено ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>Типы \\\"Коллекция\\\" помогают управлять несколькими записями, типы \\\"Одиночная запись\\\" подходят для управления только одной записью.</p> <p>Например: Для сайта-блога статьи будут типом \\\"Коллекция\\\", а домашняя страница — типом \\\"Одиночная запись\\\".</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Создайте тип записей Коллекция\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 Создайте первый тип записей Коллекция\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>Так держать!</p><b>⚡️ Чем бы вы хотели поделиться с миром?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"Шаг 1: Завершено ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Сгенерируйте здесь токен аутентификации и получите только что созданный контент.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"Сгенерируйте API-токен\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Посмотрите на контент в действии\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Посмотрите содержимое в действии, сделав HTTP-запрос:</p><ul><li><p>По этому URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>С заголовком: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>О дополнительных способах взаимодействия с контентом смотрите в <documentationLink>документации</documentationLink>.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Вернуться на главную страницу\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"Шаг 3: Завершено ✅\",\n\t\"app.components.GuidedTour.create-content\": \"Создавайте контент\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ Чем бы вы хотели поделиться с миром?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Перейти к Редактору типов контента\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 Постройте структуру контента\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"Протестируйте API\",\n\t\"app.components.GuidedTour.skip\": \"Пропустить тур\",\n\t\"app.components.GuidedTour.title\": \"3 шага для начала работы\",\n\t\"app.components.HomePage.button.blog\": \"Смотрите больше в нашем блоге\",\n\t\"app.components.HomePage.community\": \"Присоединиться к нашему сообществу\",\n\t\"app.components.HomePage.community.content\": \"Участвуйте в обсуждениях с членами команды и разработчиками.\",\n\t\"app.components.HomePage.create\": \"Создайте свой первый тип контента\",\n\t\"app.components.HomePage.roadmap\": \"Ознакомьтесь с нашей дорожной картой\",\n\t\"app.components.HomePage.welcome\": \"Добро пожаловать на борт 👋\",\n\t\"app.components.HomePage.welcome.again\": \"Добро пожаловать 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Поздравляем! Вы вошли в систему как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi, мы рекомендуем вам создать свой первый тип контента!\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Надеемся, что вы делаете успехи в вашем проекте... Следите за последними новостями о Strapi. Мы стараемся изо всех сил, чтобы улучшить продукт, основываясь на ваших пожеланиях.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"проблемах.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" или сообщать о \",\n\t\"app.components.ImgPreview.hint\": \"Перетащите файл в эту область или {browse} файл для загрузки\",\n\t\"app.components.ImgPreview.hint.browse\": \"выберите\",\n\t\"app.components.InputFile.newFile\": \"Добавить новый файл\",\n\t\"app.components.InputFileDetails.open\": \"Открыть в новой вкладке\",\n\t\"app.components.InputFileDetails.originalName\": \"Оригинальное название:\",\n\t\"app.components.InputFileDetails.remove\": \"Удалить этот файл\",\n\t\"app.components.InputFileDetails.size\": \"Размер:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Для загрузки и установки плагина может потребоваться несколько секунд.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Загрузка...\",\n\t\"app.components.InstallPluginPage.description\": \"Расширяйте ваше приложение без особых усилий.\",\n\t\"app.components.LeftMenu.collapse\": \"Свернуть панель навигации\",\n\t\"app.components.LeftMenu.expand\": \"Развернуть панель навигации\",\n\t\"app.components.LeftMenu.general\": \"Общее\",\n\t\"app.components.LeftMenu.logo.alt\": \"Логотип приложения\",\n\t\"app.components.LeftMenu.logout\": \"Выйти\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Панель управления Strapi\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Рабочая область\",\n\t\"app.components.LeftMenu.plugins\": \"Плагины\",\n\t\"app.components.LeftMenuFooter.help\": \"Помощь\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Работает на \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Типы Коллекций\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Настройки\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Общие\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Нет установленных плагинов\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Плагины\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Страницы\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Удаление плагина может занять несколько секунд.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Удаление\",\n\t\"app.components.ListPluginsPage.description\": \"Список установленных плагинов в проекте.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Список плагинов\",\n\t\"app.components.Logout.logout\": \"Выйти\",\n\t\"app.components.Logout.profile\": \"Профиль\",\n\t\"app.components.MarketplaceBanner\": \"На Маркете Strapi вы найдете плагины, созданные сообществом, и множество других удивительных вещей для запуска вашего проекта.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"A Strapi rocket logo\",\n\t\"app.components.MarketplaceBanner.link\": \"Посмотреть\",\n\t\"app.components.NotFoundPage.back\": \"Вернуться на главную\",\n\t\"app.components.NotFoundPage.description\": \"Не найдено\",\n\t\"app.components.NpsSurvey.banner-title\": \"Насколько велика вероятность того, что вы порекомендуете Strapi другу или коллеге?\",\n\t\"app.components.NpsSurvey.dismiss-survey-label\": \"Отказаться от опроса\",\n\t\"app.components.NpsSurvey.feedback-question\": \"Есть ли у вас какие-либо предложения по улучшению?\",\n\t\"app.components.NpsSurvey.feedback-response\": \"Большое вам спасибо за ваш отзыв!\",\n\t\"app.components.NpsSurvey.happy-to-recommend\": \"Чрезвычайно вероятно\",\n\t\"app.components.NpsSurvey.no-recommendation\": \"Совсем маловероятно\",\n\t\"app.components.NpsSurvey.submit-feedback\": \"Отправить отзыв\",\n\t\"app.components.Official\": \"Официальный\",\n\t\"app.components.Onboarding.help.button\": \"Кнопка помощь\",\n\t\"app.components.Onboarding.label.completed\": \"% завершено\",\n\t\"app.components.Onboarding.link.build-content\": \"Создайте архитектуру контента\",\n\t\"app.components.Onboarding.link.manage-content\": \"Добавляйте и управляйте контентом\",\n\t\"app.components.Onboarding.link.manage-media\": \"Управляйте медиа\",\n\t\"app.components.Onboarding.link.more-videos\": \"Смотреть больше видео\",\n\t\"app.components.Onboarding.title\": \"Смотреть видео о начале работы\",\n\t\"app.components.PluginCard.Button.label.download\": \"Скачать\",\n\t\"app.components.PluginCard.Button.label.install\": \"Уже установлено\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Функция autoReload (автоматической перезагрузки) должна быть включена. Пожалуйста, запустите ваше приложение с помощью `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Я понимаю!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"В целях безопасности плагин может быть загружен только в среде разработки.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Загрузка невозможна\",\n\t\"app.components.PluginCard.compatible\": \"Совместимо с вашим приложением\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Совместимо с сообществом\",\n\t\"app.components.PluginCard.more-details\": \"Больше деталей\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Нет\",\n\t\"app.components.ToggleCheckbox.on-label\": \"Да\",\n\t\"app.components.Users.MagicLink.connect\": \"Отправьте эту ссылку пользователю, чтобы предоставить ему доступ\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Отправьте эту ссылку пользователю, первый вход может быть осуществлен через провайдера SSO\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Детали\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Роли пользователя\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Пользователь может иметь одну или несколько ролей\",\n\t\"app.components.Users.SortPicker.button-label\": \"Сортировать по\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"Электронная почта (от А до Я)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"Электронная почта (от Я до А)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Имя (от А до Я)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Имя (от Я до А)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Фамилия (от А до Я)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Фамилия (от Я до А)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Имя пользователя (от А до Я)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Имя пользователя (от Я до А)\",\n\t\"app.components.listPlugins.button\": \"Добавить новый плагин\",\n\t\"app.components.listPlugins.title.none\": \"Нет установленных плагинов\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Произошла ошибка при удалении плагина\",\n\t\"app.containers.App.notification.error.init\": \"Произошла ошибка при запросе к API\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Если вы не получили эту ссылку, обратитесь к администратору.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Получение ссылки для восстановления пароля может занять несколько минут...\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Письмо отправлено\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Активный\",\n\t\"app.containers.Users.EditPage.header.label\": \"Редактировать {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Редактировать пользователя\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Атрибуты ролей\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Создать пользователя\",\n\t\"app.links.configure-view\": \"Настройка представления\",\n\t\"app.page.not.found\": \"Упс! Мы не можем найти страницу, которую вы ищете...\",\n\t\"app.static.links.cheatsheet\": \"Шпаргалка\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Добавить фильтр\",\n\t\"app.utils.close-label\": \"Закрыть\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.delete\": \"Удалить\",\n\t\"app.utils.duplicate\": \"Дублировать\",\n\t\"app.utils.edit\": \"Редактировать\",\n\t\"app.utils.errors.file-too-big.message\": \"Файл слишком большой\",\n\t\"app.utils.filter-value\": \"Значение фильтра\",\n\t\"app.utils.filters\": \"Фильтры\",\n\t\"app.utils.notify.data-loaded\": \"{target} загружена\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Опубликовать\",\n\t\"app.utils.published\": \"Опубликовано\",\n\t\"app.utils.ready-to-publish\": \"Готово к публикации\",\n\t\"app.utils.refresh\": \"Обновить\",\n\t\"app.utils.select-all\": \"Выбрать все\",\n\t\"app.utils.select-field\": \"Выберите поле\",\n\t\"app.utils.select-filter\": \"Выберите фильтр\",\n\t\"app.utils.unpublish\": \"Отменить публикацию\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Содержимое находится в стадии разработки и через несколько недель оно будет возвращено!\",\n\t\"component.Input.error.validation.integer\": \"Значение должно быть целочисленным\",\n\t\"components.AutoReloadBlocker.description\": \"Запустите Strapi с помощью одной из следующих команд:\",\n\t\"components.AutoReloadBlocker.header\": \"Для этого плагина требуется функция перезагрузки.\",\n\t\"components.ErrorBoundary.title\": \"Что-то пошло не так...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"содержит\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"содержит (без учета регистра)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"заканчивается на\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"заканчивается на (без учета регистра)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"равно\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"равно (без учета регистра)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"больше, чем\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"больше или равно\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"меньше, чем\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"меньше или равно\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"не\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"не (без учета регистра)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"не содержит\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"не содержит (без учета регистра)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"задано\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"не задано\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"начинается с\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"начинается с (без учета регистра)\",\n\t\"components.Input.error.attribute.key.taken\": \"Это значение уже существует\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Не может быть одинаковым\",\n\t\"components.Input.error.attribute.taken\": \"Поле с таким названием уже существует\",\n\t\"components.Input.error.contain.lowercase\": \"Пароль должен содержать хотя бы одну прописную букву\",\n\t\"components.Input.error.contain.number\": \"Пароль должен содержать хотя бы одну цифру\",\n\t\"components.Input.error.contain.uppercase\": \"Пароль должен содержать хотя бы одну заглавную букву\",\n\t\"components.Input.error.contentTypeName.taken\": \"Это название уже существует\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Пароли не совпадают\",\n\t\"components.Input.error.validation.email\": \"Неправильный или написанный с ошибкой email\",\n\t\"components.Input.error.validation.email.withField\": \"Поле {field} — содержит неверный адрес электронной почты\",\n\t\"components.Input.error.validation.json\": \"Не соответствует JSON формату\",\n\t\"components.Input.error.validation.json.withField\": \"Поле {field} — не соответствует формату JSON\",\n\t\"components.Input.error.validation.lowercase\": \"Значение должно быть строкой в нижнем регистре\",\n\t\"components.Input.error.validation.lowercase.withField\": \"Поле {field} — должно быть строкой в нижнем регистре (без заглавных букв)\",\n\t\"components.Input.error.validation.max\": \"Значение слишком большое.\",\n\t\"components.Input.error.validation.max.withField\": \"Поле {field} — слишком велико.\",\n\t\"components.Input.error.validation.maxLength\": \"Значение слишком длинное.\",\n\t\"components.Input.error.validation.maxLength.withField\": \"Поле {field} — слишком длинное.\",\n\t\"components.Input.error.validation.min\": \"Значение слишком маленькое.\",\n\t\"components.Input.error.validation.min.withField\": \"Поле {field} — слишком мало.\",\n\t\"components.Input.error.validation.minLength\": \"Значение слишком короткое.\",\n\t\"components.Input.error.validation.minLength.withField\": \"Поле {field} — слишком короткое.\",\n\t\"components.Input.error.validation.minSupMax\": \"Не может быть больше\",\n\t\"components.Input.error.validation.minSupMax.withField\": \"Поле {field} — не может быть выше\",\n\t\"components.Input.error.validation.regex\": \"Значение не соответствует регулярному выражению.\",\n\t\"components.Input.error.validation.regex.withField\": \"Поле {field} — не соответствует формату регулярных выражений.\",\n\t\"components.Input.error.validation.required\": \"Обязательное значение.\",\n\t\"components.Input.error.validation.required.withField\": \"Поле {field} — обязательное.\",\n\t\"components.Input.error.validation.unique\": \"Это значение уже используется.\",\n\t\"components.Input.error.validation.unique.withField\": \"Поле {field} — уже используется.\",\n\t\"components.InputSelect.option.placeholder\": \"Выберите здесь\",\n\t\"components.ListRow.empty\": \"Нет данных для отображения.\",\n\t\"components.NotAllowedInput.text\": \"Нет прав на просмотр этого поля\",\n\t\"components.OverlayBlocker.description\": \"Вы используете функцию, для которой требуется перезагрузка сервера. Пожалуйста, подождите, пока сервер не перезагрузится.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Сервер должен был перезагрузиться, пожалуйста, проверьте ваши логи в терминале.\",\n\t\"components.OverlayBlocker.title\": \"Ожидание перезагрузки...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Перезагрузка занимает больше времени, чем ожидалось\",\n\t\"components.PageFooter.select\": \"записей на странице\",\n\t\"components.ProductionBlocker.description\": \"В целях безопасности мы должны отключить этот плагин в других средах.\",\n\t\"components.ProductionBlocker.header\": \"Этот плагин доступен только на стадии разработки.\",\n\t\"components.Search.placeholder\": \"Поиск...\",\n\t\"components.TableHeader.sort\": \"Сортировать по {label}\",\n\t\"components.ViewSettings.tooltip\": \"Настройки просмотра\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Режим разметки\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Режим предпросмотра\",\n\t\"components.Wysiwyg.collapse\": \"Свернуть\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Заголовок H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Заголовок H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Заголовок H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Заголовок H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Заголовок H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Заголовок H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Добавить заголовок\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"символов\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Развернуть\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Перетащите файлы в эту область, вставьте из буфера обмена или {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"выберите их\",\n\t\"components.pagination.go-to\": \"Перейти на страницу {page}\",\n\t\"components.pagination.go-to-next\": \"Перейти на следующую страницу\",\n\t\"components.pagination.go-to-previous\": \"Перейти на предыдущую страницу\",\n\t\"components.pagination.remaining-links\": \"И {number} других ссылок\",\n\t\"components.popUpWarning.button.cancel\": \"Нет, отменить\",\n\t\"components.popUpWarning.button.confirm\": \"Да, подтвердить\",\n\t\"components.popUpWarning.message\": \"Вы уверены, что хотите удалить это?\",\n\t\"components.popUpWarning.title\": \"Пожалуйста, подтвердите\",\n\tdark: dark,\n\t\"form.button.continue\": \"Продолжить\",\n\t\"form.button.done\": \"Выполнено\",\n\t\"global.actions\": \"Действия\",\n\t\"global.auditLogs\": \"Аудит логов\",\n\t\"global.back\": \"Назад\",\n\t\"global.cancel\": \"Отмена\",\n\t\"global.change-password\": \"Сменить пароль\",\n\t\"global.close\": \"Закрыть\",\n\t\"global.content-manager\": \"Редактор контента\",\n\t\"global.continue\": \"Продолжить\",\n\t\"global.delete\": \"Удалить\",\n\t\"global.delete-target\": \"Удалить {target}\",\n\t\"global.description\": \"Описание\",\n\t\"global.details\": \"Подробности\",\n\t\"global.disabled\": \"Отключено\",\n\t\"global.documentation\": \"Документация\",\n\t\"global.enabled\": \"Включено\",\n\t\"global.finish\": \"Готово\",\n\t\"global.fullname\": \"{firstname} {lastname}\",\n\t\"global.marketplace\": \"Маркет\",\n\t\"global.name\": \"Имя\",\n\t\"global.none\": \"Нет\",\n\t\"global.password\": \"Пароль\",\n\t\"global.plugins\": \"Плагины\",\n\t\"global.plugins.content-manager\": \"Редактор контента\",\n\t\"global.plugins.content-manager.description\": \"Быстрый способ просмотра, редактирования и удаления данных в вашей базе данных.\",\n\t\"global.plugins.content-type-builder\": \"Конструктор типов содержимого\",\n\t\"global.plugins.content-type-builder.description\": \"Моделируйте структуру данных вашего API. Создавайте новые поля и отношения всего за минуту. Файлы автоматически создаются и обновляются в вашем проекте.\",\n\t\"global.plugins.documentation\": \"Документация\",\n\t\"global.plugins.documentation.description\": \"Создайте документ OpenAPI и визуализируйте свой API с помощью пользовательского интерфейса SWAGGER.\",\n\t\"global.plugins.email\": \"Email\",\n\t\"global.plugins.email.description\": \"Настройте своё приложение для отправки электронной почты.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"Добавить конечную точку GraphQL с методами API по умолчанию.\",\n\t\"global.plugins.i18n\": \"Интернационализация\",\n\t\"global.plugins.i18n.description\": \"Этот плагин позволяет создавать, читать и обновлять контент на разных языках, как из панели администратора, так и с помощью API.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"Отправка событий об ошибках Strapi в Sentry.\",\n\t\"global.plugins.upload\": \"Библиотека медиа\",\n\t\"global.plugins.upload.description\": \"Управление медиафайлами.\",\n\t\"global.plugins.users-permissions\": \"Роли и Разрешения\",\n\t\"global.plugins.users-permissions.description\": \"Защитите свой API с помощью полного процесса аутентификации на основе JWT. Этот плагин также поставляется со стратегией ACL, которая позволяет вам управлять разрешениями между группами пользователей.\",\n\t\"global.profile\": \"Профиль\",\n\t\"global.prompt.unsaved\": \"Вы уверены, что хотите покинуть эту страницу? Все ваши модификации будут потеряны\",\n\t\"global.reset-password\": \"Сброс пароля\",\n\t\"global.roles\": \"Роли\",\n\t\"global.save\": \"Сохранить\",\n\t\"global.search\": \"Поиск\",\n\t\"global.see-more\": \"Подробнее\",\n\t\"global.select\": \"Выбрать\",\n\t\"global.select-all-entries\": \"Выбрать все записи\",\n\t\"global.settings\": \"Настройки\",\n\t\"global.type\": \"Тип\",\n\t\"global.users\": \"Пользователи\",\n\tlight: light,\n\t\"notification.contentType.relations.conflict\": \"Тип контента имеет конфликтующие отношения\",\n\t\"notification.default.title\": \"Информация:\",\n\t\"notification.ee.warning.at-seat-limit.title\": \"{licenseLimitStatus, select, OVER_LIMIT {БОЛЬШЕ ЧЕМ} AT_LIMIT {РОВНО}} seat limit ({currentUserCount}/{permittedSeats})\",\n\t\"notification.ee.warning.over-.message\": \"Добавить места чтобы {licenseLimitStatus, select, OVER_LIMIT {пригласить} AT_LIMIT {повторно подкючить}} пользователей. Если вы уже сделали это, но это ещё не отображается в Strapi, обязательно перезапустите своё приложение.\",\n\t\"notification.error\": \"Произошла ошибка\",\n\t\"notification.error.invalid.configuration\": \"У вас неправильная конфигурация настроек, проверьте журнал вашего сервера для получения дополнительной информации.\",\n\t\"notification.error.layout\": \"Не удалось получить макет\",\n\t\"notification.error.tokennamenotunique\": \"Имя уже присвоено другому токену\",\n\t\"notification.form.error.fields\": \"Форма содержит некоторые ошибки\",\n\t\"notification.form.success.fields\": \"Изменения сохранены\",\n\t\"notification.link-copied\": \"Ссылка скопирована в буфер обмена\",\n\t\"notification.permission.not-allowed-read\": \"Вам не разрешено просматривать этот документ\",\n\t\"notification.success.apitokencreated\": \"API-токен успешно создан\",\n\t\"notification.success.apitokenedited\": \"API-токен успешно отредактирован\",\n\t\"notification.success.delete\": \"Элемент удален\",\n\t\"notification.success.saved\": \"Сохранено\",\n\t\"notification.success.title\": \"Успех:\",\n\t\"notification.success.transfertokencreated\": \"Токен для передачи успешно создан\",\n\t\"notification.success.transfertokenedited\": \"Токен для передачи успешно изменён\",\n\t\"notification.version.update.message\": \"Доступна новая версия Strapi!\",\n\t\"notification.warning.404\": \"404 — Не найдено\",\n\t\"notification.warning.title\": \"Внимание:\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Модель данных не существует\",\n\tselectButtonTitle: selectButtonTitle,\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, ru as default, light, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=ru-FpmG9SEf.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,KAAK;AACX,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qDAAqD;AAAA,EACrD,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,wDAAwD;AAAA,EACxD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,4CAA4C;AAAA,EAC5C,6BAA6B;AAAA,EAC7B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACD;", "names": []}