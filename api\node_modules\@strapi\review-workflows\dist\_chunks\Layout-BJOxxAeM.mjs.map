{"version": 3, "file": "Layout-BJOxxAeM.mjs", "sources": ["../../admin/src/routes/settings/hooks/useReviewWorkflows.ts", "../../admin/src/routes/settings/constants.ts", "../../admin/src/routes/settings/components/StageDragPreview.tsx", "../../admin/src/routes/settings/components/Layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { useAPIErrorHandler, useNotification } from '@strapi/admin/strapi-admin';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\n\nimport {\n  GetWorkflowsParams,\n  useCreateWorkflowMutation,\n  useDeleteWorkflowMutation,\n  useGetWorkflowsQuery,\n  useUpdateWorkflowMutation,\n} from '../../../services/settings';\n\nimport type { Create, Update } from '../../../../../shared/contracts/review-workflows';\n\nconst DEFAULT_UNEXPECTED_ERROR_MSG = {\n  id: 'notification.error',\n  defaultMessage: 'An error occurred, please try again',\n} satisfies MessageDescriptor;\n\ntype UseReviewWorkflowsArgs = GetWorkflowsParams & {\n  skip?: boolean;\n};\n\nconst useReviewWorkflows = (params: UseReviewWorkflowsArgs = {}) => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { skip = false, ...queryParams } = params;\n\n  const { data, isLoading, error } = useGetWorkflowsQuery(\n    {\n      populate: 'stages',\n      ...queryParams,\n    },\n    {\n      skip,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const [createWorkflow] = useCreateWorkflowMutation();\n  const create = React.useCallback(\n    async (data: Create.Request['body']['data']) => {\n      try {\n        const res = await createWorkflow({ data });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return res;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.created', defaultMessage: 'Created workflow' }),\n        });\n\n        return res;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [createWorkflow, formatAPIError, formatMessage, toggleNotification]\n  );\n\n  const [updateWorkflow] = useUpdateWorkflowMutation();\n  const update = React.useCallback(\n    async (id: string, data: Update.Request['body']['data']) => {\n      try {\n        const res = await updateWorkflow({ id, data });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return res;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.updated', defaultMessage: 'Updated workflow' }),\n        });\n\n        return res;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [formatAPIError, formatMessage, toggleNotification, updateWorkflow]\n  );\n\n  const [deleteWorkflow] = useDeleteWorkflowMutation();\n  const deleteAction = React.useCallback(\n    async (id: string) => {\n      try {\n        const res = await deleteWorkflow({ id });\n\n        if ('error' in res) {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'actions.deleted', defaultMessage: 'Deleted workflow' }),\n        });\n\n        return res.data;\n      } catch (err) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage(DEFAULT_UNEXPECTED_ERROR_MSG),\n        });\n\n        throw err;\n      }\n    },\n    [deleteWorkflow, formatAPIError, formatMessage, toggleNotification]\n  );\n\n  const { workflows = [], meta } = data ?? {};\n\n  return {\n    // meta contains e.g. the total of all workflows. we can not use\n    // the pagination object here, because the list is not paginated.\n    meta,\n    workflows,\n    isLoading,\n    error,\n    create,\n    delete: deleteAction,\n    update,\n  };\n};\n\nexport { useReviewWorkflows };\n", "export type DragDropTypes = 'stage';\n\nexport const DRAG_DROP_TYPES: Record<Uppercase<DragDropTypes>, DragDropTypes> = {\n  STAGE: 'stage',\n};\n", "import { Flex, Typography } from '@strapi/design-system';\nimport { CaretDown } from '@strapi/icons';\n\ninterface StageDragPreviewType {\n  name: string | null;\n}\n\nconst StageDragPreview = ({ name }: StageDragPreviewType) => {\n  return (\n    <Flex\n      background=\"primary100\"\n      borderStyle=\"dashed\"\n      borderColor=\"primary600\"\n      borderWidth=\"1px\"\n      gap={3}\n      hasRadius\n      padding={3}\n      shadow=\"tableShadow\"\n      width=\"30rem\"\n    >\n      <Flex\n        alignItems=\"center\"\n        background=\"neutral200\"\n        borderRadius=\"50%\"\n        height={6}\n        justifyContent=\"center\"\n        width={6}\n      >\n        <CaretDown width=\"0.8rem\" fill=\"neutral600\" />\n      </Flex>\n\n      <Typography fontWeight=\"bold\">{name}</Typography>\n    </Flex>\n  );\n};\n\nexport { StageDragPreview };\nexport type { StageDragPreviewType };\n", "import * as React from 'react';\n\nimport { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box } from '@strapi/design-system';\nimport { XYCoord, useDragLayer } from 'react-dnd';\nimport { useIntl } from 'react-intl';\n\nimport { DRAG_DROP_TYPES } from '../constants';\n\nimport { StageDragPreview } from './StageDragPreview';\n\nfunction getStyle(\n  initialOffset: XYCoord | null,\n  currentOffset: XYCoord | null,\n  mouseOffset: XYCoord | null\n) {\n  if (!initialOffset || !currentOffset || !mouseOffset) {\n    return { display: 'none' };\n  }\n\n  const { x, y } = mouseOffset;\n\n  return {\n    transform: `translate(${x}px, ${y}px)`,\n  };\n}\n\nconst DragLayerRendered = () => {\n  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer(\n    (monitor) => ({\n      item: monitor.getItem(),\n      itemType: monitor.getItemType(),\n      initialOffset: monitor.getInitialSourceClientOffset(),\n      currentOffset: monitor.getSourceClientOffset(),\n      isDragging: monitor.isDragging(),\n      mouseOffset: monitor.getClientOffset(),\n    })\n  );\n\n  if (!isDragging || itemType !== DRAG_DROP_TYPES.STAGE) {\n    return null;\n  }\n\n  return (\n    <Box\n      height=\"100%\"\n      left={0}\n      position=\"fixed\"\n      pointerEvents=\"none\"\n      top={0}\n      zIndex={100}\n      width=\"100%\"\n    >\n      <Box style={getStyle(initialOffset, currentOffset, mouseOffset)}>\n        <StageDragPreview name={typeof item.item === 'string' ? item.item : null} />;\n      </Box>\n    </Box>\n  );\n};\n\nconst Root: React.FC<React.PropsWithChildren> = ({ children }) => {\n  return (\n    <Page.Main>\n      <Layouts.Content>{children}</Layouts.Content>\n    </Page.Main>\n  );\n};\n\ninterface HeaderProps {\n  title: string;\n  navigationAction?: React.ReactNode;\n  primaryAction?: React.ReactNode;\n  secondaryAction?: React.ReactNode;\n  subtitle?: React.ReactNode;\n}\n\nconst Header: React.FC<HeaderProps> = ({ title, subtitle, navigationAction, primaryAction }) => {\n  const { formatMessage } = useIntl();\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: title,\n          }\n        )}\n      </Page.Title>\n      <Layouts.BaseHeader\n        navigationAction={navigationAction}\n        primaryAction={primaryAction}\n        title={title}\n        subtitle={subtitle}\n      />\n    </>\n  );\n};\n\nexport { DragLayerRendered, Header, Root };\n"], "names": ["data"], "mappings": ";;;;;;;;AAeA,MAAM,+BAA+B;AAAA,EACnC,IAAI;AAAA,EACJ,gBAAgB;AAClB;AAMA,MAAM,qBAAqB,CAAC,SAAiC,OAAO;AAC5D,QAAA,EAAE,uBAAuB;AACzB,QAAA,EAAE,kBAAkB;AAC1B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,EAAE,OAAO,OAAO,GAAG,gBAAgB;AAEzC,QAAM,EAAE,MAAM,WAAW,MAAU,IAAA;AAAA,IACjC;AAAA,MACE,UAAU;AAAA,MACV,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EAAA;AAGF,QAAM,UAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;AAAA,QACjB,MAAM;AAAA,QACN,SAAS,eAAe,KAAK;AAAA,MAAA,CAC9B;AAAA,IACH;AAAA,EACC,GAAA,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAExC,QAAA,CAAC,cAAc,IAAI;AACzB,QAAM,SAAS,MAAM;AAAA,IACnB,OAAOA,UAAyC;AAC1C,UAAA;AACF,cAAM,MAAM,MAAM,eAAe,EAAE,MAAAA,MAAM,CAAA;AAEzC,YAAI,WAAW,KAAK;AACC,6BAAA;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,eAAe,IAAI,KAAK;AAAA,UAAA,CAClC;AAEM,iBAAA;AAAA,QACT;AAEmB,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,EAAE,IAAI,mBAAmB,gBAAgB,oBAAoB;AAAA,QAAA,CACrF;AAEM,eAAA;AAAA,eACA,KAAK;AACO,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,4BAA4B;AAAA,QAAA,CACpD;AAEK,cAAA;AAAA,MACR;AAAA,IACF;AAAA,IACA,CAAC,gBAAgB,gBAAgB,eAAe,kBAAkB;AAAA,EAAA;AAG9D,QAAA,CAAC,cAAc,IAAI;AACzB,QAAM,SAAS,MAAM;AAAA,IACnB,OAAO,IAAYA,UAAyC;AACtD,UAAA;AACF,cAAM,MAAM,MAAM,eAAe,EAAE,IAAI,MAAAA,OAAM;AAE7C,YAAI,WAAW,KAAK;AACC,6BAAA;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,eAAe,IAAI,KAAK;AAAA,UAAA,CAClC;AAEM,iBAAA;AAAA,QACT;AAEmB,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,EAAE,IAAI,mBAAmB,gBAAgB,oBAAoB;AAAA,QAAA,CACrF;AAEM,eAAA;AAAA,eACA,KAAK;AACO,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,4BAA4B;AAAA,QAAA,CACpD;AAEK,cAAA;AAAA,MACR;AAAA,IACF;AAAA,IACA,CAAC,gBAAgB,eAAe,oBAAoB,cAAc;AAAA,EAAA;AAG9D,QAAA,CAAC,cAAc,IAAI;AACzB,QAAM,eAAe,MAAM;AAAA,IACzB,OAAO,OAAe;AAChB,UAAA;AACF,cAAM,MAAM,MAAM,eAAe,EAAE,GAAI,CAAA;AAEvC,YAAI,WAAW,KAAK;AACC,6BAAA;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,eAAe,IAAI,KAAK;AAAA,UAAA,CAClC;AAED;AAAA,QACF;AAEmB,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,EAAE,IAAI,mBAAmB,gBAAgB,oBAAoB;AAAA,QAAA,CACrF;AAED,eAAO,IAAI;AAAA,eACJ,KAAK;AACO,2BAAA;AAAA,UACjB,MAAM;AAAA,UACN,SAAS,cAAc,4BAA4B;AAAA,QAAA,CACpD;AAEK,cAAA;AAAA,MACR;AAAA,IACF;AAAA,IACA,CAAC,gBAAgB,gBAAgB,eAAe,kBAAkB;AAAA,EAAA;AAGpE,QAAM,EAAE,YAAY,IAAI,KAAK,IAAI,QAAQ,CAAA;AAElC,SAAA;AAAA;AAAA;AAAA,IAGL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EAAA;AAEJ;AChKO,MAAM,kBAAmE;AAAA,EAC9E,OAAO;AACT;ACGA,MAAM,mBAAmB,CAAC,EAAE,WAAiC;AAEzD,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,YAAW;AAAA,MACX,aAAY;AAAA,MACZ,aAAY;AAAA,MACZ,aAAY;AAAA,MACZ,KAAK;AAAA,MACL,WAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAO;AAAA,MACP,OAAM;AAAA,MAEN,UAAA;AAAA,QAAA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,YAAW;AAAA,YACX,YAAW;AAAA,YACX,cAAa;AAAA,YACb,QAAQ;AAAA,YACR,gBAAe;AAAA,YACf,OAAO;AAAA,YAEP,UAAC,oBAAA,WAAA,EAAU,OAAM,UAAS,MAAK,cAAa;AAAA,UAAA;AAAA,QAC9C;AAAA,QAEC,oBAAA,YAAA,EAAW,YAAW,QAAQ,UAAK,MAAA;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAG1C;ACvBA,SAAS,SACP,eACA,eACA,aACA;AACA,MAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,aAAa;AAC7C,WAAA,EAAE,SAAS;EACpB;AAEM,QAAA,EAAE,GAAG,EAAM,IAAA;AAEV,SAAA;AAAA,IACL,WAAW,aAAa,CAAC,OAAO,CAAC;AAAA,EAAA;AAErC;AAEA,MAAM,oBAAoB,MAAM;AAC9B,QAAM,EAAE,UAAU,YAAY,MAAM,eAAe,eAAe,gBAAgB;AAAA,IAChF,CAAC,aAAa;AAAA,MACZ,MAAM,QAAQ,QAAQ;AAAA,MACtB,UAAU,QAAQ,YAAY;AAAA,MAC9B,eAAe,QAAQ,6BAA6B;AAAA,MACpD,eAAe,QAAQ,sBAAsB;AAAA,MAC7C,YAAY,QAAQ,WAAW;AAAA,MAC/B,aAAa,QAAQ,gBAAgB;AAAA,IAAA;AAAA,EACvC;AAGF,MAAI,CAAC,cAAc,aAAa,gBAAgB,OAAO;AAC9C,WAAA;AAAA,EACT;AAGE,SAAA;AAAA,IAAC;AAAA,IAAA;AAAA,MACC,QAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAS;AAAA,MACT,eAAc;AAAA,MACd,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAM;AAAA,MAEN,+BAAC,KAAI,EAAA,OAAO,SAAS,eAAe,eAAe,WAAW,GAC5D,UAAA;AAAA,QAAC,oBAAA,kBAAA,EAAiB,MAAM,OAAO,KAAK,SAAS,WAAW,KAAK,OAAO,MAAM;AAAA,QAAE;AAAA,MAAA,GAC9E;AAAA,IAAA;AAAA,EAAA;AAGN;AAEA,MAAM,OAA0C,CAAC,EAAE,eAAe;AAE9D,SAAA,oBAAC,KAAK,MAAL,EACC,8BAAC,QAAQ,SAAR,EAAiB,SAAS,CAAA,EAC7B,CAAA;AAEJ;AAUA,MAAM,SAAgC,CAAC,EAAE,OAAO,UAAU,kBAAkB,oBAAoB;AACxF,QAAA,EAAE,kBAAkB;AAC1B,SAEI,qBAAA,UAAA,EAAA,UAAA;AAAA,IAAC,oBAAA,KAAK,OAAL,EACE,UAAA;AAAA,MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;AAAA,MAChE;AAAA,QACE,MAAM;AAAA,MACR;AAAA,IAAA,GAEJ;AAAA,IACA;AAAA,MAAC,QAAQ;AAAA,MAAR;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF;AAAA,EACF,EAAA,CAAA;AAEJ;"}