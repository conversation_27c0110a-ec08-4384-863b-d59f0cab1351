{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/uk-Bx5IlOKX.mjs"], "sourcesContent": ["const configurations = \"налаштування\";\nconst from = \"з\";\nconst uk = {\n  \"attribute.boolean\": \"Boolean\",\n  \"attribute.boolean.description\": \"Так чи ні, 1 чи 0, правда чи брехня\",\n  \"attribute.component\": \"Компонент\",\n  \"attribute.component.description\": \"Група полей, які ви можете повторювати\",\n  \"attribute.date\": \"Date\",\n  \"attribute.date.description\": \"Елемент вибору дати та часу\",\n  \"attribute.datetime\": \"Дата та час\",\n  \"attribute.dynamiczone\": \"Динамічна зона\",\n  \"attribute.dynamiczone.description\": \"Динамічний вибір компонентів підчас редагування контенту\",\n  \"attribute.email\": \"Email\",\n  \"attribute.email.description\": \"Поле email з перевіркою формату\",\n  \"attribute.enumeration\": \"Enumeration\",\n  \"attribute.enumeration.description\": \"Перелік значень, вибирається одне\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"Інформація у форматі JSON\",\n  \"attribute.media\": \"Media\",\n  \"attribute.media.description\": \"Файли, як-то картнки, відео тощо\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Number\",\n  \"attribute.number.description\": \"Числа (integer, float, decimal)\",\n  \"attribute.password\": \"Password\",\n  \"attribute.password.description\": \"Поле паролю з шифруванням\",\n  \"attribute.relation\": \"Relation\",\n  \"attribute.relation.description\": \"Зв'язок з Collection Type\",\n  \"attribute.richtext\": \"Rich text\",\n  \"attribute.richtext.description\": \"Текст з можливістю форматування\",\n  \"attribute.text\": \"Text\",\n  \"attribute.text.description\": \"Короткий або довгий текст, як заголовок чи опис\",\n  \"attribute.time\": \"Time\",\n  \"attribute.timestamp\": \"Мітка часу\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Унікальний ідентифікатор\",\n  \"button.attributes.add.another\": \"Додате ще одне поле\",\n  \"button.component.add\": \"Додати компонент\",\n  \"button.component.create\": \"Створити новий компонент\",\n  \"button.model.create\": \"Створити Collection Type\",\n  \"button.single-types.create\": \"Створити Single Type\",\n  \"component.repeatable\": \"(повторюваний)\",\n  \"components.componentSelect.no-component-available\": \"Ви вже добавили всі компоненти\",\n  \"components.componentSelect.no-component-available.with-search\": \"Немає компонентів, які відповідають вашему запиту\",\n  \"components.componentSelect.value-component\": \"{number} вибраних компонентів (напишіть для пошуку)\",\n  \"components.componentSelect.value-components\": \"{number} вибраних компонентів\",\n  configurations,\n  \"contentType.collectionName.description\": \"Корисно, коли назва вашего Content Type та вашої таблиці різні\",\n  \"contentType.collectionName.label\": \"Назва колекції\",\n  \"contentType.displayName.label\": \"Назва для відображення\",\n  \"contentType.kind.change.warning\": \"Ви тільки що змінили тип Content Type: API буде перезавантажене (маршрути, контролери та сервіси будуть переписані).\",\n  \"error.attributeName.reserved-name\": \"Ця назва не може буди використана для вашого Content Type, так як воно може зламати іншу функціональність\",\n  \"error.contentTypeName.reserved-name\": \"Ця назва не може буди використана у вашому проекті, так як воно може зламати іншу функціональність\",\n  \"error.validation.enum-duplicate\": \"Значення не можуть повторюватись\",\n  \"error.validation.minSupMax\": \"Не може бути більше\",\n  \"error.validation.regex\": \"Неправильний регулярний вираз\",\n  \"error.validation.relation.targetAttribute-taken\": \"Це ім'я вже існує в цільовій моделі\",\n  \"form.attribute.component.option.add\": \"Додати компонент\",\n  \"form.attribute.component.option.create\": \"Додати новий компонент\",\n  \"form.attribute.component.option.create.description\": \"Компонент використовується в типах та інших компонентах, він буде доступний всюди.\",\n  \"form.attribute.component.option.repeatable\": \"Повторюваний компонент\",\n  \"form.attribute.component.option.repeatable.description\": \"Підходить для множинних об'єктів (масиву), наприклад, інгридієнтів, метатегів тощо...\",\n  \"form.attribute.component.option.reuse-existing\": \"Використати існуючий компонент\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Використовуйте створений вами компонент, щоб підтримувати узгодженність данних серед різних Content Types.\",\n  \"form.attribute.component.option.single\": \"Одиничний компонент\",\n  \"form.attribute.component.option.single.description\": \"Підходить для групування полей, наприклад, повна адреса, основна інформація тощо...\",\n  \"form.attribute.item.customColumnName\": \"Власні назви стовпців\",\n  \"form.attribute.item.customColumnName.description\": \"Корисно для перейменування назв стовпців у базі даних для підтримки більш зрозумілого формату відповідей API\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Назва поля\",\n  \"form.attribute.item.enumeration.graphql\": \"Назва поля для GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"Дозволяє перейменувати згенеровану для GraphQL назву поля\",\n  \"form.attribute.item.enumeration.placeholder\": \"Наприклад:\\nранок\\nдень\\nвечір\",\n  \"form.attribute.item.enumeration.rules\": \"Значення (одне на рядок)\",\n  \"form.attribute.item.maximum\": \"Максимальне значення\",\n  \"form.attribute.item.maximumLength\": \"Максимальна довжина\",\n  \"form.attribute.item.minimum\": \"Мінімальне значення\",\n  \"form.attribute.item.minimumLength\": \"Мінімальна довжина\",\n  \"form.attribute.item.number.type\": \"Формат числа\",\n  \"form.attribute.item.number.type.biginteger\": \"big integer (ex: 123456789)\",\n  \"form.attribute.item.number.type.decimal\": \"decimal (ex: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"float (ex: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"integer (ex: 10)\",\n  \"form.attribute.item.privateField\": \"Приватне поле\",\n  \"form.attribute.item.privateField.description\": \"Це поле не буде відображатися у відповіді API\",\n  \"form.attribute.item.requiredField\": \"Обов'язкове поле\",\n  \"form.attribute.item.requiredField.description\": \"Ви не зможете створити запис якщо не заповните це поле\",\n  \"form.attribute.item.text.regex\": \"Регулярний вираз (RegExp)\",\n  \"form.attribute.item.text.regex.description\": \"Шаблон регулярного виразу.\",\n  \"form.attribute.item.uniqueField\": \"Унікальне поле\",\n  \"form.attribute.item.uniqueField.description\": \"Ви не зможете створити запис, якщо вже існує запис із таким самим значенням поля\",\n  \"form.attribute.media.allowed-types\": \"Виберіть дозволені типи медіа\",\n  \"form.attribute.media.allowed-types.option-files\": \"Файли\",\n  \"form.attribute.media.allowed-types.option-images\": \"Картинки\",\n  \"form.attribute.media.allowed-types.option-videos\": \"Відео\",\n  \"form.attribute.media.option.multiple\": \"Множинні медіа\",\n  \"form.attribute.media.option.multiple.description\": \"Підходить для слайдерів, каруселей або завантаження кількох файлів\",\n  \"form.attribute.media.option.single\": \"Одиничне медіа\",\n  \"form.attribute.media.option.single.description\": \"Підходить для аватарок, картинок профіля або обкладинок\",\n  \"form.attribute.settings.default\": \"Значення за замовчуванням\",\n  \"form.attribute.text.option.long-text\": \"Довгий текст\",\n  \"form.attribute.text.option.long-text.description\": \"Підходить для описів, тексту про себе. Точний пошук вимкнено.\",\n  \"form.attribute.text.option.short-text\": \"Короткий текст\",\n  \"form.attribute.text.option.short-text.description\": \"Підходить для назв, імен, посиалань (URL). Дозволяє точний пошук по цьому полю.\",\n  \"form.button.add-components-to-dynamiczone\": \"Додати компоненти у зону.\",\n  \"form.button.add-field\": \"Додати ще одне поле\",\n  \"form.button.add-first-field-to-created-component\": \"Додати перше поле компоненту\",\n  \"form.button.add.field.to.collectionType\": \"Додати ще одне поле до цього Collection Type\",\n  \"form.button.add.field.to.component\": \"Додати ще одне поле до цього компоненту\",\n  \"form.button.add.field.to.contentType\": \"Додати ще одне поле до цього Content Type\",\n  \"form.button.add.field.to.singleType\": \"Додати ще одне поле до цього Single Type\",\n  \"form.button.cancel\": \"Скасувати\",\n  \"form.button.collection-type.description\": \"Підходить для множинних об'єктів, як-то дописи, товари, коментарі тощо.\",\n  \"form.button.configure-component\": \"Налаштувати компонент\",\n  \"form.button.configure-view\": \"Налаштувати вигляд\",\n  \"form.button.select-component\": \"Вибрати компонент\",\n  \"form.button.single-type.description\": \"Підходить для поодиноких об'єктів, як-то домашня сторінка, про нас тощо\",\n  from,\n  \"modalForm.attribute.form.base.name.description\": \"Для назви атрибута не допускається пробілів\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"наприклад, slug, seoUrl, canonicalUrl\",\n  \"modalForm.attribute.target-field\": \"Пов'язане поле\",\n  \"modalForm.attributes.select-component\": \"Виберіть компонент\",\n  \"modalForm.attributes.select-components\": \"Виберіть компоненти\",\n  \"modalForm.component.header-create\": \"Створити компонент\",\n  \"modalForm.components.create-component.category.label\": \"Виберіть категорію або введіть назву для створення нової\",\n  \"modalForm.components.icon.label\": \"Іконка\",\n  \"modalForm.editCategory.base.name.description\": \"Для назви категорії не допускається пробілів\",\n  \"modalForm.header-edit\": \"Редагувати {name}\",\n  \"modalForm.header.categories\": \"Категорії\",\n  \"modalForm.header.back\": \"Назад\",\n  \"modalForm.singleType.header-create\": \"Створити Single Type\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Додати новий компонент до динамічної зони\",\n  \"modalForm.sub-header.attribute.create\": \"Додати нове поле {type}\",\n  \"modalForm.sub-header.attribute.create.step\": \"Додати новий компонент ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Редагувати {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Виберіть поле для вашего Collection Type\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Виберіть поле для вашого компоненту\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Виберіть поле для вашего Single Type\",\n  \"modelPage.attribute.relation-polymorphic\": \"Зв'язок (поліморфний)\",\n  \"modelPage.attribute.relationWith\": \"Зв'язок з\",\n  \"notification.info.autoreaload-disable\": \"Функція autoReload має буте включена. Будь ласка, запустіть свій додаток вікористовуючи `strapi develop`.\",\n  \"notification.info.creating.notSaved\": \"Будь ласка, збережіть ваші зміни перед тим як створювати новий компонент або Collection Type\",\n  \"plugin.description.long\": \"Моделюйте структуру данних для вашого API. Створюйте нові поля та зв'язки за хвилину. Файли будуть автоматично створені та оновлені в вашему проекту.\",\n  \"plugin.description.short\": \"Моделюйте структуру данних для вашого API.\",\n  \"popUpForm.navContainer.advanced\": \"Розширені налаштування\",\n  \"popUpForm.navContainer.base\": \"Основне\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Ви впевнені, що хочете скасувати свої зміни?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Ви впевнені, що хочете скасувати свої зміни? Деякі компоненти були змінені, або створені нові...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Ви впевнені, що хочете видалити цю категорію? Всі компоненти також будуть видалені.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Ви впевнені, що хочете видалити цей компонент?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Ви впевнені, що хочете видалити цей Collection Type?\",\n  \"prompt.unsaved\": \"Ви впевнені що хочете залишити сторінку? Всі виші зміни будуть втарчені.\",\n  \"relation.attributeName.placeholder\": \"Ex: author, category, tag\",\n  \"relation.manyToMany\": \"містить і належить багатьом\",\n  \"relation.manyToOne\": \"містить багато\",\n  \"relation.manyWay\": \"містить багато\",\n  \"relation.oneToMany\": \"належить до багатьох\",\n  \"relation.oneToOne\": \"містить і належить до однієї\",\n  \"relation.oneWay\": \"містить одне\"\n};\nexport {\n  configurations,\n  uk as default,\n  from\n};\n//# sourceMappingURL=uk-Bx5IlOKX.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB;", "names": []}