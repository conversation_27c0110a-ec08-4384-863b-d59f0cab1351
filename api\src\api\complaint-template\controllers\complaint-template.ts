/**
 * complaint‑template controller
 */

import { factories } from "@strapi/strapi";

const pick = (row: any) => {
  // Strapi v5: row ya viene plano (sin attributes) al usar fields = [...]
  //   pero hacemos fallback por si aparece attributes:
  const tpl = row.attributes ? { id: row.id, ...row.attributes } : row;

  return {
    id: tpl.id,
    title: tpl.title,
    content: tpl.content,
    category: tpl.category,
    subcategory: tpl.subcategory,
    isActive: tpl.isActive,
    requiredFields: tpl.requiredFields,
    fields: tpl.fields,
  };
};

export default factories.createCoreController(
  "api::complaint-template.complaint-template",
  ({ strapi }) => ({
    async find() {
      const rows = await strapi.entityService.findMany(
        "api::complaint-template.complaint-template",
        {
          filters: { isActive: true },
          fields: [
            "title",
            "content",
            "category",
            "subcategory",
            "isActive",
            "requiredFields",
            "fields",
          ],
        }
      );

      return {
        data: rows.map((r: any) => pick(r)),
        meta: { count: rows.length },
      };
    },

    async findOne(ctx) {
      const { id } = ctx.params;

      const row = await strapi.entityService.findOne(
        "api::complaint-template.complaint-template",
        id,
        {
          fields: [
            "title",
            "content",
            "category",
            "subcategory",
            "isActive",
            "requiredFields",
            "fields",
          ],
        }
      );

      return { data: row ? pick(row) : null };
    },
  })
);
