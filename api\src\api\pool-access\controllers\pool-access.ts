/**
 * pool-access controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

interface User {
  id: number;
  documentId: string;
  firstName: string;
  lastName: string;
  status: boolean;
  address: string;
}

interface Dependent {
  id: number;
  name: string;
  lastName: string;
  kinship: string;
}

interface PoolAccess {
  id: number;
  user: User | null;
  dependents: Dependent[];
  guestCount: number;
  isAlone: boolean;
  status: "active" | "completed";
  entryTime: string;
  exitTime: string | null;
}

export default factories.createCoreController(
  "api::pool-access.pool-access",
  ({ strapi }) => ({
    async create(ctx: Context) {
      try {
        const { data } = ctx.request.body;
        const { user: userId, guestCount = 0, dependentIds = [] } = data;

        if (typeof guestCount !== 'number' || guestCount < 0 || guestCount > 5) {
          return ctx.badRequest('El número de invitados debe estar entre 0 y 5');
        }

        const entry = (await strapi.entityService.create(
          "api::pool-access.pool-access",
          {
            data: {
              user: userId,
              guestCount,
              dependents: dependentIds,
              isAlone: guestCount === 0 && dependentIds.length === 0,
              status: "active",
              entryTime: new Date().toISOString(),
              publishedAt: new Date().toISOString(),
            },
            populate: {
              user: true,
              dependents: true,
            },
          }
        )) as unknown as PoolAccess;

        // Formatear la respuesta
        const formattedEntry = {
          ...entry,
          user: entry.user
            ? {
                id: entry.user.id,
                documentId: entry.user.documentId,
                firstName: entry.user.firstName,
                lastName: entry.user.lastName,
                status: entry.user.status,
                address: entry.user.address,
              }
            : null,
        };

        return ctx.send({ data: formattedEntry });
      } catch (error) {
        console.error("Error al crear acceso a piscina:", error);
        return ctx.badRequest(error.message);
      }
    },

    async current(ctx: Context) {
      try {
        const entries = (await strapi.entityService.findMany(
          "api::pool-access.pool-access",
          {
            filters: {
              status: "active",
              exitTime: null,
            },
            populate: {
              user: true,
              dependents: true,
            },
          }
        )) as unknown as PoolAccess[];

        // Formatear la respuesta
        const formattedEntries = entries.map((entry) => ({
          ...entry,
          user: entry.user
            ? {
                id: entry.user.id,
                documentId: entry.user.documentId,
                firstName: entry.user.firstName,
                lastName: entry.user.lastName,
                status: entry.user.status,
                address: entry.user.address,
              }
            : null,
        }));

        const totalPeople = formattedEntries.reduce((total, entry) => {
          return (
            total +
            1 + // usuario principal
            (entry.guestCount || 0) + // invitados
            (entry.dependents?.length || 0) // dependientes
          );
        }, 0);

        return ctx.send({
          data: formattedEntries,
          meta: {
            total: entries.length,
            totalPeople,
          },
        });
      } catch (error) {
        console.error("Error al obtener accesos actuales:", error);
        return ctx.badRequest(error.message);
      }
    },

    async find(ctx: Context) {
      try {
        const entries = (await strapi.entityService.findMany(
          "api::pool-access.pool-access",
          {
            populate: {
              user: true,
              dependents: true,
            },
          }
        )) as unknown as PoolAccess[];

        // Formatear la respuesta
        const formattedEntries = entries.map((entry) => ({
          ...entry,
          user: entry.user
            ? {
                id: entry.user.id,
                documentId: entry.user.documentId,
                firstName: entry.user.firstName,
                lastName: entry.user.lastName,
                status: entry.user.status,
                address: entry.user.address,
              }
            : null,
        }));

        return ctx.send({
          data: formattedEntries,
        });
      } catch (error) {
        console.error("Error en find:", error);
        return ctx.badRequest(error.message);
      }
    },

    async findCurrent(ctx: Context) {
      try {
        const entries = (await strapi.entityService.findMany(
          "api::pool-access.pool-access",
          {
            filters: {
              $and: [
                {
                  status: {
                    $eq: "active",
                  },
                },
              ],
            },
            populate: {
              user: true,
              dependents: true,
            },
          }
        )) as unknown as PoolAccess[];

        // Formatear la respuesta
        const formattedEntries = entries.map((entry) => ({
          ...entry,
          user: entry.user
            ? {
                id: entry.user.id,
                documentId: entry.user.documentId,
                firstName: entry.user.firstName,
                lastName: entry.user.lastName,
                status: entry.user.status,
                address: entry.user.address,
              }
            : null,
        }));

        return ctx.send({
          data: formattedEntries,
        });
      } catch (error) {
        console.error("Error en findCurrent:", error);
        return ctx.badRequest(error.message);
      }
    },

    async exit(ctx: Context) {
      try {
        const { id } = ctx.params;
        const entry = (await strapi.entityService.update(
          "api::pool-access.pool-access",
          id,
          {
            data: {
              exitTime: new Date().toISOString(),
              status: "completed",
            },
            populate: {
              user: true,
              dependents: true,
            },
          }
        )) as unknown as PoolAccess;

        // Formatear la respuesta
        const formattedEntry = {
          ...entry,
          user: entry.user
            ? {
                id: entry.user.id,
                documentId: entry.user.documentId,
                firstName: entry.user.firstName,
                lastName: entry.user.lastName,
                status: entry.user.status,
                address: entry.user.address,
              }
            : null,
        };

        return ctx.send({
          data: formattedEntry,
        });
      } catch (error) {
        console.error("Error al registrar salida:", error);
        return ctx.badRequest(error.message);
      }
    },
  })
);
