"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const fs = require("node:fs/promises");
const path = require("node:path");
const outdent = require("outdent");
const react = require("react");
const server = require("react-dom/server");
const _internal = require("@strapi/admin/_internal");
const _interopDefault = (e) => e && e.__esModule ? e : { default: e };
const fs__default = /* @__PURE__ */ _interopDefault(fs);
const path__default = /* @__PURE__ */ _interopDefault(path);
const outdent__default = /* @__PURE__ */ _interopDefault(outdent);
const getEntryModule = (ctx) => {
  const pluginsObject = ctx.plugins.map(({ name, importName }) => `'${name}': ${importName}`).join(",\n");
  const pluginsImport = ctx.plugins.map(({ importName, modulePath }) => `import ${importName} from '${modulePath}';`).join("\n");
  return outdent__default.default`
        /**
         * This file was automatically generated by Strapi.
         * Any modifications made will be discarded.
         */
        ${pluginsImport}
        import { renderAdmin } from "@strapi/strapi/admin"

        ${ctx.customisations?.modulePath ? `import customisations from '${ctx.customisations.modulePath}'` : ""}

        renderAdmin(
          document.getElementById("strapi"),
          {
            ${ctx.customisations?.modulePath ? "customisations," : ""}
            ${ctx.features ? `features: ${JSON.stringify(ctx.features)},` : ""}
            plugins: {
        ${pluginsObject}
            }
        })
      `;
};
const getDocumentHTML = ({ logger, props = {} }) => {
  const result = server.renderToStaticMarkup(react.createElement(_internal.DefaultDocument, props));
  logger.debug("Rendered the HTML");
  return outdent__default.default`<!DOCTYPE html>${result}`;
};
const AUTO_GENERATED_WARNING = `
This file was automatically generated by Strapi.
Any modifications made will be discarded.
`.trim();
const decorateHTMLWithAutoGeneratedWarning = (htmlTemplate) => htmlTemplate.replace(/<head/, `
<!--
${AUTO_GENERATED_WARNING}
-->
<head`);
const writeStaticClientFiles = async (ctx) => {
  const prettier = await import("prettier");
  await fs__default.default.mkdir(ctx.runtimeDir, { recursive: true });
  ctx.logger.debug("Created the runtime directory");
  const indexHtml = decorateHTMLWithAutoGeneratedWarning(
    await getDocumentHTML({
      logger: ctx.logger,
      props: ctx.bundler === "vite" ? {
        entryPath: `/${ctx.entry}`
      } : void 0
    })
  );
  await fs__default.default.writeFile(
    path__default.default.join(ctx.runtimeDir, "index.html"),
    await prettier.format(indexHtml, {
      parser: "html"
    })
  );
  ctx.logger.debug("Wrote the index.html file");
  await fs__default.default.writeFile(
    path__default.default.join(ctx.runtimeDir, "app.js"),
    await prettier.format(getEntryModule(ctx), {
      parser: "babel"
    })
  );
  ctx.logger.debug("Wrote the app.js file");
};
exports.getDocumentHTML = getDocumentHTML;
exports.writeStaticClientFiles = writeStaticClientFiles;
//# sourceMappingURL=staticFiles.js.map
