/**
 * payment router
 */

export default {
  routes: [
    // Rutas específicas primero
    {
      method: "PUT",
      path: "/payments/bulk-status",
      handler: "payment.updateBulkStatus",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "GET",
      path: "/payments/owner/:ownerId",
      handler: "payment.findByOwner",
      config: {
        auth: false,
      },
    },
    {
      method: "POST",
      path: "/payments/download-url",
      handler: "payment.getDownloadUrl",
      config: {
        policies: [],
      },
    },
    {
      method: "GET",
      path: "/payments/stats",
      handler: "payment.getStats",
      config: {
        policies: [],
      },
    },
    {
      method: "PUT",
      path: "/payments/:id/status",
      handler: "payment.updateStatus",
      config: {
        policies: [],
      },
    },
    // Rutas de comentarios
    {
      method: "POST",
      path: "/payments/:id/comments",
      handler: "payment.addComment",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "GET",
      path: "/payments/:id/comments",
      handler: "payment.getComments",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Rutas de verificación/rechazo
    {
      method: "PATCH",
      path: "/payments/:id/verify",
      handler: "payment.verify",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "PATCH",
      path: "/payments/:id/reject",
      handler: "payment.reject",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Rutas CRUD genéricas después
    {
      method: "GET",
      path: "/payments",
      handler: "payment.find",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/payments",
      handler: "payment.create",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/payments/:id",
      handler: "payment.update",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "DELETE",
      path: "/payments/:id",
      handler: "payment.delete",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
  ],
};
