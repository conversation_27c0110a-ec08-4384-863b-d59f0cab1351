import {
  PERMISSIONS
} from "./chunk-LQ3ZZXCQ.js";
import "./chunk-RPX6VIML.js";
import {
  Layouts,
  ValidationError,
  create4 as create,
  create6 as create2,
  errorsTrads,
  useFetchClient,
  useMutation,
  useQuery
} from "./chunk-M2UTSHEM.js";
import {
  Page,
  useNotification
} from "./chunk-6SQM3G2Q.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  SingleSelect,
  SingleSelectOption,
  TextInput,
  Typography,
  useIntl
} from "./chunk-OLXXPFXU.js";
import "./chunk-GGTKPILS.js";
import {
  ForwardRef$29
} from "./chunk-C3D7G7QO.js";
import {
  dt
} from "./chunk-JA2NBZIU.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-IYHTQODQ.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/strapi/node_modules/@strapi/email/dist/_chunks/Settings-DELoyxcs.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var extractValuesFromYupError = (errorType, errorParams) => {
  if (!errorType || !errorParams) {
    return {};
  }
  return {
    [errorType]: errorParams[errorType]
  };
};
var getYupInnerErrors = (error) => ((error == null ? void 0 : error.inner) || []).reduce((acc, currentError) => {
  if (currentError.path) {
    acc[currentError.path.split("[").join(".").split("]").join("")] = {
      id: currentError.message,
      defaultMessage: currentError.message,
      values: extractValuesFromYupError(currentError == null ? void 0 : currentError.type, currentError == null ? void 0 : currentError.params)
    };
  }
  return acc;
}, {});
var schema = create2().shape({
  email: create().email(errorsTrads.email.id).required(errorsTrads.required.id)
});
var DocumentationLink = dt.a`
  color: ${({ theme }) => theme.colors.primary600};
`;
var ProtectedSettingsPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.settings, children: (0, import_jsx_runtime.jsx)(SettingsPage, {}) });
var SettingsPage = () => {
  var _a, _b;
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const { get, post } = useFetchClient();
  const [testAddress, setTestAddress] = React.useState("");
  const [isTestAddressValid, setIsTestAddressValid] = React.useState(false);
  const [formErrors, setFormErrors] = React.useState({});
  const { data, isLoading } = useQuery(["email", "settings"], async () => {
    const res = await get("/email/settings");
    const {
      data: { config }
    } = res;
    return config;
  });
  const mutation = useMutation(
    async (body) => {
      await post("/email/test", body);
    },
    {
      onError() {
        toggleNotification({
          type: "danger",
          message: formatMessage(
            {
              id: "email.Settings.email.plugin.notification.test.error",
              defaultMessage: "Failed to send a test mail to {to}"
            },
            { to: testAddress }
          )
        });
      },
      onSuccess() {
        toggleNotification({
          type: "success",
          message: formatMessage(
            {
              id: "email.Settings.email.plugin.notification.test.success",
              defaultMessage: "Email test succeeded, check the {to} mailbox"
            },
            { to: testAddress }
          )
        });
      },
      retry: false
    }
  );
  React.useEffect(() => {
    schema.validate({ email: testAddress }, { abortEarly: false }).then(() => setIsTestAddressValid(true)).catch(() => setIsTestAddressValid(false));
  }, [testAddress]);
  const handleChange = (event) => {
    setTestAddress(() => event.target.value);
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    try {
      await schema.validate({ email: testAddress }, { abortEarly: false });
    } catch (error) {
      if (error instanceof ValidationError) {
        setFormErrors(getYupInnerErrors(error));
      }
    }
    mutation.mutate({ to: testAddress });
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, { labelledBy: "title", "aria-busy": isLoading || mutation.isLoading, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: formatMessage({
          id: "email.Settings.email.plugin.title",
          defaultMessage: "Configuration"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        id: "title",
        title: formatMessage({
          id: "email.Settings.email.plugin.title",
          defaultMessage: "Configuration"
        }),
        subtitle: formatMessage({
          id: "email.Settings.email.plugin.subTitle",
          defaultMessage: "Test the settings for the Email plugin"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: data && (0, import_jsx_runtime.jsx)("form", { onSubmit: handleSubmit, children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 7, children: [
      (0, import_jsx_runtime.jsx)(
        Box,
        {
          background: "neutral0",
          hasRadius: true,
          shadow: "filterShadow",
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 7,
          paddingRight: 7,
          children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
            (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 1, children: [
              (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                id: "email.Settings.email.plugin.title.config",
                defaultMessage: "Configuration"
              }) }),
              (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage(
                {
                  id: "email.Settings.email.plugin.text.configuration",
                  defaultMessage: "The plugin is configured through the {file} file, checkout this {link} for the documentation."
                },
                {
                  file: "./config/plugins.js",
                  link: (0, import_jsx_runtime.jsx)(
                    DocumentationLink,
                    {
                      href: "https://docs.strapi.io/developer-docs/latest/plugins/email.html",
                      target: "_blank",
                      rel: "noopener noreferrer",
                      children: formatMessage({
                        id: "email.link",
                        defaultMessage: "Link"
                      })
                    }
                  )
                }
              ) })
            ] }),
            (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 5, children: [
              (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(Field.Root, { name: "shipper-email", children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: "email.Settings.email.plugin.label.defaultFrom",
                  defaultMessage: "Default sender email"
                }) }),
                (0, import_jsx_runtime.jsx)(
                  TextInput,
                  {
                    placeholder: formatMessage({
                      id: "email.Settings.email.plugin.placeholder.defaultFrom",
                      defaultMessage: "ex: Strapi No-Reply '<'<EMAIL>'>'"
                    }),
                    disabled: true,
                    value: data.settings.defaultFrom
                  }
                )
              ] }) }),
              (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(Field.Root, { name: "response-email", children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: "email.Settings.email.plugin.label.defaultReplyTo",
                  defaultMessage: "Default response email"
                }) }),
                (0, import_jsx_runtime.jsx)(
                  TextInput,
                  {
                    placeholder: formatMessage({
                      id: "email.Settings.email.plugin.placeholder.defaultReplyTo",
                      defaultMessage: `ex: Strapi '<'<EMAIL>'>'`
                    }),
                    disabled: true,
                    value: data.settings.defaultReplyTo
                  }
                )
              ] }) }),
              (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(Field.Root, { name: "email-provider", children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: "email.Settings.email.plugin.label.provider",
                  defaultMessage: "Email provider"
                }) }),
                (0, import_jsx_runtime.jsx)(SingleSelect, { disabled: true, value: data.provider, children: (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: data.provider, children: data.provider }) })
              ] }) })
            ] })
          ] })
        }
      ),
      (0, import_jsx_runtime.jsxs)(
        Flex,
        {
          alignItems: "stretch",
          background: "neutral0",
          direction: "column",
          gap: 4,
          hasRadius: true,
          shadow: "filterShadow",
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 7,
          paddingRight: 7,
          children: [
            (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
              id: "email.Settings.email.plugin.title.test",
              defaultMessage: "Test email delivery"
            }) }),
            (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 5, children: [
              (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                Field.Root,
                {
                  name: "test-address",
                  error: ((_a = formErrors.email) == null ? void 0 : _a.id) && formatMessage({
                    id: `email.${(_b = formErrors.email) == null ? void 0 : _b.id}`,
                    defaultMessage: "This is not a valid email"
                  }),
                  children: [
                    (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                      id: "email.Settings.email.plugin.label.testAddress",
                      defaultMessage: "Recipient email"
                    }) }),
                    (0, import_jsx_runtime.jsx)(
                      TextInput,
                      {
                        onChange: handleChange,
                        value: testAddress,
                        placeholder: formatMessage({
                          id: "email.Settings.email.plugin.placeholder.testAddress",
                          defaultMessage: "ex: <EMAIL>"
                        })
                      }
                    )
                  ]
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Grid.Item, { col: 7, s: 12, direction: "column", alignItems: "start", children: (0, import_jsx_runtime.jsx)(
                Button,
                {
                  loading: mutation.isLoading,
                  disabled: !isTestAddressValid,
                  type: "submit",
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$29, {}),
                  children: formatMessage({
                    id: "email.Settings.email.plugin.button.test-email",
                    defaultMessage: "Send test email"
                  })
                }
              ) })
            ] })
          ]
        }
      )
    ] }) }) })
  ] });
};
export {
  ProtectedSettingsPage
};
//# sourceMappingURL=Settings-DELoyxcs-TFMILIUR.js.map
