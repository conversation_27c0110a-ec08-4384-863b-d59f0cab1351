{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var link = \"链接\";\nvar zhHans = {\n    link: link,\n    \"Settings.email.plugin.button.test-email\": \"发送测试邮件\",\n    \"Settings.email.plugin.label.defaultFrom\": \"默认发件地址\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"默认回复地址\",\n    \"Settings.email.plugin.label.provider\": \"电子邮件供应商\",\n    \"Settings.email.plugin.title.test\": \"测试电子邮件发送\",\n    \"Settings.email.plugin.label.testAddress\": \"收件地址\",\n    \"SettingsNav.link.settings\": \"电子邮件设置\",\n    \"SettingsNav.section-label\": \"电子邮件插件\",\n    \"Settings.email.plugin.title\": \"配置\",\n    \"Settings.email.plugin.subTitle\": \"测试电子邮件插件的配置\",\n    \"Settings.email.plugin.title.config\": \"配置\",\n    \"Settings.email.plugin.text.configuration\": \"通过 {file} 文件来配置插件，打开 {link} 查看文档。\"\n};\n\nexport { zhHans as default, link };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,SAAS;AAAA,EACT;AAAA,EACA,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,sCAAsC;AAAA,EACtC,4CAA4C;AAChD;", "names": []}