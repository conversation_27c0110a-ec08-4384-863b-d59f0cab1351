"use strict";
/**
 *  reservation controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
const resevationEmailUtils_1 = require("../../../utils/resevationEmailUtils");
// Función auxiliar para obtener el nombre del área
const getAreaName = (areaCode) => {
    if (!areaCode)
        return "Área no especificada";
    try {
        const areaNames = {
            // Áreas básicas
            communalHall: "Salón Comunal",
            pool: "Piscina",
            bbq: "BBQ",
            // Variantes comunes (para compatibilidad)
            "communal-hall": "Salón Comunal",
            "party-room": "Salón de Fiestas",
        };
        // Normalizar el código de área (case insensitive)
        const normalizedAreaCode = areaCode.toLowerCase().trim();
        // Buscar coincidencia exacta primero
        for (const [key, value] of Object.entries(areaNames)) {
            if (key.toLowerCase() === normalizedAreaCode) {
                return value;
            }
        }
        // Si no hay coincidencia exacta, buscar coincidencia parcial
        for (const [key, value] of Object.entries(areaNames)) {
            if (normalizedAreaCode.includes(key.toLowerCase())) {
                return value;
            }
        }
        // Si no se encuentra coincidencia, capitalizar y formatear el código
        return areaCode
            .split(/[-_\s]+/) // Separar por guiones, guiones bajos o espacios
            .map((word) => word.length > 0
            ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            : "")
            .filter((word) => word.length > 0) // Eliminar palabras vacías
            .join(" ");
    }
    catch (error) {
        console.error("Error en getAreaName:", error);
        return areaCode || "Área no especificada"; // Devolver el código original o mensaje por defecto
    }
};
// Función auxiliar para formatear datos de correo
const formatEmailData = (data, user) => {
    const sanitizeString = (str) => {
        if (str === null || str === undefined)
            return "";
        return String(str).trim();
    };
    const sanitizeNumber = (num) => {
        const parsed = parseInt(num);
        return isNaN(parsed) ? 0 : parsed;
    };
    const formatDate = (dateStr) => {
        if (!dateStr)
            return "";
        try {
            const date = new Date(dateStr);
            return date.toLocaleDateString("es-ES", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
            });
        }
        catch (error) {
            console.error("Error formateando fecha:", dateStr, error);
            return dateStr;
        }
    };
    const formatTime = (timeStr) => {
        if (!timeStr)
            return "";
        try {
            return timeStr.substring(0, 5);
        }
        catch (error) {
            console.error("Error formateando hora:", timeStr, error);
            return timeStr;
        }
    };
    // Asegurarse de que data y user existan
    const safeData = data || {};
    const safeUser = safeData.owner || user || {};
    // Log para depuración
    // Variables planas para la plantilla HTML
    const flattenedData = {
        "user.username": sanitizeString(safeUser.username),
        "user.firstName": sanitizeString(safeUser.firstName),
        "user.lastName": sanitizeString(safeUser.lastName),
        "user.email": sanitizeString(safeUser.email),
        "user.status": !!safeUser.status, // true = al día, false = moroso
        "user.address": sanitizeString(safeUser.address),
        "reservation.area": getAreaName(sanitizeString(safeData.socialArea)),
        "reservation.date": formatDate(sanitizeString(safeData.date)),
        "reservation.startTime": formatTime(sanitizeString(safeData.startTime)),
        "reservation.endTime": formatTime(sanitizeString(safeData.endTime)),
        "reservation.eventType": sanitizeString(safeData.eventType),
        "reservation.attendees": Array.isArray(safeData.guests)
            ? safeData.guests.length
            : 0,
    };
    // También incluimos los objetos anidados para mantener la compatibilidad
    return {
        ...flattenedData,
        user: {
            username: sanitizeString(safeUser.username),
            firstName: sanitizeString(safeUser.firstName),
            lastName: sanitizeString(safeUser.lastName),
            email: sanitizeString(safeUser.email),
            status: !!safeUser.status,
            address: sanitizeString(safeUser.address),
        },
        reservation: {
            id: sanitizeNumber(safeData.id),
            area: getAreaName(sanitizeString(safeData.socialArea)),
            date: formatDate(sanitizeString(safeData.date)),
            startTime: formatTime(sanitizeString(safeData.startTime)),
            endTime: formatTime(sanitizeString(safeData.endTime)),
            eventType: sanitizeString(safeData.eventType),
            guests: Array.isArray(safeData.guests) ? safeData.guests.length : 0,
            rejectionReason: sanitizeString(safeData.rejectionReason),
            status: sanitizeString(safeData.status),
        },
        URL: process.env.FRONTEND_URL || "http://localhost:3000",
    };
};
// Función mejorada de envío de correos con reintentos
const sendReservationEmail = async (type, data, recipientEmail) => {
    try {
        // Formatear datos para el email
        const emailData = formatEmailData(data, data.owner);
        // Usar el emailUtils existente que ya funciona
        await resevationEmailUtils_1.resevationEmailUtils.sendEmail(type, emailData, recipientEmail);
        console.log(`Correo de reservación (${type}) enviado exitosamente a ${recipientEmail}`);
    }
    catch (error) {
        console.error(`Error enviando correo de reservación (${type}):`, error);
        // No lanzar el error para no interrumpir el flujo
    }
};
exports.default = strapi_1.factories.createCoreController("api::reservation.reservation", ({ strapi }) => ({
    async create(ctx) {
        try {
            const user = ctx.state.user;
            if (!user) {
                return ctx.unauthorized("Usuario no autenticado");
            }
            // Obtener datos completos del usuario
            const fullUser = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: user.id },
                populate: ["role", "imgUrl"],
            });
            if (!fullUser) {
                return ctx.badRequest("Usuario no encontrado");
            }
            const { data } = ctx.request.body;
            // Validaciones básicas
            if (!data.date ||
                !data.startTime ||
                !data.endTime ||
                !data.socialArea) {
                return ctx.badRequest("Faltan campos requeridos");
            }
            const attendees = Array.isArray(data.guests) ? data.guests.length : 0;
            if (attendees < 0 || attendees > 100) {
                return ctx.badRequest("El número de invitados debe estar entre 0 y 100");
            }
            // Crear la reservación
            const reservation = await strapi.entityService.create("api::reservation.reservation", {
                data: {
                    ...data,
                    owner: user.id,
                    status: "pending",
                    guests: data.guests || [],
                },
                populate: ["owner", "guests"],
            });
            // Enviar email y notificación al administrador
            try {
                const adminUser = await strapi.db
                    .query("plugin::users-permissions.user")
                    .findOne({
                    where: { role: { type: "admin" } },
                    populate: ["role"],
                });
                if (adminUser) {
                    // Obtener los datos del usuario incluyendo el campo status
                    const ownerData = await strapi.db
                        .query("plugin::users-permissions.user")
                        .findOne({
                        where: { id: user.id },
                        select: [
                            "username",
                            "firstName",
                            "lastName",
                            "email",
                            "status",
                            "address",
                            "phone",
                        ],
                        populate: ["imgUrl"],
                    });
                    // 1. Enviar email al admin
                    if (adminUser.email && ownerData) {
                        await sendReservationEmail("created", {
                            ...reservation,
                            owner: ownerData,
                        }, adminUser.email);
                    }
                    // 2. Crear notificación en BD para el admin
                    if (ownerData) {
                        const newNotification = await strapi.entityService.create("api::notification.notification", {
                            data: {
                                title: "Nueva Reservación",
                                message: `${ownerData.firstName} ${ownerData.lastName} ha creado una nueva reservación para ${getAreaName(reservation.socialArea)}`,
                                type: "reservation_created",
                                read: false,
                                user: adminUser.id,
                                data: {
                                    reservationId: reservation.id,
                                    ownerName: `${ownerData.firstName} ${ownerData.lastName}`,
                                    socialArea: reservation.socialArea,
                                    date: reservation.date,
                                    startTime: reservation.startTime,
                                    endTime: reservation.endTime,
                                    url: `/admin/reservations/${reservation.id}`,
                                },
                                publishedAt: new Date(),
                            },
                        });
                        console.log(`✅ Notificación creada en BD para admin:`, newNotification.id);
                        // 3. Enviar notificación SSE en tiempo real al admin
                        try {
                            const notificationService = strapi.service("api::notification.notification");
                            if (notificationService &&
                                typeof notificationService.notifyUser === "function") {
                                const notificationData = {
                                    type: "notification",
                                    data: {
                                        id: newNotification.id,
                                        title: "Nueva Reservación",
                                        message: `${ownerData.firstName} ${ownerData.lastName} ha creado una nueva reservación para ${getAreaName(reservation.socialArea)}`,
                                        type: "reservation_created",
                                        ownerName: `${ownerData.firstName} ${ownerData.lastName}`,
                                        reservationId: reservation.id,
                                    },
                                };
                                console.log(`📤 Enviando notificación SSE al admin:`, notificationData);
                                const result = await notificationService.notifyUser(adminUser.id, notificationData);
                                console.log(`✅ Resultado del envío SSE al admin:`, result);
                            }
                        }
                        catch (realtimeError) {
                            console.error("⚠️ Error al enviar notificación SSE al admin:", realtimeError);
                        }
                    }
                }
            }
            catch (emailError) {
                console.error("Error al enviar email/notificación de nueva reservación:", emailError);
                // No interrumpimos el flujo por error de correo/notificación
            }
            return { data: reservation };
        }
        catch (error) {
            console.error("Error en create:", error);
            return ctx.badRequest(error.message || "Error al crear la reservación");
        }
    },
    async find(ctx) {
        var _a;
        try {
            const { user } = ctx.state;
            // Obtener el usuario completo con su rol
            const userWithRole = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: user.id },
                populate: ["role"],
            });
            // Log para depuración
            // Permitir también super-admin y otros posibles nombres de admin
            const adminRoles = ["admin", "vigilante"];
            const filters = !adminRoles.includes((_a = userWithRole === null || userWithRole === void 0 ? void 0 : userWithRole.role) === null || _a === void 0 ? void 0 : _a.type)
                ? { $and: [{ owner: user.id }] }
                : {};
            const [reservations, count] = await strapi.db
                .query("api::reservation.reservation")
                .findWithCount({
                where: filters,
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                    guests: { fields: ["name", "lastName", "isConfirmed"] },
                },
                ...ctx.query,
            });
            const typedReservations = reservations;
            // Transformar y sanitizar los resultados
            const sanitizedData = typedReservations.map((item) => {
                var _a;
                return ({
                    id: item.id,
                    documentId: item.documentId,
                    date: item.date,
                    startTime: String(item.startTime || "")
                        .split(".")[0]
                        .slice(0, 5) || null,
                    endTime: String(item.endTime || "")
                        .split(".")[0]
                        .slice(0, 5) || null,
                    socialArea: item.socialArea,
                    eventType: item.eventType,
                    status: item.status,
                    createdAt: item.createdAt,
                    updatedAt: item.updatedAt,
                    publishedAt: item.publishedAt,
                    rejectionReason: item.rejectionReason,
                    guests: (item.guests || []).map((g) => ({
                        name: g.name,
                        lastName: g.lastName,
                        isConfirmed: g.isConfirmed, // ← aquí lo pasamos al front
                    })),
                    user: item.owner
                        ? {
                            id: item.owner.id,
                            username: item.owner.username,
                            firstName: item.owner.firstName,
                            lastName: item.owner.lastName,
                            imgUrl: (_a = item.owner.imgUrl) === null || _a === void 0 ? void 0 : _a.url,
                            status: item.owner.status,
                            address: item.owner.address,
                            phone: item.owner.phone,
                        }
                        : null,
                });
            });
            return {
                data: sanitizedData,
                meta: {
                    pagination: {
                        page: parseInt(String(ctx.query.page) || "1", 10),
                        pageSize: parseInt(String(ctx.query.pageSize) || "25", 10),
                        pageCount: Math.ceil(count / parseInt(String(ctx.query.pageSize) || "25", 10)),
                        total: count,
                    },
                },
            };
        }
        catch (error) {
            console.error("Error en find:", error);
            return ctx.badRequest("Error al obtener las reservaciones");
        }
    },
    async findOne(ctx) {
        var _a, _b;
        try {
            const { id } = ctx.params;
            if (!id) {
                return ctx.badRequest("ID is required");
            }
            // Ensure id is a number
            const numericId = parseInt(id);
            if (isNaN(numericId)) {
                return ctx.badRequest("Invalid ID format");
            }
            const reservation = (await strapi.entityService.findOne("api::reservation.reservation", numericId, {
                populate: ["owner", "owner.imgUrl", "guests"],
            }));
            if (!reservation) {
                return ctx.notFound("Reservación no encontrada");
            }
            // Transform the data to match the frontend's expected structure
            const response = {
                data: {
                    id: reservation.id.toString(),
                    commonArea: {
                        name: reservation.socialArea === "communalHall"
                            ? "Salón Comunal"
                            : reservation.socialArea === "bbq"
                                ? "BBQ"
                                : reservation.socialArea,
                        description: "",
                        capacity: 100, // TODO: Add proper capacity based on area type
                    },
                    guests: (reservation.guests || []).map((g) => ({
                        name: g.name,
                        lastName: g.lastName,
                        isConfirmed: g.isConfirmed, // ← lo devolvemos aquí también
                    })),
                    user: reservation.owner
                        ? {
                            firstName: reservation.owner.firstName,
                            lastName: reservation.owner.lastName,
                            email: reservation.owner.email,
                            phone: reservation.owner.phone || {
                                countryCode: 57,
                                areaCode: "",
                                phoneNumber: "",
                                isoCode: "CO",
                            },
                            status: reservation.owner.status,
                        }
                        : null,
                    reservationDate: reservation.date,
                    startTime: ((_a = reservation.startTime) === null || _a === void 0 ? void 0 : _a.substring(0, 5)) || "",
                    endTime: ((_b = reservation.endTime) === null || _b === void 0 ? void 0 : _b.substring(0, 5)) || "",
                    purpose: reservation.eventType,
                    attendees: reservation.attendees,
                    status: reservation.status,
                    adminComments: reservation.rejectionReason,
                    createdAt: reservation.createdAt,
                    resolvedAt: reservation.updatedAt,
                },
            };
            return response;
        }
        catch (error) {
            console.error("Error en findOne:", error);
            return ctx.badRequest(`Error al obtener la reservación: ${error.message}`);
        }
    },
    async update(ctx) {
        var _a, _b, _c, _d, _e;
        try {
            const { id } = ctx.params;
            const { data } = ctx.request.body;
            // Asegurar usuario con rol incluido
            const user = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: ctx.state.user.id },
                populate: ["role"],
            });
            if (!user) {
                return ctx.unauthorized("Usuario no válido");
            }
            // Obtener la reservación existente
            const existingReservation = (await strapi.entityService.findOne("api::reservation.reservation", id, { populate: ["owner", "guests"] }));
            if (!existingReservation) {
                return ctx.notFound("Reservación no encontrada");
            }
            // Validar permisos
            const canUpdate = ((_a = user.role) === null || _a === void 0 ? void 0 : _a.type) === "admin" ||
                ((_b = existingReservation.owner) === null || _b === void 0 ? void 0 : _b.id) === user.id;
            if (!canUpdate) {
                return ctx.forbidden("No tienes permiso para actualizar esta reservación");
            }
            const updateData = {
                ...data,
                resolvedAt: data.status !== existingReservation.status
                    ? new Date().toISOString()
                    : undefined,
            };
            // Actualizar la reservación
            const updatedReservation = await strapi.entityService.update("api::reservation.reservation", id, {
                data: updateData,
                populate: ["owner", "guests"],
            });
            // Enviar correo y notificación si cambió el estado
            if (data.status &&
                data.status !== existingReservation.status &&
                existingReservation.owner) {
                try {
                    const statusChanged = data.status;
                    const ownerData = existingReservation.owner;
                    // 1. Enviar email si tiene email
                    if (ownerData.email) {
                        if (statusChanged === "approved") {
                            await sendReservationEmail("approved", updatedReservation, ownerData.email);
                        }
                        else if (statusChanged === "rejected") {
                            await sendReservationEmail("rejected", updatedReservation, ownerData.email);
                        }
                    }
                    // 2. Crear notificación en BD para el residente
                    let notificationTitle = "";
                    let notificationMessage = "";
                    let notificationType = "";
                    if (statusChanged === "approved") {
                        notificationTitle = "Reservación Aprobada";
                        notificationMessage = `Tu reservación para ${getAreaName(updatedReservation.socialArea)} el ${new Date(updatedReservation.date).toLocaleDateString("es-ES")} ha sido aprobada`;
                        notificationType = "reservation_approved";
                    }
                    else if (statusChanged === "rejected") {
                        notificationTitle = "Reservación Rechazada";
                        notificationMessage = `Tu reservación para ${getAreaName(updatedReservation.socialArea)} el ${new Date(updatedReservation.date).toLocaleDateString("es-ES")} ha sido rechazada`;
                        notificationType = "reservation_rejected";
                    }
                    if (notificationTitle) {
                        const newNotification = await strapi.entityService.create("api::notification.notification", {
                            data: {
                                title: notificationTitle,
                                message: notificationMessage,
                                type: notificationType,
                                read: false,
                                user: ownerData.id,
                                data: {
                                    reservationId: updatedReservation.id,
                                    socialArea: updatedReservation.socialArea,
                                    date: updatedReservation.date,
                                    status: statusChanged,
                                    rejectionReason: data.rejectionReason || null,
                                    url: `/reservations/${updatedReservation.id}`,
                                },
                                publishedAt: new Date(),
                            },
                        });
                        console.log(`✅ Notificación creada en BD para residente:`, newNotification.id);
                        // 3. Enviar notificación SSE en tiempo real al residente
                        try {
                            const notificationService = strapi.service("api::notification.notification");
                            if (notificationService &&
                                typeof notificationService.notifyUser === "function") {
                                const notificationData = {
                                    type: "notification",
                                    data: {
                                        id: newNotification.id,
                                        title: notificationTitle,
                                        message: notificationMessage,
                                        type: notificationType,
                                        reservationId: updatedReservation.id,
                                        status: statusChanged,
                                    },
                                };
                                console.log(`📤 Enviando notificación SSE al residente:`, notificationData);
                                const result = await notificationService.notifyUser(ownerData.id, notificationData);
                                console.log(`✅ Resultado del envío SSE al residente:`, result);
                            }
                        }
                        catch (realtimeError) {
                            console.error("⚠️ Error al enviar notificación SSE al residente:", realtimeError);
                        }
                    }
                }
                catch (emailError) {
                    console.error("Error al enviar email/notificación de actualización:", emailError);
                }
            }
            return { data: updatedReservation };
        }
        catch (error) {
            console.error("Error en update:", error);
            if (error.name === "ValidationError") {
                return ctx.badRequest("Error de validación: " +
                    (((_e = (_d = (_c = error.details) === null || _c === void 0 ? void 0 : _c.errors) === null || _d === void 0 ? void 0 : _d.map) === null || _e === void 0 ? void 0 : _e.call(_d, (e) => e.message).join("; ")) || ""));
            }
            return ctx.badRequest(error.message || "Error al actualizar la reservación");
        }
    },
    // Línea 521 - Método delete corregido
    async delete(ctx) {
        var _a, _b;
        try {
            const { user } = ctx.state;
            const { id } = ctx.params;
            if (!user) {
                return ctx.unauthorized("Usuario no autenticado");
            }
            const userWithRole = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: user.id },
                populate: ["role"],
            });
            const isAdmin = ((_a = userWithRole === null || userWithRole === void 0 ? void 0 : userWithRole.role) === null || _a === void 0 ? void 0 : _a.type) === "admin";
            // Obtener la reservación con el casting apropiado
            const reservation = (await strapi.entityService.findOne("api::reservation.reservation", id, {
                populate: ["owner", "guests"], // ✅ CORREGIDO: Removida la comilla mal colocada
                fields: [
                    "id",
                    "date",
                    "startTime",
                    "endTime",
                    "socialArea",
                    "eventType",
                    "status",
                    "rejectionReason",
                ],
            }));
            if (!reservation) {
                return ctx.notFound("Reservación no encontrada");
            }
            // Ahora TypeScript reconocerá la propiedad owner
            if (!isAdmin && ((_b = reservation === null || reservation === void 0 ? void 0 : reservation.owner) === null || _b === void 0 ? void 0 : _b.id) !== user.id) {
                return ctx.forbidden("No tienes permiso para eliminar esta reserva");
            }
            const deletedReservation = await strapi.entityService.delete("api::reservation.reservation", id);
            return {
                data: deletedReservation,
                meta: {
                    deletedBy: user.id,
                    deletedAt: new Date(),
                    isAdminAction: isAdmin,
                },
            };
        }
        catch (error) {
            console.error("Error al eliminar reservación:", error);
            return ctx.badRequest("Error al eliminar la reservación");
        }
    },
    async findByOwner(ctx) {
        try {
            const { user } = ctx.state;
            const { ownerId } = ctx.params;
            const { role } = user;
            // Solo los administradores pueden ver las reservas de otros usuarios
            if ((role === null || role === void 0 ? void 0 : role.type) !== "admin" &&
                (role === null || role === void 0 ? void 0 : role.type) !== "vigilante" &&
                user.id !== parseInt(ownerId)) {
                return ctx.forbidden("No tienes permiso para ver las reservas de otros usuarios");
            }
            const reservations = (await strapi.entityService.findMany("api::reservation.reservation", {
                filters: {
                    owner: {
                        id: ownerId,
                    },
                },
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                },
                sort: { createdAt: "desc" },
            }));
            return {
                data: reservations.map((reservation) => {
                    var _a, _b;
                    return ({
                        id: Number(reservation.id),
                        attributes: {
                            ...reservation,
                            id: undefined,
                            startTime: ((_a = reservation.startTime) === null || _a === void 0 ? void 0 : _a.slice(0, 5)) || null, // Aseguramos formato HH:mm
                            endTime: ((_b = reservation.endTime) === null || _b === void 0 ? void 0 : _b.slice(0, 5)) || null, // Aseguramos formato HH:mm
                            date: reservation.date, // Aseguramos que sea una fecha válida
                            createdAt: reservation.createdAt, // Aseguramos que sea una fecha válida
                            owner: reservation.owner
                                ? {
                                    data: {
                                        id: Number(reservation.owner.id),
                                        attributes: {
                                            firstName: reservation.owner.firstName || "",
                                            lastName: reservation.owner.lastName || "",
                                            address: reservation.owner.address,
                                            imgUrl: reservation.owner.imgUrl,
                                            status: reservation.owner.status,
                                            phone: reservation.owner.phone,
                                        },
                                    },
                                }
                                : null,
                        },
                    });
                }),
            };
        }
        catch (error) {
            console.error("Error en findByOwner:", error);
            return ctx.badRequest("Error al obtener las reservaciones");
        }
    },
    async me(ctx) {
        try {
            const { user } = ctx.state;
            if (!user) {
                return ctx.unauthorized("Usuario no autenticado");
            }
            const reservations = (await strapi.entityService.findMany("api::reservation.reservation", {
                filters: {
                    owner: {
                        id: user.id,
                    },
                },
                populate: {
                    owner: {
                        populate: ["imgUrl"],
                    },
                },
                sort: { createdAt: "desc" },
            }));
            return {
                data: reservations.map((reservation) => {
                    var _a, _b;
                    return ({
                        id: Number(reservation.id),
                        attributes: {
                            ...reservation,
                            id: undefined,
                            startTime: ((_a = reservation.startTime) === null || _a === void 0 ? void 0 : _a.slice(0, 5)) || null, // Aseguramos formato HH:mm
                            endTime: ((_b = reservation.endTime) === null || _b === void 0 ? void 0 : _b.slice(0, 5)) || null, // Aseguramos formato HH:mm
                            date: reservation.date, // Aseguramos que sea una fecha válida
                            createdAt: reservation.createdAt, // Aseguramos que sea una fecha válida
                            owner: reservation.owner
                                ? {
                                    data: {
                                        id: Number(reservation.owner.id),
                                        attributes: {
                                            firstName: reservation.owner.firstName || "",
                                            lastName: reservation.owner.lastName || "",
                                            address: reservation.owner.address,
                                            imgUrl: reservation.owner.imgUrl,
                                            status: reservation.owner.status,
                                            phone: reservation.owner.phone || null,
                                        },
                                    },
                                }
                                : null,
                        },
                    });
                }),
            };
        }
        catch (error) {
            console.error("Error en me:", error);
            return ctx.badRequest("Error al obtener las reservaciones");
        }
    },
    async findAll(ctx) {
        try {
            const reservations = await strapi.entityService.findMany("api::reservation.reservation", {
                populate: {
                    guests: true,
                    owner: {
                        populate: ["imgUrl"],
                        fields: ["firstName", "lastName", "address"],
                    },
                },
                sort: { date: "desc" },
            });
            // Transformar y sanitizar la respuesta
            const sanitizedData = reservations.map((reservation) => {
                // Sanitizar el owner
                const ownerData = reservation.owner
                    ? {
                        data: {
                            id: Number(reservation.owner.id),
                            attributes: {
                                firstName: reservation.owner.firstName || "",
                                lastName: reservation.owner.lastName || "",
                                address: reservation.owner.address || "",
                                imgUrl: reservation.owner.imgUrl
                                    ? {
                                        url: reservation.owner.imgUrl.url,
                                    }
                                    : null,
                                status: reservation.owner.status,
                                phone: reservation.owner.phone || null,
                            },
                        },
                    }
                    : null;
                return {
                    id: Number(reservation.id),
                    attributes: {
                        documentId: reservation.documentId,
                        date: reservation.date,
                        startTime: String(reservation.startTime || "")
                            .split(".")[0]
                            .slice(0, 5) || null,
                        endTime: String(reservation.endTime || "")
                            .split(".")[0]
                            .slice(0, 5) || null,
                        socialArea: reservation.socialArea,
                        eventType: reservation.eventType,
                        status: reservation.status,
                        rejectionReason: reservation.rejectionReason,
                        guests: reservation.guests || [],
                        createdAt: reservation.createdAt,
                        updatedAt: reservation.updatedAt,
                        publishedAt: reservation.publishedAt,
                        owner: ownerData,
                    },
                };
            });
            return { data: sanitizedData };
        }
        catch (error) {
            console.error("Error en findAll:", error);
            return ctx.badRequest("Error al obtener las reservaciones");
        }
    },
    // Nuevo método para confirmar la llegada de un guest
    async confirmGuest(ctx) {
        var _a, _b, _c;
        try {
            const { reservationId, guestIndex } = ctx.params;
            const { isConfirmed } = ctx.request.body;
            // Validar usuario autenticado
            const user = ctx.state.user;
            if (!user) {
                return ctx.unauthorized("Usuario no autenticado");
            }
            // Obtener usuario con rol
            const userWithRole = await strapi.db
                .query("plugin::users-permissions.user")
                .findOne({
                where: { id: user.id },
                populate: ["role"],
            });
            if (!userWithRole) {
                return ctx.unauthorized("Usuario no válido");
            }
            // Obtener la reservación
            const reservation = (await strapi.entityService.findOne("api::reservation.reservation", reservationId, {
                populate: ["owner", "guests"],
            }));
            if (!reservation) {
                return ctx.notFound("Reservación no encontrada");
            }
            // Validar permisos (solo admin, vigilante o propietario)
            const canUpdate = ((_a = userWithRole.role) === null || _a === void 0 ? void 0 : _a.type) === "admin" ||
                ((_b = userWithRole.role) === null || _b === void 0 ? void 0 : _b.type) === "vigilante" ||
                ((_c = reservation.owner) === null || _c === void 0 ? void 0 : _c.id) === user.id;
            if (!canUpdate) {
                return ctx.forbidden("No tienes permiso para confirmar guests en esta reservación");
            }
            // Validar que el índice del guest sea válido
            const guestIndexNum = parseInt(guestIndex);
            if (isNaN(guestIndexNum) ||
                guestIndexNum < 0 ||
                !reservation.guests ||
                guestIndexNum >= reservation.guests.length) {
                return ctx.badRequest("Índice de guest inválido");
            }
            // Actualizar el estado del guest
            const updatedGuests = [...reservation.guests];
            const previousState = updatedGuests[guestIndexNum].isConfirmed;
            updatedGuests[guestIndexNum] = {
                ...updatedGuests[guestIndexNum],
                isConfirmed: isConfirmed,
            };
            // Actualizar la reservación
            const updatedReservation = await strapi.entityService.update("api::reservation.reservation", reservationId, {
                data: {
                    guests: updatedGuests,
                },
                populate: ["owner", "guests"],
            });
            // Enviar notificación solo si el guest se confirmó (cambió de false a true)
            if (isConfirmed && !previousState && reservation.owner) {
                try {
                    console.log(`🔔 Iniciando envío de notificación:`);
                    console.log(`   - Usuario destino: ${reservation.owner.id}`);
                    console.log(`   - Guest: ${updatedGuests[guestIndexNum].name} ${updatedGuests[guestIndexNum].lastName}`);
                    console.log(`   - Estado anterior: ${previousState}, nuevo estado: ${isConfirmed}`);
                    const guestName = `${updatedGuests[guestIndexNum].name} ${updatedGuests[guestIndexNum].lastName}`;
                    // 1. Crear la notificación en la base de datos (patrón que funciona)
                    const newNotification = await strapi.entityService.create("api::notification.notification", {
                        data: {
                            title: "Invitado Confirmado",
                            message: `El invitado ${guestName} ha llegado a su evento`,
                            type: "guest_arrival",
                            read: false,
                            user: reservation.owner.id,
                            data: {
                                reservationId: reservationId,
                                guestName: guestName,
                                guestIndex: guestIndexNum,
                                socialArea: reservation.socialArea,
                                date: reservation.date,
                                url: `/reservations/${reservationId}`,
                            },
                            publishedAt: new Date(),
                        },
                    });
                    console.log(`✅ Notificación creada en BD:`, newNotification.id);
                    // 2. Enviar notificación en tiempo real usando el nuevo sistema WebSocket
                    try {
                        const { notificationManager, } = require("../../../services/notification-manager");
                        const guestData = {
                            id: `${reservationId}-${guestIndexNum}`,
                            name: guestName,
                            reservationId: reservationId,
                            socialArea: reservation.socialArea,
                            date: reservation.date,
                        };
                        const confirmedBy = {
                            id: userWithRole.id,
                            username: userWithRole.username,
                        };
                        console.log(`📤 Enviando notificación WebSocket para confirmación de huésped:`, guestData);
                        // Enviar notificación de confirmación de huésped
                        const result = await notificationManager.sendGuestConfirmationNotification(guestData, confirmedBy);
                        console.log(`✅ Notificación WebSocket enviada:`, result);
                    }
                    catch (realtimeError) {
                        console.error("⚠️ Error al enviar notificación WebSocket:", realtimeError);
                        // No fallar si la notificación en tiempo real falla
                    }
                    console.log(`✅ Notificación procesada para usuario ${reservation.owner.id} por llegada del invitado ${guestName}`);
                }
                catch (notificationError) {
                    console.error("❌ Error enviando notificación:", notificationError);
                    // No fallar la operación principal por error de notificación
                }
            }
            else {
                console.log(`🚫 No se envía notificación:`);
                console.log(`   - isConfirmed: ${isConfirmed}`);
                console.log(`   - previousState: ${previousState}`);
                console.log(`   - reservation.owner: ${!!reservation.owner}`);
            }
            return {
                data: {
                    id: Number(updatedReservation.id),
                    attributes: {
                        ...updatedReservation,
                        id: undefined,
                        guests: updatedGuests,
                    },
                },
                message: isConfirmed
                    ? `Guest ${updatedGuests[guestIndexNum].name} ${updatedGuests[guestIndexNum].lastName} confirmado como presente`
                    : `Guest ${updatedGuests[guestIndexNum].name} ${updatedGuests[guestIndexNum].lastName} marcado como ausente`,
            };
        }
        catch (error) {
            console.error("Error en confirmGuest:", error);
            return ctx.badRequest(`Error al confirmar guest: ${error.message}`);
        }
    },
}));
