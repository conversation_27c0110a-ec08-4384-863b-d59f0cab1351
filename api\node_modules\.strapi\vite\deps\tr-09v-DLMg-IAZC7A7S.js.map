{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/tr-09v-DLMg.mjs"], "sourcesContent": ["const Analytics = \"Analizler\";\nconst Documentation = \"Dokümantasyon\";\nconst Email = \"E-posta\";\nconst Password = \"Şifre\";\nconst Provider = \"Sağlayıcı\";\nconst ResetPasswordToken = \"Şifre sıfırlama anahtarı\";\nconst Role = \"Rol\";\nconst Username = \"Kullanıcı Adı\";\nconst Users = \"Kullanıcılar\";\nconst anErrorOccurred = \"Haydaa! Bir şeyler ters gitti. Lütfen tekrar dene.\";\nconst clearLabel = \"Temizle\";\nconst dark = \"Koyu\";\nconst light = \"Açık\";\nconst or = \"YA DA\";\nconst skipToContent = \"İçeriğe atla\";\nconst submit = \"Gönder\";\nconst tr = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Hesabın donduruldu.\",\n\t\"Auth.components.Oops.text.admin\": \"<PERSON>alı olduğunu düşünüyorsanız lütfen yöneticinize ulaşın.\",\n\t\"Auth.components.Oops.title\": \"Haydaa...\",\n\t\"Auth.form.active.label\": \"Aktif\",\n\t\"Auth.form.button.forgot-password\": \"E-posta gönder\",\n\t\"Auth.form.button.go-home\": \"ANASAYFAYA GERİ DÖN\",\n\t\"Auth.form.button.login\": \"Giriş\",\n\t\"Auth.form.button.password-recovery\": \"Şifre Kurtarma\",\n\t\"Auth.form.button.register\": \"Başlamaya hazır\",\n\t\"Auth.form.email.label\": \"E-posta\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Hesabınız yönetici tarafından engellendi.\",\n\t\"Auth.form.error.code.provide\": \"Geçersiz sağlanmış kod.\",\n\t\"Auth.form.error.confirmed\": \"Tanımladığınız e-posta onaylanmadı.\",\n\t\"Auth.form.error.email.invalid\": \"E-postası geçersiz.\",\n\t\"Auth.form.error.email.provide\": \"Lütfen kullanıcı adınızı veya e-postanızı belirtin.\",\n\t\"Auth.form.error.email.taken\": \"E-posta zaten alınmış\",\n\t\"Auth.form.error.invalid\": \"Kimlik veya şifre geçersiz.\",\n\t\"Auth.form.error.params.provide\": \"Geçersiz sağlanmış kod parametresi.\",\n\t\"Auth.form.error.password.format\": \"Şifreniz `$` sembolünü üç kezden fazla içeremez.\",\n\t\"Auth.form.error.password.local\": \"Bu kullanıcı hiçbir bir şifre belirlemedi; hesap oluşturma sırasında kullanılan sağlayıcı aracılığıyla lütfen giriş yapınız..\",\n\t\"Auth.form.error.password.matching\": \"Parolalar uyuşmuyor.\",\n\t\"Auth.form.error.password.provide\": \"Lütfen şifrenizi girin.\",\n\t\"Auth.form.error.ratelimit\": \"Çok fazla deneme var. Lütfen bir dakika sonra tekrar deneyin.\",\n\t\"Auth.form.error.user.not-exist\": \"Bu e-posta bulunmamaktadır..\",\n\t\"Auth.form.error.username.taken\": \"Kullanıcı adı zaten alınmış\",\n\t\"Auth.form.firstname.label\": \"Adın\",\n\t\"Auth.form.firstname.placeholder\": \"ör. Zeynep\",\n\t\"Auth.form.forgot-password.email.label\": \"E-postanızı giriniz\",\n\t\"Auth.form.forgot-password.email.label.success\": \"E-posta başarıyla gönderildi, \",\n\t\"Auth.form.lastname.label\": \"Soyadın\",\n\t\"Auth.form.lastname.placeholder\": \"ör. Yılmaz\",\n\t\"Auth.form.password.hide-password\": \"Şifreyi gizle\",\n\t\"Auth.form.password.hint\": \"8 karakterden uzun olmalı ve en az 1 büyük harf, 1 küçük harf ve 1 sayı içermeli\",\n\t\"Auth.form.password.show-password\": \"Şifreyi göster\",\n\t\"Auth.form.register.news.label\": \"Beni gelecekteki özellikler ve geliştirmeler hakkında bilgilendir (bunu seçerek {terms} ve {policy}'leri kabul etmiş sayılırsınız)\",\n\t\"Auth.form.register.subtitle\": \"Bilgiler yalnızca Strapi kimlik doğrulaması için kullanılacak. Tüm veriler sizin veritabanınızda saklanacak.\",\n\t\"Auth.form.rememberMe.label\": \"Beni hatırla\",\n\t\"Auth.form.username.label\": \"Kullanıcı Adı\",\n\t\"Auth.form.username.placeholder\": \"Kai Doe\",\n\t\"Auth.form.welcome.subtitle\": \"Strapi hesabına giriş yap\",\n\t\"Auth.form.welcome.title\": \"Strapi'ye hoşgeldiniz!\",\n\t\"Auth.link.forgot-password\": \"Parolanızı mı unuttunuz ?\",\n\t\"Auth.link.ready\": \"Zaten kayıtlı mısınız?\",\n\t\"Auth.link.signin\": \"Giriş yap\",\n\t\"Auth.link.signin.account\": \"Hesabın var mı?\",\n\t\"Auth.login.sso.divider\": \"Ya da bunlarla giriş yap\",\n\t\"Auth.login.sso.loading\": \"Sağlayıcılar yükleniyor...\",\n\t\"Auth.login.sso.subtitle\": \"Hesabına SSO ile giriş yap\",\n\t\"Auth.privacy-policy-agreement.policy\": \"gizlilik sözleşmesi\",\n\t\"Auth.privacy-policy-agreement.terms\": \"koşullar\",\n\t\"Auth.reset-password.title\": \"Şifreni sıfırla\",\n\t\"Content Manager\": \"İçerik Yönetimi\",\n\t\"Content Type Builder\": \"İçerik-Tipi Kurucusu\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Dosya yükleme\",\n\t\"HomePage.head.title\": \"Anasayfa\",\n\t\"HomePage.roadmap\": \"Yol haritamızı görüntüleyin\",\n\t\"HomePage.welcome.congrats\": \"Tebrikler!\",\n\t\"HomePage.welcome.congrats.content\": \"İlk yönetici olarak giriş yaptınız. Strapi'nin güçlü özelliklerini keşfetmek için,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"ilk İçerik-Tipi'ni yaratmanızı öneriyoruz.\",\n\t\"Media Library\": \"Ortam Kütüphanesi\",\n\t\"New entry\": \"Yeni kayıt\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Settings.PageTitle\": \"Ayarlar - {name}\",\n\t\"Settings.tokens.Button.cancel\": \"İptal\",\n\t\"Settings.tokens.Button.regenerate\": \"Yeniden üret\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"Oluşturuldu\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"Tanım\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"En son kullanıldı\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"İsim\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"Token tipi\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"Tokenı yeniden üret.\",\n\t\"Settings.apiTokens.addFirstToken\": \"İlk API Tokenınını ekle\",\n\t\"Settings.apiTokens.addNewToken\": \"Yeni API Tokenı ekle\",\n\t\"Settings.tokens.copy.editMessage\": \"Güvenlik sebebiyle, tokenı yalnızca bir kere görebilirsin.\",\n\t\"Settings.tokens.copy.editTitle\": \"Bu tokena artık erişilemez.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Bu tokenı kopyalamayı unutma. Bir daha erişemeyeceksin!\",\n\t\"Settings.apiTokens.create\": \"Yeni API Tokenı oluştur\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"Sadece bir yol ile bağlanmış eylemler listelenmektedir.\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"İzinler\",\n\t\"Settings.apiTokens.description\": \"API'ı kullanmak için oluşturulmuş token listesi\",\n\t\"Settings.tokens.duration.30-days\": \"30 gün\",\n\t\"Settings.tokens.duration.7-days\": \"7 gün\",\n\t\"Settings.tokens.duration.90-days\": \"90 gün\",\n\t\"Settings.tokens.duration.expiration-date\": \"Sona erme tarihi\",\n\t\"Settings.tokens.duration.unlimited\": \"Sınırsız\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"Henüz hiç içeriğin yok...\",\n\t\"Settings.tokens.form.duration\": \"Token süresi\",\n\t\"Settings.tokens.form.type\": \"Token tipi\",\n\t\"Settings.tokens.notification.copied\": \"Token panoya kopyalandı.\",\n\t\"Settings.tokens.popUpWarning.message\": \"Bu tokenı yeniden üretmek istediğinden emin misin?\",\n\t\"Settings.apiTokens.title\": \"API Tokenları\",\n\t\"Settings.tokens.types.full-access\": \"Tam yetki\",\n\t\"Settings.tokens.types.read-only\": \"Salt-okunur\",\n\t\"Settings.application.customization\": \"Özelleştirme\",\n\t\"Settings.application.customization.carousel-hint\": \"Yönetim paneli logosunu değiştir. (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)\",\n\t\"Settings.application.customization.carousel-slide.label\": \"Logo slaytı\",\n\t\"Settings.application.customization.carousel.change-action\": \"Logoyu değiştir\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Logoyu sıfırla\",\n\t\"Settings.application.customization.carousel.title\": \"Logo\",\n\t\"Settings.application.customization.modal.cancel\": \"İptal\",\n\t\"Settings.application.customization.modal.pending\": \"Bekleyen logo\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"görsel\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Başka bir logo seç\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Yüklemeden önce seçilen logoyu yönet\",\n\t\"Settings.application.customization.modal.pending.title\": \"Logo yüklemeye hazır\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Logo yükle\",\n\t\"Settings.application.customization.modal.tab.label\": \"Dosyaları nasıl yüklemek istersin?\",\n\t\"Settings.application.customization.modal.upload\": \"Logo yükle\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Dosyalara gözat\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Buraya sürükle bırak ya da\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Desteklenmeyen biçim algılandı (desteklenen biçimler: jpeg, jpg, png, svg).\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Ağ hatası\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"Yüklenen dosya çok büyük (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"Bilgisayarımdan\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"URLden\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"İleri\",\n\t\"Settings.application.description\": \"Yönetim panelinin tüm bilgileri\",\n\t\"Settings.application.edition-title\": \"mevcut plan\",\n\t\"Settings.application.get-help\": \"Yardım al\",\n\t\"Settings.application.link-pricing\": \"Tüm ücret planlarını gör\",\n\t\"Settings.application.link-upgrade\": \"Admin panelini yükselt\",\n\t\"Settings.application.node-version\": \"node versiyonu\",\n\t\"Settings.application.strapi-version\": \"strapi versiyonu\",\n\t\"Settings.application.strapiVersion\": \"strapi versiyonu\",\n\t\"Settings.application.title\": \"Kuşbakışı\",\n\t\"Settings.error\": \"Hata\",\n\t\"Settings.global\": \"Genel Ayarlar\",\n\t\"Settings.permissions\": \"Yönetim paneli\",\n\t\"Settings.permissions.category\": \"{category} için izin ayarları\",\n\t\"Settings.permissions.category.plugins\": \"{category} eklentisi için izin ayarları\",\n\t\"Settings.permissions.conditions.anytime\": \"Her zaman\",\n\t\"Settings.permissions.conditions.apply\": \"Uygula\",\n\t\"Settings.permissions.conditions.can\": \"Yapabilir\",\n\t\"Settings.permissions.conditions.conditions\": \"Koşullar\",\n\t\"Settings.permissions.conditions.links\": \"Bağlantılar\",\n\t\"Settings.permissions.conditions.no-actions\": \"Koşulları belirtmeden önce eylemleri (oluştur, oku, güncelle, ...) seçmelisin.\",\n\t\"Settings.permissions.conditions.none-selected\": \"Her zaman\",\n\t\"Settings.permissions.conditions.or\": \"YA DA\",\n\t\"Settings.permissions.conditions.when\": \"Olduğunda\",\n\t\"Settings.permissions.select-all-by-permission\": \"Tüm {label} izinlerini seç\",\n\t\"Settings.permissions.select-by-permission\": \"{label} iznini seç\",\n\t\"Settings.permissions.users.active\": \"Aktif\",\n\t\"Settings.permissions.users.create\": \"Kullanıcı davet et\",\n\t\"Settings.permissions.users.email\": \"E-Posta\",\n\t\"Settings.permissions.users.firstname\": \"Adı\",\n\t\"Settings.permissions.users.form.sso\": \"SSO ile bağlan\",\n\t\"Settings.permissions.users.form.sso.description\": \"Açıldığında kullanıcılar SSO ile giriş yapabilir\",\n\t\"Settings.permissions.users.inactive\": \"Pasif\",\n\t\"Settings.permissions.users.lastname\": \"Soyadı\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Strapi yönetim paneline erişimi olan kullanıcılar\",\n\t\"Settings.permissions.users.roles\": \"Roller\",\n\t\"Settings.permissions.users.strapi-author\": \"Yazar\",\n\t\"Settings.permissions.users.strapi-editor\": \"Editör\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Süper Yönetici\",\n\t\"Settings.permissions.users.tabs.label\": \"Sekme İzinleri\",\n\t\"Settings.permissions.users.user-status\": \"Kullanıcı durumu\",\n\t\"Settings.permissions.users.username\": \"Kullanıcı adı\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Profil verilerin yüklendi\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Seçilmiş arayüz dilini temizle\",\n\t\"Settings.profile.form.section.experience.here\": \"buradan\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Arayüz dili\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Bu yalnızca senin arayüzünü seçilen dilde gösterecek.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Tercih değişiklikleri yalnızca sana uygulanır. Daha fazla bilgi için {here}.\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Arayüzünü seçilen modda gösterir.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Arayüz modu\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} modu\",\n\t\"Settings.profile.form.section.experience.title\": \"Deneyim\",\n\t\"Settings.profile.form.section.head.title\": \"Kullanıcı profili\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Profil sayfası\",\n\t\"Settings.roles.create.description\": \"Role verilen hakları tanımla\",\n\t\"Settings.roles.create.title\": \"Rol oluştur\",\n\t\"Settings.roles.created\": \"Rol oluşturuldu\",\n\t\"Settings.roles.edit.title\": \"Rolü düzenle\",\n\t\"Settings.roles.form.button.users-with-role\": \"Bu rolde {number} kullanıcı\",\n\t\"Settings.roles.form.created\": \"Oluşturuldu\",\n\t\"Settings.roles.form.description\": \"Rolün adı ve tanımı\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} izinleri\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Alanların izinleri\",\n\t\"Settings.roles.form.permissions.create\": \"Oluştur\",\n\t\"Settings.roles.form.permissions.delete\": \"Sil\",\n\t\"Settings.roles.form.permissions.publish\": \"Yayınla\",\n\t\"Settings.roles.form.permissions.read\": \"Oku\",\n\t\"Settings.roles.form.permissions.update\": \"Güncelle\",\n\t\"Settings.roles.list.button.add\": \"Yeni rol ekle\",\n\t\"Settings.roles.list.description\": \"Rollerin listesi\",\n\t\"Settings.roles.title.singular\": \"rol\",\n\t\"Settings.sso.description\": \"Single Sign-On (SSO) özelliğini ayarla.\",\n\t\"Settings.sso.form.registration.description\": \"SSO girişi sırasında hesabı olmayanlara yeni hesap oluştur\",\n\t\"Settings.sso.form.registration.label\": \"Otomatik kayıt\",\n\t\"Settings.sso.title\": \"Single Sign-On\",\n\t\"Settings.webhooks.create\": \"Webhook oluştur\",\n\t\"Settings.webhooks.create.header\": \"Yeni başlık yarat\",\n\t\"Settings.webhooks.created\": \"Webhook oluşturuldu\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Bu eylem yalnızca Taslak/Yayımla sistemi açık olduğunda vardır\",\n\t\"Settings.webhooks.events.create\": \"Oluştur\",\n\t\"Settings.webhooks.events.update\": \"Güncelle\",\n\t\"Settings.webhooks.form.events\": \"Etkinlikler\",\n\t\"Settings.webhooks.form.headers\": \"Başlıklar\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.headers.remove\": \"{number} başlık satırını kaldır\",\n\t\"Settings.webhooks.key\": \"Anahtar\",\n\t\"Settings.webhooks.list.button.add\": \"Yeni webhook ekle\",\n\t\"Settings.webhooks.list.description\": \"POST değişiklikleri bildirimi al.\",\n\t\"Settings.webhooks.list.empty.description\": \"İlkini bu listeye ekleyin.\",\n\t\"Settings.webhooks.list.empty.link\": \"Dökümantasyonumuzu görüntüleyin\",\n\t\"Settings.webhooks.list.empty.title\": \"Henüz bir webhook yok\",\n\t\"Settings.webhooks.list.th.actions\": \"eylemler\",\n\t\"Settings.webhooks.list.th.status\": \"durum\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooklar\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength} dosya seçildi\",\n\t\"Settings.webhooks.trigger\": \"Tetikleyici\",\n\t\"Settings.webhooks.trigger.cancel\": \"Tetikleyiciyi iptal et\",\n\t\"Settings.webhooks.trigger.pending\": \"Bekleniyor...\",\n\t\"Settings.webhooks.trigger.save\": \"Lütfen tetikleyiciyi kaydedin\",\n\t\"Settings.webhooks.trigger.success\": \"Başarılı!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Tetikleyici başarılı\",\n\t\"Settings.webhooks.trigger.test\": \"Test-tetikleyici\",\n\t\"Settings.webhooks.trigger.title\": \"Tetikleyiciden önce kaydet\",\n\t\"Settings.webhooks.value\": \"Değer\",\n\t\"Usecase.back-end\": \"Arkayüz Geliştiricisi\",\n\t\"Usecase.button.skip\": \"Bu soruyu atla\",\n\t\"Usecase.content-creator\": \"İçerik Üreticisi\",\n\t\"Usecase.front-end\": \"Önyüz Geliştiricisi\",\n\t\"Usecase.full-stack\": \"Tümyüz Geliştiricisi\",\n\t\"Usecase.input.work-type\": \"Ne işle meşgulsun?\",\n\t\"Usecase.notification.success.project-created\": \"Proje başarıyla oluşturuldu\",\n\t\"Usecase.other\": \"Diğer\",\n\t\"Usecase.title\": \"Biraz kendinden bahset\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Kullanıcılar & İzinler\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"Kategoriler\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count} kategori seçildi\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"Koleksiyonlar\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count} koleksiyon seçildi\",\n\t\"admin.pages.MarketPlacePage.head\": \"Pazaryeri - Eklentiler\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Bize aradığın eklentiyi anlat ki biz de eklenti geliştirici topluluğumuzdaki ilham arayanlara iletelim!\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Aradığın eklentiyi bulamadın mı?\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Strapi Market'e erişmek için Internet'e bağlı olmalısın.\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Çevrimdışısın\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Yükleme komutunu kopyala\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"Yükleme komutu terminaline yapıştırılmak için hazır\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"Bu eklenti haftada {downloadsCount} kez indirilmiş\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"Bu eklenti Github'da {starsCount} yıldız almış\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"Daha fazla\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} hakkında daha fazla öğren\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"Daha\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Yüklendi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Strapi tarafından geliştirildi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Eklenti Strapi tarafından onaylandı\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"Strapi'yi \\\"{strapiAppVersion}\\\" versiyonundan \\\"{versionRange}\\\" versiyonuna yükselt\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"Yüklü olan \\\"{strapiAppVersion}\\\" Strapi versiyonu ile uyumluluğu doğrulanamıyor\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"Eklentiler\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"Bu sağlayıcı haftada {downloadsCount} kez indirilmiş\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"Bu sağlayıcı Github'da {starsCount} yıldız almış\",\n\t\"admin.pages.MarketPlacePage.providers\": \"Sağlayıcılar\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Aramayı temizle\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"\\\"{target}\\\" için sonuç yok\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Arama\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"Alfabetik sıraya diz\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Alfabetik sıra\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"Yeniden eskiye diz\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"Yeniden eskiye\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Eklenti gönder\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"Sağlayıcı gönder\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Strapi'den daha fazlasını al\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi Eklenti ve Sağlayıcıları\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Panoya kopyala\",\n\t\"app.component.search.label\": \"{target} için arama yap\",\n\t\"app.component.table.duplicate\": \"{target} kaydını yinele\",\n\t\"app.component.table.edit\": \"{target} kaydını düzenle\",\n\t\"app.component.table.select.one-entry\": \"{target} kaydını seç\",\n\t\"app.components.BlockLink.blog\": \"Blog\",\n\t\"app.components.BlockLink.blog.content\": \"Strapi ve ekosistemi hakkındaki son haberleri oku.\",\n\t\"app.components.BlockLink.code\": \"Kod örnekleri\",\n\t\"app.components.BlockLink.documentation.content\": \"Başlıca konseptleri, rehberleri ve talimatları keşfet.\",\n\t\"app.components.BlockLink.tutorial\": \"Eğitimler\",\n\t\"app.components.BlockLink.tutorial.content\": \"Strapi'yi kullanmak ve özelleştirmek için adım adım talimatları takip et.\",\n\t\"app.components.Button.cancel\": \"İptal\",\n\t\"app.components.Button.confirm\": \"Onayla\",\n\t\"app.components.Button.reset\": \"Sıfırla\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Çok Yakında\",\n\t\"app.components.ConfirmDialog.title\": \"Onay\",\n\t\"app.components.DownloadInfo.download\": \"İndirme devam ediyor...\",\n\t\"app.components.DownloadInfo.text\": \"Bu birkaç dakika sürebilir. Sabrınız için teşekkürler.\",\n\t\"app.components.EmptyAttributes.title\": \"Alan henüz yok\",\n\t\"app.components.EmptyStateLayout.content-document\": \"İçerik bulunamadı\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Bu içeriğe erişim yetkiniz yok.\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Buradaki tüm içerikleri İçerik Yöneticisi ile oluştur ve yönet.</p><p>Ör: Blog websitesi örneğini bir adım daha öteye götürürsek, kişiler burada istedikleri gibi Makale yazabilir, kaydedip yayımlayabilir.</p><p>💡 Bir ipucu - Oluşturduğun içeriklerde yayınla butonuna basmayı unutma.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ İçerik oluştur\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Müthiş! Son bir adım kaldı.</p><b>🚀 İçeriği çalışırken gör</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"APIyi test et\",\n\t\"app.components.GuidedTour.CM.success.title\": \"Adım 2: Tamamlandı ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>Koleksiyon tipleri birden çok girdiyi yönetmene yardımcı olur. Tekil tipler tek bir girdiyi yönetmek için uygundur.</p> <p>Ör: Bir blog sayfası için, Makaleler Koleksiyon tipinde olabilecekken, Ana Sayfa Tekil tipte olacaktır.</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Bir Koleksiyon tipi kur\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 İlk Koleksiyon tipini oluştur\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>İyi gidiyorsun!</p><b>⚡️ Dünya ile ne paylaşmak isterdin?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"Adım 1: Tamamlandı ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Bir kimlik doğrulama tokenı üret ve yeni oluşturduğun içeriğe ulaş.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"Bir API Token üret\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 İçeriği çalışırken gör\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Bir HTTP isteiği yaparak içeriği çalışırlen gör:</p><ul><li><p>URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>İçeriklerle etkileşimin farklı yöntemler için <documentationLink>dokümantasyonu</documentationLink> oku.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Ana sayfaya geri dön\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"Adım 3: Tamamlandı ✅\",\n\t\"app.components.GuidedTour.create-content\": \"İçerik oluştur\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ Dünya ile ne paylaşmak isterdin?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"İçerik tipi kurucusuna git\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 İçerik yapısını kur\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"APIyi test et\",\n\t\"app.components.GuidedTour.skip\": \"Turu atla\",\n\t\"app.components.GuidedTour.title\": \"Başlamak için 3 adım\",\n\t\"app.components.HomePage.button.blog\": \"BLOG SAYFASINDA DAHA FAZLASINI GÖRÜN\",\n\t\"app.components.HomePage.community\": \"Topluluğumuza ulaşın\",\n\t\"app.components.HomePage.community.content\": \"Farklı kanallarda takım üyeleri, katkıda bulunanlar ve geliştiricilere ulaşın.\",\n\t\"app.components.HomePage.create\": \"İlk içerik tipini oluştur\",\n\t\"app.components.HomePage.welcome\": \"Panele hoşgeldiniz.\",\n\t\"app.components.HomePage.welcome.again\": \"Hoşgeldiniz \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Sizi topluluk üyelerinden biri olarak görmekten mutluyuz. Sürekli olarak geri bildirim alabilmemiz için bize doğrudan mesaj göndermeye çekinmeyin \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Projenizde ilerleme kaydedeceğinizi umuyoruz... Strapi ile ilgili en yeni yenilikleri okumaktan çekinmeyin. Ürünü geri bildirimlerinize göre geliştirmek için elimizden geleni yapıyoruz.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"sorunlar\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" yada yükselt \",\n\t\"app.components.ImgPreview.hint\": \"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}\",\n\t\"app.components.ImgPreview.hint.browse\": \"gözat\",\n\t\"app.components.InputFile.newFile\": \"Yeni dosya ekle\",\n\t\"app.components.InputFileDetails.open\": \"Yeni sekmede aç\",\n\t\"app.components.InputFileDetails.originalName\": \"Orjinal isim:\",\n\t\"app.components.InputFileDetails.remove\": \"Bu dosyayı sil\",\n\t\"app.components.InputFileDetails.size\": \"Boyut:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Eklentiyi indirmek ve yüklemek bir kaç saniye sürebilir.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"İndiriliyor...\",\n\t\"app.components.InstallPluginPage.description\": \"Uygulamanızı rahatlıkla genişletin.\",\n\t\"app.components.LeftMenu.collapse\": \"Menüyü ufalt\",\n\t\"app.components.LeftMenu.expand\": \"Menüyü büyüt\",\n\t\"app.components.LeftMenu.general\": \"Genel\",\n\t\"app.components.LeftMenu.logo.alt\": \"Uygulama logosu\",\n\t\"app.components.LeftMenu.logout\": \"Çıkış\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Panosu\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"İş Yeri\",\n\t\"app.components.LeftMenu.plugins\": \"Eklentiler\",\n\t\"app.components.LeftMenuFooter.help\": \"Yardım\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Gururla sunar \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Koleksiyon Tipleri\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Yapılandırma\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Genel\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Yüklenen eklenti bulunmamaktadır.\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Eklentiler\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Tekil Tipler\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Eklentiyi kaldırmak bir kaç saniye alabilir.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Kaldırılıyor\",\n\t\"app.components.ListPluginsPage.description\": \"Projedeki yüklenen eklentiler.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Eklenti Listesi\",\n\t\"app.components.Logout.logout\": \"Çıkış Yap\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.MarketplaceBanner\": \"Strapi Awesome'da projeni hayata geçirmek için harika şeyleri ve topluluk tarafından geliştirilmiş eklentileri keşfet.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"bir strapi roket logosu\",\n\t\"app.components.MarketplaceBanner.link\": \"Şimdi gözden geçir\",\n\t\"app.components.NotFoundPage.back\": \"Anasayfaya geri dön\",\n\t\"app.components.NotFoundPage.description\": \"Bulunamadı\",\n\t\"app.components.Official\": \"Resmi\",\n\t\"app.components.Onboarding.help.button\": \"Yardım butonu\",\n\t\"app.components.Onboarding.label.completed\": \"% tamamlandı\",\n\t\"app.components.Onboarding.title\": \"Başlangıç Videolaro\",\n\t\"app.components.PluginCard.Button.label.download\": \"İndir\",\n\t\"app.components.PluginCard.Button.label.install\": \"Zaten yüklenmiş\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"autoReload özelliği aktif edilmeli. Lütfen uygulamayı `yarn develop` ile başlatın.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Anladım!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Güvenlik nedeniyle bir eklenti yalnızca geliştirme ortamında indirilebilir.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"İndirme imkansız\",\n\t\"app.components.PluginCard.compatible\": \"Uygulamanızla uyumlu\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Toplulukla uyumlu\",\n\t\"app.components.PluginCard.more-details\": \"Daha fazla detay\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Yanlış\",\n\t\"app.components.ToggleCheckbox.on-label\": \"Doğru\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Bir kullanıcı bir ya da daha fazla role sahip olabilir\",\n\t\"app.components.listPlugins.button\": \"Yeni eklenti ekle\",\n\t\"app.components.listPlugins.title.none\": \"Yüklenen eklenti bulunmamaktadır.\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Eklenti kaldırılırken bir hata oluştu\",\n\t\"app.containers.App.notification.error.init\": \"API isteği sırasında bir hata oluştu\",\n\t\"app.links.configure-view\": \"Ekranı düzenle\",\n\t\"app.page.not.found\": \"Haydaa! Aradığın sayfayı bulamıyor gibiyiz...\",\n\t\"app.static.links.cheatsheet\": \"ÖzetYardım\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Filtre ekle\",\n\t\"app.utils.close-label\": \"Kapat\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.delete\": \"Sil\",\n\t\"app.utils.duplicate\": \"Yinele\",\n\t\"app.utils.edit\": \"Düzenle\",\n\t\"app.utils.errors.file-too-big.message\": \"Dosya çok büyük\",\n\t\"app.utils.filter-value\": \"Değeri filtrele\",\n\t\"app.utils.filters\": \"Filtreler\",\n\t\"app.utils.notify.data-loaded\": \"{target} yüklendi\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Yayınla\",\n\t\"app.utils.select-all\": \"Tümünü seç\",\n\t\"app.utils.select-field\": \"Alanı seç\",\n\t\"app.utils.select-filter\": \"Filtreyi seç\",\n\t\"app.utils.unpublish\": \"Yayından Kaldır\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Bu içerik şuanda düzenleniyor. Bir kaç hafta sonra yayında olacak!\",\n\t\"component.Input.error.validation.integer\": \"Değer sayı olmalı\",\n\t\"components.AutoReloadBlocker.description\": \"Strapi'yi aşağıdaki komutlardan biri ile çalıştırın:\",\n\t\"components.AutoReloadBlocker.header\": \"Bu eklenti için tekrar yükleme özelliği gerekiyor.\",\n\t\"components.ErrorBoundary.title\": \"Bir şeyler yanlış gitti...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"içerir\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"içerir (büyük/küçük harfe duyarsız)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"ile biter\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"ile biter (büyük/küçük harfe duyarsız)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"eşittir\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"eşittir (büyük/küçük harfe duyarsız)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"büyüktür\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"büyük eşittir\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"küçüktür\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"küçük eşittir\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"eşit değildir\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"eşit değildir (büyük/küçük harfe duyarsız)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"içermez\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"içermez (büyük/küçük harfe duyarsız)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"null değildir\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"null'dur\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"başlar\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"başlar (büyük/küçük harfe duyarsız)\",\n\t\"components.Input.error.attribute.key.taken\": \"Bu değer zaten var.\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Eşit olamaz\",\n\t\"components.Input.error.attribute.taken\": \"Bu alan ismi zaten var.\",\n\t\"components.Input.error.contain.lowercase\": \"Şifre en az bir küçük harf içermelidir\",\n\t\"components.Input.error.contain.number\": \"Şifre en az bir sayı içermelidir\",\n\t\"components.Input.error.contain.uppercase\": \"Şifre en az bir büyük harf içermelidir\",\n\t\"components.Input.error.contentTypeName.taken\": \"Bu isim zaten var.\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Şifreler uyuşmuyor\",\n\t\"components.Input.error.validation.email\": \"Geçersiz e-posta adresi.\",\n\t\"components.Input.error.validation.json\": \"Bu JSON biçimi ile eşleşmiyor\",\n\t\"components.Input.error.validation.lowercase\": \"Değerin tamamı küçük harf olmalıdır\",\n\t\"components.Input.error.validation.max\": \"Değer çok yüksek {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Değer çok uzun {max}.\",\n\t\"components.Input.error.validation.min\": \"Değer çok az {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Değer çok kısa {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Üstü olamaz\",\n\t\"components.Input.error.validation.regex\": \"Regex ile eşleşmiyor.\",\n\t\"components.Input.error.validation.required\": \"Zorunlu alandır.\",\n\t\"components.Input.error.validation.unique\": \"Değer zaten kullanılmış.\",\n\t\"components.InputSelect.option.placeholder\": \"Buradan seçin\",\n\t\"components.ListRow.empty\": \"Gösterilecek veri bulunmamaktadır.\",\n\t\"components.NotAllowedInput.text\": \"Bu alanı görmek için yetkin yok\",\n\t\"components.OverlayBlocker.description\": \"Sunucunun yeniden başlatılması gereken bir özellik kullanıyorsunuz. Lütfen sunucu çalışana kadar bekleyin.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Sunucu yeniden başlatılmalı, lütfen terminal üzerinden logları kontrol edin.\",\n\t\"components.OverlayBlocker.title\": \"Yeniden başlatılmayı bekliyor...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Yeniden başlatma beklendiğinden uzun sürüyor\",\n\t\"components.PageFooter.select\": \"sayfa başına kayıt\",\n\t\"components.ProductionBlocker.description\": \"Güvenlik nedeniyle, bu eklentiyi diğer ortamlarda devre dışı bırakmamız gerekir.\",\n\t\"components.ProductionBlocker.header\": \"Bu eklenti yalnızca geliştirme aşamasında mevcuttur.\",\n\t\"components.Search.placeholder\": \"Arama...\",\n\t\"components.TableHeader.sort\": \"Şuna göre diz: {label}\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown modu\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Önizleme modu\",\n\t\"components.Wysiwyg.collapse\": \"Daralt\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"H1 başlık\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"H2 başlık\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"H3 başlık\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"H4 başlık\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"H5 başlık\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"H6 başlık\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Başlık ekle\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"karakter\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Genişlet\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"Bunları seç\",\n\t\"components.pagination.go-to\": \"{page} nolu sayfaya git\",\n\t\"components.pagination.go-to-next\": \"Sonraki sayfaya git\",\n\t\"components.pagination.go-to-previous\": \"Önceki sayfaya git\",\n\t\"components.pagination.remaining-links\": \"Ve {number} diğer bağlantı\",\n\t\"components.popUpWarning.button.cancel\": \"Hayır, iptal et\",\n\t\"components.popUpWarning.button.confirm\": \"Evet, onayla\",\n\t\"components.popUpWarning.message\": \"Bunu silmek istediğinizden emin misiniz?\",\n\t\"components.popUpWarning.title\": \"Lütfen onaylayın\",\n\tdark: dark,\n\t\"form.button.continue\": \"Devam\",\n\t\"form.button.done\": \"Tamam\",\n\t\"global.actions\": \"Eylemler\",\n\t\"global.back\": \"Geri\",\n\t\"global.cancel\": \"İptal\",\n\t\"global.change-password\": \"Şifreyi değiştir\",\n\t\"global.content-manager\": \"İçerik Yöneticisi\",\n\t\"global.continue\": \"Devam\",\n\t\"global.delete\": \"Sil\",\n\t\"global.delete-target\": \"Sil: {target}\",\n\t\"global.description\": \"Tanım\",\n\t\"global.details\": \"Detaylar\",\n\t\"global.disabled\": \"Devredışı\",\n\t\"global.documentation\": \"Dokümantasyon\",\n\t\"global.enabled\": \"Etkin\",\n\t\"global.finish\": \"Bitir\",\n\t\"global.marketplace\": \"Pazaryeri\",\n\t\"global.name\": \"İsim\",\n\t\"global.none\": \"Hiçbiri\",\n\t\"global.password\": \"Şifre\",\n\t\"global.plugins\": \"Eklentiler\",\n\t\"global.plugins.content-manager\": \"İçerik Yöneticisi\",\n\t\"global.plugins.content-manager.description\": \"Veritabanındaki verileri görüntüleme, düzenleme ve silmenin kolay yolu.\",\n\t\"global.plugins.content-type-builder\": \"İçerik Tipi Kurucusu\",\n\t\"global.plugins.content-type-builder.description\": \"APInin veri yapısını modelle. Sadece bir iki dakikada yeni alanlar ve ilişkiler oluştur. Projendeki dosyalar otomatik olarak oluşturulur ve güncellenir.\",\n\t\"global.plugins.documentation\": \"Dokümantasyon\",\n\t\"global.plugins.documentation.description\": \"Bir OpenAPI Dokümanı oluştur ve SWAGGER UI ile APIni görselleştir.\",\n\t\"global.plugins.email\": \"E-Posta\",\n\t\"global.plugins.email.description\": \"Uygulamanı e-posta gönderecek şekilde ayarla.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"Varsayılan API metodları ile bir GraphQL uç noktası ekler.\",\n\t\"global.plugins.i18n\": \"Uluslararasılaştırma\",\n\t\"global.plugins.i18n.description\": \"Bu eklenti, hem Yönetim paneli hem de API üzerinden, farklı dillerdeki içeriği oluşturma, okuma ve güncelleme imkanı sağlar.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"Strapi hata olaylarını Sentry'e ilet.\",\n\t\"global.plugins.upload\": \"Ortam Kütüphanesi\",\n\t\"global.plugins.upload.description\": \"Ortam dosyaları yönetimi.\",\n\t\"global.plugins.users-permissions\": \"Roller ve İzinler\",\n\t\"global.plugins.users-permissions.description\": \"Servisinizi JWT'ye dayalı tam bir kimlik doğrulama işlemi ile koruyun. Bu eklenti, kullanıcı grupları arasındaki izinleri yönetmenize izin veren bir ACL stratejisiyle de gelir.\",\n\t\"global.profile\": \"Profil\",\n\t\"global.prompt.unsaved\": \"Bu sayfadan ayrılmak istediğinize emin misiniz? Tüm düzenlemeleriniz kaybolacak\",\n\t\"global.reset-password\": \"Şifreni sıfırla\",\n\t\"global.roles\": \"Roller\",\n\t\"global.save\": \"Kaydet\",\n\t\"global.search\": \"Arama\",\n\t\"global.see-more\": \"Daha fazla\",\n\t\"global.select\": \"Seç\",\n\t\"global.select-all-entries\": \"Tüm girdileri seç\",\n\t\"global.settings\": \"Ayarlar\",\n\t\"global.type\": \"Tip\",\n\t\"global.users\": \"Kullanıcılar\",\n\tlight: light,\n\t\"notification.contentType.relations.conflict\": \"İçerik tipinde çakışan ilişkiler var\",\n\t\"notification.default.title\": \"Bilgi:\",\n\t\"notification.error\": \"Bir hata oluştu\",\n\t\"notification.error.layout\": \"Düzen alınamadı\",\n\t\"notification.form.error.fields\": \"Form birden fazla hata içeriyor\",\n\t\"notification.form.success.fields\": \"Değişiklikler kaydedildi\",\n\t\"notification.link-copied\": \"Bağlantı panoya kopyalandı\",\n\t\"notification.permission.not-allowed-read\": \"Bu dokümanı görme yetkin yok\",\n\t\"notification.success.delete\": \"Öğe silindi\",\n\t\"notification.success.saved\": \"Kaydedildi\",\n\t\"notification.success.title\": \"Başarılı:\",\n\t\"notification.success.apitokencreated\": \"API Token başarıyla oluşturuldu\",\n\t\"notification.success.apitokenedited\": \"API Token başarıyla düzenlendi\",\n\t\"notification.version.update.message\": \"Strapi'nin yeni versiyonu çıktı!\",\n\t\"notification.warning.404\": \"404 - Bulunamadı\",\n\t\"notification.warning.title\": \"Dikkat:\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Bu model bulunmamaktadır.\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, tr as default, light, or, skipToContent, submit };\n//# sourceMappingURL=tr-09v-DLMg.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,sEAAsE;AAAA,EACtE,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}