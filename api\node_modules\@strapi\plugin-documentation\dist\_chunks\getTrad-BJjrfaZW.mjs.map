{"version": 3, "file": "getTrad-BJjrfaZW.mjs", "sources": ["../../admin/src/services/api.ts", "../../admin/src/utils/getTrad.ts"], "sourcesContent": ["import { adminApi } from '@strapi/admin/strapi-admin';\n\nimport { DocumentInfos } from '../types';\n\ntype SettingsInput = {\n  restrictedAccess: boolean;\n  password: string;\n};\n\nconst api = adminApi\n  .enhanceEndpoints({\n    addTagTypes: ['DocumentInfo'],\n  })\n  .injectEndpoints({\n    endpoints: (builder) => {\n      return {\n        getInfo: builder.query<DocumentInfos, void>({\n          query: () => '/documentation/getInfos',\n          providesTags: ['DocumentInfo'],\n        }),\n\n        deleteVersion: builder.mutation<void, { version: string }>({\n          query: ({ version }) => ({\n            url: `/documentation/deleteDoc/${version}`,\n            method: 'DELETE',\n          }),\n          invalidatesTags: ['DocumentInfo'],\n        }),\n\n        updateSettings: builder.mutation<void, { body: SettingsInput }>({\n          query: ({ body }) => ({\n            url: `/documentation/updateSettings`,\n            method: 'PUT',\n            data: body,\n          }),\n          invalidatesTags: ['DocumentInfo'],\n        }),\n\n        regenerateDoc: builder.mutation<void, { version: string }>({\n          query: ({ version }) => ({\n            url: `/documentation/regenerateDoc`,\n            method: 'POST',\n            data: { version },\n          }),\n        }),\n      };\n    },\n  });\n\nexport const {\n  useGetInfoQuery,\n  useDeleteVersionMutation,\n  useUpdateSettingsMutation,\n  useRegenerateDocMutation,\n} = api;\n", "import { pluginId } from '../pluginId';\n\nexport const getTrad = (id: string) => `${pluginId}.${id}`;\n"], "names": [], "mappings": ";;AASA,MAAM,MAAM,SACT,iBAAiB;AAAA,EAChB,aAAa,CAAC,cAAc;AAC9B,CAAC,EACA,gBAAgB;AAAA,EACf,WAAW,CAAC,YAAY;AACf,WAAA;AAAA,MACL,SAAS,QAAQ,MAA2B;AAAA,QAC1C,OAAO,MAAM;AAAA,QACb,cAAc,CAAC,cAAc;AAAA,MAAA,CAC9B;AAAA,MAED,eAAe,QAAQ,SAAoC;AAAA,QACzD,OAAO,CAAC,EAAE,eAAe;AAAA,UACvB,KAAK,4BAA4B,OAAO;AAAA,UACxC,QAAQ;AAAA,QAAA;AAAA,QAEV,iBAAiB,CAAC,cAAc;AAAA,MAAA,CACjC;AAAA,MAED,gBAAgB,QAAQ,SAAwC;AAAA,QAC9D,OAAO,CAAC,EAAE,YAAY;AAAA,UACpB,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,QAAA;AAAA,QAER,iBAAiB,CAAC,cAAc;AAAA,MAAA,CACjC;AAAA,MAED,eAAe,QAAQ,SAAoC;AAAA,QACzD,OAAO,CAAC,EAAE,eAAe;AAAA,UACvB,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM,EAAE,QAAQ;AAAA,QAAA;AAAA,MAClB,CACD;AAAA,IAAA;AAAA,EAEL;AACF,CAAC;AAEU,MAAA;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;ACpDG,MAAM,UAAU,CAAC,OAAe,GAAG,QAAQ,IAAI,EAAE;"}