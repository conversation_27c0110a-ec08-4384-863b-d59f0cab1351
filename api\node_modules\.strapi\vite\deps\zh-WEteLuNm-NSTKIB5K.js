import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/zh-WEteLuNm.mjs
var Analytics = "分析器";
var Documentation = "文件";
var Email = "電子郵件";
var Password = "密碼";
var Provider = "提供者";
var ResetPasswordToken = "密碼重設代碼";
var Role = "身份";
var light = "明亮";
var dark = "黑暗";
var Username = "使用者名稱";
var Users = "使用者";
var anErrorOccurred = "糟糕！出了點問題。請再試一次。";
var clearLabel = "清除";
var or = "或";
var skipToContent = "跳過到內容";
var submit = "送出";
var zh = {
  Analytics,
  "Auth.components.Oops.text": "您的帳號已經被停用",
  "Auth.components.Oops.text.admin": "如果是個誤會，請聯繫您的管理員協助處理。",
  "Auth.components.Oops.title": "唉呀...",
  "Auth.form.active.label": "啟用",
  "Auth.form.button.forgot-password": "傳送電子郵件",
  "Auth.form.button.go-home": "回到首頁",
  "Auth.form.button.login": "登入",
  "Auth.form.button.login.providers.error": "無法透過所選的驗證方式連接您的帳號。",
  "Auth.form.button.login.strapi": "透過 Strapi 登入",
  "Auth.form.button.password-recovery": "找回密碼",
  "Auth.form.button.register": "準備開始",
  "Auth.form.confirmPassword.label": "再確認一次密碼",
  "Auth.form.currentPassword.label": "目前密碼",
  "Auth.form.email.label": "電子郵件",
  "Auth.form.email.placeholder": "例如 <EMAIL>",
  "Auth.form.error.blocked": "您的帳號已經被系統管理員停用。",
  "Auth.form.error.code.provide": "提供的代碼不正確。",
  "Auth.form.error.confirmed": "您的電子郵件地址尚未經過認證。",
  "Auth.form.error.email.invalid": "電子郵件地址無效。",
  "Auth.form.error.email.provide": "請輸入使用者名稱或電子郵件地址。",
  "Auth.form.error.email.taken": "此電子郵件地址已被使用。",
  "Auth.form.error.invalid": "使用者名稱或密碼不正確。",
  "Auth.form.error.params.provide": "參數不正確。",
  "Auth.form.error.password.format": "您的密碼不能包含超過三個 `$` 符號",
  "Auth.form.error.password.local": "這個使用者還沒有設定本站密碼，請使用建立帳號時使用的驗證方式登入。",
  "Auth.form.error.password.matching": "密碼不符。",
  "Auth.form.error.password.provide": "請輸入您的密碼。",
  "Auth.form.error.ratelimit": "嘗試次數過多，請稍後再試。",
  "Auth.form.error.user.not-exist": "這個電子郵件地址不存在。",
  "Auth.form.error.username.taken": "使用者名稱已被使用。",
  "Auth.form.firstname.label": "名字",
  "Auth.form.firstname.placeholder": "例如 Kai",
  "Auth.form.forgot-password.email.label": "輸入您的電子郵件地址",
  "Auth.form.forgot-password.email.label.success": "郵件已成功寄至 ",
  "Auth.form.lastname.label": "姓氏",
  "Auth.form.lastname.placeholder": "例如 Doe",
  "Auth.form.password.hide-password": "隱藏密碼",
  "Auth.form.password.hint": "密碼至少要有 8 個字元，1 個大寫字母，1 個小寫字母和 1 個數字",
  "Auth.form.password.show-password": "顯示密碼",
  "Auth.form.register.news.label": "有新功能和改進時通知我 (打勾表示您接受 {terms} 和 {policy})。",
  "Auth.form.register.subtitle": "認證僅用於 Strapi 的驗證，所有儲存的資料都放在您的資料庫中。",
  "Auth.form.rememberMe.label": "記得我",
  "Auth.form.username.label": "使用者名稱",
  "Auth.form.username.placeholder": "例如 Kai Doe",
  "Auth.form.welcome.subtitle": "登入您的 Strapi 帳號",
  "Auth.form.welcome.title": "歡迎使用 Strapi！",
  "Auth.link.forgot-password": "忘記密碼？",
  "Auth.link.ready": "準備好登入了嗎？",
  "Auth.link.signin": "登入",
  "Auth.link.signin.account": "已經有帳號了嗎？",
  "Auth.login.sso.divider": "或登入透過",
  "Auth.login.sso.loading": "正在載入驗證方式...",
  "Auth.login.sso.subtitle": "透過 SSO 登入您的帳號",
  "Auth.privacy-policy-agreement.policy": "隱私權政策",
  "Auth.privacy-policy-agreement.terms": "服務條款",
  "Auth.reset-password.title": "重設密碼",
  "Content Manager": "內容管理員",
  "Content Type Builder": "內容型別建立員",
  Documentation,
  Email,
  "Files Upload": "檔案上傳",
  "HomePage.head.title": "首頁",
  "HomePage.roadmap": "查看我們的藍圖",
  "HomePage.welcome.congrats": "恭喜！",
  "HomePage.welcome.congrats.content": "您以首位管理員的身分登入，若要探索 Strapi 的強大功能，",
  "HomePage.welcome.congrats.content.bold": "建議您從建立第一個內容型別開始。",
  "Media Library": "媒體庫",
  "New entry": "新增項目",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "角色與權限",
  "Roles.ListPage.notification.delete-all-not-allowed": "無法刪除一些角色，因為角色與使用者還有關聯。",
  "Roles.ListPage.notification.delete-not-allowed": "與使用者有關聯的角色無法刪除",
  "Roles.RoleRow.select-all": "選擇 {name} 進行大量操作",
  "Roles.RoleRow.user-count": "{number, plural, =0 {#  user} one {#  user} other {# users}}",
  "Roles.components.List.empty.withSearch": "找不到您搜尋的角色 ({search})...",
  "Settings.PageTitle": "設定 - {name}",
  "Settings.apiTokens.addFirstToken": "新增您的第一個 API 權杖",
  "Settings.apiTokens.addNewToken": "新增 API 權杖",
  "Settings.tokens.copy.editMessage": "基於安全考量，您只能查看您的權杖一次。",
  "Settings.tokens.copy.editTitle": "這個權杖已無法存取。",
  "Settings.tokens.copy.lastWarning": "請務必複製這個權杖，錯過就不會再顯示了！",
  "Settings.apiTokens.create": "新增",
  "Settings.apiTokens.description": "已產生可使用 API 的權杖列表",
  "Settings.apiTokens.emptyStateLayout": "您還沒有任何內容...",
  "Settings.apiTokens.ListView.headers.name": "名稱",
  "Settings.apiTokens.ListView.headers.description": "說明",
  "Settings.apiTokens.ListView.headers.type": "權杖類型",
  "Settings.apiTokens.ListView.headers.createdAt": "建立時間",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "最後使用時間",
  "Settings.tokens.notification.copied": "權杖已複製到剪貼簿。",
  "Settings.apiTokens.title": "API 權杖",
  "Settings.tokens.types.full-access": "完全控制",
  "Settings.tokens.types.read-only": "唯讀",
  "Settings.tokens.duration.7-days": "7 天",
  "Settings.tokens.duration.30-days": "30 天",
  "Settings.tokens.duration.90-days": "90 天",
  "Settings.tokens.duration.unlimited": "無限制",
  "Settings.tokens.form.duration": "權杖有效期限",
  "Settings.tokens.form.type": "權杖類型",
  "Settings.tokens.duration.expiration-date": "到期日",
  "Settings.apiTokens.createPage.permissions.title": "權限",
  "Settings.apiTokens.createPage.permissions.description": "下方僅列出與路徑繫結的操作。",
  "Settings.tokens.RegenerateDialog.title": "重新產生權杖",
  "Settings.tokens.popUpWarning.message": "您確定要重新產生此權杖嗎？",
  "Settings.tokens.Button.cancel": "取消",
  "Settings.tokens.Button.regenerate": "重新產生",
  "Settings.application.description": "管理後台的全域資訊",
  "Settings.application.edition-title": "目前方案",
  "Settings.application.get-help": "取得協助",
  "Settings.application.link-pricing": "查看所有方案",
  "Settings.application.link-upgrade": "升級您的管理後台",
  "Settings.application.node-version": "node 版本",
  "Settings.application.strapi-version": "strapi 版本",
  "Settings.application.strapiVersion": "strapi 版本",
  "Settings.application.title": "總覽",
  "Settings.application.customization": "自訂",
  "Settings.application.customization.carousel.title": "標誌",
  "Settings.application.customization.carousel.change-action": "更改標誌",
  "Settings.application.customization.carousel.reset-action": "重設標誌",
  "Settings.application.customization.carousel-slide.label": "標誌投影片",
  "Settings.application.customization.carousel-hint": "更改管理面板標誌 (最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB)",
  "Settings.application.customization.modal.cancel": "取消",
  "Settings.application.customization.modal.upload": "上傳標誌",
  "Settings.application.customization.modal.tab.label": "您要如何上傳檔案？",
  "Settings.application.customization.modal.upload.from-computer": "從電腦",
  "Settings.application.customization.modal.upload.file-validation": "最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB",
  "Settings.application.customization.modal.upload.error-format": "上傳格式錯誤 (接受的格式：jpeg、jpg、png、svg)。",
  "Settings.application.customization.modal.upload.error-size": "上傳的檔案過大 (最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB)",
  "Settings.application.customization.modal.upload.error-network": "網路錯誤",
  "Settings.application.customization.modal.upload.cta.browse": "瀏覽檔案",
  "Settings.application.customization.modal.upload.drag-drop": "拖曳至此或",
  "Settings.application.customization.modal.upload.from-url": "從網址",
  "Settings.application.customization.modal.upload.from-url.input-label": "網址",
  "Settings.application.customization.modal.upload.next": "下一步",
  "Settings.application.customization.modal.pending": "擱置中的標誌",
  "Settings.application.customization.modal.pending.choose-another": "選擇其他標誌",
  "Settings.application.customization.modal.pending.title": "標誌已準備上傳",
  "Settings.application.customization.modal.pending.subtitle": "在上傳前管理選擇的標誌",
  "Settings.application.customization.modal.pending.upload": "上傳標誌",
  "Settings.application.customization.modal.pending.card-badge": "圖片",
  "Settings.error": "錯誤",
  "Settings.global": "全域設定",
  "Settings.permissions": "管理員後台",
  "Settings.permissions.category": "{category} 的權限設定",
  "Settings.permissions.category.plugins": "{category} 擴充功能的權限設定",
  "Settings.permissions.conditions.anytime": "隨時",
  "Settings.permissions.conditions.apply": "套用",
  "Settings.permissions.conditions.can": "可以",
  "Settings.permissions.conditions.conditions": "設定條件",
  "Settings.permissions.conditions.links": "連結",
  "Settings.permissions.conditions.no-actions": "在設定條件之前，您要先選則動作（新增、讀取、更新...）",
  "Settings.permissions.conditions.none-selected": "隨時",
  "Settings.permissions.conditions.or": "或",
  "Settings.permissions.conditions.when": "當",
  "Settings.permissions.select-all-by-permission": "選擇所有 {label} 權限",
  "Settings.permissions.select-by-permission": "選擇 {label} 權限",
  "Settings.permissions.users.create": "新增使用者",
  "Settings.permissions.users.email": "電子郵件",
  "Settings.permissions.users.firstname": "名字",
  "Settings.permissions.users.lastname": "姓氏",
  "Settings.permissions.users.user-status": "使用者狀態",
  "Settings.permissions.users.roles": "角色",
  "Settings.permissions.users.username": "使用者名稱",
  "Settings.permissions.users.active": "使用中",
  "Settings.permissions.users.inactive": "閒置中",
  "Settings.permissions.users.form.sso": "透過 SSO 登入",
  "Settings.permissions.users.form.sso.description": "當啟動這個選項時 (ON)，使用者可以透過 SSO 登入",
  "Settings.permissions.users.listview.header.subtitle": "找到 {number, plural, =0 {# users} one {# user } other {# users}}",
  "Settings.permissions.users.tabs.label": "分頁權限",
  "Settings.permissions.users.strapi-super-admin": "超級管理員",
  "Settings.permissions.users.strapi-editor": "編輯者",
  "Settings.permissions.users.strapi-author": "作者",
  "Settings.profile.form.notify.data.loaded": "您的個人檔案資料已經載入",
  "Settings.profile.form.section.experience.clear.select": "清除已選的介面語言",
  "Settings.profile.form.section.experience.here": "此文檔",
  "Settings.profile.form.section.experience.interfaceLanguage": "介面語言",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "將會用所選擇的語言顯示您的介面",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "只有您的介面會變為所選擇的語言。如果要為您的團隊提供其他語言，請參考{here}。",
  "Settings.profile.form.section.experience.mode.label": "介面模式",
  "Settings.profile.form.section.experience.mode.hint": "在選擇的模式中顯示您的介面。",
  "Settings.profile.form.section.experience.mode.option-label": "{name} 模式",
  light,
  dark,
  "Settings.profile.form.section.experience.title": "體驗",
  "Settings.profile.form.section.head.title": "使用者個人檔案",
  "Settings.profile.form.section.profile.page.title": "個人檔案頁面",
  "Settings.roles.create.description": "定義賦予角色的權限",
  "Settings.roles.create.title": "新增角色",
  "Settings.roles.created": "角色已新增",
  "Settings.roles.edit.title": "編輯角色",
  "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# 使用者} one {# 使用者} other {# 使用者}} 是這個角色",
  "Settings.roles.form.created": "已新增",
  "Settings.roles.form.description": "角色的名字與描述",
  "Settings.roles.form.permission.property-label": "{label} 權限",
  "Settings.roles.form.permissions.attributesPermissions": "欄位權限",
  "Settings.roles.form.permissions.create": "新增",
  "Settings.roles.form.permissions.delete": "刪除",
  "Settings.roles.form.permissions.publish": "發布",
  "Settings.roles.form.permissions.read": "讀取",
  "Settings.roles.form.permissions.update": "更新",
  "Settings.roles.list.button.add": "加入新的角色",
  "Settings.roles.list.description": "角色列表",
  "Settings.roles.title.singular": "角色",
  "Settings.sso.description": "調整 Single Sign-On 功能的設定",
  "Settings.sso.form.defaultRole.description": "它將新驗證的使用者附加到所選角色",
  "Settings.sso.form.defaultRole.description-not-allowed": "您需要有讀取管理員角色的權限",
  "Settings.sso.form.defaultRole.label": "預設角色",
  "Settings.sso.form.registration.description": "SSO 登入帳號不存在時就新增使用者",
  "Settings.sso.form.registration.label": "自動註冊",
  "Settings.sso.title": "Single Sign-On",
  "Settings.webhooks.create": "新增 webhook",
  "Settings.webhooks.create.header": "新增一個 Header",
  "Settings.webhooks.created": "Webhook 已新增",
  "Settings.webhooks.event.publish-tooltip": "此事件僅適用於啟用了草稿/發布系統的內容",
  "Settings.webhooks.events.create": "新增",
  "Settings.webhooks.events.update": "更新",
  "Settings.webhooks.form.events": "事件",
  "Settings.webhooks.form.headers": "Headers",
  "Settings.webhooks.form.url": "網址",
  "Settings.webhooks.headers.remove": "移除 header 第 {number} 行",
  "Settings.webhooks.key": "密鑰",
  "Settings.webhooks.list.button.add": "新增新的 webhook",
  "Settings.webhooks.list.description": "獲得 POST 更新通知",
  "Settings.webhooks.list.empty.description": "新增您第一個 webhook",
  "Settings.webhooks.list.empty.link": "參考我們的文件",
  "Settings.webhooks.list.empty.title": "這裡還沒有 webhook",
  "Settings.webhooks.list.th.actions": "動作",
  "Settings.webhooks.list.th.status": "狀態",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.to.delete": "已選擇 {webhooksToDeleteLength, plural, one {# asset} other {# assets}}",
  "Settings.webhooks.trigger": "觸發",
  "Settings.webhooks.trigger.cancel": "取消觸發",
  "Settings.webhooks.trigger.pending": "等待…",
  "Settings.webhooks.trigger.save": "請儲存到觸發",
  "Settings.webhooks.trigger.success": "成功！",
  "Settings.webhooks.trigger.success.label": "觸發成功",
  "Settings.webhooks.trigger.test": "測試觸發",
  "Settings.webhooks.trigger.title": "觸發前先儲存",
  "Settings.webhooks.value": "數值",
  "Usecase.back-end": "後端開發者",
  "Usecase.button.skip": "跳過此問題",
  "Usecase.content-creator": "內容創作者",
  "Usecase.front-end": "後端開發者",
  "Usecase.full-stack": "全棧開發者",
  "Usecase.input.work-type": "您做甚麼類型的工作？",
  "Usecase.notification.success.project-created": "專案已成功建立",
  "Usecase.other": "其他",
  "Usecase.title": "再多向我們介紹您自己",
  Username,
  Users,
  "Users & Permissions": "使用者與權限",
  "Users.components.List.empty": "還沒有使用者...",
  "Users.components.List.empty.withFilters": "沒有符合篩選條件的使用者...",
  "Users.components.List.empty.withSearch": "沒有符合搜尋條件 ({search}) 的使用者...",
  "admin.pages.MarketPlacePage.head": "市集 - 擴充功能",
  "admin.pages.MarketPlacePage.offline.title": "您已離線",
  "admin.pages.MarketPlacePage.offline.subtitle": "您必須連接至網際網路以存取 Strapi 市集。",
  "admin.pages.MarketPlacePage.plugins": "外掛程式",
  "admin.pages.MarketPlacePage.plugin.copy": "複製安裝命令",
  "admin.pages.MarketPlacePage.plugin.copy.success": "安裝命令已可貼至您的終端機",
  "admin.pages.MarketPlacePage.plugin.info": "了解詳情",
  "admin.pages.MarketPlacePage.plugin.info.label": "了解關於 {pluginName} 的詳細資訊",
  "admin.pages.MarketPlacePage.plugin.info.text": "更多",
  "admin.pages.MarketPlacePage.plugin.installed": "已安裝",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Strapi 製作",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "經 Strapi 驗證的外掛程式",
  "admin.pages.MarketPlacePage.plugin.version": '更新您的 Strapi 版本："{strapiAppVersion}" 至："{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": '無法驗證與您的 Strapi 版本的相容性："{strapiAppVersion}"',
  "admin.pages.MarketPlacePage.plugin.githubStars": "此外掛程式在 GitHub 上獲得了 {starsCount} 顆星星",
  "admin.pages.MarketPlacePage.plugin.downloads": "此外掛程式的每周下載量為 {downloadsCount}",
  "admin.pages.MarketPlacePage.providers": "供應者",
  "admin.pages.MarketPlacePage.provider.githubStars": "此供應者在 GitHub 上獲得了 {starsCount} 顆星星",
  "admin.pages.MarketPlacePage.provider.downloads": "此供應者的每周下載量為 {downloadsCount}",
  "admin.pages.MarketPlacePage.search.clear": "清除搜尋",
  "admin.pages.MarketPlacePage.search.empty": '沒有 "{target}" 的搜尋結果',
  "admin.pages.MarketPlacePage.search.placeholder": "搜尋",
  "admin.pages.MarketPlacePage.submit.plugin.link": "申請您的擴充功能",
  "admin.pages.MarketPlacePage.submit.provider.link": "申請供應者",
  "admin.pages.MarketPlacePage.subtitle": "充分利用 Strapi",
  "admin.pages.MarketPlacePage.tab-group.label": "Strapi 的外掛程式和供應者",
  "admin.pages.MarketPlacePage.missingPlugin.title": "找不到外掛程式？",
  "admin.pages.MarketPlacePage.missingPlugin.description": "告訴我們您在尋找什麼外掛程式，我們會轉告我們的社群外掛程式開發者，這將成為他們的靈感來源！",
  "admin.pages.MarketPlacePage.sort.alphabetical": "按字母排序",
  "admin.pages.MarketPlacePage.sort.newest": "最新",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "按字母排序",
  "admin.pages.MarketPlacePage.sort.newest.selected": "依最新時間排序",
  "admin.pages.MarketPlacePage.filters.collections": "集合",
  "admin.pages.MarketPlacePage.filters.collectionsSelected": "{count, plural, =0 {No collections} one {# collection} other {# collections}} selected",
  "admin.pages.MarketPlacePage.filters.categories": "類別",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {No categories} one {# category} other {# categories}} selected",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "複製到剪貼簿",
  "app.component.search.label": "搜尋 {target}",
  "app.component.table.duplicate": "建立副本 {target}",
  "app.component.table.edit": "編輯 {target}",
  "app.component.table.select.one-entry": "選擇 {target}",
  "app.components.BlockLink.blog": "部落格",
  "app.components.BlockLink.blog.content": "閱讀關於 Strapi 和相關的最新資訊",
  "app.components.BlockLink.code": "範例",
  "app.components.BlockLink.code.content": "透過測試社群開發的真實專案而學習。",
  "app.components.BlockLink.documentation.content": "探索基礎觀念、指南和分解步驟說明。",
  "app.components.BlockLink.tutorial": "引導",
  "app.components.BlockLink.tutorial.content": "跟著手把手的教學引導使用和客製 Strapi",
  "app.components.Button.cancel": "取消",
  "app.components.Button.confirm": "確認",
  "app.components.Button.reset": "重設",
  "app.components.ComingSoonPage.comingSoon": "即將推出",
  "app.components.ConfirmDialog.title": "確認",
  "app.components.DownloadInfo.download": "下載中...",
  "app.components.DownloadInfo.text": "可能需要一分鐘。感謝您的耐心。",
  "app.components.EmptyAttributes.title": "這裡還沒有任何欄位",
  "app.components.EmptyStateLayout.content-document": "找不到資料",
  "app.components.EmptyStateLayout.content-permissions": "您沒有權限存取這個內容",
  "app.components.GuidedTour.CM.create.content": "<p>在內容管理員中建立和管理所有內容。</p><p>範例：再以部落格作為例子，使用者可以撰寫、儲存、和發布文章。</p><p>💡 小提示 - 別忘了發佈您剛建立的內容。</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ 建立內容",
  "app.components.GuidedTour.CM.success.content": "<p>太棒了，只差最後一步！</p><b>🚀  See content in action</b>",
  "app.components.GuidedTour.CM.success.cta.title": "測試 API",
  "app.components.GuidedTour.CM.success.title": "第 2 步：完成 ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>集合型別能夠幫助您管理多個項目，單一型別只適合管理一個項目。</p> <p>範例：對於部落格來說，文章會是集合型別，而首頁會是單一型別。</p>",
  "app.components.GuidedTour.CTB.create.cta.title": "建立集合型別",
  "app.components.GuidedTour.CTB.create.title": "🧠 建立首個集合型別",
  "app.components.GuidedTour.CTB.success.content": "<p>做得好！</p><b>⚡️ 您想要和世界分享些什麼？</b>",
  "app.components.GuidedTour.CTB.success.title": "第 1 步：完成 ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>在此產生驗證權杖，取得您剛建立的內容。</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "產生 API 權杖",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 See content in action",
  "app.components.GuidedTour.apiTokens.success.content": "<p>See content in action by making an HTTP request:</p><ul><li><p>向此網址傳送 HTTP 請求：<light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>並附帶此標頭：<light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>For more ways to interact with content, see the <documentationLink>documentation</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "返回首頁",
  "app.components.GuidedTour.apiTokens.success.title": "第 3 步：完成 ✅",
  "app.components.GuidedTour.create-content": "建立內容",
  "app.components.GuidedTour.home.CM.title": "⚡️ 您想要和世界分享些什麼？",
  "app.components.GuidedTour.home.CTB.cta.title": "前往內容建立員",
  "app.components.GuidedTour.home.CTB.title": "🧠 建立內容結構",
  "app.components.GuidedTour.home.apiTokens.cta.title": "測試 API",
  "app.components.GuidedTour.skip": "跳過導覽",
  "app.components.GuidedTour.title": "3 steps to get started",
  "app.components.HomePage.button.blog": "到部落格上閱讀更多",
  "app.components.HomePage.community": "探索開發社群",
  "app.components.HomePage.community.content": "在不同的社群中與其他成員、貢獻者以及開發者討論。",
  "app.components.HomePage.create": "建立您第一個內容型別",
  "app.components.HomePage.roadmap": "看我們的 roadmap",
  "app.components.HomePage.welcome": "歡迎加入！",
  "app.components.HomePage.welcome.again": "歡迎回來! ",
  "app.components.HomePage.welcomeBlock.content": "恭喜！您登入成為第一個管理員，探索 Strapi 的強大功能，建議您從建立第一個內容型別開始。",
  "app.components.HomePage.welcomeBlock.content.again": "我們希望您在專案上有所進展！請隨時閱讀有關 Strapi 的最新消息。我們將根據您的回饋盡最大努力改進產品。",
  "app.components.HomePage.welcomeBlock.content.issues": "問題。",
  "app.components.HomePage.welcomeBlock.content.raise": "或是回報",
  "app.components.ImgPreview.hint": "將您要上傳的檔案拖曳到此區域，或是瀏覽檔案",
  "app.components.ImgPreview.hint.browse": "瀏覽",
  "app.components.InputFile.newFile": "增加新檔案",
  "app.components.InputFileDetails.open": "在新分頁中開啟",
  "app.components.InputFileDetails.originalName": "原始名稱：",
  "app.components.InputFileDetails.remove": "移除檔案",
  "app.components.InputFileDetails.size": "大小：",
  "app.components.InstallPluginPage.Download.description": "下載和安裝擴充功能可能需要幾秒鐘的時間。",
  "app.components.InstallPluginPage.Download.title": "下載中...",
  "app.components.InstallPluginPage.description": "輕鬆擴充您的應用程式",
  "app.components.LeftMenu.collapse": "收起導航列",
  "app.components.LeftMenu.expand": "打開導航列",
  "app.components.LeftMenu.general": "一般",
  "app.components.LeftMenu.logout": "登出",
  "app.components.LeftMenu.logo.alt": "應用程式標誌",
  "app.components.LeftMenu.plugins": "外掛程式",
  "app.components.LeftMenu.navbrand.title": "Strapi 控制台",
  "app.components.LeftMenu.navbrand.workplace": "工作區",
  "app.components.LeftMenuFooter.help": "說明",
  "app.components.LeftMenuFooter.poweredBy": "Powered by ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "集合型別",
  "app.components.LeftMenuLinkContainer.configuration": "設定",
  "app.components.LeftMenuLinkContainer.general": "一般",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "目前沒有安裝任何擴充功能",
  "app.components.LeftMenuLinkContainer.plugins": "擴充功能",
  "app.components.LeftMenuLinkContainer.singleTypes": "單一型別",
  "app.components.ListPluginsPage.deletePlugin.description": "解除安裝擴充功能可能需要幾秒鐘的時間。",
  "app.components.ListPluginsPage.deletePlugin.title": "解除安裝",
  "app.components.ListPluginsPage.description": "這個專案安裝的擴充功能列表",
  "app.components.ListPluginsPage.head.title": "擴充功能列表",
  "app.components.Logout.logout": "登出",
  "app.components.Logout.profile": "個人檔案",
  "app.components.MarketplaceBanner": "在 Strapi Awesome 上發現社群建構的擴充套件，以及更多可驅動您的專案的精彩內容。",
  "app.components.MarketplaceBanner.image.alt": "strapi 火箭 logo",
  "app.components.MarketplaceBanner.link": "現在看看",
  "app.components.NotFoundPage.back": "回到主頁",
  "app.components.NotFoundPage.description": "找不到此頁面",
  "app.components.Official": "官方",
  "app.components.Onboarding.help.button": "幫助按鈕",
  "app.components.Onboarding.label.completed": "% 完成",
  "app.components.Onboarding.title": "入門影片",
  "app.components.PluginCard.Button.label.download": "下載",
  "app.components.PluginCard.Button.label.install": "已安裝",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "要啟用自動重新整理功能。請使用 yarn develop 啟動您的 App。",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "我了解！",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "基於安全考量，擴充套件只能在開發環境中下載。",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "不能下載",
  "app.components.PluginCard.compatible": "與您的專案相容",
  "app.components.PluginCard.compatibleCommunity": "相容社群",
  "app.components.PluginCard.more-details": "顯示更多",
  "app.components.ToggleCheckbox.off-label": "關",
  "app.components.ToggleCheckbox.on-label": "開",
  "app.components.Users.MagicLink.connect": "將此連結傳送給使用者讓他可以開通帳號。",
  "app.components.Users.MagicLink.connect.sso": "將此連結傳送給使用者讓他可以透過 SSO 登入。",
  "app.components.Users.ModalCreateBody.block-title.details": "使用者詳細資料",
  "app.components.Users.ModalCreateBody.block-title.roles": "使用者角色",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "使用者可以有一個或多個角色",
  "app.components.Users.SortPicker.button-label": "排序按照",
  "app.components.Users.SortPicker.sortby.email_asc": "電子郵件地址 (A 到 Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "電子郵件地址 (Z 到 A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "名字 (A 到 Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "名字 (Z 到 A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "姓氏 (A 到 Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "姓氏 (Z 到 A)",
  "app.components.Users.SortPicker.sortby.username_asc": "使用者名稱 (A 到 Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "使用者名稱 (Z 到 A)",
  "app.components.listPlugins.button": "安裝新的擴充功能",
  "app.components.listPlugins.title.none": "目前沒有安裝任何擴充功能",
  "app.components.listPluginsPage.deletePlugin.error": "解除安裝擴充功能時發生錯誤",
  "app.containers.App.notification.error.init": "向 API 請求時發生錯誤",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "如果您沒有收到這個連結，請詢問您的管理員。",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "可能需要幾分鐘才能收到您的恢復密碼連結。",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "送出電子郵件",
  "app.containers.Users.EditPage.form.active.label": "有效",
  "app.containers.Users.EditPage.header.label": "編輯 {name}",
  "app.containers.Users.EditPage.header.label-loading": "編輯使用者",
  "app.containers.Users.EditPage.roles-bloc-title": "歸屬的角色",
  "app.containers.Users.ModalForm.footer.button-success": "新增使用者",
  "app.links.configure-view": "設定檢視",
  "app.page.not.found": "哎呀! 似乎找不到您要瀏覽的網頁...",
  "app.static.links.cheatsheet": "備忘錄",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "新增篩選器",
  "app.utils.close-label": "關閉",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "建立副本",
  "app.utils.edit": "編輯",
  "app.utils.delete": "刪除",
  "app.utils.errors.file-too-big.message": "檔案容量太大了",
  "app.utils.filter-value": "篩選器的值",
  "app.utils.filters": "篩選條件",
  "app.utils.notify.data-loaded": "{target} 已經讀取",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "發布",
  "app.utils.select-all": "全選",
  "app.utils.select-field": "選擇欄位",
  "app.utils.select-filter": "選擇篩選",
  "app.utils.unpublish": "取消發布",
  clearLabel,
  "coming.soon": "此內容目前正在建設中，將在幾週後恢復！",
  "component.Input.error.validation.integer": "這個值必須是數值",
  "components.AutoReloadBlocker.description": "使用以下指令之一執行 Strapi：",
  "components.AutoReloadBlocker.header": "這個擴充功能需要自動重新整理功能才能載入",
  "components.ErrorBoundary.title": "有錯誤發生...",
  "components.FilterOptions.FILTER_TYPES.$contains": "包含",
  "components.FilterOptions.FILTER_TYPES.$containsi": "包含 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "結尾是",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "結尾是 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "是",
  "components.FilterOptions.FILTER_TYPES.$eqi": "是 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "大於",
  "components.FilterOptions.FILTER_TYPES.$gte": "大於或等於",
  "components.FilterOptions.FILTER_TYPES.$lt": "小於",
  "components.FilterOptions.FILTER_TYPES.$lte": "小於或等於",
  "components.FilterOptions.FILTER_TYPES.$ne": "不是",
  "components.FilterOptions.FILTER_TYPES.$nei": "不是 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "不包含",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "不包含 (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "不是 null",
  "components.FilterOptions.FILTER_TYPES.$null": "是 null",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "起首是",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "起首是 (case insensitive)",
  "components.Input.error.attribute.key.taken": "這個值已經存在了",
  "components.Input.error.attribute.sameKeyAndName": "不能等於",
  "components.Input.error.attribute.taken": "這個欄位名稱已經存在了",
  "components.Input.error.contain.lowercase": "密碼必須至少包含一個小寫字母",
  "components.Input.error.contain.number": "密碼必須至少包含一個數字",
  "components.Input.error.contain.uppercase": "密碼必須至少包含一個大寫字母",
  "components.Input.error.contentTypeName.taken": "這個名稱已經存在了",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "密碼不符",
  "components.Input.error.validation.email": "請輸入有效的電子郵件地址",
  "components.Input.error.validation.json": "非法的 JSON 格式",
  "components.Input.error.validation.lowercase": "必須是小寫字母",
  "components.Input.error.validation.max": "數值太大 {max}",
  "components.Input.error.validation.maxLength": "超過長度 {max}",
  "components.Input.error.validation.min": "數值太小 {min}",
  "components.Input.error.validation.minLength": "長度不夠 {min}",
  "components.Input.error.validation.minSupMax": "不能接受這個值",
  "components.Input.error.validation.regex": "此值無法通過正規表達式",
  "components.Input.error.validation.required": "必填欄位",
  "components.Input.error.validation.unique": "這個值已經有了",
  "components.InputSelect.option.placeholder": "這裡選擇",
  "components.ListRow.empty": "沒有資料可以顯示",
  "components.NotAllowedInput.text": "沒有權限顯示這個欄位",
  "components.OverlayBlocker.description": "您正在使用的功能需要重新啟動，請等待重新啟動完成。",
  "components.OverlayBlocker.description.serverError": "伺服器應該已經重新啟動，請在終端機中查看 日誌。",
  "components.OverlayBlocker.title": "等待重新啟動中...",
  "components.OverlayBlocker.title.serverError": "重新啟動花費的時間比預期的久。",
  "components.PageFooter.select": "項目/頁",
  "components.ProductionBlocker.description": "基於安全考量，我們需要在其他環境關閉這個擴充功能",
  "components.ProductionBlocker.header": "這個擴充功能只能在開發環境中使用",
  "components.Search.placeholder": "搜尋...",
  "components.TableHeader.sort": "以 {label} 排序",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Markdown 模式",
  "components.Wysiwyg.ToggleMode.preview-mode": "預覽模式",
  "components.Wysiwyg.collapse": "折疊",
  "components.Wysiwyg.selectOptions.H1": "標題1",
  "components.Wysiwyg.selectOptions.H2": "標題2",
  "components.Wysiwyg.selectOptions.H3": "標題3",
  "components.Wysiwyg.selectOptions.H4": "標題4",
  "components.Wysiwyg.selectOptions.H5": "標題5",
  "components.Wysiwyg.selectOptions.H6": "標題6",
  "components.Wysiwyg.selectOptions.title": "新增標題",
  "components.WysiwygBottomControls.charactersIndicators": "字元",
  "components.WysiwygBottomControls.fullscreen": "展開",
  "components.WysiwygBottomControls.uploadFiles": "拖曳檔案、從剪貼簿貼上或 {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "選擇檔案",
  "components.pagination.go-to": "前往 {page} 頁面",
  "components.pagination.go-to-next": "前往下一頁",
  "components.pagination.go-to-previous": "前往上一頁",
  "components.pagination.remaining-links": "及 {number} 個其他連結",
  "components.popUpWarning.button.cancel": "不, 取消",
  "components.popUpWarning.button.confirm": "是, 確認",
  "components.popUpWarning.message": "您確定要刪除此項目嗎？",
  "components.popUpWarning.title": "請確認",
  "form.button.continue": "繼續",
  "form.button.done": "完成",
  "global.search": "搜尋",
  "global.actions": "操作",
  "global.back": "返回",
  "global.cancel": "取消",
  "global.change-password": "更改密碼",
  "global.content-manager": "內容管理者",
  "global.continue": "繼續",
  "global.delete": "刪除",
  "global.delete-target": "刪除 {target}",
  "global.description": "說明",
  "global.details": "詳細資訊",
  "global.disabled": "已停用",
  "global.documentation": "說明文件",
  "global.enabled": "已啟用",
  "global.finish": "完成",
  "global.marketplace": "市集",
  "global.name": "名稱",
  "global.move": "移動",
  "global.none": "無",
  "global.password": "密碼",
  "global.plugins": "外掛程式",
  "global.plugins.content-manager": "內容管理員",
  "global.plugins.content-manager.description": "快速查看、編輯、並刪除資料庫中的資料。",
  "global.plugins.content-type-builder": "內容型別建立員",
  "global.plugins.content-type-builder.description": "為您的 API 的資料結構建立模型。在幾分鐘內建立新的欄位和關聯。檔案將自動在您的專案中自動建立並更新。",
  "global.plugins.email": "電子郵件",
  "global.plugins.email.description": "設定您的應用程式以傳送電子郵件。",
  "global.plugins.upload": "媒體庫",
  "global.plugins.upload.description": "媒體檔案管理。",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "新增使用預設 API 方法的 GraphQL 端點。",
  "global.plugins.documentation": "說明文件",
  "global.plugins.documentation.description": "建立 OpenAPI 文件，並透過 SWAGGER UI 來可視化您的 API。",
  "global.plugins.i18n": "國際化",
  "global.plugins.i18n.description": "此外掛程式允許您從管理面板和 API 建立、讀取、更新不同語言的內容。",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "將 Strapi 錯誤事件傳送至 Sentry。",
  "global.plugins.users-permissions": "角色與權限",
  "global.plugins.users-permissions.description": "使用基於 JWT 的完整驗證流程來保護您的 API。此外掛程式還附帶 ACT 策略，讓您能夠管理使用者群組間的權限。",
  "global.profile": "個人檔案",
  "global.prompt.unsaved": "您確定要離開網頁嗎？您的所有未存檔的編輯都會消失",
  "global.reset-password": "重設密碼",
  "global.roles": "角色",
  "global.save": "儲存",
  "global.see-more": "查看更多",
  "global.select": "選取",
  "global.select-all-entries": "選取所有實體",
  "global.settings": "設定",
  "global.type": "類型",
  "global.users": "使用者",
  "notification.contentType.relations.conflict": "內容型別有關聯衝突",
  "notification.default.title": "訊息：",
  "notification.error": "發生錯誤",
  "notification.error.layout": "無法取得佈局",
  "notification.form.error.fields": "表單包含一些錯誤",
  "notification.form.success.fields": "修改已儲存",
  "notification.link-copied": "連結已複製到剪貼簿",
  "notification.permission.not-allowed-read": "您沒有查看此檔案的權限",
  "notification.success.delete": "該項已被刪除",
  "notification.success.saved": "已儲存",
  "notification.success.title": "成功:",
  "notification.success.apitokencreated": "成功建立 API 權杖",
  "notification.success.apitokenedited": "成功編輯 API 權杖",
  "notification.error.tokennamenotunique": "名稱已經指派給其他權杖",
  "notification.version.update.message": "有新版本的 Strapi 可用!",
  "notification.warning.title": "警告:",
  "notification.warning.404": "404 - 找不到",
  or,
  "request.error.model.unknown": "不存在的資料",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  zh as default,
  light,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=zh-WEteLuNm-NSTKIB5K.js.map
