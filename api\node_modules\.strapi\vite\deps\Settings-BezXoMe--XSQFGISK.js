import {
  getTrad,
  useGetInfoQuery,
  useUpdateSettingsMutation
} from "./chunk-TEGAIJ7L.js";
import {
  PERMISSIONS
} from "./chunk-7UEG2OTL.js";
import "./chunk-LUCLFAUY.js";
import "./chunk-HZKGX44A.js";
import "./chunk-CPGB3PN2.js";
import "./chunk-C7H2BX76.js";
import "./chunk-RZNXC7D6.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-QMDMQMVF.js";
import "./chunk-LQ3ZZXCQ.js";
import "./chunk-JFNDUCW5.js";
import "./chunk-6LGIX3D6.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-O6QFUROF.js";
import "./chunk-Y72YK32Y.js";
import "./chunk-RPZ2IAY7.js";
import "./chunk-7NY4WJ74.js";
import "./chunk-3RO7T4F6.js";
import "./chunk-7W4I2N3Z.js";
import "./chunk-LVPA57IX.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Form,
  Formik,
  Layouts,
  create3 as create,
  create4 as create2,
  create6 as create3,
  errorsTrads
} from "./chunk-M2UTSHEM.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useRBAC
} from "./chunk-6SQM3G2Q.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  Main,
  TextInput,
  Toggle,
  Typography,
  useIntl
} from "./chunk-OLXXPFXU.js";
import "./chunk-GGTKPILS.js";
import {
  ForwardRef$3p,
  ForwardRef$3r,
  ForwardRef$4p
} from "./chunk-C3D7G7QO.js";
import {
  dt
} from "./chunk-JA2NBZIU.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-IYHTQODQ.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/Settings-BezXoMe-.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var isBaseQueryError = (error) => {
  return error.name !== void 0;
};
var schema = create3().shape({
  restrictedAccess: create(),
  password: create2().when("restrictedAccess", (value, initSchema) => {
    return value ? initSchema.required(errorsTrads.required.id).min(8).matches(/[a-z]/, "components.Input.error.contain.lowercase").matches(/[A-Z]/, "components.Input.error.contain.uppercase").matches(/\d/, "components.Input.error.contain.number") : initSchema;
  })
});
var FieldActionWrapper = dt(Field.Action)`
  svg {
    height: 1.6rem;
    width: 1.6rem;
    path {
      fill: ${({ theme }) => theme.colors.neutral600};
    }
  }
`;
var SettingsForm = ({ data, onSubmit }) => {
  const { formatMessage } = useIntl();
  const [passwordShown, setPasswordShown] = React.useState(false);
  const { allowedActions } = useRBAC(PERMISSIONS);
  return (0, import_jsx_runtime.jsx)(
    Formik,
    {
      enableReinitialize: true,
      initialValues: {
        restrictedAccess: (data == null ? void 0 : data.documentationAccess.restrictedAccess) || false,
        password: ""
      },
      onSubmit,
      validationSchema: schema,
      children: ({
        handleSubmit,
        values,
        handleChange,
        errors,
        setFieldTouched,
        setFieldValue,
        setFieldError,
        dirty
      }) => {
        return (0, import_jsx_runtime.jsxs)(Form, { noValidate: true, onSubmit: handleSubmit, children: [
          (0, import_jsx_runtime.jsx)(
            Layouts.Header,
            {
              title: formatMessage({
                id: getTrad("plugin.name"),
                defaultMessage: "Documentation"
              }),
              subtitle: formatMessage({
                id: getTrad("pages.SettingsPage.header.description"),
                defaultMessage: "Configure the documentation plugin"
              }),
              primaryAction: (0, import_jsx_runtime.jsx)(
                Button,
                {
                  type: "submit",
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                  disabled: !dirty && allowedActions.canUpdate,
                  children: formatMessage({
                    id: getTrad("pages.SettingsPage.Button.save"),
                    defaultMessage: "Save"
                  })
                }
              )
            }
          ),
          (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(
            Box,
            {
              background: "neutral0",
              hasRadius: true,
              shadow: "filterShadow",
              paddingTop: 6,
              paddingBottom: 6,
              paddingLeft: 7,
              paddingRight: 7,
              children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
                (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                  id: "global.settings",
                  defaultMessage: "Settings"
                }) }),
                (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 4, children: [
                  (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                    Field.Root,
                    {
                      name: "restrictedAccess",
                      hint: formatMessage({
                        id: getTrad("pages.SettingsPage.toggle.hint"),
                        defaultMessage: "Make the documentation endpoint private"
                      }),
                      children: [
                        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                          id: getTrad("pages.SettingsPage.toggle.label"),
                          defaultMessage: "Restricted Access"
                        }) }),
                        (0, import_jsx_runtime.jsx)(
                          Toggle,
                          {
                            checked: values.restrictedAccess,
                            onChange: () => {
                              if (values.restrictedAccess === true) {
                                setFieldValue("password", "", false);
                                setFieldTouched("password", false, false);
                                setFieldError("password", void 0);
                              }
                              setFieldValue("restrictedAccess", !values.restrictedAccess, false);
                            },
                            onLabel: "On",
                            offLabel: "Off"
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(Field.Hint, {})
                      ]
                    }
                  ) }),
                  values.restrictedAccess && (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                    Field.Root,
                    {
                      name: "password",
                      error: errors.password ? formatMessage({
                        id: errors.password,
                        defaultMessage: errors.password
                      }) : void 0,
                      children: [
                        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                          id: "global.password",
                          defaultMessage: "Password"
                        }) }),
                        (0, import_jsx_runtime.jsx)(
                          TextInput,
                          {
                            placeholder: "**********",
                            type: passwordShown ? "text" : "password",
                            value: values.password,
                            onChange: handleChange,
                            endAction: (0, import_jsx_runtime.jsx)(
                              FieldActionWrapper,
                              {
                                onClick: (e) => {
                                  e.stopPropagation();
                                  setPasswordShown((prev) => !prev);
                                },
                                label: formatMessage(
                                  passwordShown ? {
                                    id: "Auth.form.password.show-password",
                                    defaultMessage: "Show password"
                                  } : {
                                    id: "Auth.form.password.hide-password",
                                    defaultMessage: "Hide password"
                                  }
                                ),
                                children: passwordShown ? (0, import_jsx_runtime.jsx)(ForwardRef$3r, {}) : (0, import_jsx_runtime.jsx)(ForwardRef$3p, {})
                              }
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(Field.Error, {})
                      ]
                    }
                  ) })
                ] })
              ] })
            }
          ) })
        ] });
      }
    }
  );
};
var SettingsPage = () => {
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const {
    _unstableFormatAPIError: formatAPIError,
    _unstableFormatValidationErrors: formatValidationErrors
  } = useAPIErrorHandler();
  const { data, isError, isLoading, isFetching } = useGetInfoQuery();
  const [updateSettings] = useUpdateSettingsMutation();
  const onUpdateSettings = async (body, formik) => {
    return updateSettings({ body }).unwrap().then(() => {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("notification.update.success"),
          defaultMessage: "Successfully updated settings"
        })
      });
    }).catch((err) => {
      if (isBaseQueryError(err) && err.name === "ValidationError") {
        toggleNotification({
          type: "danger",
          message: formatAPIError(err)
        });
      }
    });
  };
  if (isLoading || isFetching) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (isError) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime.jsx)(Main, { children: (0, import_jsx_runtime.jsx)(SettingsForm, { data, onSubmit: onUpdateSettings }) });
};
export {
  SettingsPage
};
//# sourceMappingURL=Settings-BezXoMe--XSQFGISK.js.map
