"use strict";
/**
 * notification service
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreService("api::notification.notification", ({ strapi }) => ({
    // Método para notificar a un usuario específico
    async notifyUser(userId, data) {
        try {
            console.log(`🔔 NotificationService.notifyUser llamado:`);
            console.log(`   - userId: ${userId}`);
            console.log(`   - data:`, data);
            if (!userId) {
                console.error("Intento de notificar sin ID de usuario");
                return false;
            }
            let notificationSent = false;
            // Verificar si strapi.sse está disponible
            console.log(`🔍 Verificando strapi.sse:`, {
                exists: !!strapi.sse,
                hasMethod: strapi.sse &&
                    typeof strapi.sse.sendNotification === "function",
            });
            // Intentar enviar la notificación a través de SSE si está disponible
            if (strapi.sse &&
                typeof strapi.sse.sendNotification === "function") {
                console.log(`📡 Enviando notificación SSE...`);
                notificationSent = await strapi.sse.sendNotification(userId, data);
                console.log(`📡 Resultado SSE:`, notificationSent);
            }
            else {
                console.warn("⚠️ No se encontró un método para enviar notificaciones SSE");
            }
            // Intentar enviar la notificación push si está disponible
            try {
                const pushService = strapi.service("api::push-subscription.push-subscription");
                if (pushService &&
                    typeof pushService.sendNotification === "function") {
                    console.log(`📱 Enviando notificación push...`);
                    const pushResult = await pushService.sendNotification({
                        userId,
                        notification: {
                            title: data.title || "Nueva notificación",
                            body: data.message || data.description || "",
                            icon: "/icon-192x192.png", // Asegúrate de que este archivo exista en tu frontend
                            badge: "/badge-72x72.png", // Asegúrate de que este archivo exista en tu frontend
                            data: {
                                url: data.url || "/",
                                ...data,
                            },
                        },
                    });
                    console.log(`📱 Resultado push:`, pushResult);
                    if (pushResult.results &&
                        pushResult.results.some((r) => r.success)) {
                        notificationSent = true;
                    }
                }
                else {
                    console.log(`📱 Push service no disponible o método sendNotification no existe`);
                }
            }
            catch (pushError) {
                console.error("Error al enviar notificación push:", pushError);
                // No interrumpimos el flujo si falla la notificación push
            }
            return notificationSent;
        }
        catch (error) {
            console.error("Error al notificar al usuario:", error);
            return false;
        }
    },
}));
