import { AppDate } from "constants/Dates";

export interface Guest {
  [x: string]: any;
  id?: number;
  name: string;
  lastName: string;
}

export interface ImageFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  path: string | null;
  width: number;
  height: number;
  size: number;
  sizeInBytes: number;
  url: string;
  provider_metadata: {
    public_id: string;
    resource_type: string;
  };
}

export interface ImageAttributes {
  id: number;
  documentId: string;
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: {
    thumbnail: ImageFormat;
    small: ImageFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: {
    public_id: string;
    resource_type: string;
  };
  folderPath: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string | null;
}

export interface OwnerAttributes {
  firstName: string;
  lastName: string;
  address: string;
  imgUrl: ImageAttributes;
}

export interface ReservationData {
  date: AppDate;
  startTime: AppDate;
  endTime: AppDate;
  socialArea: string;
  eventType: string;
  guests: Guest[];
}

export interface ReservationAttributes {
  documentId: string;
  date: string;
  startTime: string;
  endTime: string;
  socialArea: string;
  eventType: string;
  status: "pending" | "approved" | "rejected";
  rejectionReason?: string;
  guests: Guest[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  owner: {
    data: {
      id: number;
      attributes: OwnerAttributes;
    };
  };
}

export interface Reservation {
  id: number;
  attributes: ReservationAttributes;
}

export interface ReservationListResponse {
  data: {
    id: number;
    documentId: string;
    date: string;
    startTime: string;
    endTime: string;
    socialArea: string;
    eventType: string;
    status: string;
    rejectionReason: string | null;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    user: {
      data: {
        id: number;
        attributes: OwnerAttributes;
      };
    };
    guests: Guest[];
  }[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface ReservationDetailResponse {
  data: {
    id: number;
    attributes: {
      documentId: string;
      date: string;
      startTime: string;
      endTime: string;
      socialArea: string;
      eventType: string;
      status: string;
      rejectionReason: string | null;
      guests: Guest[];
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
      owner: {
        data: {
          id: number;
          attributes: {
            firstName: string;
            lastName: string;
            address: string;
            imgUrl: {
              url: string;
              formats?: {
                thumbnail?: {
                  url: string;
                };
                small?: {
                  url: string;
                };
              };
            } | null;
          };
        };
      } | null;
    };
  }[];
}
