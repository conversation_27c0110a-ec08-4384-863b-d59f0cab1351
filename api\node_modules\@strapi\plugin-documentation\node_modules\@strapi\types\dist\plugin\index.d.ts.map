{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/plugin/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AAEpC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AACvF,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACzC,OAAO,KAAK,KAAK,MAAM,MAAM,WAAW,CAAC;AACzC,OAAO,KAAK,KAAK,GAAG,MAAM,QAAQ,CAAC;AACnC,OAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAE5D,MAAM,MAAM,SAAS,CACnB,KAAK,SAAS,MAAM,GAAG,EACvB,UAAU,SAAS,GAAG,CAAC,MAAM,IAC3B,KAAK,SAAS,MAAM,MAAM,CAAC,gBAAgB,GAE3C,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS,MAAM,KAAK,GAChD,EAAE,CAEA,GAAG,CAAC,SAAS,CAAC,2BAA2B,CAAC,EAE1C,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,EAAE;KAAG,GAAG,IAAI,KAAK,GAAG,KAAK;CAAE,CAAC,CACzE,GACD,KAAK,GACP,KAAK,CAAC;AAEV,MAAM,MAAM,YAAY,GAAG;IACzB,MAAM,EAAE;QACN,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;YAAE,GAAG,EAAE,OAAO,GAAG,CAAA;SAAE,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5F,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC;KACtD,CAAC;IACF,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACpE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACnE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/B,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACxC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACxC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE;QAAE,MAAM,EAAE,iBAAiB,CAAA;KAAE,CAAC,CAAC;CAC7D,CAAC;AAEF,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC"}