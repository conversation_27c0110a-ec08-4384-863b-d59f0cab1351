/**
 * reservation router
 */

export default {
  routes: [
    // Obtener todas las reservas
    {
      method: "GET",
      path: "/reservations/all",
      handler: "reservation.findAll",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Obtener mis reservas
    {
      method: "GET",
      path: "/reservations/me",
      handler: "reservation.me",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Obtener reservas
    {
      method: "GET",
      path: "/reservations",
      handler: "reservation.find",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Obtener una reserva específica
    {
      method: "GET",
      path: "/reservations/:id",
      handler: "reservation.findOne",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    // Crear una reserva
    {
      method: "POST",
      path: "/reservations",
      handler: "reservation.create",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "PUT",
      path: "/reservations/:id",
      handler: "reservation.update",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "DELETE",
      path: "/reservations/:id",
      handler: "reservation.delete",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/reservations/user/:ownerId",
      handler: "reservation.findByOwner",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
  ],
};
