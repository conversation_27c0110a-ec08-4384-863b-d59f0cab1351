{"version": 3, "file": "pretty-print.js", "sources": ["../../src/formats/pretty-print.ts"], "sourcesContent": ["import { format, Logform } from 'winston';\nimport logErrors from './log-errors';\n\nconst defaultTimestampFormat = 'YYYY-MM-DD HH:mm:ss.SSS';\n\nexport interface PrettyPrintOptions {\n  /**\n   * Enable or disable timestamps print if it's a boolean value. Use the given format for the timestamps if it's a string\n   */\n  timestamps?: Logform.TimestampOptions['format'] | boolean;\n  /**\n   * Enable or disable the use of colors for the log level\n   */\n  colors?: boolean;\n}\n\n/**\n * Create a pretty print formatter for a winston logger\n * @param options\n */\nexport default (options: PrettyPrintOptions = {}): Logform.Format => {\n  const { timestamps = true, colors = true } = options;\n\n  const handlers: Logform.Format[] = [];\n\n  if (timestamps) {\n    handlers.push(\n      format.timestamp({\n        format: timestamps === true ? defaultTimestampFormat : timestamps,\n      })\n    );\n  }\n\n  if (colors) {\n    handlers.push(format.colorize());\n  }\n\n  handlers.push(logErrors());\n\n  handlers.push(\n    format.printf(({ level, message, timestamp }) => {\n      return `${timestamps ? `[${timestamp as string}] ` : ''}${level}: ${message as string}`;\n    })\n  );\n\n  return format.combine(...handlers);\n};\n"], "names": ["defaultTimestampFormat", "options", "timestamps", "colors", "handlers", "push", "format", "timestamp", "colorize", "logErrors", "printf", "level", "message", "combine"], "mappings": ";;;;;AAGA,MAAMA,sBAAyB,GAAA,yBAAA;AAa/B;;;AAGC,IACD,kBAAe,CAAA,CAACC,OAAAA,GAA8B,EAAE,GAAA;AAC9C,IAAA,MAAM,EAAEC,UAAa,GAAA,IAAI,EAAEC,MAAS,GAAA,IAAI,EAAE,GAAGF,OAAAA;AAE7C,IAAA,MAAMG,WAA6B,EAAE;AAErC,IAAA,IAAIF,UAAY,EAAA;AACdE,QAAAA,QAAAA,CAASC,IAAI,CACXC,cAAOC,CAAAA,SAAS,CAAC;YACfD,MAAQJ,EAAAA,UAAAA,KAAe,OAAOF,sBAAyBE,GAAAA;AACzD,SAAA,CAAA,CAAA;AAEJ;AAEA,IAAA,IAAIC,MAAQ,EAAA;QACVC,QAASC,CAAAA,IAAI,CAACC,cAAAA,CAAOE,QAAQ,EAAA,CAAA;AAC/B;AAEAJ,IAAAA,QAAAA,CAASC,IAAI,CAACI,SAAAA,EAAAA,CAAAA;AAEdL,IAAAA,QAAAA,CAASC,IAAI,CACXC,cAAOI,CAAAA,MAAM,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAEL,SAAS,EAAE,GAAA;AAC1C,QAAA,OAAO,CAAC,EAAEL,UAAAA,GAAa,CAAC,CAAC,EAAEK,SAAoB,CAAA,EAAE,CAAC,GAAG,GAAG,EAAEI,KAAAA,CAAM,EAAE,EAAEC,QAAkB,CAAC;AACzF,KAAA,CAAA,CAAA;IAGF,OAAON,cAAAA,CAAOO,OAAO,CAAIT,GAAAA,QAAAA,CAAAA;AAC3B,CAAA;;;;"}