import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/email/dist/admin/translations/zh-Hans.json.mjs
var link = "链接";
var zhHans = {
  link,
  "Settings.email.plugin.button.test-email": "发送测试邮件",
  "Settings.email.plugin.label.defaultFrom": "默认发件地址",
  "Settings.email.plugin.label.defaultReplyTo": "默认回复地址",
  "Settings.email.plugin.label.provider": "电子邮件供应商",
  "Settings.email.plugin.title.test": "测试电子邮件发送",
  "Settings.email.plugin.label.testAddress": "收件地址",
  "SettingsNav.link.settings": "电子邮件设置",
  "SettingsNav.section-label": "电子邮件插件",
  "Settings.email.plugin.title": "配置",
  "Settings.email.plugin.subTitle": "测试电子邮件插件的配置",
  "Settings.email.plugin.title.config": "配置",
  "Settings.email.plugin.text.configuration": "通过 {file} 文件来配置插件，打开 {link} 查看文档。"
};
export {
  zhHans as default,
  link
};
//# sourceMappingURL=zh-<PERSON>.json-5WY6YZCH.js.map
