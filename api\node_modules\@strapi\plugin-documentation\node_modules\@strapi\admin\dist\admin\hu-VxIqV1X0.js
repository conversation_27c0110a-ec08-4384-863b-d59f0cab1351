'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const Analytics = "Analitika";
const Documentation = "Dokumentáció";
const Email = "Email";
const Password = "Jelsz<PERSON>";
const Provider = "Szolgáltató";
const ResetPasswordToken = "Token visszaállítása";
const Role = "Szerepkör";
const light = "Világos";
const dark = "Sötét";
const Username = "Felhasználónév";
const Users = "Felhasználók";
const anErrorOccurred = "Hoppá! Valami elromlott. Kérlek próbáld újra.";
const clearLabel = "Kiürít";
const or = "Vagy";
const skipToContent = "Kihagyás";
const submit = "Küldés";
const hu = {
	Analytics: Analytics,
	"Auth.components.Oops.text": "A fiókodat felfüggesztettük",
	"Auth.components.Oops.text.admin": "Amennyiben ez hiba, kérjük vegye fel a kapcsolatot az adminisztrátorokkal!",
	"Auth.components.Oops.title": "Oops...",
	"Auth.form.active.label": "Aktív",
	"Auth.form.button.forgot-password": "Email küldése",
	"Auth.form.button.go-home": "Vissza a kezdőlapra",
	"Auth.form.button.login": "Bejelentkezés",
	"Auth.form.button.login.providers.error": "Nem sikerült kapcsolódni a szolgáltatón keresztül",
	"Auth.form.button.login.strapi": "Bejelentkezés Strapi-val",
	"Auth.form.button.password-recovery": "Jelszó visszaállítása",
	"Auth.form.button.register": "Kezdjük",
	"Auth.form.confirmPassword.label": "Jelszó megerősítése",
	"Auth.form.currentPassword.label": "Jelenlegi jelszó",
	"Auth.form.email.label": "Email",
	"Auth.form.email.placeholder": "e.g. <EMAIL>",
	"Auth.form.error.blocked": "A fiókodat az adminisztrátor blokkolta",
	"Auth.form.error.code.provide": "Hibás a megadott kód",
	"Auth.form.error.confirmed": "Az email cím nincs megerősítve",
	"Auth.form.error.email.invalid": "Hibás email.",
	"Auth.form.error.email.provide": "Kérjük adja meg felhasználónevét és jelszavát.",
	"Auth.form.error.email.taken": "Ez az email cím már foglalt.",
	"Auth.form.error.invalid": "Felhasználónév vagy jelszó hibás.",
	"Auth.form.error.params.provide": "Hibás a megadott adat.",
	"Auth.form.error.password.format": "A jelszó nem tartalmazhatja a `$` szimbólumot többször, mint három.",
	"Auth.form.error.password.local": "Ez a felhasználó nem állított be jelszót, kérjük jelentkezzen be szolgáltatón keresztül.",
	"Auth.form.error.password.matching": "A jelszavak nem egeyznek.",
	"Auth.form.error.password.provide": "Kérjük adja meg a jelszavát.",
	"Auth.form.error.ratelimit": "Túl sok próbálkozás, kérjük próbálkozzon újra egy perc múlva.",
	"Auth.form.error.user.not-exist": "Ez az email nem létezik.",
	"Auth.form.error.username.taken": "A felhasználónév foglalt.",
	"Auth.form.firstname.label": "Keresztnév",
	"Auth.form.firstname.placeholder": "pl. Elek",
	"Auth.form.forgot-password.email.label": "Adja meg az email címét",
	"Auth.form.forgot-password.email.label.success": "Az email-t sikeresen kiküldtük",
	"Auth.form.lastname.label": "Vezetéknév",
	"Auth.form.lastname.placeholder": "pl. Teszt",
	"Auth.form.password.hide-password": "Jelszó elrejtése",
	"Auth.form.password.hint": "A jelszónak legalább 8 karaktert, 1 nagybetűt, 1 kisbetűt és 1 számot kell tartalmaznia.",
	"Auth.form.password.show-password": "Jelszó megjelenítése",
	"Auth.form.register.news.label": "Értesítést kérek az új funkciókról és javításokról (ezzel elfogadja a {terms} és a {policy}).",
	"Auth.form.register.subtitle": "Ez csak az admin oldalra való bejelentkezésre ad lehetőséget. Minden elmentett adat a saját adatbázisaba kerül mentésre.",
	"Auth.form.rememberMe.label": "Emlékezz rám",
	"Auth.form.username.label": "Felhasználónév",
	"Auth.form.username.placeholder": "e.g. Kai_Doe",
	"Auth.form.welcome.subtitle": "Bejelentkezés a Strapi fiókjába",
	"Auth.form.welcome.title": "Üdvözöljük!",
	"Auth.link.forgot-password": "Elfelejtette a jelszavát?",
	"Auth.link.ready": "Készen áll a bejelentkezésre?",
	"Auth.link.signin": "Bejelentkezés",
	"Auth.link.signin.account": "Már van felhasználói fiókja?",
	"Auth.login.sso.divider": "Vagy bejelentkezés ezzel:",
	"Auth.login.sso.loading": "Szolgáltatók betöltése...",
	"Auth.login.sso.subtitle": "Bejelentkezés a fiókjába SSO-val",
	"Auth.privacy-policy-agreement.policy": "adatvédelmi nyilatkozat",
	"Auth.privacy-policy-agreement.terms": "felhasználási feltételek",
	"Auth.reset-password.title": "Jelszó visszaállítása",
	"Content Manager": "Tartalom Menedzser",
	"Content Type Builder": "Tartalomtípus építő",
	Documentation: Documentation,
	Email: Email,
	"Files Upload": "Fájl feltöltés",
	"HomePage.head.title": "Kezdőlap",
	"HomePage.roadmap": "Nézze meg a terveinket",
	"HomePage.welcome.congrats": "Gratulálunk!",
	"HomePage.welcome.congrats.content": "Első adminisztrátorként jelentkezett be. Ahhoz, hogy felfedezhesse a Strapi funkcióit,",
	"HomePage.welcome.congrats.content.bold": "azt ajánljuk, hogy hozza létre az első tartalomtípust.",
	"Media Library": "Média Könyvtár",
	"New entry": "Új elem",
	Password: Password,
	Provider: Provider,
	ResetPasswordToken: ResetPasswordToken,
	Role: Role,
	"Roles & Permissions": "Szerepkörök & Engedélyek",
	"Roles.ListPage.notification.delete-all-not-allowed": "Egyes szerepkörök nem törölhetők, mivel felhasználókhoz vannak társítva",
	"Roles.ListPage.notification.delete-not-allowed": "A felhasználókhoz társított szerepkör nem törölhető",
	"Roles.RoleRow.select-all": "{name} kiválasztása tömeges műveletekhez",
	"Roles.RoleRow.user-count": "{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}}",
	"Roles.components.List.empty.withSearch": "Nincs a keresésnek megfelelő szerepkör ({search})...",
	"Settings.PageTitle": "Beállítások - {name}",
	"Settings.apiTokens.addFirstToken": "Első API Token hozzáadása",
	"Settings.apiTokens.addNewToken": "Új API Token hozzáadása",
	"Settings.tokens.copy.editMessage": "Biztonsági okokból csak egyszer láthatja a tokent.",
	"Settings.tokens.copy.editTitle": "Ez a token már nem elérhető.",
	"Settings.tokens.copy.lastWarning": "Másolja le a tokent, mert később már nem lesz látható!",
	"Settings.apiTokens.create": "Új hozzáadása",
	"Settings.apiTokens.description": "Az API felhasználásához generált tokenek listája",
	"Settings.apiTokens.emptyStateLayout": "Még nincs tartalom hozzáadva...",
	"Settings.apiTokens.ListView.headers.name": "Név",
	"Settings.apiTokens.ListView.headers.description": "Leírás",
	"Settings.apiTokens.ListView.headers.type": "Token típusa",
	"Settings.apiTokens.ListView.headers.createdAt": "Létrehozva",
	"Settings.apiTokens.ListView.headers.lastUsedAt": "Utoljára használva",
	"Settings.tokens.notification.copied": "Token a vágólapra másolva.",
	"Settings.apiTokens.title": "API Token-ek",
	"Settings.tokens.types.full-access": "Teljes hozzáférés",
	"Settings.tokens.types.read-only": "Csak olvasható",
	"Settings.tokens.duration.7-days": "7 nap",
	"Settings.tokens.duration.30-days": "30 nap",
	"Settings.tokens.duration.90-days": "90 nap",
	"Settings.tokens.duration.unlimited": "Korlátlan",
	"Settings.tokens.form.duration": "Token időtartama",
	"Settings.tokens.form.type": "Token típusa",
	"Settings.tokens.duration.expiration-date": "Lejárati dátum",
	"Settings.apiTokens.createPage.permissions.title": "Engedélyek",
	"Settings.apiTokens.createPage.permissions.description": "Csak az útvonalakhoz kötött műveletek szerepelnek az alábbiakban.",
	"Settings.tokens.RegenerateDialog.title": "Token újragenerálása",
	"Settings.tokens.popUpWarning.message": "Biztosan újragenerálod ezt a token-t?",
	"Settings.tokens.Button.cancel": "Mégse",
	"Settings.tokens.Button.regenerate": "Újragenerálás",
	"Settings.application.description": "Az adminisztrációs panel globális információi",
	"Settings.application.edition-title": "Aktuális csomag",
	"Settings.application.get-help": "Kérje segítségünket",
	"Settings.application.link-pricing": "Tekintse meg az összes csomagot",
	"Settings.application.link-upgrade": "Frissítse az adminisztrációs panelt",
	"Settings.application.node-version": "node verzió",
	"Settings.application.strapi-version": "strapi verzió",
	"Settings.application.strapiVersion": "strapi verzió",
	"Settings.application.title": "Áttekintés",
	"Settings.application.customization": "Testreszabás",
	"Settings.application.customization.carousel.title": "Logó",
	"Settings.application.customization.carousel.change-action": "Logó módosítása",
	"Settings.application.customization.carousel.reset-action": "Logó visszaállítása",
	"Settings.application.customization.carousel-slide.label": "Logó diasor",
	"Settings.application.customization.carousel-hint": "Változtasd meg az admin panel logóját (Max méret: {dimension}x{dimension}, Max fájlméret: {size}KB)",
	"Settings.application.customization.modal.cancel": "Mégse",
	"Settings.application.customization.modal.upload": "Logó feltöltése",
	"Settings.application.customization.modal.tab.label": "Hogyan szeretnéd feltölteni az állományaidat?",
	"Settings.application.customization.modal.upload.from-computer": "Számítógépről",
	"Settings.application.customization.modal.upload.file-validation": "Max méret: {dimension}x{dimension}, Max méret: {size}KB",
	"Settings.application.customization.modal.upload.error-format": "Rossz formátumot töltöttél fel (csak a következő formátumokat fogadja el: jpeg, jpg, png, svg).",
	"Settings.application.customization.modal.upload.error-size": "A feltöltött fájl túl nagy (max méret: {dimension}x{dimension}, max fájlméret: {size}KB)",
	"Settings.application.customization.modal.upload.error-network": "Hálózati hiba",
	"Settings.application.customization.modal.upload.cta.browse": "Fájlok tallózása",
	"Settings.application.customization.modal.upload.drag-drop": "Húzz és ejtsd ide vagy",
	"Settings.application.customization.modal.upload.from-url": "URL-ről",
	"Settings.application.customization.modal.upload.from-url.input-label": "URL",
	"Settings.application.customization.modal.upload.next": "Következő",
	"Settings.application.customization.modal.pending": "Függőben lévő logó",
	"Settings.application.customization.modal.pending.choose-another": "Válassz másik logót",
	"Settings.application.customization.modal.pending.title": "Logó készen áll a feltöltésre",
	"Settings.application.customization.modal.pending.subtitle": "Kezeljed a kiválasztott logót a feltöltés előtt",
	"Settings.application.customization.modal.pending.upload": "Logó feltöltése",
	"Settings.application.customization.modal.pending.card-badge": "kép",
	"Settings.error": "Hiba",
	"Settings.global": "Globális Beállítások",
	"Settings.permissions": "Adminisztrációs panel",
	"Settings.permissions.category": "{category} engedélyeinek beállításai",
	"Settings.permissions.category.plugins": "{category} plugin engedélyeinek beállításai",
	"Settings.permissions.conditions.anytime": "Bármikor",
	"Settings.permissions.conditions.apply": "Alkalmaz",
	"Settings.permissions.conditions.can": "Tudja",
	"Settings.permissions.conditions.conditions": "Határozza meg a feltételeket",
	"Settings.permissions.conditions.links": "Linkek",
	"Settings.permissions.conditions.no-actions": "Először választania kell egy műveletet (create, read, update, ...) mielőtt megadja a feltételeket.",
	"Settings.permissions.conditions.none-selected": "Bármikor",
	"Settings.permissions.conditions.or": "VAGY",
	"Settings.permissions.conditions.when": "Mikor",
	"Settings.permissions.select-all-by-permission": "Minden {label} hozzáféres kiválasztása",
	"Settings.permissions.select-by-permission": "{label} hozzáféres kiválasztása",
	"Settings.permissions.users.create": "Új felhasználó meghívása",
	"Settings.permissions.users.email": "Email",
	"Settings.permissions.users.firstname": "Keresztnév",
	"Settings.permissions.users.lastname": "Vezetéknév",
	"Settings.permissions.users.user-status": "Felhasználói állapot",
	"Settings.permissions.users.roles": "Szerepek",
	"Settings.permissions.users.username": "Felhasználónév",
	"Settings.permissions.users.active": "Aktív",
	"Settings.permissions.users.inactive": "Inaktív",
	"Settings.permissions.users.form.sso": "Csatlakozas SSO-val",
	"Settings.permissions.users.form.sso.description": "Ha engedélyezve van (ON), a felhasználók bejelentkezhetnek SSO-n keresztül",
	"Settings.permissions.users.listview.header.subtitle": "Minden felhasználó, aki hozzáfér a Strapi adminisztrációs panelhez",
	"Settings.permissions.users.tabs.label": "Hozzáférések Tab",
	"Settings.permissions.users.strapi-super-admin": "Super Adminisztrátor",
	"Settings.permissions.users.strapi-editor": "Szerkesztő",
	"Settings.permissions.users.strapi-author": "Szerző",
	"Settings.profile.form.notify.data.loaded": "Profiladatok betöltve",
	"Settings.profile.form.section.experience.clear.select": "A kiválasztott felület nyelvének törlése",
	"Settings.profile.form.section.experience.here": "itt",
	"Settings.profile.form.section.experience.documentation": "dokumentáció",
	"Settings.profile.form.section.experience.interfaceLanguage": "A felület nyelve",
	"Settings.profile.form.section.experience.interfaceLanguage.hint": "Ez csak a saját felületét jeleníti meg a kiválasztott nyelven.",
	"Settings.profile.form.section.experience.interfaceLanguageHelp": "A kiválasztás csak az Ön számára módosítja a felület nyelvét. Kérjük, olvassa el ezt a {document}, hogy más nyelveket a csapata számára is elérhetővé tehesse.",
	"Settings.profile.form.section.experience.mode.label": "Felület mód",
	"Settings.profile.form.section.experience.mode.hint": "Megjeleníti a felhasználói felületedet a kiválasztott módban.",
	"Settings.profile.form.section.experience.mode.option-label": "{name} mód",
	light: light,
	dark: dark,
	"Settings.profile.form.section.experience.title": "Tapasztalat",
	"Settings.profile.form.section.head.title": "Felhasználói profil",
	"Settings.profile.form.section.profile.page.title": "Profil oldal",
	"Settings.roles.create.description": "Határozza meg a szerephezkörhöz biztosított jogokat",
	"Settings.roles.create.title": "Szerepkör létrehozása",
	"Settings.roles.created": "A szerepkör létrejött",
	"Settings.roles.edit.title": "Szerepkör módosítása",
	"Settings.roles.form.button.users-with-role": "{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}} ezzel a szereppel",
	"Settings.roles.form.created": "Létrehozva",
	"Settings.roles.form.description": "A szerepkör neve és leírása",
	"Settings.roles.form.permission.property-label": "{label} hozzáfére's",
	"Settings.roles.form.permissions.attributesPermissions": "Mezők hozzáférései",
	"Settings.roles.form.permissions.create": "Létrehoz",
	"Settings.roles.form.permissions.delete": "Töröl",
	"Settings.roles.form.permissions.publish": "Közzétesz",
	"Settings.roles.form.permissions.read": "Olvasás",
	"Settings.roles.form.permissions.update": "Frissítés",
	"Settings.roles.list.button.add": "Új szerepkör hozzáadása",
	"Settings.roles.list.description": "Szerepkörök listája",
	"Settings.roles.title.singular": "Szerepkör",
	"Settings.sso.description": "Konfigurálja az egyszeri bejelentkezés funkció beállításait.",
	"Settings.sso.form.defaultRole.description": "Az új hitelesített felhasználót a kiválasztott szerepkörhöz csatolja",
	"Settings.sso.form.defaultRole.description-not-allowed": "Nincs megfelelő engedélye az adminisztrátori szerepkörök olvasásához",
	"Settings.sso.form.defaultRole.label": "Alapértelmezett szerepkör",
	"Settings.sso.form.registration.description": "Egyszeri bejelentkezéskor, ha nincs fiók, hozzon létre új felhasználót",
	"Settings.sso.form.registration.label": "Automatikus regisztráció",
	"Settings.sso.title": "Egyszeri bejelentkezés",
	"Settings.webhooks.create": "Webhook létrehozása",
	"Settings.webhooks.create.header": "Új fejléc létrehozása",
	"Settings.webhooks.created": "Webhook létrehozva",
	"Settings.webhooks.event.publish-tooltip": "Ez az esemény csak olyan tartalmak esetében létezik, amelyeknél engedélyezve van a Piszkozat/Közzététel rendszer",
	"Settings.webhooks.events.create": "Létrehoz",
	"Settings.webhooks.events.update": "Frissít",
	"Settings.webhooks.form.events": "Esemnények",
	"Settings.webhooks.form.headers": "Fejléc",
	"Settings.webhooks.form.url": "Url",
	"Settings.webhooks.headers.remove": "Fejlésor eltávolítása {number}",
	"Settings.webhooks.key": "Kulcs",
	"Settings.webhooks.list.button.add": "Új webhook létrehozása",
	"Settings.webhooks.list.description": "Értesítések a POST módosításairól",
	"Settings.webhooks.list.empty.description": "Nem található webhook",
	"Settings.webhooks.list.empty.link": "Tekintse meg dokumentációnkat",
	"Settings.webhooks.list.empty.title": "Még nincsenek webhookok",
	"Settings.webhooks.list.th.actions": "Műveletek",
	"Settings.webhooks.list.th.status": "Státusz",
	"Settings.webhooks.singular": "webhook",
	"Settings.webhooks.title": "Webhook-ok",
	"Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# elem} other {# elemek}} kiválasztva",
	"Settings.webhooks.trigger": "Kapcsoló",
	"Settings.webhooks.trigger.cancel": "Kapcsoló törlése",
	"Settings.webhooks.trigger.pending": "Folyamatban…",
	"Settings.webhooks.trigger.save": "Kérjük mentse",
	"Settings.webhooks.trigger.success": "Sikerült!",
	"Settings.webhooks.trigger.success.label": "A kapcsoló sikerült",
	"Settings.webhooks.trigger.test": "Teszt-kapcsoló",
	"Settings.webhooks.trigger.title": "Először mentsen",
	"Settings.webhooks.value": "Érték",
	"Usecase.back-end": "Back-end fejlesztő",
	"Usecase.button.skip": "Kérdezés kihagyása",
	"Usecase.content-creator": "Tartalomkészítő",
	"Usecase.front-end": "Front-end fejlesztő",
	"Usecase.full-stack": "Teljeskörű fejlesztő",
	"Usecase.input.work-type": "Milyen típusú munkát végzel?",
	"Usecase.notification.success.project-created": "A projekt sikeresen létrehozva",
	"Usecase.other": "Egyéb",
	"Usecase.title": "Mesélj egy kicsit magadról",
	Username: Username,
	Users: Users,
	"Users & Permissions": "Felhasználók & Engedélyek",
	"Users.components.List.empty": "Nincsenek felhasználók...",
	"Users.components.List.empty.withFilters": "Nincs a beállított szűrőknek megfelelő felhasználó..",
	"Users.components.List.empty.withSearch": "Nincs a keresének megfelelő felhasználó ({search})...",
	"admin.pages.MarketPlacePage.head": "Piactér - Plugin-ok",
	"admin.pages.MarketPlacePage.offline.title": "Ön offline állapotban van",
	"admin.pages.MarketPlacePage.offline.subtitle": "Csatlakoznia kell az internethez a Strapi Market eléréséhez.",
	"admin.pages.MarketPlacePage.plugins": "Bővítmények",
	"admin.pages.MarketPlacePage.plugin.copy": "Telepítési parancs másolása",
	"admin.pages.MarketPlacePage.plugin.copy.success": "A telepítési parancs készen áll a terminálba való bemásolásra",
	"admin.pages.MarketPlacePage.plugin.info": "További információk",
	"admin.pages.MarketPlacePage.plugin.info.label": "{pluginName} bővítmény további információi",
	"admin.pages.MarketPlacePage.plugin.info.text": "További információk",
	"admin.pages.MarketPlacePage.plugin.installed": "Telepítve",
	"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Készítette: Strapi",
	"admin.pages.MarketPlacePage.plugin.tooltip.verified": "Bővítmény hitelesítve a Strapi által",
	"admin.pages.MarketPlacePage.plugin.version": "Frissítsd a Strapi verziód: \"{strapiAppVersion}\" erre: \"{versionRange}\"",
	"admin.pages.MarketPlacePage.plugin.version.null": "Nem sikerült ellenőrizni a kompatibilitást a Strapi verzióddal: \"{strapiAppVersion}\"",
	"admin.pages.MarketPlacePage.plugin.githubStars": "Ezt a plugint {starsCount} csillagra jelölték a GitHub-on",
	"admin.pages.MarketPlacePage.plugin.downloads": "Ezt a plugint hetente {downloadsCount} alkalommal töltik le",
	"admin.pages.MarketPlacePage.providers": "Szolgáltatók",
	"admin.pages.MarketPlacePage.provider.githubStars": "Ezt a szolgáltatót {starsCount} csillagra jelölték a GitHub-on",
	"admin.pages.MarketPlacePage.provider.downloads": "Ezt a szolgáltatót hetente {downloadsCount} alkalommal töltik le",
	"admin.pages.MarketPlacePage.search.clear": "Keresés törlése",
	"admin.pages.MarketPlacePage.search.empty": "Nincs találat erre: \"{target}\"",
	"admin.pages.MarketPlacePage.search.placeholder": "Keresés",
	"admin.pages.MarketPlacePage.submit.plugin.link": "Plugin küldése",
	"admin.pages.MarketPlacePage.submit.provider.link": "Provider beküldése",
	"admin.pages.MarketPlacePage.subtitle": "Hozzon ki többet a Strapi-ból",
	"admin.pages.MarketPlacePage.tab-group.label": "Strapi pluginek és szolgáltatók",
	"admin.pages.MarketPlacePage.missingPlugin.title": "Hiányzik egy plugin?",
	"admin.pages.MarketPlacePage.missingPlugin.description": "Mondd el, milyen pluginra van szükséged, és tájékoztatjuk a közösségi plugin fejlesztőinket, hogy esetleg ötletet meríthessenek belőle!",
	"admin.pages.MarketPlacePage.sort.alphabetical": "Betűrendes rendezés",
	"admin.pages.MarketPlacePage.sort.newest": "Legújabb",
	"admin.pages.MarketPlacePage.sort.alphabetical.selected": "Rendezés betűrend szerint",
	"admin.pages.MarketPlacePage.sort.newest.selected": "Rendezés legújabbak szerint",
	"admin.pages.MarketPlacePage.sort.githubStars": "GitHub csillagok száma",
	"admin.pages.MarketPlacePage.sort.githubStars.selected": "Rendezés GitHub csillagok szerint",
	"admin.pages.MarketPlacePage.sort.npmDownloads": "Letöltések száma",
	"admin.pages.MarketPlacePage.sort.npmDownloads.selected": "Rendezés npm letöltések szerint",
	"admin.pages.MarketPlacePage.filters.collections": "Gyűjtemények",
	"admin.pages.MarketPlacePage.filters.collectionsSelected": "{count, plural, =0 {Nincsenek gyűjtemények} one {# gyűjtemény} other {# gyűjtemények}} kiválasztva",
	"admin.pages.MarketPlacePage.filters.categories": "Kategóriák",
	"admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {Nincsenek kategóriák} one {# kategória} other {# kategóriák}} kiválasztva",
	anErrorOccurred: anErrorOccurred,
	"app.component.CopyToClipboard.label": "Másolás a vágólapra",
	"app.component.search.label": "{target} keresése",
	"app.component.table.duplicate": "{target} duplikálása",
	"app.component.table.edit": "{target} szerkesztése",
	"app.component.table.select.one-entry": "{target} kiválasztása",
	"app.components.BlockLink.blog": "Blog",
	"app.components.BlockLink.blog.content": "Olvassa el a legfrissebb híreket a Strapiról és az ökoszisztémáról.",
	"app.components.BlockLink.code": "Kód példák",
	"app.components.BlockLink.code.content": "Tanuljon a közösség által fejlesztett valós projektek segítségével.",
	"app.components.BlockLink.documentation.content": "Fedezze fel az alapvető fogalmakat, útmutatókat és utasításokat.",
	"app.components.BlockLink.tutorial": "Oktatóanyagok",
	"app.components.BlockLink.tutorial.content": "Kövesse az utasításokat a Strapi használatához és testreszabásához.",
	"app.components.Button.cancel": "Mégsem",
	"app.components.Button.confirm": "Megerősítés",
	"app.components.Button.reset": "Visszaállítás",
	"app.components.ComingSoonPage.comingSoon": "Hamarosan",
	"app.components.ConfirmDialog.title": "Megerősítés",
	"app.components.DownloadInfo.download": "Letöltés folyamatban...",
	"app.components.DownloadInfo.text": "Ez eltarthat egy percig. Köszönjük a türelmét.",
	"app.components.EmptyAttributes.title": "Még nincsenek mezők",
	"app.components.EmptyStateLayout.content-document": "Nem található tartalom",
	"app.components.EmptyStateLayout.content-permissions": "Nincs megfelelő jogosultsága a tartalomhozhoz",
	"app.components.GuidedTour.CM.create.content": "<p>Hozz létre és kezelj minden tartalmat itt a Tartalomkezelőben.</p><p>Például: A Blog weboldal példáját folytatva, írhatsz egy Cikket, mentheted és publikálhatod úgy, ahogy szeretnéd.</p><p>💡 Gyors tipp - Ne felejtsd el publikálni a létrehozott tartalmat.</p>",
	"app.components.GuidedTour.CM.create.title": "⚡️ Tartalom létrehozása",
	"app.components.GuidedTour.CM.success.content": "<p>Szuper, még egy lépés van hátra!</p><b>🚀 Lásd a tartalmat működés közben</b>",
	"app.components.GuidedTour.CM.success.cta.title": "API tesztelése",
	"app.components.GuidedTour.CM.success.title": "2. lépés: Kész ✅",
	"app.components.GuidedTour.CTB.create.content": "<p>A Gyűjtemény típusok segítségével több bejegyzést tudsz kezelni, míg az Egy típusok a csak egy bejegyzés kezelésére alkalmasak.</p> <p>Például: Egy Blog weboldalnál a Cikkek lennek egy Gyűjtemény típus, míg a Honlap lenne egy Egy típus.</p>",
	"app.components.GuidedTour.CTB.create.cta.title": "Hozz létre egy Gyűjtemény típust",
	"app.components.GuidedTour.CTB.create.title": "🧠 Hozz létre első Gyűjtemény típust",
	"app.components.GuidedTour.CTB.success.content": "<p>Jól haladsz!</p><b>⚡️ Mit szeretnél megosztani a világgal?</b>",
	"app.components.GuidedTour.CTB.success.title": "1. lépés: Kész ✅",
	"app.components.GuidedTour.apiTokens.create.content": "<p>Hozz létre itt egy hitelesítési token-t, és töltsd le az általad létrehozott tartalmat.</p>",
	"app.components.GuidedTour.apiTokens.create.cta.title": "API Token generálása",
	"app.components.GuidedTour.apiTokens.create.title": "🚀 Lásd a tartalmat működés közben",
	"app.components.GuidedTour.apiTokens.success.content": "<p>Lásd a tartalmat működés közben az HTTP kéréssel:</p><ul><li><p>Erre a URL-re: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Ezzel a fejléccel: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>További lehetőségek a tartalommal való interakcióhoz, lásd a <documentationLink>dokumentációt</documentationLink>.</p>",
	"app.components.GuidedTour.apiTokens.success.cta.title": "Menj vissza a főoldalra",
	"app.components.GuidedTour.apiTokens.success.title": "3. lépés: befejezve ✅",
	"app.components.GuidedTour.create-content": "Tartalom létrehozása",
	"app.components.GuidedTour.home.CM.title": "⚡️ Mire szeretnéd megosztani a világgal?",
	"app.components.GuidedTour.home.CTB.cta.title": "Menj a Content type Builder-be",
	"app.components.GuidedTour.home.CTB.title": "🧠 Építsd fel a tartalom struktúráját",
	"app.components.GuidedTour.home.apiTokens.cta.title": "API tesztelése",
	"app.components.GuidedTour.skip": "A túra átugrása",
	"app.components.GuidedTour.title": "3 lépés a kezdéshez",
	"app.components.HomePage.button.blog": "Bővebben a blogon",
	"app.components.HomePage.community": "Csatlakozz a közösséghez",
	"app.components.HomePage.community.content": "Beszélgessen a csapattagokkal, a közreműködőkkel és a fejlesztőkkel különböző csatornákon.",
	"app.components.HomePage.create": "Hozza létre az első tartalomtípust",
	"app.components.HomePage.roadmap": "Tekintse meg terveinket",
	"app.components.HomePage.welcome": "Üdvözöljük a fedélzeten 👋",
	"app.components.HomePage.welcome.again": "Üdvözöljük 👋",
	"app.components.HomePage.welcomeBlock.content": "Gratulálunk! Első rendszergazdaként jelentkezett be. A Strapi által nyújtott funkciók felfedezéséhez javasoljuk, hogy hozza létre első tartalomtípusát!",
	"app.components.HomePage.welcomeBlock.content.again": "Reméljük, hogy jól halad a projektje! Olvassa el a Strapi legfrissebb híreit. Visszajelzései alapján mindent megteszünk, hogy javítsuk a terméket.",
	"app.components.HomePage.welcomeBlock.content.issues": "problémák.",
	"app.components.HomePage.welcomeBlock.content.raise": " vagy írjon ",
	"app.components.ImgPreview.hint": "Húzza a fájlt erre a területre, vagy {browse} a feltöltendő fájlért",
	"app.components.ImgPreview.hint.browse": "tallózás",
	"app.components.InputFile.newFile": "Új fájl hozzáadása",
	"app.components.InputFileDetails.open": "Megnyitás új lapon",
	"app.components.InputFileDetails.originalName": "Eredeti név:",
	"app.components.InputFileDetails.remove": "Fájl eltávolítása",
	"app.components.InputFileDetails.size": "Méret:",
	"app.components.InstallPluginPage.Download.description": "A bővítmény letöltése és telepítése eltarthat néhány másodpercig.",
	"app.components.InstallPluginPage.Download.title": "Letöltés...",
	"app.components.InstallPluginPage.description": "Bővítse alkalmazását erőfeszítés nélkül.",
	"app.components.LeftMenu.collapse": "A navigációs sáv összecsukása",
	"app.components.LeftMenu.expand": "A navigációs sáv kinyitása",
	"app.components.LeftMenu.general": "Általános",
	"app.components.LeftMenu.logout": "Kijelentkezés",
	"app.components.LeftMenu.logo.alt": "Alkalmazás logó",
	"app.components.LeftMenu.plugins": "Bővítmények",
	"app.components.LeftMenu.navbrand.title": "Strapi Műszerfal",
	"app.components.LeftMenu.navbrand.workplace": "Munkaterület",
	"app.components.LeftMenuFooter.help": "Segítség",
	"app.components.LeftMenuFooter.poweredBy": "Powered by ",
	"app.components.LeftMenuLinkContainer.collectionTypes": "Gyűjtemény típusai",
	"app.components.LeftMenuLinkContainer.configuration": "Beállítások",
	"app.components.LeftMenuLinkContainer.general": "Általános",
	"app.components.LeftMenuLinkContainer.noPluginsInstalled": "Nincs bővítmény telepítve",
	"app.components.LeftMenuLinkContainer.plugins": "Bővítmények",
	"app.components.LeftMenuLinkContainer.singleTypes": "Egyedülálló típusok",
	"app.components.ListPluginsPage.deletePlugin.description": "A bővítmény eltávolítása eltarthat néhány másodpercig.",
	"app.components.ListPluginsPage.deletePlugin.title": "Eltávolítás",
	"app.components.ListPluginsPage.description": "A telepített bővítmények listája.",
	"app.components.ListPluginsPage.head.title": "A bővítmények listája",
	"app.components.Logout.logout": "Kijelentkezés",
	"app.components.Logout.profile": "Profil",
	"app.components.MarketplaceBanner": "Fedezze fel a közösség által épített modulokat, és még sok más fantasztikus dolgot, amik segítenek a projekt elindításában.",
	"app.components.MarketplaceBanner.image.alt": "a strapi rocket logo",
	"app.components.MarketplaceBanner.link": "Nézze meg most",
	"app.components.NotFoundPage.back": "Vissza a kezdőoldalra",
	"app.components.NotFoundPage.description": "Nem található",
	"app.components.Official": "Hivatalos",
	"app.components.Onboarding.help.button": "Súgó gomb",
	"app.components.Onboarding.label.completed": "% elkészült",
	"app.components.Onboarding.title": "Bemutató videók",
	"app.components.PluginCard.Button.label.download": "Letöltés",
	"app.components.PluginCard.Button.label.install": "Már telepítve van",
	"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Az automatikus újratöltés funkciót engedélyezni kell. Kérjük, indítsa el az alkalmazást ezzel a paranccsal: `yarn develop`.",
	"app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Megértettem!",
	"app.components.PluginCard.PopUpWarning.install.impossible.environment": "Biztonsági okokból egy plugin csak fejlesztői környezetben tölthető le.",
	"app.components.PluginCard.PopUpWarning.install.impossible.title": "A letöltés nem lehetséges",
	"app.components.PluginCard.compatible": "Kompatibilis az alkalmazásoddal",
	"app.components.PluginCard.compatibleCommunity": "Kompatibilis a közösséggel",
	"app.components.PluginCard.more-details": "További részletek",
	"app.components.ToggleCheckbox.off-label": "Kikapcsol",
	"app.components.ToggleCheckbox.on-label": "Bekapcsol",
	"app.components.Users.MagicLink.connect": "Másolja ki és ossza meg ezt a linket, hogy hozzáférést biztosítson ehhez a felhasználóhoz",
	"app.components.Users.MagicLink.connect.sso": "Küldje el ezt a linket a felhasználónak. Az első bejelentkezés történhet SSO szolgáltatón keresztül",
	"app.components.Users.ModalCreateBody.block-title.details": "Felhasználói adatok",
	"app.components.Users.ModalCreateBody.block-title.roles": "A felhasználó szerepkörei",
	"app.components.Users.ModalCreateBody.block-title.roles.description": "Egy felhasználónak lehet egy, illetve több szerepköre is",
	"app.components.Users.SortPicker.button-label": "Rendezés",
	"app.components.Users.SortPicker.sortby.email_asc": "Email (A - Z)",
	"app.components.Users.SortPicker.sortby.email_desc": "Email (Z - A)",
	"app.components.Users.SortPicker.sortby.firstname_asc": "Keresztnév (A - Z)",
	"app.components.Users.SortPicker.sortby.firstname_desc": "Keresztnév (Z - A)",
	"app.components.Users.SortPicker.sortby.lastname_asc": "Vezetéknév (A - Z)",
	"app.components.Users.SortPicker.sortby.lastname_desc": "Vezetéknév (Z - A)",
	"app.components.Users.SortPicker.sortby.username_asc": "Felhasználónév (A - Z)",
	"app.components.Users.SortPicker.sortby.username_desc": "Felhasználónév (Z - A)",
	"app.components.listPlugins.button": "Új bővítmény hozzáadása",
	"app.components.listPlugins.title.none": "Nincs telepítve bővítmény",
	"app.components.listPluginsPage.deletePlugin.error": "Hiba történt a bővítmény eltávolítása közben",
	"app.containers.App.notification.error.init": "Hiba történt az API kérése közben",
	"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Ha nem kapja meg ezt a linket, forduljon az adminisztrátorhoz.",
	"app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Eltarthat néhány percig, amíg megkapja a jelszó-helyreállítási linket.",
	"app.containers.AuthPage.ForgotPasswordSuccess.title": "Email elküdlve",
	"app.containers.Users.EditPage.form.active.label": "Aktív",
	"app.containers.Users.EditPage.header.label": "{name} módosítása",
	"app.containers.Users.EditPage.header.label-loading": "Felhasználó szerkesztése",
	"app.containers.Users.EditPage.roles-bloc-title": "A hozzárendelt szerepkörök",
	"app.containers.Users.ModalForm.footer.button-success": "Felhasználó meghívása",
	"app.links.configure-view": "A nézet testreszabása",
	"app.page.not.found": "Hoppá! Úgy tűnik, nem találjuk a keresett oldalt...",
	"app.static.links.cheatsheet": "Puska",
	"app.utils.SelectOption.defaultMessage": " ",
	"app.utils.add-filter": "Szűrő hozzáadása",
	"app.utils.close-label": "Bezárás",
	"app.utils.defaultMessage": " ",
	"app.utils.duplicate": "Duplikálás",
	"app.utils.edit": "Szerkesztés",
	"app.utils.errors.file-too-big.message": "A fájl mérete túl nagy",
	"app.utils.filter-value": "Szűrési érték",
	"app.utils.filters": "Szűrők",
	"app.utils.notify.data-loaded": "A {target} betöltődött",
	"app.utils.placeholder.defaultMessage": " ",
	"app.utils.publish": "Közzétesz",
	"app.utils.select-all": "Minden kiválasztása",
	"app.utils.select-field": "Mező kiválasztása",
	"app.utils.select-filter": "Szűrő kiválasztása",
	"app.utils.unpublish": "Közzététel visszavonása",
	clearLabel: clearLabel,
	"coming.soon": "Ez a tartalom jelenleg fejlesztés alatt áll, és néhány héten belül újra elérhető lesz!",
	"component.Input.error.validation.integer": "Az értéknek egész számnak kell lennie",
	"components.AutoReloadBlocker.description": "Futtassa a Strapit a következő parancsok egyikével:",
	"components.AutoReloadBlocker.header": "Ehhez a bővítményhez töltse be újra a funkciót.",
	"components.ErrorBoundary.title": "Valami elromlott...",
	"components.FilterOptions.FILTER_TYPES.$contains": "tartalmazza",
	"components.FilterOptions.FILTER_TYPES.$containsi": "tartalmazza (nem nagybetű érzékeny)",
	"components.FilterOptions.FILTER_TYPES.$endsWith": "erre végződik",
	"components.FilterOptions.FILTER_TYPES.$endsWithi": "erre végződik (nem nagybetű érzékeny)",
	"components.FilterOptions.FILTER_TYPES.$eq": "egyenlő",
	"components.FilterOptions.FILTER_TYPES.$eqi": "egyenlő (nem nagybetű érzékeny)",
	"components.FilterOptions.FILTER_TYPES.$gt": "nagyobb, mint",
	"components.FilterOptions.FILTER_TYPES.$gte": "nagyobb, vagy egyenlő, mint",
	"components.FilterOptions.FILTER_TYPES.$lt": "kisebb, mint",
	"components.FilterOptions.FILTER_TYPES.$lte": "kisebb, vagy egyenlő, mint",
	"components.FilterOptions.FILTER_TYPES.$ne": "nem egyenlő",
	"components.FilterOptions.FILTER_TYPES.$nei": "nem egyenlő (nem nagybetű érzékeny)",
	"components.FilterOptions.FILTER_TYPES.$notContains": "nem tartalmazza",
	"components.FilterOptions.FILTER_TYPES.$notContainsi": "nem tartalmazza (nem nagybetű érzékeny)",
	"components.FilterOptions.FILTER_TYPES.$notNull": "nem null",
	"components.FilterOptions.FILTER_TYPES.$null": "null",
	"components.FilterOptions.FILTER_TYPES.$startsWith": "ezzel kezdődik",
	"components.FilterOptions.FILTER_TYPES.$startsWithi": "ezzel kezdődik (nem nagybetű érzékeny)",
	"components.Input.error.attribute.key.taken": "Ez az érték már létezik",
	"components.Input.error.attribute.sameKeyAndName": "Nem lehet egyenlő",
	"components.Input.error.attribute.taken": "Ez a mezőnév már létezik",
	"components.Input.error.contain.lowercase": "A jelszónak tartalmaznia kell legalább egy kisbetűt",
	"components.Input.error.contain.number": "A jelszónak tartalmaznia kell legalább egy számot",
	"components.Input.error.contain.uppercase": "A jelszónak tartalmaznia kell legalább egy nagybetűt",
	"components.Input.error.contentTypeName.taken": "Ez a név már létezik",
	"components.Input.error.custom-error": "{errorMessage} ",
	"components.Input.error.password.noMatch": "A jelszavak nem egyeznek",
	"components.Input.error.validation.email": "Érvénytelen e-mail",
	"components.Input.error.validation.json": "Hibás JSON formátum",
	"components.Input.error.validation.lowercase": "Az értéknek kisbetűs karakterláncnak kell lennie",
	"components.Input.error.validation.max": "A megadott érték túl nagy {max}.",
	"components.Input.error.validation.maxLength": "A megadott érték túl hosszú {max}.",
	"components.Input.error.validation.min": "A megadott érték túl alacsony {min}.",
	"components.Input.error.validation.minLength": "A megadott érték túl rövid {min}.",
	"components.Input.error.validation.minSupMax": "Nem lehet felsőbbrendű",
	"components.Input.error.validation.regex": "A megadott érték formátuma nem megfelelő.",
	"components.Input.error.validation.required": "Ez az érték kötelező.",
	"components.Input.error.validation.unique": "Ez az érték már használatban van.",
	"components.InputSelect.option.placeholder": "Válasszon itt",
	"components.ListRow.empty": "Nincsenek megjelenítendő adatok.",
	"components.NotAllowedInput.text": "Nincs jogosultsága a mező megtekintéséhez",
	"components.OverlayBlocker.description": "Olyan funkciót használ, amelynek újra kell indítania a szervert. Kérjük, várja meg, amíg a szerver feláll.",
	"components.OverlayBlocker.description.serverError": "A szervernek újra kellett volna indulnia, kérjük, ellenőrizze a logokat a terminálban.",
	"components.OverlayBlocker.title": "Újraindításra vár...",
	"components.OverlayBlocker.title.serverError": "Az újraindítás a vártnál tovább tart",
	"components.PageFooter.select": "Bejegyzések oldalanként",
	"components.ProductionBlocker.description": "Biztonsági okokból le kell tiltanunk ezt a bővítményt más környezetekben.",
	"components.ProductionBlocker.header": "Ez a bővítmény csak fejlesztői környezetben érhető el.",
	"components.Search.placeholder": "Keresés...",
	"components.TableHeader.sort": "Rendezés {label} szerint",
	"components.Wysiwyg.ToggleMode.markdown-mode": "Markdown mód",
	"components.Wysiwyg.ToggleMode.preview-mode": "Előnézet mód",
	"components.Wysiwyg.collapse": "Összecsuk",
	"components.Wysiwyg.selectOptions.H1": "Cím H1",
	"components.Wysiwyg.selectOptions.H2": "Cím H2",
	"components.Wysiwyg.selectOptions.H3": "Cím H3",
	"components.Wysiwyg.selectOptions.H4": "Cím H4",
	"components.Wysiwyg.selectOptions.H5": "Cím H5",
	"components.Wysiwyg.selectOptions.H6": "Cím H6",
	"components.Wysiwyg.selectOptions.title": "Cím hozzádása",
	"components.WysiwygBottomControls.charactersIndicators": "karakterek",
	"components.WysiwygBottomControls.fullscreen": "Kinyit",
	"components.WysiwygBottomControls.uploadFiles": "Fájlok behúzása, beillesztése a vágólapról vagy {browse}.",
	"components.WysiwygBottomControls.uploadFiles.browse": "Válassza ki őket",
	"components.pagination.go-to": "Ugrás a(z) {page} oldalra",
	"components.pagination.go-to-next": "Ugrás a következő oldalra",
	"components.pagination.go-to-previous": "Ugrás az előző oldalra",
	"components.pagination.remaining-links": "És {number} további link",
	"components.popUpWarning.button.cancel": "Mégsem",
	"components.popUpWarning.button.confirm": "Megerősítés",
	"components.popUpWarning.message": "Biztosan törölni szeretné?",
	"components.popUpWarning.title": "Erősítse meg",
	"form.button.continue": "Folytatás",
	"form.button.done": "Kész",
	"global.search": "Keresés",
	"global.actions": "Műveletek",
	"global.back": "Vissza",
	"global.cancel": "Mégsem",
	"global.change-password": "Jelszó megváltoztatása",
	"global.content-manager": "Tartalomkezelő",
	"global.continue": "Folytatás",
	"global.delete": "Törlés",
	"global.delete-target": "{target} törlése",
	"global.description": "Leírás",
	"global.details": "Részletek",
	"global.disabled": "Letiltva",
	"global.documentation": "Dokumentáció",
	"global.enabled": "Engedélyezve",
	"global.finish": "Befejezés",
	"global.marketplace": "Piactér",
	"global.name": "Név",
	"global.none": "Nincs",
	"global.password": "Jelszó",
	"global.plugins": "Bővítmények",
	"global.plugins.content-manager": "Tartalomkezelő",
	"global.plugins.content-manager.description": "Gyors módja annak, hogy megtekintse, szerkesztse és törölje az adatokat az adatbázisában.",
	"global.plugins.content-type-builder": "Tartalomtípus-építő",
	"global.plugins.content-type-builder.description": "Modellezze az API adatszerkezetét. Hozzon létre új mezőket és relationokat csak egy perc alatt. A fájlok automatikusan létrehozódnak és frissülnek a projektjében.",
	"global.plugins.email": "E-mail",
	"global.plugins.email.description": "Állítsa be az alkalmazást, hogy e-maileket küldjön.",
	"global.plugins.upload": "Médiatár",
	"global.plugins.upload.description": "Médiafájlok kezelése.",
	"global.plugins.graphql": "GraphQL",
	"global.plugins.graphql.description": "GraphQL végpont hozzáadása alapértelmezett API metódusokkal.",
	"global.plugins.documentation": "Dokumentáció",
	"global.plugins.documentation.description": "OpenAPI Dokumentum létrehozása és API megjelenítése SWAGGER UI-val.",
	"global.plugins.i18n": "Nemzetköziítés",
	"global.plugins.i18n.description": "Ez a plugin lehetővé teszi különböző nyelveken történő tartalom létrehozását, olvasását és frissítését, tanto az Admin Panelból, mint az API-ból.",
	"global.plugins.sentry": "Sentry",
	"global.plugins.sentry.description": "Strapi hibaesemények küldése a Sentry-be.",
	"global.plugins.users-permissions": "Szerepek & Engedélyek",
	"global.plugins.users-permissions.description": "API védelme teljes hitelesítési folyamattal JWT alapján. Ez a plugin egyúttal olyan ACL stratégiát is tartalmaz, amely lehetővé teszi a felhasználói csoportok közötti engedélyek kezelését.",
	"global.profile": "Profil",
	"global.prompt.unsaved": "Biztos, hogy elhagyja ezt az oldalt? Az összes módosítása elveszik",
	"global.reset-password": "Jelszó visszaállítása",
	"global.roles": "Szerepek",
	"global.save": "Mentés",
	"global.see-more": "Továbbiak megtekintése",
	"global.select": "Kiválasztás",
	"global.select-all-entries": "Az összes bejegyzés kiválasztása",
	"global.settings": "Beállítások",
	"global.type": "Típus",
	"global.users": "Felhasználók",
	"notification.contentType.relations.conflict": "A tartalomtípusnak ellenkező kapcsolatai vannak",
	"notification.default.title": "Információ:",
	"notification.error": "Hiba lépett fel",
	"notification.error.layout": "Nem sikerült lekérni az elrendezést",
	"notification.form.error.fields": "Az űrlap kitöltése hibás",
	"notification.form.success.fields": "Változtatások elmentve",
	"notification.link-copied": "A link a vágólapra másolva",
	"notification.permission.not-allowed-read": "Ezt a dokumentumot nem tekintheti meg",
	"notification.success.delete": "Az elemet törölték",
	"notification.success.saved": "Mentve",
	"notification.success.title": "Sikeres:",
	"notification.success.apitokencreated": "API Token sikeresen létrehozva",
	"notification.success.apitokenedited": "API Token sikeresen szerkesztve",
	"notification.error.tokennamenotunique": "Név már hozzárendelve egy másik tokenhez",
	"notification.version.update.message": "Megjelent a Strapi új verziója!",
	"notification.warning.title": "Figyelmeztetés:",
	"notification.warning.404": "404 - Nem található",
	or: or,
	"request.error.model.unknown": "Ez a modell nem létezik",
	skipToContent: skipToContent,
	submit: submit
};

exports.Analytics = Analytics;
exports.Documentation = Documentation;
exports.Email = Email;
exports.Password = Password;
exports.Provider = Provider;
exports.ResetPasswordToken = ResetPasswordToken;
exports.Role = Role;
exports.Username = Username;
exports.Users = Users;
exports.anErrorOccurred = anErrorOccurred;
exports.clearLabel = clearLabel;
exports.dark = dark;
exports.default = hu;
exports.light = light;
exports.or = or;
exports.skipToContent = skipToContent;
exports.submit = submit;
//# sourceMappingURL=hu-VxIqV1X0.js.map
