'use strict';

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const sgMail = require('@sendgrid/mail');

// Cargar variables de entorno
dotenv.config();

// Configuración del correo de prueba
const TEST_EMAIL = '<EMAIL>'; // Ya configurado con tu correo
const SITE_URL = process.env.RESET_PASSWORD_URL || 'http://localhost:3000/auth/reset-password';
const FROM_EMAIL = process.env.SENDGRID_EMAIL || '<EMAIL>';
const SENDGRID_API_KEY = process.env.SENDGRID_API_KEY;

// Función para leer el archivo de plantilla
const readTemplate = (templateName) => {
  const templatePath = path.join(process.cwd(), 'config', 'email-templates', `${templateName}.html`);
  try {
    return fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error(`Error al leer la plantilla ${templateName}:`, error);
    return null;
  }
};

// Función para enviar un correo de prueba usando SendGrid directamente
const sendTestEmail = async () => {
  try {
    console.log(' Iniciando prueba de envío de correo...');
    
    // Verificar que el correo de prueba esté configurado
    if (!TEST_EMAIL || TEST_EMAIL === '<EMAIL>') {
      console.error(' ERROR: Por favor configura un correo de prueba válido en el script.');
      process.exit(1);
    }

    // Verificar que la API key de SendGrid esté configurada
    if (!SENDGRID_API_KEY) {
      console.error(' ERROR: La API key de SendGrid no está configurada. Por favor, configura la variable de entorno SENDGRID_API_KEY.');
      process.exit(1);
    }

    // Configurar SendGrid
    sgMail.setApiKey(SENDGRID_API_KEY);
    
    // Datos de prueba para la plantilla
    const testData = {
      user: {
        username: 'Usuario de Prueba',
        email: TEST_EMAIL
      },
      URL: SITE_URL,
      TOKEN: 'test-token-1234567890',
      CODE: 'test-code-1234567890'
    };
    
    // Leer la plantilla y reemplazar las variables
    const template = readTemplate('reset-password');
    if (!template) {
      console.error(' ERROR: No se pudo leer la plantilla de correo.');
      process.exit(1);
    }
    
    // Reemplazar las variables en la plantilla
    const htmlContent = template
      .replace(/<%= user\.username %>/g, testData.user.username)
      .replace(/<%= URL %>\?code=<%= TOKEN %>/g, `${testData.URL}?code=${testData.TOKEN}`)
      .replace(/<%= URL %>/g, testData.URL)
      .replace(/<%= TOKEN %>/g, testData.TOKEN)
      .replace(/<%= CODE %>/g, testData.CODE);
    
    // Configurar el mensaje
    const msg = {
      to: TEST_EMAIL,
      from: FROM_EMAIL,
      subject: 'Prueba de Plantilla - Restablecimiento de Contraseña',
      text: `Hola ${testData.user.username}, para restablecer tu contraseña, haz clic en este enlace: ${testData.URL}?code=${testData.TOKEN}`,
      html: htmlContent,
    };
    
    console.log(' Enviando correo de prueba a:', TEST_EMAIL);
    
    // Enviar el correo
    await sgMail.send(msg);
    
    console.log(' Correo de prueba enviado correctamente');
    console.log(' Verifica tu bandeja de entrada (y la carpeta de spam) para ver el correo de prueba');
    process.exit(0);
    
  } catch (error) {
    console.error(' Error al enviar el correo de prueba:', error);
    console.error(error.response ? error.response.body : error);
    process.exit(1);
  }
};

// Ejecutar la función principal
sendTestEmail();
