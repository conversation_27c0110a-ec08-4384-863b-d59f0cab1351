/**
 * pool-sanction controller
 */

import { factories } from "@strapi/strapi";
import { Context } from 'koa';

export default factories.createCoreController('api::pool-sanction.pool-sanction' as any, ({ strapi }) => ({
  async create(ctx: Context) {
    try {
      const { user } = ctx.state;
      const { userId, reason } = ctx.request.body.data;

      // Por defecto, la sanción dura 30 días
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);

      const entry = await strapi.entityService.create('api::pool-sanction.pool-sanction' as any, {
        data: {
          user: userId,
          reason,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          status: 'active',
          createdBy: user.id,
        },
      });

      return ctx.send(entry);
    } catch (error) {
      console.error("Error al crear sanción:", error);
      return ctx.badRequest({
        error: error.message + " Error al crear sanción",
      });
    }
  },

  async findActive(ctx: Context) {
    try {
      const entries = await strapi.db.query('api::pool-sanction.pool-sanction' as any).findMany({
        where: {
          status: 'active',
          endDate: {
            $gt: new Date().toISOString(),
          },
        },
        populate: {
          user: {
            fields: ["firstName", "lastName", "id", "address"],
          },
          createdBy: {
            fields: ["firstName", "lastName", "id"],
          },
        },
      });

      // Transformar los datos al formato esperado por el frontend
      const processedEntries = entries.map((entry) => ({
        id: entry.id,
        reason: entry.reason,
        startDate: entry.startDate,
        endDate: entry.endDate,
        userName: `${entry.user?.firstName} ${entry.user?.lastName}`,
        userAddress: entry.user?.address,
        userId: entry.user?.id,
        createdByName: `${entry.createdBy?.firstName} ${entry.createdBy?.lastName}`,
        createdById: entry.createdBy?.id,
      }));

      return ctx.send(processedEntries);
    } catch (error) {
      console.error("Error en findActive:", error);
      ctx.throw(500, error);
    }
  },

  async complete(ctx: Context) {
    try {
      const { id } = ctx.params;
      const entry = await strapi.entityService.update('api::pool-sanction.pool-sanction' as any, id, {
        data: {
          status: 'completed',
          endDate: new Date().toISOString(),
        },
      });

      return ctx.send(entry);
    } catch (error) {
      console.error("Error al completar sanción:", error);
      return ctx.badRequest({
        error: error.message + " Error al completar sanción",
      });
    }
  },
}));
