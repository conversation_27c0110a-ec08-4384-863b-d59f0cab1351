import type { Strapi } from "@strapi/strapi";
import fs from "fs";
import path from "path";

// Variable para controlar si el script se ejecuta automáticamente al iniciar Strapi
const SKIP_AUTO_EXECUTION = true; // Cambia a false si quieres que se ejecute siempre

// Función para forzar la salida de logs
const log = (...args: any[]) => {
  const message = args
    .map((arg) =>
      typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
    )
    .join(" ");
  process.stdout.write(`[${new Date().toISOString()}] ${message}\n`);
};

// Rutas de los archivos de plantilla
const TEMPLATES_DIR = path.join(process.cwd(), "config", "email-templates");
const RESET_PASSWORD_PATH = path.join(TEMPLATES_DIR, "reset-password.html");
const EMAIL_CONFIRMATION_PATH = path.join(
  TEMPLATES_DIR,
  "email-confirmation.html"
);

// Plantilla de restablecimiento de contraseña
const RESET_PASSWORD_HTML = `<!DOCTYPE html>
<html>
<head>
  <title>Restablecer contraseña - CCPALCAN</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .header img { max-width: 200px; }
    .button {
      background: #2563eb;
      color: white !important;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      display: inline-block;
      margin: 10px 0;
    }
    .footer {
      margin-top: 30px;
      font-size: 12px;
      color: #666;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="https://res.cloudinary.com/dhcpss5pg/image/upload/v1748288029/ydeferxz12snop3qu5zu.png" alt="CCPALCAN Logo">
    <h2>Restablecer contraseña</h2>
  </div>
  
  <p>Hola <%= user.username %>,</p>
  <p>Hemos recibido una solicitud para restablecer la contraseña de tu cuenta en <strong>CCPALCAN</strong>.</p>
  <p>Haz clic en el siguiente botón para crear una nueva contraseña de manera segura:</p>
  
  <div style="text-align: center; margin: 25px 0;">
    <a href="<%= URL %>?code=<%= TOKEN %>" class="button">Restablecer contraseña</a>
  </div>
  
  <p>Si no solicitaste este cambio, puedes ignorar este mensaje. Tu contraseña actual permanecerá sin modificaciones.</p>
  
  <p>Si tienes dudas o necesitas asistencia, por favor comunícate con nuestro equipo de soporte.</p>
  
  <div class="footer">
    <p>Este es un correo automático, por favor no respondas a este mensaje.</p>
    <p>© ${new Date().getFullYear()} CCPALCAN - Todos los derechos reservados</p>
  </div>
</body>
</html>`;

// Plantilla de confirmación de correo
const EMAIL_CONFIRMATION_HTML = `<!DOCTYPE html>
<html>
<head>
  <title>Confirma tu correo - CCPALCAN</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .header img { max-width: 200px; }
    .button {
      background: #10b981;
      color: white !important;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      display: inline-block;
      margin: 10px 0;
    }
    .footer {
      margin-top: 30px;
      font-size: 12px;
      color: #666;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="https://res.cloudinary.com/dhcpss5pg/image/upload/v1748288029/ydeferxz12snop3qu5zu.png" alt="CCPALCAN Logo">
    <h2>Confirma tu correo electrónico</h2>
  </div>
  
  <p>Hola <%= user.username %>,</p>
  <p>¡Gracias por registrarte en <strong>CCPALCAN</strong>! Para completar tu registro y activar tu cuenta, por favor confirma tu dirección de correo electrónico haciendo clic en el siguiente botón:</p>
  
  <div style="text-align: center; margin: 25px 0;">
    <a href="<%= URL %>?confirmation=<%= CODE %>" class="button">Confirmar mi correo</a>
  </div>
  
  <p>Si no creaste esta cuenta, puedes ignorar este mensaje o contactar a nuestro equipo de soporte si crees que se trata de un error.</p>
  
  <p>¡Esperamos que disfrutes de todos los beneficios de ser parte de nuestra comunidad!</p>
  
  <div class="footer">
    <p>Este es un correo automático, por favor no respondas a este mensaje.</p>
    <p>© ${new Date().getFullYear()} CCPALCAN - Todos los derechos reservados</p>
  </div>
</body>
</html>`;

// Función principal para configurar las plantillas
const setupEmailTemplates = async () => {
  try {
    log("🚀 INICIANDO: Configuración de plantillas de correo");

    // Crear directorio de plantillas si no existe
    if (!fs.existsSync(TEMPLATES_DIR)) {
      log("📂 Creando directorio de plantillas...");
      fs.mkdirSync(TEMPLATES_DIR, { recursive: true });
    }

    // Plantilla de restablecimiento de contraseña
    log("\n📝 Creando plantilla de restablecimiento de contraseña...");
    fs.writeFileSync(RESET_PASSWORD_PATH, RESET_PASSWORD_HTML);
    log(`✅ Plantilla creada en: ${RESET_PASSWORD_PATH}`);

    // Plantilla de confirmación de correo
    log("\n📝 Creando plantilla de confirmación de correo...");
    fs.writeFileSync(EMAIL_CONFIRMATION_PATH, EMAIL_CONFIRMATION_HTML);
    log(`✅ Plantilla creada en: ${EMAIL_CONFIRMATION_PATH}`);

    log("\n✨ Todas las plantillas se han creado correctamente");
    log("\n📋 Siguientes pasos para configurar el envío de correos:");
    log("1. Configura el proveedor de correo en tu archivo de configuración:");
    log("   config/plugins.js o config/env/production/plugins.js");
    log("\nEjemplo de configuración para Sendmail:");
    log(
      JSON.stringify(
        {
          email: {
            config: {
              provider: "sendmail",
              settings: {
                defaultFrom: "<EMAIL>",
                defaultReplyTo: "<EMAIL>",
              },
            },
          },
        },
        null,
        2
      )
    );

    log("\n2. Para usar las plantillas en tu código:");
    log("   await strapi.plugins.email.service('email').sendTemplatedEmail({");
    log("     to: user.email,");
    log('     from: "<EMAIL>",');
    log(
      '     template: path.resolve(__dirname, "../../config/email-templates/reset-password.html"),'
    );
    log("     data: {");
    log("       user: { username: user.username },");
    log("       URL: resetUrl,");
    log("       TOKEN: resetToken");
    log("     }");
    log("   });");
  } catch (error) {
    log("❌ ERROR: Fallo en la configuración de plantillas");
    log("🔧 Detalles del error:", error.message);
    log("📌 Stack:", error.stack);
    throw error;
  }
};

// Determinar si el script se está ejecutando como parte del proceso de seed
const isRunningAsSeed = () => {
  // Verificar si se está ejecutando como script independiente
  if (require.main === module) {
    return true;
  }

  // Verificar si se está ejecutando como parte del proceso de seed
  const stackTrace = new Error().stack || "";
  return (
    stackTrace.includes("seed-email-templates") ||
    process.argv.some((arg) => arg.includes("setup-email-templates"))
  );
};

// Exportar la función para ser usada por Strapi
export default async ({ strapi }: { strapi: Strapi }) => {
  // Solo ejecutar cuando se llama explícitamente como script o durante el seed
  // o cuando SKIP_AUTO_EXECUTION está configurado como false
  if (!SKIP_AUTO_EXECUTION || isRunningAsSeed()) {
    await setupEmailTemplates();
  } else {
    // Si se está ejecutando como parte del ciclo de vida de Strapi, no hacer nada
    console.log(
      "[Email Templates] Omitiendo creación automática de plantillas durante el inicio del servidor"
    );
  }
};
