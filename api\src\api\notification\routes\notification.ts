/**
 * notification router
 */

// Exportar rutas personalizadas además de las rutas CRUD estándar
export default {
  routes: [
    // Rutas CRUD estándar
    {
      method: "GET",
      path: "/notifications",
      handler: "notification.find",
      config: {
        policies: [],
      },
    },
    {
      method: "GET",
      path: "/notifications/:id",
      handler: "notification.findOne",
      config: {
        policies: [],
      },
    },
    {
      method: "POST",
      path: "/notifications",
      handler: "notification.create",
      config: {
        policies: [],
      },
    },
    {
      method: "PUT",
      path: "/notifications/:id",
      handler: "notification.update",
      config: {
        policies: [],
      },
    },
    {
      method: "DELETE",
      path: "/notifications/:id",
      handler: "notification.delete",
      config: {
        policies: [],
      },
    },

    // Rutas personalizadas
    {
      method: "GET",
      path: "/notifications/user/:userId",
      handler: "notification.findByUser",
      config: {
        policies: [],
      },
    },
    {
      method: "PUT",
      path: "/notifications/:id/read",
      handler: "notification.markAsRead",
      config: {
        policies: [],
      },
    },
    {
      method: "PUT",
      path: "/notifications/user/:userId/read-all",
      handler: "notification.markAllAsRead",
      config: {
        policies: [],
      },
    },
    {
      method: "POST",
      path: "/notifications/send/:userId",
      handler: "notification.sendToUser",
      config: {
        policies: [],
      },
    },

    // Ruta SSE (Server-Sent Events)
    {
      method: "GET",
      path: "/notifications/sse/:userId",
      handler: "sse.connect",
      config: {
        auth: false, // Permitir conexiones sin autenticación para SSE
        policies: [],
        middlewares: [],
      },
    },

    // Rutas WebSocket
    {
      method: "POST",
      path: "/notifications/websocket/send/:userId",
      handler: "websocket.sendToUser",
      config: {
        policies: [],
      },
    },
    {
      method: "POST",
      path: "/notifications/websocket/broadcast",
      handler: "websocket.broadcast",
      config: {
        policies: [],
      },
    },
    {
      method: "GET",
      path: "/notifications/websocket/stats",
      handler: "websocket.getStats",
      config: {
        policies: [],
      },
    },
    {
      method: "POST",
      path: "/notifications/websocket/test/:userId",
      handler: "websocket.test",
      config: {
        policies: [],
      },
    },
  ],
};
