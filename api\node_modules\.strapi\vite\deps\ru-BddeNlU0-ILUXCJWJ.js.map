{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/ru-BddeNlU0.mjs"], "sourcesContent": ["const ru = {\n  \"coming-soon\": \"This content is currently under construction and will be back in a few weeks!\",\n  \"components.Row.open\": \"Открыть\",\n  \"components.Row.regenerate\": \"Сгенерировать\",\n  \"containers.HomePage.Block.title\": \"Версии\",\n  \"containers.HomePage.Button.update\": \"Обновить\",\n  \"containers.HomePage.PluginHeader.title\": \"Документация — Настройки\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"Я понимаю\",\n  \"containers.HomePage.PopUpWarning.message\": \"Вы уверены что хотите удалить эту версию?\",\n  \"containers.HomePage.copied\": \"Токен скопирован в буфер обмена\",\n  \"containers.HomePage.form.jwtToken\": \"Получите ваш JWT токен\",\n  \"containers.HomePage.form.jwtToken.description\": \"Скопируйте этот токен и используйте его в swagger для выполнения запросов\",\n  \"containers.HomePage.form.password\": \"Пароль\",\n  \"containers.HomePage.form.password.inputDescription\": \"Установите пароль для доступа к документации\",\n  \"containers.HomePage.form.restrictedAccess\": \"Ограниченный доступ\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"Сделайте вашу документацию приватной. По умолчанию доступ открыт\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"Показать сгенерированные файлы\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"Полезно, если вы хотите изменить сгенерированную документацию. \\nПлагин разделяет файлы на модели и плагины. \\nПри включенной опции вам будет проще кастомизировать документацию\",\n  \"error.deleteDoc.versionMissing\": \"Версии, которую вы пытаетесь удалить, не существует.\",\n  \"error.noVersion\": \"Необходимо указать версию\",\n  \"error.regenerateDoc\": \"При генерации документации возникла ошибка\",\n  \"error.regenerateDoc.versionMissing\": \"Версии, которую вы пытаетесь сгенерировать, не существует\",\n  \"notification.delete.success\": \"Документация удалена\",\n  \"notification.generate.success\": \"Документация сгенерирована\",\n  \"notification.update.success\": \"Настройки успешно обновлены\",\n  \"pages.PluginPage.Button.open\": \"Открыть документацию\",\n  \"pages.PluginPage.header.description\": \"Возможности плагина документации\",\n  \"pages.PluginPage.table.generated\": \"Последняя генерация\",\n  \"pages.PluginPage.table.icon.regenerate\": \"Сгерерировать {target}\",\n  \"pages.PluginPage.table.icon.show\": \"Открыть {target}\",\n  \"pages.PluginPage.table.version\": \"Версия\",\n  \"pages.SettingsPage.header.description\": \"Настройки плагина документации\",\n  \"pages.SettingsPage.header.save\": \"Сохранить\",\n  \"pages.SettingsPage.toggle.hint\": \"Сделать документацию приватной\",\n  \"pages.SettingsPage.toggle.label\": \"Доступ ограничен\",\n  \"plugin.description.long\": \"Создайте OpenAPI документацию для удобной работы с вашим API через SWAGGER UI.\",\n  \"plugin.description.short\": \"Создайте OpenAPI документацию для удобной работы с вашим API через SWAGGER UI.\",\n  \"plugin.name\": \"Документация\"\n};\nexport {\n  ru as default\n};\n//# sourceMappingURL=ru-BddeNlU0.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}