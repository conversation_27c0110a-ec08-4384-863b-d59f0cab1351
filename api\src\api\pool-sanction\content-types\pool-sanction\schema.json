{"kind": "collectionType", "collectionName": "pool_sanctions", "info": {"singularName": "pool-sanction", "pluralName": "pool-sanctions", "displayName": "Pool Sanction", "description": "Records of pool access sanctions for residents"}, "options": {"draftAndPublish": false}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "pool_sanctions"}, "reason": {"type": "text", "required": true}, "startDate": {"type": "datetime", "required": true}, "endDate": {"type": "datetime", "required": true}, "status": {"type": "enumeration", "enum": ["active", "completed"], "default": "active"}, "createdBy": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}}}