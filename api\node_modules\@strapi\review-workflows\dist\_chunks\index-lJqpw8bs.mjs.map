{"version": 3, "file": "index-lJqpw8bs.mjs", "sources": ["../../admin/src/constants.ts", "../../admin/src/modules/hooks.ts", "../../admin/src/services/api.ts", "../../admin/src/services/content-manager.ts", "../../admin/src/utils/api.ts", "../../admin/src/utils/users.ts", "../../admin/src/routes/content-manager/[model]/[id]/components/constants.ts", "../../admin/src/routes/content-manager/[model]/[id]/components/AssigneeSelect.tsx", "../../admin/src/assets/balloon.png", "../../admin/src/components/LimitsModal.tsx", "../../admin/src/utils/colors.ts", "../../admin/src/routes/content-manager/[model]/[id]/components/StageSelect.tsx", "../../admin/src/routes/content-manager/[model]/[id]/components/Panel.tsx", "../../admin/src/services/settings.ts", "../../admin/src/routes/content-manager/[model]/components/TableColumns.tsx", "../../admin/src/routes/content-manager/[model]/constants.tsx", "../../admin/src/utils/cm-hooks.ts", "../../admin/src/utils/translations.ts", "../../admin/src/index.ts"], "sourcesContent": ["import { lightTheme } from '@strapi/design-system';\n\nconst PLUGIN_ID = 'review-workflows';\n/**\n * The name of the feature in the license.\n */\nconst FEATURE_ID = 'review-workflows';\n\nexport const CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME = 'numberOfWorkflows';\nexport const CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME = 'stagesPerWorkflow';\n\nexport const STAGE_COLOR_DEFAULT = lightTheme.colors.primary600;\n\nexport { FEATURE_ID, PLUGIN_ID };\n", "import { Dispatch } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\n\nimport type { Store } from '@strapi/admin/strapi-admin';\n\ntype RootState = ReturnType<Store['getState']>;\n\nconst useTypedDispatch: () => Dispatch = useDispatch;\nconst useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;\n\nexport { useTypedSelector, useTypedDispatch };\n", "import { adminApi } from '@strapi/admin/strapi-admin';\n\nconst reviewWorkflowsApi = adminApi.enhanceEndpoints({\n  addTagTypes: ['ReviewWorkflow', 'ReviewWorkflowStages', 'Document', 'ContentTypeSettings'],\n});\n\nexport { reviewWorkflowsApi };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport { reviewWorkflowsApi } from './api';\n\nimport type {\n  GetStages,\n  UpdateStage,\n  UpdateAssignee,\n} from '../../../shared/contracts/review-workflows';\nimport type { Contracts } from '@strapi/content-manager/_internal/shared';\n\ntype ContentType = Contracts.ContentTypes.ContentType;\ninterface ContentTypes {\n  collectionType: ContentType[];\n  singleType: ContentType[];\n}\n\nconst SINGLE_TYPES = 'single-types';\n\nconst contentManagerApi = reviewWorkflowsApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getStages: builder.query<\n      {\n        stages: NonNullable<GetStages.Response['data']>;\n        meta: NonNullable<GetStages.Response['meta']>;\n      },\n      GetStages.Params & { slug: string; params?: object }\n    >({\n      query: ({ model, slug, id, params }) => ({\n        url: `/review-workflows/content-manager/${slug}/${model}/${id}/stages`,\n        method: 'GET',\n        config: {\n          params,\n        },\n      }),\n      transformResponse: (res: GetStages.Response) => {\n        return {\n          meta: res.meta ?? { workflowCount: 0 },\n          stages: res.data ?? [],\n        };\n      },\n      providesTags: ['ReviewWorkflowStages'],\n    }),\n    updateStage: builder.mutation<\n      UpdateStage.Response['data'],\n      UpdateStage.Request['body'] & UpdateStage.Params & { slug: string; params?: object }\n    >({\n      query: ({ model, slug, id, params, ...data }) => ({\n        url: `/review-workflows/content-manager/${slug}/${model}/${id}/stage`,\n        method: 'PUT',\n        data,\n        config: {\n          params,\n        },\n      }),\n      transformResponse: (res: UpdateStage.Response) => res.data,\n      invalidatesTags: (_result, _error, { slug, id, model }) => {\n        return [\n          {\n            type: 'Document',\n            id: slug !== SINGLE_TYPES ? `${model}_${id}` : model,\n          },\n          { type: 'Document', id: `${model}_LIST` },\n          'ReviewWorkflowStages',\n        ];\n      },\n    }),\n    updateAssignee: builder.mutation<\n      UpdateAssignee.Response['data'],\n      UpdateAssignee.Request['body'] & UpdateAssignee.Params & { slug: string; params?: object }\n    >({\n      query: ({ model, slug, id, params, ...data }) => ({\n        url: `/review-workflows/content-manager/${slug}/${model}/${id}/assignee`,\n        method: 'PUT',\n        data,\n        config: {\n          params,\n        },\n      }),\n      transformResponse: (res: UpdateAssignee.Response) => res.data,\n      invalidatesTags: (_result, _error, { slug, id, model }) => {\n        return [\n          {\n            type: 'Document',\n            id: slug !== SINGLE_TYPES ? `${model}_${id}` : model,\n          },\n          { type: 'Document', id: `${model}_LIST` },\n        ];\n      },\n    }),\n    getContentTypes: builder.query<ContentTypes, void>({\n      query: () => ({\n        url: `/content-manager/content-types`,\n        method: 'GET',\n      }),\n      transformResponse: (res: { data: Contracts.ContentTypes.ContentType[] }) => {\n        return res.data.reduce<ContentTypes>(\n          (acc, curr) => {\n            if (curr.isDisplayed) {\n              acc[curr.kind].push(curr);\n            }\n            return acc;\n          },\n          {\n            collectionType: [],\n            singleType: [],\n          }\n        );\n      },\n    }),\n  }),\n  overrideExisting: true,\n});\n\nconst {\n  useGetStagesQuery,\n  useUpdateStageMutation,\n  useUpdateAssigneeMutation,\n  useGetContentTypesQuery,\n} = contentManagerApi;\n\nexport {\n  useGetStagesQuery,\n  useUpdateStageMutation,\n  useUpdateAssigneeMutation,\n  useGetContentTypesQuery,\n};\nexport type { ContentTypes, ContentType };\n", "import { SerializedError } from '@reduxjs/toolkit';\nimport { type UnknownApiError, type ApiError } from '@strapi/admin/strapi-admin';\n\nexport type BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\nconst isBaseQueryError = (error: BaseQueryError): error is ApiError | UnknownApiError => {\n  return error.name !== undefined;\n};\n\ninterface Query {\n  plugins?: Record<string, unknown>;\n  _q?: string;\n  [key: string]: any;\n}\n\n/**\n * This type extracts the plugin options from the query\n * and appends them to the root of the query\n */\ntype TransformedQuery<TQuery extends Query> = Omit<TQuery, 'plugins'> & {\n  [key: string]: string;\n};\n\n/**\n * @description\n * Creates a valid query params object for get requests\n * ie. plugins[18n][locale]=en becomes locale=en\n */\nconst buildValidParams = <TQuery extends Query>(query: TQuery): TransformedQuery<TQuery> => {\n  if (!query) return query;\n\n  // Extract pluginOptions from the query, they shouldn't be part of the URL\n  const { plugins: _, ...validQueryParams } = {\n    ...query,\n    ...Object.values(query?.plugins ?? {}).reduce<Record<string, string>>(\n      (acc, current) => Object.assign(acc, current),\n      {}\n    ),\n  };\n\n  if ('_q' in validQueryParams) {\n    // Encode the search query here since the paramsSerializer will not\n    // @ts-expect-error – TODO: fix this type error\n    validQueryParams._q = encodeURIComponent(validQueryParams._q);\n  }\n\n  return validQueryParams;\n};\n\nexport { isBaseQueryError, buildValidParams };\n", "import type { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\n\n/**\n * Retrieves the display name of an admin panel user\n */\nconst getDisplayName = ({\n  firstname,\n  lastname,\n  username,\n  email,\n}: Partial<\n  Pick<SanitizedAdminUser, 'firstname' | 'lastname' | 'username' | 'email'>\n> = {}): string => {\n  if (username) {\n    return username;\n  }\n\n  // firstname is not required if the user is created with a username\n  if (firstname) {\n    return `${firstname} ${lastname ?? ''}`.trim();\n  }\n\n  return email ?? '';\n};\n\nexport { getDisplayName };\n", "export const STAGE_ATTRIBUTE_NAME = 'strapi_stage';\nexport const ASSIGNEE_ATTRIBUTE_NAME = 'strapi_assignee';\n", "import * as React from 'react';\n\nimport {\n  useNotification,\n  useAPIError<PERSON><PERSON><PERSON>,\n  useRBAC,\n  useAdminUsers,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { unstable_useDocument } from '@strapi/content-manager/strapi-admin';\nimport { Combobox, ComboboxOption, Field } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { useTypedSelector } from '../../../../../modules/hooks';\nimport { useUpdateAssigneeMutation } from '../../../../../services/content-manager';\nimport { buildValidParams } from '../../../../../utils/api';\nimport { getDisplayName } from '../../../../../utils/users';\n\nimport { ASSIGNEE_ATTRIBUTE_NAME } from './constants';\n\nconst AssigneeSelect = () => {\n  const {\n    collectionType = '',\n    id,\n    slug: model = '',\n  } = useParams<{ collectionType: string; slug: string; id: string }>();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const {\n    allowedActions: { canRead },\n    isLoading: isLoadingPermissions,\n  } = useRBAC(permissions.settings?.users);\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const { data, isLoading, isError } = useAdminUsers(undefined, {\n    skip: isLoadingPermissions || !canRead,\n  });\n  const { document } = unstable_useDocument(\n    {\n      collectionType,\n      model,\n      documentId: id,\n    },\n    {\n      skip: !id && collectionType !== 'single-types',\n    }\n  );\n\n  const users = data?.users || [];\n\n  const currentAssignee = document ? document[ASSIGNEE_ATTRIBUTE_NAME] : null;\n\n  const [updateAssignee, { error, isLoading: isMutating }] = useUpdateAssigneeMutation();\n\n  if (!collectionType || !model || !document?.documentId) {\n    return null;\n  }\n\n  const handleChange = async (assigneeId: string | null) => {\n    // a simple way to avoid erroneous updates\n    if (currentAssignee?.id === assigneeId) {\n      return;\n    }\n\n    const res = await updateAssignee({\n      slug: collectionType,\n      model,\n      id: document.documentId,\n      params,\n      data: {\n        id: assigneeId ? parseInt(assigneeId, 10) : null,\n      },\n    });\n\n    if ('data' in res) {\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: 'content-manager.reviewWorkflows.assignee.notification.saved',\n          defaultMessage: 'Assignee updated',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Field.Root\n      name={ASSIGNEE_ATTRIBUTE_NAME}\n      id={ASSIGNEE_ATTRIBUTE_NAME}\n      error={\n        ((isError &&\n          canRead &&\n          formatMessage({\n            id: 'content-manager.reviewWorkflows.assignee.error',\n            defaultMessage: 'An error occurred while fetching users',\n          })) ||\n          (error && formatAPIError(error))) ??\n        undefined\n      }\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'content-manager.reviewWorkflows.assignee.label',\n          defaultMessage: 'Assignee',\n        })}\n      </Field.Label>\n      <Combobox\n        clearLabel={formatMessage({\n          id: 'content-manager.reviewWorkflows.assignee.clear',\n          defaultMessage: 'Clear assignee',\n        })}\n        disabled={\n          (!isLoadingPermissions && !isLoading && users.length === 0) || !document.documentId\n        }\n        value={currentAssignee ? currentAssignee.id.toString() : null}\n        onChange={handleChange}\n        onClear={() => handleChange(null)}\n        placeholder={formatMessage({\n          id: 'content-manager.reviewWorkflows.assignee.placeholder',\n          defaultMessage: 'Select…',\n        })}\n        loading={isLoading || isLoadingPermissions || isMutating}\n      >\n        {users.map((user) => {\n          return (\n            <ComboboxOption\n              key={user.id}\n              value={user.id.toString()}\n              textValue={getDisplayName(user)}\n            >\n              {getDisplayName(user)}\n            </ComboboxOption>\n          );\n        })}\n      </Combobox>\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nexport { AssigneeSelect };\n", "export default \"data:image/png;base64,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*************************************************************/iTdqKJjlKWVIouF0Zb5g498jDU6sP7B7p/Bs39fuZziNHMCy6+yqK28c+jj141TIA57kh7QGx+BW13Wo9y9W080snXBOSTi2UWhH+d0FgxpRhL2GTYoStP5mdBKpsOMDUuXg/fUZhynnA2i6aqPCelmvV14gIE2UZgOkyuip/NpZDgqJbvnOK8X6t+aVBLnoIWxFboQBNpIS5lxFdC+bGISmtJcbmRYIcy1cul2Lhp8Yv5DbAczMlKdHN4DnvnX2PKrINpuFJf6WojJMB9DU79NiLoNe4WV/yBA8V+qcfdjHwwRTT77FCHVSvEJ9ZXTY8thWCVOAW+xK3nSPa+EW7Id4lSCrK2Vsfc9rPSxnyjS9d3Dw5Bo6PIw09FGFisU7EZzBoqHJXHsU2w/2GFaym4Kg0FNJCwaaCJtmk20FZrWophWRNVqhr2iOfmMKFUfitLl3GDj6JH75tCxXZ186CG2ImlxbFIbO+c1pdBQRtUzThoehHZy5vH5397Lcmx8rOE1qPS6o3sK3kR9HWFVArHrRwRaSacAdFL71HbFG7hiWZmQcVZrtXobyEKsyzbYXlfayQ4YjzNPO/vss0+DSH8HpJ8j0eogHSPQ7teoVHZXqwDaJPMS5Nvvbu+ohVmhxzAKRPGFlwwpxmLm4JiRaqd4k2m309DnTL0TRZnyxOLs588T6F/ZO5fQxsoojisIunVMQtMhUvGRFIdWTLqrFAQ3gmm6GQTB0lVFKCJTLfiAbETiMCK0ZhNqXQxuilURN8Igo8KQRaHYLJXZCFlWSq0UN/4f93i9Bt8tRM3JzXcf6ai1v575f+ec73yd1x4Cz2MUSM6P5A3uuOvAgXYCtLsk8ZuNGv8ICbkSdxTlOH27f1BzZCHOQu7rYyfBC+MRWeXoPIsWZHFmKNnhHZ/4uQV1keiHe44m0pDNFtE5AoKpFCgG3NwG//wEXkD6u+9eBNGPwkVDSHBqaHLhgx/vLChoR+PJrIfQFs5OfMM0iObqnPOBfoNlgFwF0J1OVRq7s4UzlAZSKVxDeLO8e2Wn1V7fXEbHshn653eapfE8QQWZCr/J7KR9Q9kR7QFVUyu9Zb1h740PR91HT91SXrMemsOAlI4Lqo7wNnLQOlkkjynUUXJWPG+Y9aHlJFG1S8dJPOcwqkRabJ8nyHTRzrDkzp8H0CL6xXfffJTpb64tfD6CcxfvoeIIrxxqg4M8+EOkOeGZaIeDZtUGrUJetTPsZBU22SnjDmjfKGt1940Own2VqQ+u1JbgmsHzsnl++J0DuuS0qtChHz/BEUpMv8KJKIl1sSr0xzMQPiofPQNL0c1iHGc/zSoSxTpk9kI8BLWcdFMy2j8/JRGlsEWwvTMliLw6Ptay77xyLd5jqOB6DtAtD42dWiZyFB5NIP3em6vwzVwti5MFxhuPe8vkwDnyKzrRQzvGQeec+mf5aLLsZ0R4gV4bjWpwTY2xJZ47NypAv/bB/vLS8l4fWgNNrGc6C6sPnzTx+ydILbyItONx7hasnlDxnUtyGXCraj7zx6O9vk/fUkkx4KGzIMegN310NHGUp/HbQSwtmk3CdWGWGgbfEydC7S0rjHEOKUJizIidtqAFzjggOfAG0UD6xacuvwYBjRemhveot90bHfeHDvUc6joEiNwzjozkQNI7jeB578FOVYmXjv331gJ5nsRusZO7S+0+NlLc3vu4hT2cd2vwzw8eNhWtkFdOcio+vN49Fq1g8Pwg5FjSyjWkNC5HxUmnbiY2C/DgXeZh6OhxB6STAEeUKpXc8E4e234sdjn0ahX/NUwKDLbcu7W0Ru19D6QLoBmvCarojfOJk37qsvrLMGR3UdB2ZsrQHxHXsFsO9UEj0HyJZssL0T0ZNEePjqqyLmzUgejeG1VKjZXWLvaA6XfJc39zcw3ueWrrzWvg2dt0RA7QQQycJSconu2v5YJ98Dfdv74RlLfkHnUfPXVL+MzymnrqAUUSd9bRmqprjD5hfI95YphPqnG8mN/yMnHQzqLYZduH2VFzOziQjDduPSssEGnYhpz0S6+w6A7kIgjNgv+p/Z3Ko1GSFNLDZ2dfPCkM7cxTJXXQhhr0Emhcv0KgmaypTk7u7rTR5He7392G4OhRcCxhFfrCa08clhyho3f2b6q+Of71IkjD/2JdsOMf/HJaaDKfxzGO+kOfvgWpWQUd9lvXVh3prFCZQiFNGxPRdktKLRSMvfeCkn/yNb7AfEfu2yaUQTbeEByQHRsbIBoDiD755tqqFmAhxUKg+70KsocxG9SqQ50SolGeJBO7POkmUPY9HXcZM8IqYh0YsO/EzpWdnc1ub3tze/vj/SUK6KUrLWwJu9V55Ql0fcbuof7GYbF9ClU/fXBkk8yxJLXQNcZ231r4oxaBI8lx6jbAa8ZHD16H2UcXpZu1uFniA4afanGsSaLJMw4aP/K6K15ojKKlKDdliENeGqOShJoTUk9DQwNoHhvnm82Ta9euMdbhgPQKgH7+eQvoJE8IiyieiPassGrFUYWUCMdsxnEA4touOthdWduZ4Q7dvb29ze1ef3tzebvbW1pa2mxv7jxLnhdeef3g7hJh/Vk4a0Ua8ytUSvlwwsoFuiIprZ9N2jXg7FUto0zhWRhRzbD7+5Zl22vB3abRyV6Y2oONcWJowh10VTol70BH1OhFDanqUTGyJolY4ELJFYbtGIbOTdg2CHXz8Ilr17R6Bc3uLs4A6HtsSUo85oQRvktdNE+iOoxaGlauXdnZX96D9Ths93oY9vp70M59uOYr++3ZVutlNPjtvHbkNrmxP120cYgWduIcrKZCmbIkIhx8ZNUdE+Wh1NC33XrrHf+Gu9+wQYp9+4eAR6xDrobZBTnrohewjI1F8I40h0e2eSOAiExzw2szwXokBPByDHiwgFQ1pBQcBZNM09zwENvQPqQYx+prnf5+JXIqLvBnRYevjPfFtG2Sj6p2haVVwfIOxMUmUN7uYegTaKC8vQ2qsQH5Xn95aaf1WWOxtfLAyludV8CzpgRkWaHHaOWOt3pgR9zO0NtwSa5dxcWTnpnuIUx933Er7Lbhv/tNoP+Zec9Zhu6SnyVTKpCYJRGNBSwkniYd7fR36A6eojyJp7w8NF70zXl5aBz00Ix1bAhpsn3u4B1sQ+uA9OMzUzEH1AkXellMC3POCgNmBp8np65cWSLHAnmP/vjrHlzyni6FMrRGHzwvd/f3Fxuf1emga5gPkudI9LnWFUhHKbRLvv2RW2MXoZ6tTjzRcMYbA5+6JHEImzXeKhv+u78O9GN/jmhVlEXZBr2xtHQJpaTercIzP08Pox2HaIimWb4my8gZEnGcJyCiHfBg6Z2iHKLZRBc2Pn/iwVWrjkrVSRQzrCK7iHmEiI7VKtYe1ZmezfZ17+s9EE2et4E1rLcNrdGDdl5ut+fr9W6j9UxrqtzpvHmEcF1EKeWT0z0ZXT7nSDye4EF838JWDjtKtHjt5d9D6aGHCdpTAPrv+GgJR+sOQpusGHXwzhoD8lkk3OUNoKi2cZfmW3gix/LNZLtAd30eIxQIdIemhAnWOKCrJTvA7kOvzMUehrSo59CjGKajL0eF7wtr/S+wXT3eX/OQaqZjpm8Gzst7vWVMA/vLrVa3i61mu4211tpUGQkVlCOpRsVzBU7vxpMpAS7sgXV49QOIpbkfIF+ebijYF3twFobRQ99Gam4f/rszAdpEK+NnR81IlFQGM4VNE+0cAz+Wi2KbjuBY7tlKxZNBRjngmLW+EJZjAA8HSXaYg9qDPvrOA+wV/jzk8qs3K6GXA+sQ0B6konHIEOSY6RFmOGYZYaZftm9ehs7YR8XGZrexWG98XJ+db6+31tZWauXOZfCsvg13WTd409yIW0baEJ+VwLJERewfxstkp+9oTJqI6+Hsy3E7hOq/4e5sgLbq8Jrm+AE6rgUlTaJLclB84B9/Xh5cniwGl4K4f6OUBns+kmpAvXFnfgJAK3JHw8mvu7BXOIT0vZ13axe1dDbksywEhx5grayZVoBusw+gExPGHuibt5cY2Jifr7e784v1jxutVrvdWkOP6gfmLp+gKlb/6RLBTGkma1/NqNdQwmVbVItZemndRcU4Dt4rRuIvGkIP/W+3+0/BWB9tNq2TCTRFR8lEM3wn/y2LGDRN115/x5GGUiWfQTPfOUY6CqrpEM321IhIF84dvg+iH7+5haYzEa8LlHUKvt0jetozwlqvb5ihm8OWl7alm/f34ZLb9XqjW19c7LafaTXW16Zq1alvFy6jlwxoTFai6JuRsEj32PA7CpCSmI8e4UzgQ3Lw913abHw4NfS/3e4/JaINbazV9+rYooku4Rk0NIjwnF8RjjwdlO4LONtRcyDLMIc+cMpN3IlgNI1IO2do8XHnuYPXH37o4tbVaavnQBh0p4WkotzrCmXlnZ6Ec6As29zs9TD9azfq9bpZXmy1u7Ot6+vP1ir3LtyYO6J/jo3PIwrNc1RswBuL7Ux2O5Fh+uYZlCbZjAGVGJLX1Lk4SqwMC9D3Zc8m2pVKCk1zk1j8vMbcxBE3Etd4p0vv3NGQo/sMiWG5Zg7e416ieiIHK+QnbOftp2EFxO8eXl149XHzbMeclRw8s6CDXcGkoWubqWcGyPv7vfqlRqPRbzTqjafrEM7txcVGd7FV/3hxbfH6y5Xpeye35o7c6ytdnyM8OcAsOTxzCGmcZP31QQmDn0qYAWj/aivaOYwa+l9uf4DsIME4dONzHMecMCmvIpE5jiv9KB2OjpIdEesNAGJLC5HLMwGRmWZfSHQU+J4ouI40J+HBA/fnCp8/uPraRSPsIT1suoSKFtBYR1Xv93uXLvUu9Rt8wZ5uNIg0hEZjfnGxXV+c7TZmZ7uLU89evYD1LJM35i7/MIZvplS0APYWuk5tJoV1WnflLGEQnvhoSZQo3uJcWTTjxkseRn05Tt+y1PrQFQe/Mne2uIw7zgy5pLBIqqO7fxFawwkWz/olJ717rKt2uMFKwvkvYEfMTuY2SjmxDKjpnNPI3YbPucLh66veU9ZIZ2v9OergLuAEurze7Xaf7pJkcHxJGBvmOqzVqrdbz+Dd+mwd/aa5IQDaGFz7oUlXbImBE+QDDtLrQI04lglds2ycQ3JwsHAuFgk2HjC3OqrlOAPLgGwLgLOsZ5DPXGOQjy4VoTTGi8otMNELncGZobr7m+CC0mixyYoHxvHUo7Tg1UvE2Gk4VpAiiIfRuhqFdxAcMgONsQAhrR6OUs9GOZUbYVz+DReN1VTtbldO2f65LpufxwClsdhqNerPzLYXsVXiBbS2074TK68d3A1kI72HI6I0LnWO+LLTQ8DUX5jkCGmK9fmmhAFfUpT8kkYbAX3a9gtUM3ymmsKnQd8dj+Lu2NN2HnY+mAqxTZZmhkpJ5O2aCWhs0mA1KdmR55CsZAkfrSpjRqNxwzmhRLSOiHggEf6+dsgK3ZwuwLLZS3t7t8mr7X6XJF+qE+ZevdEDzfOzs9evt2avP9Nqrbeevf7shZmblUqHXRsrK1crR81zjjrn8wnOWv8bretid460dyVJNf0sGC/q+xP61hzUYvxTnB+O2hicvqUCYoDjeJA+8jmuPaR3x04xFF3cEVUc9tGfajsdpsnAAkgHD1zhrVy3wxmJcI6L0KhUHXlmWHAOzUGqQ3nASR98/iCBfsQYi+o0eZg8dcOZznqjT5wv1ef5grVmZ/efmZ2Fc25dX1tZQfv+LWxHf2H6FTXqQJuZ1cOSW8c48jzu/UVTmGEcFehQYwZZBJ4J751p4A4SzLUBrh8dH7UxOH1LyU059jjooLNfOMj1cUlOCdrD7dDpjqCjY92sfoAwAk0GxAJJ0Y9aOIf8ED95+mgkWFTQYdURikODkcY1EuHPax14GuCIaxwy7e5WbXH+Nz9/6dL8/KyO+frs4jPPgOaVKQScK2huAM+8tTC9MAecJ1duvn30/kEyn82LRNVmYCS7rtzAqMHfUrzCZ5cC/ohBj2PwSp9Rb7szMTE6KCdSblNqs+eBE3200whGlRhAUI+VQLiIxkd5OTPBHIsMrTh47xNfENLMHBboltk0T5GOvItJcybaHtoGorUuC2aSnWiRWVar0L9Svv50owum+/TRDUnodYgNJE+wmwrbm8/dM9e5F2u959BAqTq5s45ujAdRUeXiK2fqi7qAaRFhupU33tIc5ruEC3cooahWarxk7RzNGocy9f1vqaP+bQ2dOTzEXfoauI9TDLJjzvjHVR2NQVhrSuQtWOit7br4FtIi2gKTlRDiIrqSxqqsHJw1O94x0hEJQw2pFT5f9W6c0hkejbcv8AbQK5912902SeZWta3Ws9AYM7VJFFRPQ5RgB4DHp9+oXriJPmCVyuRu7d0nm003d/LKV7tZ8pp3lgTPZUUCLI5jZ1JHNciu1lnJFxf9pz2/9P+D/1IcemjqobOEZm8Gac5+qqv4gOboXbSw06skxWgdHa6bg/LDER2IDCK9nfA2ztqfQuID00PwTB1tpgPosIN34KMNtHW0DU8i6AEJvQCAp7QLBdcO0inD+BlpRnpmbhqu+UZtslNF+4Ldqy9+x5yeIuSCT+tSRGF8C8kKlCIei3R9Tbp/rJHmDS8FNN6eYdD+U5PC4SkfvW8A5l/iC9MDXwra+EofvooLNURX3M5xDuUUOCEMHa2Xq4oDCkIMZmLK5Y73/iCHlwJ3OCYgOQJorzKUpaoDQFtppJEOWRRLA1lEoz1zJOy/6O14kR4acb1OdWWmvHIBOO/eeOHTppM98rAClucIMOtJdH7HESkU8Z/HF+tIe4QpWVpibN7qxcuHi7f8V2xogBasGpIjZVVvjb4Obgeu49Y8Sy7i0I/Mc58S5lSqJi2Voo+SwCUMEZb2E16oYyNjXTm5Z3dSgqExGCMdjtpxLbi9tO3gQQTvDC8sHLXOOoFXtZ3xunA98mmanzEMMlfp1GpXd1de3i3Xdleew98o/DsiUcshjjgmHbI9ynVj8N5geMrnkiDUVTHqqggNbS1ixv9Tve2Gph5aPjfgzXrdwVcW4bhNJ4U/FwHnOTjMDL1B5VFKKpWEOFE3vm5CYxxiUxZtTsGPcnkHO9SrQ7NCGpgW0nzLbO+8ruidVbPOBjs89vSCdnmjr9YrXRgOoFmLV57cevn6W2vguVyuPfnlpy7jjyS3BEdawm+ikyS47kWxIx66VhrFLOOx5Ydjz46U3Dn+n5Icw1IPbSazzPI9yHEMmfuMJz/2bD7xUVQaAtw6GmnDMVYq4SkB9wc0c+G/mJOUeF5A24Q3i6S5RgtkJ7V3G/TRGDJC+v1IGqZzwrRXNDww1qywkbQY9of4YBoRkCo+rE5enbm61nq5XL789osv/fDpudiUTiyntRn+DjGQ0nSj6NDNseOdp48lIe2/i2j6SCpbgI82rz91S8n1kL22DXLsI/0sgnYSxqpTsrZ0mR1OIrsk1THGn3qeOwf7r2SV9kQjcP89rPWn2oVWLLHmLqLSrlFK7FdAFwqcGkaXmYjhCWCM3hj5ojvoalTPf15XJvGq1dYQkJ6avHzyw6dfoj1/soGGJ3n6D3L4zvXOjkw7JIOXH2piKPgd6hhnccu4Qxq2kGQ8RjvJnoUJRw3phU8ZvHWfDj5nHfSx8l/xUyPURc2K/ExXIFqlSiUQbK8XEQFXeERbXV+7PlrMgGR1vsuxCTqcdJJZwdwwa3cePkwhnW4fpMEPOlPlqvYEUnQjtQp2jy1j25S3kPa+unV02HSbnGSRlVd0w4JYFyHxl9DKyVLJwthJb50wyiFLY/jvLH21yz0SYTaqtjt9y+rgLLBZWZ3hOj0H0see6Ls4o4RzEUAX3ZsxLI/9Zh3sMPbJDsG88dsdWwquThLQlhzQz1LSKrsLEQ3RESCHTYjoWCSrE42Sebq8MjNZVqd+HbZpDmicVK7NYMHVzZdOmtb6d5NmCQ71Q1LdUehjgR57HvthVOJZX5tnC5T4VcDJVOMkYaL43yhTeOpmJAedcDCc+Sx9OOChjy0QPbmz4LBWHle/sDxFJPFwQFquTvLUS77Nh1xeQWUc/shvQaW1szmuL8SbTPMYVB0TBRAdQONIYx2Vq6212gwizHDGk7JqVTBXL6DfTLl8Y/2Dd7UrrP2zI+EqtNLMUKAqCAPzOB7Nr6MWySdJk1jtjQNXkW30HJnk6+tGQJ+BBcQZggedcEaVDJp59o8oOWNIfoii1xDEvkJNsM5Wu1GBRzRg8Zd83p5RApVLtNxzhqUc+XwS6Yi6uwHVUThEsMPOmaMOAX3zOsqPXr6wu3vlyu7Lu7u13V2wDMDRdPTy0clLL3z4ItzzOfcTCf0jsezQuEcvBQxoRXAQnkTRlemmCfZoDmaaabzhqMTiMLYC+5dbMJwh2Fe/Se/gR8eOTIWKFsERAnBiLYGay77vtgtE8sGF0/6hC39ALKR5pZRG7KWlhtF5vHIFYZwLqDcGbMJEe5O3tPRu+gb3pC3DG8PKMtAsmH9ofvrpl598efJ5E/8u8Hw3/sXR6NfTPgfmzHSeHEdihNvjWmLrIVF3aomFHDD/QlB64ZaHHmPAHGNUnHQWNsinLwfv4iZ7st7gJBCiGQCqeL2osOu5IFaqE2Nk2cboBMfGqakFR8CjLIbzgxQZovuce4ipMpoLC3Mwao5cHlTnBLWddIp2geE7UuxXcrr4xveffPXC29hf8OTkxxPb5Ye++a4J/WOgD9ib7m7wTG71L1c2PgS9OzrH5v3oEWyR5PUqJW3URpRVQ4pzsTSuL7bMkF7WSaEQHhxGBf6nbgPsDoKdFdeDxnhd8hOydixyJSjveM/Rjjutq9TUUAEAmbA21LFej006eALHFCX8QO12STOJZ6FSju+cl68EzkH0arTYjW1XsMHh0dFTTx6tov8+l9I0aSerJyVKmSZ5bnIJIwX0uQLglIwPsew0T1EJbHjWCL0ZZSBM1v0CsI68Y60VLOG/qM9Vz+Fwh/6p/61ajmGxVCEPoP2nzPmUotWFnI7ckhKCWqgfANg83Xc9abPEQjSbFbjzh9akjm947Yq7Oopu8MwaabzAtBdm2Ue7/0xaq5R0n0lV9EOP3rPw5tHhAbTLef3hXOHg8CCXE9CffNKkJ9aE0CtnLBY8nVVAPJJEVlfOdIt534fWIr2hvXTlc7HoDCmp1m8tyB/tgnUG9rcYzvJMjEFwMfomMQJdjIm+UI2CSU0RKUKYNdRSw1hDnXwxz0m5EozeGW9HPfK6n4CTBsxuuMvafhwKdrBnR6aeVD7aNItr7P0998p332ENLkIhUio4k2uMzS8/aW5QpjMC43p8jpG6tBL2mgX+qlpTuA1jtFE10f47ytnt+Co1BzPW9s3MHzrcN/7/zBSeaT30ff/U2FDXhQpRW1cs4o07F11G7JbeyQtjDW6zKbXqlJmxdlXbuJLf/pN5URMRYZQsTaCHo3arAIsYJKmT2WG0kE6IPnn4+Z/L+2Go4Xj8o+++b0KG82sc8eMlfDy3vy1MQMjwl4y/LElfBSkPVYGacJykOEylc0juSSCI9Q16xzY/5QDVwfvw33lFO4rJP2QYmzX+xN6ZhMZSRWHYGRzB1m5zIyXaCwccIqgBERHElQMugsGtC3EAEX1GxbE3IiKKIgo+cSCIC0VNELMIOKFigigSk/RGfSvJJpJG1BjceP7/q2Mb56Ejon26uqq6utX37K9O//ecc8/9VfuXVU7vFNDfBL2ak8IQiC+uqrRJdEp7sGZwmwit4Cj2c4fn2kLMvwKYXOc9kxUWzzoaaZbpRG60mvSQrhscpI7WDlNA2iKaWtFoGPZab7630eITCb9kypGdjXlx3BLPtXeV1BiVj61D4vkn9KCXesHMkOBpkdCy1F4NmV/ZmO9QLLLjHN7/l20MdrZ89O/ybH88KqQtoOXAyHnHsfL3jZTIWkpJzFAaHlxVCpHZg6FXZfSibaphh1mGLdVStEA6W+B5ZFgvcJ9TwYG6T7SaotdFzw8++OlGo/XlhhQG73vxQ4mWw2N4GP92r0bHf9D5nRpG1LxoZKZCBbVwm4nuWk5YTuvTuPCw5L/R98kWJVz9fy4a9G8GWjwHmAElwNJspmprnizDIvNK7IrsCgio3r1ZzzWsmNbP18xn3IoUsrKxUq6PVYeFabQbHlpM1vMM+543a/77PvrT7zarVqPT2/hy6+v5DVlHHEdBk9Y/DKDNs6IsSnW3KOSIewiVT6x5W4NcYspxys0LoCBrUvV2XSxLqIOJD5wV4/4/LU7a2Xrov8ezIs6Vp3dXozobLQG0x0GBNYG8HOc3ACGO7dpr6eKInHQlN+74nD/koyDGCZIkl5lnRTjY+aVz4SRZ+vajpGGdXzn3q82NuAk6119++Refjy+9+s7XvdmZmd68qO5t9jrmeUT/CcNM+FDTDPKcaJtPc1yQSOucuzfjHIhshoilGGrf4pU3KQ0COv/T8tEdrYf+WzwHvqNyyWH8uIaO1qk0clvfXrEvMr2wyQxo+WtKPqi/qwDE2LKGthmg3IfuYIQ+tNqbEHMuvL9U1pHMYkFMwzMWOhrVcc93GxvhjHu71599ZvfS0udh4+Oxe/XVd+ZjqNhxBYdXk4sdMTspdqGL5s1AnpkWjQhh4nAILCqkY7MVKgkr/92rIqCFNf8EEZHY/o8eegA2IKD3ZpfP/fbZt1SllEroWksTvhPUakqoZ7quUfJinBHcQjUn0kCf00slmfXVO12nbJ2rpltk8ORFhTOdd7WAsolWRJm1hZifRYrlZJcq3bDV+fLGEBc3r1/08UufvXzmnbed/8SeqZnZqQC6d+WrncoJFcHsFQfR0TrqxkMlWE6jnIAyLmncmDqajDaLUPhzsgyw639FiUZSnqKFy/6vTZL9t9gvk/vLG8/8RHj/ffaV3NCXVsJPFYWgS2ThtJdKdkDaetLoIkoA3ARACkR3KnybAfFJptRyLnhdt4QgaIBzhDyMswvwIl8Sp1mGl+WkIvq4mzY6Gg92rrzrsuW3Vh45aWvz9PPOevmzs9R4Y3MqPLQDdi2Izq5NFhzIH7SEnjmr0HFoaNWpDNp1Asd59xatU9Eeja1AebzF7TCshx68QSdb7n7DkmzzLKI96GkHxP5JVUSOHqR6rYt89wyLZHqdPowAgaIdnslCebHez24HMhZojafvi5AYxDmadeNdqpaEovscBNISHmEE8Si+O/e1SAZGKnDzjbvuemT27C/23HnbhQ99tnv3E191Du9sXn9zTzyrNyStfHONWK82n1XQzYYVcY2zbrBizsO4QYG+QjzXzd15xl3gHzBu7wb3LSOPaq+hDdbg82fQJuIc2HjV988mmvibsmFFWV/56Uqiw+qxVBBsx5u+2erCkwBslbCwkx7xVK0fN45jTouOcWABZZrnt2ol3XRlR1gz9m4m7To8Vhniebjak17SEdCd2165a/G095bGl5aWplYuHh+f/Wqj1dl6Zn0K/3yEa1QT6tGm19K0iK97SoKtb8gsAAXjgiqxpLCq5nW2K9UJGKO8iXDK2nsNbbC2ze3mkX3Sm3uOnMEzRBd5H32XGXsNvrV5YE8wS3jncJCPZeyWyjaUtNblUeM7WK4Xp6WKlExzK15ZcnhCViMX6xSL2il9KLib9PmvTQ13XwPoO9fXHjjphD0zj+x5ZnZq5ovxb7/68vCN+996eH2p0zha6Uca6tXzuKP2uk5v/7jbESNbGaibzqIDjlkKOchOlVw8qCTix01ggSae22Vk6KEHbttY1ZbIsuNK7hNueMYOUCSjUkCDyCrRqbZOnTActccy7iiN9FtllIp+nBy6Y74iqkA4wTobbjISHZBZbgRtP4Q9msob6sQdpVVXqvYdOVMr3owF7zcE9MZ7iw+ccMJZT88888Tle8574tuz99x/75df3n/f6vpUx60SWnpYqnt4R/enfvEzGXwcr56yKhQWg11UEp+nZ0fqk9IXzvbgpegOrQLn6r/TaObfYnC8TU0A70/JtnERntMOseZoaIZz8dYoFs+NBhADRZbuyPh+ZSQgqGFTvZIr/9XxgFCtx4TaAFpPg27Y2CnBYkEtNy2qLardkcZzaeMkFjV8bUsR563rlh+47+NbTpu5/umbZ59+Zml89v6tF+999L3uwzMd/g08lctpMT2X2wmkM+vpHcLZmRMmAqMofErxM4ZK4Z9L+eGrhIKGYbuBA52WWG8HGbDZ8UBvYNj+bgo0KpYp5CjkVBDOhTZZJQuY4DlMeYs6GGAwJDtA2oKlqjUqWPnhYgoqh1Aq9tpNbS2dhB0J4UwC0MiwJaA7W19FNrB352Prj+3+cP2NpZlnZ6eefnp298qdXz311W1Xrj08oyJo7hAODPVSyMcjOU5fSyi6qiwuPGZgtdGMMOfIttRk8x7TrlzA5R+1oYYeuG1HlVc/YbjefkFvJNEGWjyHbw0rKAzEZclANK/jRYoTbQ7i0v7ON8MxSGkJcL0JGiJXAKFbZS2dcRpJcktpLjebKnR2a8ejA+0A+mgtSPvlU5u9d15dGh8fj3zKxWfPnvLy7PFPr6ys3BbTVjZ7b62uhyAhakKPX0t7bj3dT87xQLSprG9JirKsqKiPznA70UfqoPloxvBQLBDeHh0mVgZvfU3xc5r7WwKP3viZHabvShSWNnID1UxiBSils9OVmQKzkfU6VVhcl/YQ0XUMD7mK3tDRFNMUwwcC1JIcCGkUQ7NJwkV70S3Aj934sve5jBTh0jNTs088vXLxzG2bW/Lca4tT815HgB56zboqKXuDGGv75IxqoIXdrMF3sF6xPJBdcsqt0o9sxOuUKnTZ9XhjOAXrl+zv1UNv1xQ/3fWJzscvZtMP1pyrYJagneN28s3IxeBXjOtSW98qpBdLzjjSlkYKXARXKTxceUwbUzDyM5W1kHENHJNa4BCX7ZpScUwM7uiI3MlHz0AzNv75ymknvLfn8s2Nrd5G56uX1t66c6Ou52tQoETTJqZXiVO0so1RrW5CL5liOVFF1kT6Kl7q72kFFXoDp4wLN+n4dj0V2ft/VtsNoHL6N2yb3tjOsy1fpv3Kv/BQR56rgNLasBRpkCgf9evaX0degS+V8qVajlaUMYW706cblZDuKOAxMlJBkKN2aIzsjmEd3WJ42LSKDppbAtp+tY5K66jeB3Ha2VwKtQHM7GfPOvO6+7fCNjpbz3Y/eq6nlDdEM1GMkEXLt5GA9kmKBtb8sXuWdjbCoygn3doivWpQSwvH1iOMFvvDyv9Sw/NB2t8rH02Vsd24lmD/ln/G9h01whaUdtFF+MZecOsCg/qMxdozB+LxpfthuEkYy++N2EsfI6TjAfrCixi1e+1S5dGSNzXSfqZJcTQlRViiJUqPvl6KIjtYxsYfuW7PbTffeXkA3Xtj/Zrd8w1aRLaQ6tm6y/eOtooItJ0v4bqGqA2YkU8w72KtI0C9mHmEuHBXlWwhS5pppqGGHjzQOeL72aOPdP8Bz79MtDOAVWn7d9c/yG1IZSwIyeayspPKVkJ4wErWUHE1EgWk1ZGGcpFsWwRrAppIh9hmCqKOtVtWhoX5tNLVnjMoBz2zneilpdv2vPHGzTMzM+vd9SkB3crICfUcJpXOdEI1by5q+0VsXCBcwceY2sDvjZLdudIVOUbh7F8t3h8dFif9pcrp3wf61x55+CM8U9aBFv6huL+05aj1QlyjNeoKJstrNfT0dSrfq2L+wagWHrhpOTt54lz1UhE/2urDnqWIzgJIt0hn1qEad6hbeicKRL+eOXt8fGW34hxnnx2nb745Pj419fJj6+9efPaH73aXX3mm14gPB9T1Gs3ZZYbmSYSWjXRGaBRa9GjWVOuqXTsFhsgRe2KoLpJTdTslSTJ+kv6fU7AGUDn9Gwavv4X1H+EZokuNdMkksHt0FJVG2+vaQ7VRHdnqEHyZJKDxo59879WIkKZxWJa1uRmN57OQmRFvsjgjI+I2NV6UJUw+2jn1UBW7z9798u7X19enJ6an37V9+KF3H+r1h89sCmYGhcxkrNPy2QhKXHJOE1LolJDQGEGEWmAXK2nGEAG9iI7TNGo4Cp8vw/7QO2C/zTMemu03eaasw+sAjyIbgZa4NBKagECpdBAfhVI8lwiTj5GLphyVrSGmCeMR4RbRpArRAfT+90qHhp1stVagjZ18LYWhnQD63Tc/e2uxu9q97KKJuek52fS00T5VZH/4cqzojX62BveBwApssz5uHDLhKSBZ+Lig/5NximRLlRmmQpCvOKhXmXf9VOl3bKihB2+w+vMH2x/Tz32iR4si0chIg+1eQ0zLMuzxXfOdanhEjR45NscLdDAsRA+MPkgL6s5IO8MG9r/6cPCDP8VJy9TnwA68M08AsBOpwtveePfD9Y+X17p3X7A6Nzcxh2HTctOvr3w930GKq+qavM2on/VSm0iNRooiYxrnHP2HDeTph6aP6h4lq4KM5tM4br3jvEoZ1nIM2pLcXznmKerld4mO76htgYzUNMekWGovVkqNKwMpQgdcqr9xO0FShXj49kgHN22mRyrrVGYgVvLWJFwgmjmurUaGszUXRr662nxmfeXZD6765P3uZT8FWv5aCuTxma879OjPHGZm3bP2sx4itv2Ksoz8KSHWrEf2LdBEtHqIgOHVVZioW94DzKGHHrxt4/fnMP9hvYEdkHKhYYjJhphY0igkJFAgilIDicRGsXp2LMEqE1lCEETVpeLTiAbUkXQRbAIa2uiHl93248Om2V49/HVI46r33sdnnnjGSx8t71p4f1V262p3ItC+LJGW+FgKomMQ6eh2g9Gmz/uSAjWdM3/pYe4yDX6R+Aijh/yArmfLEu5cjZH19x1KDmynPXSfYxkX4PkP2CE5BPLoqE0Io+gVw/pKw0K5L0/Vykol3sUJCgix7Mwy3hp/KT0clr46wJY5ylA1iDjoM3bnwCzn3IpYXGydmx87c+ykl656cjl09Oorr7xy662xrcb5XGwGenru3ZlobKc2v0iYfjtzqpQysdKmqaifvk5UR5uOKbFJEcZDVKOtLKGVMsTiZFgPPWhLdDlCs6zPOTz/Uds/iVbyV1XR/jqzh0ExEhR1hBHRIi6SS5+RlXB2xnQAORp8pDLURhpqQVujRt7RNYSG8oZMP5ES6Vy//sDYGSe8dPULu5a7YQurzz8voKdfn55ejS1cdnD9eFTdEdWG3lyzigJnnRORM5e0eCebUrI2MMPW/VBlttfhxjbEGeYZSg7sn/bQ8PwniC6WmU4RejzP90lRRNEJFR65SpRkt+GtBD6zuPiUQmIFsEDFSiWenZFj5tOOsUuGcgEuq/OF2mxHzF/f/WDywpM++GhhblUxDjvm1W5XProbMIfDfmVhdX1pXjeBdXA2mKHlV+atXZJBKWxBSJEQwj+L0JQnyCv/SOmy3uHJP+z0039pJdl/i8EsO56AnOd/jmdK79p1XlcRD7AOEFDFNmHNXES3pmGyljwbJFCuFxcd5TXmlbVx/fvv6VcGewS6BXfnqE7Ij5ZRVJginlTqE4TrrXc/iNXbblnrzq1uGxIa7kBbMmRRRXfEOegUSVmHQRaXOvpPxO3nhAt90PI2NdTmFznCxEJEShmF7TTLkWE99A4AvR3p5JgdeuPP2UFZy4+LDq6DW1daoq2pPfO7uDTmzVKoWVwkEXeCq0GUVstV0PDpLrJvtgK2ZvYwwtvXFRjGEcf8o30AvXbi5Blnfvx2RDkmgFg2IW/NWVxb3fXMfCNUtzwzXRqpjALLyl65WGJk8C6nUFKlAqVwm40buYjeUO1Ku3Brw/pQQw/c+kK5zzUH9n+WZwqV0gGROKzqOJapzklJbSUOq2LUYQMHVuXVyuEOhl8ZzOUka9iaTR0p+zfRBhoHjSXQ1ZXry2NjZ5z18S4NAy+Lh5CemHAAz3tlWya69/W8UKKcvEMd2T6kGL6q4q8Tl3MM6z8uNItU3X/IjRTKDP7w8rzG+D8xrIceuIFvWiKdR3j+80SjmUvNchDQ/wpTEpcwBd6IbomeuOSlWkyRAwIoVPwzY6tcv4LeGbSUbrKmD/MAm6zQ3aQ6nxZiHhO+dOLYSWc9uRxAB8rx7HYvu2w17LK56cskqwPo6YlXbu5EPSqrcXGTpGUpHRhbDhnYQNi0F5S1YnIWUmryVw9ys8AD1BUFTKgbw9T3wA2Ot1Gc9td4pqzDWGaxHcwW59HqTTqkQpDQTVyfE+GOGAQ/RKpFdO38Uns0c3a1jD4Hbquoy+GxqZhDdQC1ts71ixoTnhZAy0XnqDDOLgqku8t1JnxudXbeddYtlDRTCnMOYeBsAaWsSIDLg7ut4Lorlwq26fXXLtyi/PoU/j+gTXQLyO3/P7uP7qwlw4C9fUc+5a8RjasthZqd3ILLduzIFEdUr8JPJxxM43KaJZ4iuBCKBhxazOkcAZL9DeSmY4dkhmEjTSrcrZ8fve7EADo89NrCIsFnpVWwCQc+JiYC6ImVALpJCR+JlWCXsV+GKHSip3KaliCxUfCtvH+7ahRpKVR2sele5G/oXRgFAGWYWNkB2yY30v66fu4TTTDZKbHMrLQVkaPuTh5M1aXBdBxw5I2CX6bFNMnvAs2I2IYeQpuNQVcuaNG02qA1OUDH0S074nzjjhPGAugzr73lpSteWut2F3ftWlywq54Ikp0qZLfSc2jE/pi0d3+5q8ol+v7TiXBC6Q7KtItHCtrx5yTB36iyti5HjHhrP7UNazkGb79EM6/+Os8UKhW0pIGWs/I3iprUOQrTrdH9CWY1xWa8i5WoWTAiiGf8MWWcJNdFSI2xEQ5j8ooush6trn95+oljk+edd9JJsY7sVSE71nZ9tBBILywsri1GFFqx6AmAntcUWSNtsCUnnIss7pxDxwVpIf/tfB5vUy/aoKefDkE804VpWKkfFrCu6gwjw8NhphDbAaC3s/w3eYboCAsTg7ZfKrXi6JfouBmW9hTNm3chTdCWiaS6KCVN0jkrhRyVlkI3wK5Zhl+Y1lqdvmok1cHx8KfOnZy88LwTx864/WrlvuOxsLqw0I1NUO9ajC0soF75Wiod83+X0gycryRGm+BLxnCQDvGuxb4ObvE1YqiDZ36RuKdlDXqwlgZ/6aGGxnYQ6EHwjB3QbrsSGhfsGUkYlWnWImhQRlt6A98cFCGm41AaObdPcVsIzrUh4NsfEMGBsZHPUlLnVNyyo/PVZAB94diJDywvBMxOpWROxe551c760l1rn/VcQ8rtQ76bLlCUdrugmXGsiBblgtaUxqnHg2VETKvWmwR+zsVCQHMo8tTDSbI7YTvEM0Q3mCBrLRx7kJVlhFqerz8JDxX6wyrvVqo2Qnbknhk92nsyi8VsWIuoCJoAHnibaM3/7jx64diFF06OnfnkGkBPGOi+wfXCro/u61UUf2hHJauBtbynXp87E5Bdwk+ekBrnSF5qE9caJNSTKUkgVf7lgWl09DCxMnjbGZ6xA/ULDMNtp7KLCR9tc62qOG871GGarTMNrUg3O3ZxIjbNw0BUtLdSuecAHbtQvzhtk+kOBxv3h4OenBw766O1tageJWYXIbu6drRv3asf7TQJcSDYA2ga4JjD4BT1XtD/1PDToBLXW0uTsFAdpS0BIvLhODGGZ0/uGXroQdtO8QzRTH6WSUJQlKMsoSan0AC8MHbySYHl/NJx4HqLmeAsqJXNS6mWhnCUtJAmUK1r9AmLx9GN0a3Txy48b3LyxNuvWg6goxBaDvl5Sw6M8o5zpucW98zX/aHpmCD2ED0a7NGxLANwgatDclhB8Dv+jklJ62/Noh246ZJYy4bNGnfAdpJnSu+cQtFm4dyuv1v/lEuJEA4wuVlKDbL2bW0q7xxHyO52aI1EWVeIrdGyy6dG0t3BmvGsnrrhuFAckxee8NLy8oKLoGO3azV9NGDjq9+Iag5uDrfeVwqegpEixZG1/A394rQdi3GiSGFIxgsy+eWGtbRlFdNxKqqjmUKpu0P/yLCd7sBtx3jGDv6evXMHja6KorAoCj7BO5nx3oTB5AjqD2KCUUEwCNMIQdFqSsFKGwsxoGCKNCKaRhAFRVTEysIH6YSoYKE2dmqlVmIjREQRO9da3z3G9JMUw92Zua+Z/88M+WbPOnvvs094dj5FfLpnB/nBJsdFMOOgiy9TLI0iNSNOtPEVHnICFnXFuQcPJLURT25F572MNpNjNz+fKAr98NPzAH3pw2c8KhTAMiiG5y+1wX5LvR15GWqTMv5EQuuWSsIy9TnxGl8tfPoyJdJHJYEO4NbJ1JZMUyoQ8dOlOG04AL1oOz+esWsy81X8JhbtPIqQ6JJdgeI2f//Olw1sYeIpWQtBA0ey2u3DCNfFLBEDYM2iEbhn+oNN6BvdTP/Y29oV0LsG+plfvzk6eu/oKIlC3ZEbP1DLoe1nf9HJIGtSMP+KLCVBZpJFQrrzIWM+36L4+Z4h7d0K6SLweZ/eT8NzV/Qj83ByAPoc7Bx5xq4oq1EVhhagaycWHaWPPRSk9GGUUraS8aJAwPGlJMgOOjNoE2sghus1qRLM65vt02iAHIvFxihAjw4efnorDvr2S3c++8TjT97/+mePPnn/N7+5rCOuGkNyPOdFOJn93Vfx1Y7tiY2LQhR9PG/TEZ3pW/lZiehC3qhfOBMcTLotIE/9rRQ3nQeHTOHC7Xx5pvSu1IBFVGQXB5y4BslwhCZY6LSPWkO1j1LkX0vXvImPxEkTdDhtTCr0hHDj5kmi2YVKctASHAF6d/u2x5996ZFHH/nmm8cecyrlSPb9EZNmZZnK8om6HqTD9HiCRDfSvp2GEVd5vSWtRKKLmYbFXBRGDcbf9FOXgp7O3IDM07EG86YbwnaLtnPmmbIOwWoPneKdSF+7qIQvUJkMCgtdkxj+G+5i5cERGBggpoV4g6QmkOc7k7WDtmhcoTCpmfYKenNXQN/z5COPaXLsZzHR/NlzRw8++ODR0QffHD0izB8U2ALaNmYdN9r/1i5eTBozu3bNPpBR90/EMZ/aXEZd8+y2aNdGeURxr/alIYOHPgc7b54r0Ya5eJusYFLclMfHz1FP3MPLhEMiuCTlRAkqNfO1aH7YT6auDloki2aSipRwUA9tBT1nSKi43SuPiuY33njjvvu+eOihh9S/Ubf7HnrDcD/65CP3K1v4tYFu7KBp5G/BQZqPFBEeNzPaA7bTJVbUzGhnPntY7kfDYFzzLGRMGdmW0bBo0DnYefMM0aWLkZqwG7PP5ZrAgANq7To8nOHOA2LWZyUP8ngjnY2DZxI2nW+JQKN7cc4s+9OhODbms93bb31VEwc///y77968z5bVvtW4UQ0cv7j3vjfE9XM//PrWwbppZmIAyUjiHORYUqxBDQe1GZYeZrmLfEpqJeFGjxf1CMI5idJcJefdV2oNHnrxdu48U6hUQwAlHoxJ1JEQ/F3NLnNqzYqvONVWRH3cdUOHf2ET/QpXSTkbcbqG2egafaaWo1n9Y293d6YYh+ydj96/59Kl2y/d+vydX/386kefqL9u7Jdsv5C98dm7v08mKaXu847Mes0rpqyok0OmUwxaRNjy3rIhj4IU0a3o4aLTaJUoZ1hmSmUZNPTC7dx5huiuYxBF5owBf1XKlBETdEZrk3NIZSlriHdIEIo9YJj6y3ABzo2s5qyz7ycUnhxbcah4dH74zm23b25ubN4u02b70m13fvX+zz+/+9Enn3z38ZtvvvmQ7Ns3f5dz979MCDqGfqa6PzKiS1GpPpw69FVEBkNfPo3mNm+AN5SHeLH5htJxhPgA9MLt3HnGroLbZBX4QjaHyGkdpgoTd02TQ/7yNMEn4GGWqMsz16artjeNu9YprW7Hno+FjvZmsiKgZ7u2+csPbMJziNYeE9mXbrtb9tT777/+031/HayvSH3TDAy92+NMNQfzVJL4K0mUEITjc5ZzPpamPXTbRXO9DoTR0doMceiF2/nzjF3XlZuiG4ohZnI3Hk2u1wd8ZRvSSkfhmo7zbLKFOkpLjpoSZMjV1Jo72nEwHGyyHNbo5C4BfTjb3ZnPzPMtInlbP9oFa+19lGuyO37+60BRu2aihrwol0mfWSd6Ufo3obtVMk6YFVVAurNyJlZHZENAE4XPHIDsK85DcdLi7QJ4xq5MOBbHVTIkKhkxwbf2wK3TjoCWibC1umhnjeko7bSMfgQsDrpzzM6WNY0lgWNZ+/tEGnr/Nano2Wy+GYCxALw53zTVucH39j9eJlnmVI39PWGUrkEndHyoGLaOaiVsPptUI3X4aUNt6BOxC8W8BZpHJ4auwyHKsXC7EJ4huhHP9lUUQDMSdIjAeCfz1jNNSCTeTfeYc3QoUxNf3CMsmEWnxE0jo8csn1w7c5jnsQeFh/uq8J/NBfSGCcY2zW/21UkrVr25cXxioIUyGhr5rE0s4TeTzIdNNPvOSUsFVi4zDGj1EW4DdB/tK/Ac+WwRNixev3C7EJ6xGzoAMIqmuZjZjiU3yQZTe8R6PCach2GA8qUu2W9ZzXMAUQQHk1tH4hjhIRuvjUcCOmHoF17AQSM14qE5y2FsQ6cbxwf27KlPIk2TX0Tj/cRa2Ph30guqFqVoC6kBnJqrVkwLaFkeYxDJZMikQwcPvWi7GJ6xa5LWpjgp/unmKEz/4WGhzmoSHE0uwc+qcREKxQ6OSiCBBNCUKHf0GTfVTMEK0l46aKUR0Fvyz1v7M/M7B2HMF8AZ/XGLHp0/LKDHiXQ0mT+YuYwJWejM4secmteSDWqi6ASX7e8Ob8l9+rn+Yso0Q91ktdujGR/W+l64XRDP2PVtR4ehjJf484YNkm9+EKBLK4CJiCSIkOLTPAd3TQJcEEdw46DHGSbSaJE1jkeKcdhDe4Ls7gv78w0pDhiGau8CtAaEnEqPbP3T1qZ4mUgYB51ekjIGr3QTYZu4OC0Nkg4lqh5DiuicRtYoKO0ZDuqxoR76HOyieMauIC5HJoLqO9t/k+/McmWmBsDAAkXdxUum4ph+pmKZHHJDcUdUBysXZmC3dmOAnu1LRgtXBzls6GfhDdgAnevz45Opa5poPFqLk2pexW7Z0MryTlL4T4mVrO3LBqnEQ35YdLTekhZNkXehD9i0DJJj8XZxPEO0dIYp5Tu6cyItmrkAtwgFZJD23veIjJykHoKItI9oKR2ymKbiHUZR9GQiyeEAxwsvz4E44HJ4y0ZNsviO957/eTCe2LOT+zbXzMmlVkN3nG43jXb2LaQazkQjMwjUPS+cXg0+zRjXB/4AJH4tG6rtFm8XyDNlHQRrSRj2eWACzoRnKb0Dkyk8+1Ffr0KDxlxRAhEcBCIAL5v/CjEM9Ohkb747m8tBYxYY2Fw8xzCc99bfzYqjfR5RpnoPseHfnJQKrtiOmhEgu4ijjAJtyKk6XbYY93Cex+q/sQ21HIu3i+QZogWDvDSDImJchSGgDRKCgSGpAd80uiBKLZLZE8mDaEIejQwZjZsWz56AtbexO9t6ef9sEBrJYb7reWx+rL4c6+uS4PrRf5Q2ptQmofGZZBWbAjQaqr5mLgb4yjfvqITqlg+rLHN2BsmxcLtIniGaFDiBOjtq9HKvMSFhlbs30apxa6YYBy1LKCF5Olx07a7kDd11tcmSFCOnvmdbhzOIRV7YOANobTnd1ZDwxrV1TQ+gQV7fuhyB30fCGwLLctnegbmOXPxXi6LjpmHejxHXIxqCdy5ZfLNtB6AXbRfLM0S3FET3ooIZhzGE9SrFPvAdsuPWLDvrYn9WHTjpfiVPuhylkkMnFCYRqRif7CnGMVNm5TQGHasOOjeAnm/vnzTuTSPFMWaCrCxpPQoDqd2w5IjPTZhj1IVSX8EB6zhHDUXQOo+gLiGfyDnv1mPFywZbrF0szxCdsFwSxAaga8uq/TJZYrx0ra5kjXCsjHQufnxOPLd3jL7SDweFOE3tPKmQOIfLR1XLoaxKhoBnotCcIKqVQ9T2z4Mbk/h2lMORv9ohgTVhMtVXeDqQGDUUTJmhQjfdlvS3aM8wkNJS3qsf9hOjuGNl0NDLYVcx2Cc10faa0icU3OGUq99OtjhpmBHNmfGGUN0vdXK6UFpYxj+PJmM3aiRTeDjDQccwziG6P93e/efA+fLxpDaZZqaiU9ax+lmyoeT1UJLhhQ9pZ9pbnyeIh4LCVls/idwRmqMbUt9LYlcm4sXM/7a0aNMw05pg+mAUxoMc1bgBEpW4SG1IRKxEUFNDJKZ7uSFXuzZJpnB/f+f/MQ7fAVrn27lzNvt7KsHh7mH8F6ymYscP0v6tNQlvI5ntA0ebTXK+c4y1nXTRuTFmXFDa/xoMy/K0MkiO5bAru2SNkyYs2qCXb05wF5dMI/TUFCuxDUg9QREZFW93zGPtCsfX6GnXZA6VwFxZW1GU49hDwh0rDlzydn5Q0EH71EH/edCsrUdyEClxxz0CdhY4FGWT5mFR8wxr08G9zUBR21Yw5y3ZVZMjj0U8ZUpWvVKGsN3y2NV851LkQK+ZUhAfpwnDWhUfGuLnCo6ZK3ab5J77VSOYW0hVtNzsytpkRfGKgz+3Zi8r5gzQCAzdNlMYjYuWbZjv2T8H4wC9goNOczEa5ZhpJljhg7tss0ibXzveuxR7ZeSycKblSBw3LSf9HqtaitruhlqOZbEbohr6MdPI3i4O27iCrXeQS41aA/92kkhVIhx8AOiw29HDMa5VgTchvbK+vnLw9tP7hzv/AxoF7XOATuHops/2f7dTXxPQE48qrZ77uj6PYxsmGPAS8+0R+VOz4bqbdT+Yz6itkBIdRVZHTU19Stc+XRuKk5bHrjGLqXOIh8OXwWhtRvpfAoMqTRjXCZkXc2Uj6ej1T/q+0vKsY+f5pKAVgJscvL11KAkdFxwj+815NbNuxaEgdP5Rn0b38rUkCJEMhd/MMVXOxhSOGRGWEjXd+/Ba463HDDeRG+J+Rc8ewnZLZFe0lBC3eGT3LgTrgGLKjXtQoKmHr+paEixwDeA6TAKGbCHNFVdS0SzFod6j/+y9vCWA7YOxVIqeBfoWPXhp9sf0xsm65gSsCGeZXlEW16eqD6fLmM/bGrCjWRKP5RU3RQf+iRsnanOzr+UNlCpYvBkSK8tkV7hGtI2GpqaY3EqUqnCWUaxmdlLv4z0skImzObniHRG8ut4bU1bGHhca6MO5ge4BZlvlx7aMKPT2A/u/NxMDvWbFQRB6qltjhwzHfKjMZpywTpK1ZHybTSjOOVkhmmITafRb4x1FgegJg+RYJrv8CtNMeMAkh+I2g0Bg9WMly7yVm8q0mCeYsAjN8wxazEI63jkmHN2dTkArptz8cTyzhBbShvlM2jtAo6g3tzWb0Kp7MmHVezrlpTA1K1zhYhnI+s6nTEkhPR5aC8gSsdMu6NIzlZyLcEdY+yHHQIZM4XKZiC6hQFsKHBIjgGYyhrhs2g61xsCOLxBDfe34ojvrzAu/zClkRuGaI3B//DifC+dojE2MISGWEN4tG1Icv7eK9aUtHmsUrrLilQ5r06ZaXGSmSwREmuWUfPYMeB0V3tS1N1kj53MqE/+6OO2vJPju60NiZblMxaSyjpGhIWG41JuBMeRVogroAlkycuPZ07d51JeRZmjImkGN5mCtrY1Pjnc2RbQsM1XA+cwY0SG87QcOPx2tJYI9GdOvphcctB7ta/6khks0fpUfJV8jbdQQQlueOOdUIplmPnx+oo4QUiVjyCEOvWR2ufUxvu7m/J1NBaFcKAacNnIT8VzOTnfhLhtRUppV71nYPl0M1gX03vx0NiFxO/gWx1BuvLcVhG6knyU4kByrjX4dPr+fBTmq8lgoWi57W3SiK3zVGNOChCqI/YTwiGeczltAgMtlD4PCpTOV3vmP701ccG4Rx4Eaj2bkIcGTZ4EdYmRN39ZD5jyIM4enSxfeKKDVmKPmvWWMAs23XHMuGewdAb3/+1RiY4X4SMQ4vUXzUSGUUrsmMU+QuIZ+HxKIomd0MiMCvnwylOVdinpESP+cMiRWls6uaqc329PZyQlmb+3lplGe1a/BLnlFCjVx10YjHeZY9FCZQ9ZX1k/frtGpwj+e3sE/k0fx7mylP806pDgiN7RpZNqMOtb3joOO8s2HyjiGydLa9JtzOXlvxoTa8/pCtoW/JXT2eZzgn9EfBoVLZ1elZZYt389USJvcmj5JzXxsVPtMW6h6b6bSTyz1drk1Zpnm5y7xV6pQQG+kcAP3fIk+YGdj0jtzp71vFP3RGzJWro2/z9KvI3pxFH6tYSWI3gTz0gG5r1CmlHTRzdMmepp4OvHrm/omZwiTwUMvn11Z7PQIBRhTUg5mOti06cDF6A+pwRd+ddzJL9cif/qE6pYZ4FREx0NjNWwHz1RHkwVX2vvvprHaSEuxlFR3iUP3oe5Vg0vxag1llHynEEFPAEbWq+PcgrrfEmlDn/GByASAlMsOkmMZ7erEdE0AWjpp4Xg7pGkgR3IqbgZQ3AI+a3F2umd6lk8acitNEuDNwcMQjQlgDLT9YweetLfoB+la2k+bURZHlrfVjiQgkXNeBLnMwHuToCYFrmeQCbrJm2m5GcUBxeh/XS1Ds8altKvzx0Vu2ifjdmtiuw0TvrXMqm1rNo47XTyjOKjxp97OWwHtRgYnL27tgPQ8vWXmOGuaNAL0fCs9GtdpXUpahRKSEcWs8bUksoUhPJL7QSODuSW1A87a6efmkkcI2WXE2PIvahRkCNstqV1bajBAm/ABP9rbqUFA7T8DWyDB13sC0SM0tIx2YCxaOHYdaXvwz4vHe3tbW3P1BItfnidtiCUrvnV80qSyH55H0dDx/nT/qhNSVvmY1SmBcr246ATu0BtEH4tv/jT66fmIMkC4KfEcXuewCtbS2vUJDZSma2+utZgePrWFCkwCCz4ihJskOGVuumuTmU20JcdF01fXmmP0L3tndMM0EARRFCQkBAiBCLcHnA65AmqgCCqhAP5ogHYohRqQED/8MTPvTlBD4iW+OMZERnrejHf39uSiX1Z9+fLz568/f35//a56f9snOW2hjOP++OPXYJIKQKNanJ/Mt6ZornJlqXPFBlKoj9x3lFElBtKqme8iF06N3W533fox8y1+He2MctyoPewliz4VCaa4VoZwLaZMAJcYGT4PcY0NlqZIfI1VA+2mr1kphSc9fTarXwK2yf794/v3zx8+m+7P3//8dDUTMwn1D3n5W5iwi17eMkFjLqgGXvuw0biR4KON3JAQJ8mtg/Po3KHrnsg2H5x2m/aQGPTm1ICQNaZc9CjgyUraKOyixjiffdaaUqKRBF86agRRcoZ6vUpT0TzZ1RfYFty/fn1Rdxn/5StxzwIUfumbo9n5SnrURUZ0jAx2jnElEUI+QvGoebVuZtpkACbJb7LD+jznFN6sXR4a43IyxW8rjFA+5kc+HaRAbR5mWs4YwIsfcu2nTJO+BzL7WMNp/RATztpSSXc1rTn++u17/8lJL69R0DjnFz6jj+QKA3UCzaRK+sH8XmZWET03zTmJDNBOCK5s4OSxMJqfY4P/zjwfCm/XVKhEYV1QWEK6KggEl6o8hfHaMb2xK6VpMZfhcJUck7JYESs8E/F4r6dEV/Bfrx7eCudsov3KVG+jvVz0YBKuW4aGVR2I7bttVC4sNCOhqT2hVgOEqdgo+2RKoic9pQelsKeHvmG7DDhNGgJ3Td0aTjiecJqGqoT0UKmIEO/RpZeWinawpFjMJ2nw96/SE0lQazTC2nv/TZMO9dkCWkDbO/NMmJVV8tURHHRKPRazMu9G7AO4PmzKR66vcODazYex0y7Ux6443zjDdjdtF360vcnybgZmNSNLlq2C13KX0FXU4NElTzSzxAXtu4y17A3Ly2pqVYh+oU3vAlo4R4YQfV6+Ofo78bpVLhIi9yqKIbGO5XxRSUY6l8NOA+i45ho5LZKJfaLtCYKcxUm3bZdeVPMsz3zUrEqe4qWpmfq7RqUPhRPx59NnlHeN2J5o6wE0abEbXt8IW6PraSmZzeJZtEthi/iobWkN25HCpKzszWoDVb5lShupFLpwzKPMJTQXl+D3/KXGwF/Ff4pL74c/zyqffHro27ZHFcVJjbTo9UuHSjsOdcXf4S63B+cA/pIlPVdwLQrY0egIafqShmbstfS05fQ1HUrdapQedmmTpN3uwqQeiztubaApUmKH5CC5PYVooPUtyE2F0kZmV4vKCM3H1DHuOr7knIJ16/aU1ISs2R0b6ZrtyKeyjQonoYuqtj4PIOpZelncBOe92qv98l46WR6annXXq14Cei0vhHPmlEwa1zjGaEWVZzxrI6aY+6lR08yUdWQGbvhATDSeU6u1MtC9AjG/McQm8dV9nA+Ft26Pe9na+oGeGppwNkLwgdMc5TRb7zlv970ja5iSe9y1sEYSR3roLTVHq1OYBjqV6hDqOe2X2DyT0Mj2CF+L+EJQFKVTppxsps/wfgxOKzdBnDPXTBLfyBNDnwY8SulMfd+8PfYvsyjozTuUaDKjpUT2LtzsJr1kGUkgIjeGrWc4UrdkkmG1L19M8MNMB+awnGFVQGc1FYv4ScjCtw6qHjFESJkR8b6bAUcSkWgxreGZJLhBHruj4+wo/jqbNd6BPRcwstGYON1mi9pIjq4lY0GimUy5Hbj/JoAozrYUNd3JaepM/9AhcFmeU439qXe2zgBk6vMyicvgx9UTV4bXhMUJxUX5esAd00oJjHsxpVdXicBvArpx1+15ATvK6F2NdUY5bt+eUL4fgO2qZ8X1rbxxMxrZjBpukIg0NRcmGxWdBZO7wm8aUjdNnsT0WisnVN21H5nxgqom+iGs2DLxuBX5DrzUaCwBXMTgcMfWJIgIeM0VIp18LhfN0yDyu3L+OWPlHuxZyVZJpnamg3VJtK3+LKhVy9RAXwSiqeIw9qwkr3GEzejhtChlhuBI0sSFeNEbJFNS0aQ9n+Ig9rEWE8g3Ayj6HW6HjBsJX8zdZpap8KcGr9rk6dbWmaSCPpLl1jiLk+7CHgrguMTmUeAUUS/DbE/sg73cLs7AbPxT47l9t+VzX62bDVdctAVFH0ZdALtIKY+DEM6kFyUDaS/D/GzWakag50jXQMJkHhqRD2ROOE9GbDHi35e3M5rFjMMRuJmeU/2cJHsf9rA180DUq442cJGiAG+ZEri4xdbtvYuZTWwm7v9iz2H9TI9n4+09mjqynCb9zGPi39+Nu8+7jtE0lC4EAXXPEwzKu+yv437jonn54iqvUehogtehmkjfuTTyvdjlYesA3ZpVc00gAZR4OOM2cdUHfJH06AQaWCxirIKlTB33vry1MaZ4P26ZheMqPlz2kuY2iOjSjjeiylsn7GhedPW/NgUk/ojdkdLk96P5VAJ4aCWGaad+TpK9F7s83NFoAEGnzq2gl5+Em92uCO9ImDhLKNM1f2VhZNo3veS0NWbTHioEoFeP5+JOoFlM4EvgsOKRm91xC5Ro41ogk4WJUWUH0LKM616g3mM6IH0CfTd2ETx2a5T/DG1oVlqF+xA/9DZyF1BLebIdLM5TMgJRHYrWgpfWy8eq+QDtDLWTf3o79BdQ2WGWEF6PsqfHTFtZzY64B299wvI1Gm2z2ehzt2/GhPnOlWTvxy51hFTSajNUJschJPJhZq95lAXkYoZTIRSQGjKNmPHNY2FWsogPB3UzFqT5FPQk0oM5gWeibgA6uIdGkPZZm+FFK5I6pHMmwzHDM5WvnXql86HwfuyRFehRcxqYmsI51qaJLVygSZnH2AWenGJozCbF0otPjaxES7tmH4uAporJkgPDW6/ABmMfS+jgnwda+t1ER5crN6YP+e+Obb7SMdpieS4lBPdj/ctk8x+c9pe9c9txGgiCqAQCaQW8wEOP0SYlzf9/JK463TL/YPduEq89mRjppKm+eHwX+25Ycm0esV+hR80WpQy4kT7LsGRMVK/NtHRkR6qP1EhbpzTshg3Zn9Omgjc3LoraJi5kmtNaWhACGlkku2mOkb7jC4Znvro5+CYgYJJP3979dNvdyT7cPirqc2V6w8fqHiD8KM2l6wBuaPbhYIiNx/Uxe+WEhttFQejHD8+6YsyUu8g1wFPsi0+dziMDrQCNcM4UtCalB6VklGtVA31FikeUSWYsPUDfyj6sU02Kf1J2aweZbulNB71IpbGOCynoFiUhmiWLTkD9QwdFX96yyUznxnCuoYD0tBht0oDBb4XB4moVRsFntlO0PDyjP6jfQ3td8iI1ROdfIq9NA/Z244/kuJV9gxwCtkkRcH/4dnjl5jXCRBRqKGRp243DNNUvA/cngV6odkDoTPUVLZKlm5tN5K2mEoiNPp/OH734TXEmNpIqteK6p9PDE1ZObYDOVsXRJ5p9gL6Z/VCXIshhkK/L1vlAE3i7RJg17dKJERXvWQsJa83sA2Wg/0QiX5ds9Q0EkeI7kFLAubLHL5j0514YswA0/L84lCZp7yP7IW1dNJtnv5MPWU9QeD/7saRNPRnbV80t9JEuUMQGZFl9wKBHVHztcBRfS+Ii9rdXqUEwkLpGGfA245nsBU45OyJBxo3vKyfewSGJjz7Tdu3uR0GD0y3YMqaeoPB+5pJhBdi0+kxjRIdWOtHTgeskLb2GPAI2GLMU6I76ST2nisjV42SEux004oZEMmqH0BAMFV1Mi528hUD3oep0XK3T3vlQ/0K9MoDvYKbl7kNP++gd7WuwcPykUGKk5V02JWsQxRDYq04y0c3RJIYMBZKdbtYwrVNDWVRhjGXRDretR1g0YeFHj9oe2ToaylMkWfPhSXTTTNW5OuU1m+32F2pFKeBn2kikB+j72ZevgbcApYyJcpkh2lrdDWSYUsnw7jycrVj0tKFeCdcW/Rbm3gd+p2sPUYzCsFBYQyBpZm/nD4Xy/BbaGR3OthnWeOg+WRLVE1EGbWRRJnouwbqhnUSX3V9Yjnf+7F68os9uODK92oF1OolQJ11l8YhZPE+Eeh2kebpM5Q7rQ4ku5y2KlBhtTWWlPqPNa3f9uj+mlX5dRvW+A0rECWPIbz8N/re0k2gZjoHlRQFOYKHCZXMZokND4V1rupCvC6jYG2//avUwRRUK0dPWCXreayerIjR9ESd6RtWWVpIYJjqRI9+7S3i8fGLloUdlRCv/fkjrWWjmnvYFp2eM6PnHTS6ZN+Hx2D39QThnKGYXXdTZlzlywO6d/ryMz2NiSaY4st4YTh6Ykx8U3y2B7nhcrZPciQZ5yWlb/Vwzo2TkMPIJCm9qX8Y751loj3jAlI97vxksgEMAJEA0Q4ESys5XOeSTzFNUhY+fSNPW7KGgl8hNFh86D5RCOdZfoP1JQ3ZtTxasVRUt/1kMqZL1vje2SpHa8niCy0dy3NS+Ew2+wbhgGEcI3JMiw9EaPq/hTMP/3G6b9/oe8jKbZnhULX1FM40tTKNDLMpVhh/FUKtH0suKRc7oHG7zc3toyjqFAkFrVPWo9RRWbms/g5BSNS5w+f/JoMMgrRPcA+CqkpzglhNzzNFXVM1SdFRKWL13Zus2KCA3g4dz3vbEyserUD5cKlNk7WoIruuXr0hRciTd1zD79QH6tvYhqWXu6NNWG41HL2oLgeaUAh5CwqXBIjzsHosQzaHjL5X0DY8qQrrQKD9KRS6E6h6o9+BO4uVAvfO+iQnL7Csqn3OkqsiJM+ipFN7XPgLHDjDv98KMcwMYNONuxcIvJpeVS8kZa1eHh6JMbQnNUkxAP2m2gOhJ8dDZDrQ2MPSrPNwaGisG+qHeejNRkQnZSPx3jQuvp/R9a/uQWWkSwstoUZymaH9j1aKudcxtD7trOQMnWhyxgb+2/mj3PKKjRq3jlnueo4T+NspiFkBfDjX1HtnR5zmuvmpsNP+joW9uP2CBQFBAR/glmuFIN88a0zSQ9k5WsB1vK4OP9mAxRh0w2nK85+7xlZRIERFmks1rZqaI/WKwJrlx2udanTOfvfP+qjkTPUDf2X5pDR31CT75Ee6Rnk5ctOjfsF+WnW+z53HxoBA9KWg/Iy/0fgfOkdItfquk9ILiigFTpdQc5YmUE2ISv4nsimDb+6K1Nf9JUMdfz403721fS4jUdelQ/xleZHjTR4e/rs5wEAwu+eVKMLCCuWW0NjfUIs6EPmZG1ix08ifZ5nCOiI5EnrLfcK4W2b1Af5Eu5/r1hQLK7B72NPj/Y+/sdqWGYSCMhISEAHEZkFqPdN7/Iannyyj8HeAem92ebpM2e/HVTG0n+9/b2xUH1+hF59ZytAGikMYUy5Gbszgm9gGsuE13wY9+P1fbCIoTup8plx8scedaJ7CnpeY8ntnS3YPo+TYthHIDPXuev3ViIPe9Jsrx31uvqGRE4LkUshrw9sqUmgJwLTBGIFhd4IF52BPPc9SBZg7BTcBETICJTO/qZ3VvG4TK1+h23qiKBpqqbaaB6WRaejgbNwpgT6bwP7cmGoD8KtCEwiVkMpwtH36hhC7L3x09QY0FycXuIDNNPi+/WZVxLCOydBOI0xSZHcfbMRa9MAIHaEj/Hq2zmBbokykca6JBGaERbnjZ2b50w05rSHIZ6POW9QH44TZ9Zp7x9ChusCOPYvJw+wlQWHWrfW9g7gud8iSu16Y+T3RYfMs6w71Q0W3ZMZJj7CF6+7ZsV1lXyJ/K7rFBAquNO9yHzuI8GxcJfi71cPXQTRQintTK2u8MDZfPpbAtVVpGI3dAnm6MF6Hke8LnDNBjD9FLAARdzQvZ5XZ4tEiqnc3oF1zJPAEYbjzxB7Arn4uXRR50s0ynNpPajCI1pNtss+5XTj1r6N6gTgc0P6WlbLppNPRYl97hbZsz4Ve/nhI8Ke1NKXtJJyY1Qwc+6Sk7gjyKjlIkDYh1CQ1dS41/72MZQ30r1dMzvwO+0DYehnGS4iyPvRhnUt9j2z6uGxqB1e40j2i8tH2iGkR6ArtP8S6/CMEV6ALwxN54vOSKRQqHCEnxZ+0758Wu/eZQFruhaFvPYaUi0Gbs+RhNPQ+FY4+9v1mkEX6z3l3x3Afq7B6eEuGTP2apjIjoJrvOucwuYUqKrDzi5/1UyCXcSe6eeLWcUDlzVgAaPZOkZht4j4cei70XYTltLZA5hAGYfcjCzwIzPTJnNeihR7J1tb+BxnhqVESM/+HIUdvFFeSrArRPtUDJkDxySsp38Wt+Gnls2+cHoCBYJhQaC4Ar6hYnC08+yDmkWxAu8BVaaf/qan55X+VedDryxOA6nKeI813lB8I6mkhLUT1sE9seyTEW+5Dq/kzq7jRgEnfHsxICBvhAmbCHYcfLQzM73WpiE/Yzs+lko0411U47K5nvofslRdqSyW6rU/bKVXRP2G7shxWVVNW0ON+sfttdR3TcNwDjpl9QEMdF12EYVRDDoYe6y5V93i+FaCQH/L6saBCV2kV3MicCWRvioz1+qLeeBc/HMIhu5yppUQWth60bSu78ly4Cwk5Or3BEAzvweYjeR4lbrLwl4W1FFzYy0O2b7+Jy/iKURzEa0hu9EZR9yYw2cegxG2UdhsxAA1fzgy+1sI6vpTyZxJ4Cs+kKXmLHjrtw29LXUqiLTuGzfJ4Xepbbbz7nV/bF3BlO4qzURT8eX0JzP91nwfMxLETzyEX9Zy/sZeqaT1Nzr+JpTA/sFzAmnLwT0AE7elsc4gGPvlwknWD5lEXFlZf3e2IuIexomX7jpK/tlXNPtVCaeuixn0rvwNUa2h7PEMoECTTV+1EJbgPBJZXr9xO3VrHTXN9Wx/1yq5AwCJNgjWfnMLxbxvvu0rIZZe9rd2I8qTojOQ+FYz8RXetxyywI5imB8YIbHBguC95UQVOCYTvVR98VMn0XUdZjL3TBWWdxUV+SR7xIcpKPFj7WHFEiCxWvyHGPA/wvUw899kuh0oJUvSCYD6JxnFIdv7ptYXhcsFSEBWcCtlrI2LXvJ0OnTG6TD+c+DXXMNBVmIMI/uKPIJe9U019yCbWmHnrsZ3t32ExqBLCvBH0tgxWGgbsiFZrALL14p0AkzDsqR1m0r4S6Ua2jMrh++D4lSG6uVIhIZjkPqMF8pmCN/Z5o+OnnrmJf4Q2y5RcIAWa/urP0yAT8bMJs/abu9AXbQNeWGLhaCwqBKO9EMghx00UFwTxcxqtvxueHN8d+X9aRSJosHK7AZRBhOcGNqOBNVqEjBGUghkv1Fp61Lqko0q8rLvnHoMc+NeOqPCiBkxxTo96EU3mNAhkNPfYr0SAKQNmKPT8TQg8Q0pY0N3zTmCb3i73wSSlH5WoEN4roHXLj3DRI67hrDoNz/01Mm/HmoXDsd0QbEKsB9dsEwkzvFzLhUnMJ6CBIzMFMbhAjodMOxpBMpxMbyZzvfIqe5vHydE+2J565KFsCeg3QY6+vEYYvFEqjKCmyqcKYgppSaFG1JXNMIRElnjLqMJ5/mcLoQfjDgPRGOgd//krR0+LgRDnGXiNaztXZhH8uRMIGuaVw6jICt0Mh8ehO5YF6N6Wo/0oKJdLZE1mC+rkFhMuG6CM99l5d+iIOAbTZvjRLgY29VnqnTN8u64wk8gqHuPFUraiGgqxrke8r3K5Je5p0P2fqEj9vLJF42Zn2PYMx/AqNXA4OVm4KN0dtgLmEnC+rn6qRHGOvEr1qi2ZTlaw3eOskVsyaEnPYIeLjSlNTtyX4rqeLCIFKLwB5ypSAFX7x04ieiPWbtiIx472mnuUmrwF67LVCpQa3lioOsd+AxyYJD3ehMZimotSGO9dDG7mSJEOK++AsbKfcQRcD4PJ5Oqx4ZdHSbZvxuug2yxiM/ZFotYxIraYiZuEmTG0hgsQW6oRnPlvov3m4jL/3GTbprBBpvC/5eio3l1QIahw+uRpHx6V8B4Ofr1VvxsZeK70DZaEZrmuJqSI2EDbpMmalqzfmMLwmOAzkcbocw70qiyLRndeWLKH0rHXOsULLfDX7t88l2jG1HGN/Ihr+lN/5cS29gPCwumPBl70lMgI5kJUGdNl/V7z8TazjPNxVrQhxPD+hwpJ8kef0DAjn9OEGSkBwX06aKMfY60TDkJEyTAn21sKuk7aDuqo6cmRX9lcW0KjoZTRK6pOuajqDapaq2UNyec6rEwV0aueq/nw5tkHv+Vm3sT/aOyQySEXuRjgk6oCWUFkAXEQc6ARh3p6CaoMPsBHpl8tVk6GporFfBMD9h5p+4oA9VrGb5USgfoAe+6N95BGsLcm9lPWHWoXu7oqrlTvjhoHXkbVMdSG5ggQRl1Fl2TCDm7jziknfiWoOKDdMuXl/u5EcY38pVFLQjYjgHY5Lp/TCGrpbwI4+qcYL9BHJ13UqpQkAyqKZA/f24veW2fkWOlNdSlyppwfktlONhx77M9FfBKlgdOo8z6r/1tR+XwWwuix48yAoyQnEC++Niz310KZVhFT4/Qkvs05tqSlVhi1vnu0N1s+IX/omquIemtT32N/s82oTT2R+3+vaWEq8jyiRUs+U3gacDM3RItVkpsi5zlK6e8GCF2sPqRhuy/gs+8Xwemx/RGY3/pNYGfurfYAdwnd3s3PJ4TlrYO0cYMmKONm/2vQBeLeqJJNMXKP580G89XUWqGFmlkwnsPouyEh2/O2+kd1J4lxMmJkZK2N/tU8FSwksw95erjGVbt5cCAyLhA2ynbu5Nuh4avhkQwlGUjh4ZRcwnZJUQfq9MzSEBuV9yWdoz92dh8Kxf7C3TZppkZKwK4C2kI4nRj5XrSqZN4C+BJRy37rib6NNSGyr2u0jO9qeXdQ4aXHL5tsYM+vAl4Bkq5Cmfuqhx/6N6FQd40jZIbuXCSOUkkJZ0YFT8KKJQkhm79lyfzTuqxAltUWHu7/EMRteNyk5wXyoixNWTBOH/sbeGeRYDcRAFAkJCQFi6SyKGoll7n9AcL22InbAn6ULZshP0gmLh6m23T2rv2y9s+IHwlM7X4gFS9J6JEIO8CY8E5x/C/M700VCNDjL1zhvVnSBMXWWae1QEtxUcGaLMZ6QKWLL7gv7w+tXf72PIwE60IHfm+jVaOioq3BprK07maFJvkEjEZusiRpoC7pB/NlHl4A8BZOZY9LUV2M77j5x5pdvC/Tq74n206LsZxfbk0ye/DJzOGDt3ER/A/OfxVamDEhrnt0HwIw3iY+e7aevedupdV+y+vCPBYaJ09Y8YVesrP6a6AROq/GbBF3Ypd5tginfnD9ayi10XXQqziDcDxK91qI36dRLOmTPdgeT+2u3Q5kmJgcrDuhEdpv21JzTh9Xq74hOWixy/4IpynzVn04B5Jk/YgPU2IF/hmeUyxhjmAzqmGkyIz+rlVHzhfWeWWc/uPJ6wOYV3sLK6m/1KX41xP0gpwzGIEwUtlw1Z/AR5D7E7eT+pHICbjMM0nl0yuA5K9qNZqVM3AkoE8JRZ1i6KTpcq1+yHnr19/r6ZrvhgVmb9HMfHtw0nzpWYkgKpBv1XOmLUiI0NyaIMzSDieIzj5x8Hz/AnoAusuITl/vRMUNv28ux+id9bqBbheEld5YTeFn15TndOpzOGtaQP2lqyjRhOaJmosuwek1bapxIBMUxHtMWbesk/VJd2d1HV/9EdINsB2hb+GHoVB1ncPCj7BHS+zbNBE6avaF1SM+1PheA6e4/xRocRz9Al81pwO4XJuD/lvIYzNBmOVb/ou8xvkciY9EnLgDD18Jrg2dWHOIGlPsP7WG46cZV5COx+am7gCofBO+iLuNGN+TLbuuNqhbo1b/pS1PUMFu2RBIavEIzQVtEy2ThZltyYut9LLjqMSsTbivZk8lT99kYCZrv2o1gcnTmkq3UJ3kzIX9L36t/00fJZUm3DY99Qo4RUdiCR3gFcC70GSmoTiUEcwzFZEbExhsOv4LahHBXfj8DOzJTU3Ef869isxyrfye63AAXlqMMzz7I4huGX+J1A4llaHZNROWOAZ9JZnjt55edQE+0x9lAPkPqyT6r9FZSTu3+0Kt/b+vwJct3ObbXoY+2DXY88M+ESyxE5ft4XDqo9aaGWtTOWX5ogBxSzeAWIfvcxqZ2xR2689D+iVwM3zz06n+IriBcpiNDlP5AKlDiFchAtF945n1VMF99re8o7EbL1PpknhFLQeBuOcDCesI0XE8j63nzWo7VfxFt31YZNXLBi3xGqoAFbaxzxSDPihenXy8oW1jsp2ZySujNOpDX9KBa6sOC4vN/A/9WuJ0tIXdvu9W/E92tb8TShjpMkbKAw/A9yYfb2OV8ahBzGI/COesPr6GQn2juicQlsiU8h+fHuRC2OZnLC/Tqf4i2ZWM1rimS1MwLy4Kz2Yj8j1UmVAadGh+DZ3/cZ0bpPEIeT0H87atxKEwOCeFN9XUVl1S7L8fqf/QJo/FATEM/5RLnqFpEYw4PsDX9eQbWyjCTWs7RJLbV2E6iOmf6Fi5Ymio7f4MT43fD89V/Eh0gp8JNAq1lyTpFRMVaqO+UWL5l63gTeGStFXfjguvUyYnYgvnzE1v0jGWZytza6vu3Urh6qVFJibcB0yBKUroCqOXWRYZCpODs+llWKJ3l4oGSI+wKfBflwpa5izSJKvAP8u5WD1Irda3lWP33D5zFvD7RUYpjuFiO0jwjkd4gFXI8NwAzrv10c9qfg67uqsGYWeSJ1BmcmnqrQeZBisfZXo7VS0QziaOUN7m7CtOUEoE5oTxOwlA88rRWH0zV40I5I6uOLyFgS3wAe2zMG/fnQj9+sxyr/9eXKW6PYb5m8qdydBmmc248AkG3z7C0EB6fB4idxDxbIHBKmUb22L49OiYcz3NM/BZWVi8QfQe+p0aYT+UjctUt8nqXWLZyrDFM4iI6/NrEXuCG977QpLJE4Gz2dWL6QE0BvdU2Zy3H6sUdlWIlmJABMJIa6TtcV0l1BWizEkVNrhjen8lde35LJuTii4N6RpDeBnRZZW6o6W9aoFcvEa2YCZen0HdUwj6rZZo7ZCSCcrn6evl00TFrTOGxkS016e5jtrhrnu/mvg1Jj5kSeMPPUlpthF693KhEZlgp2T3zQDWS/V3qSwDdl5pJg3ERxwmwzpclLvQlCi+TG8xttECLGuHFI1vl6kG7WePqVaIbr2dP9LA1lAmqfVhtMklLj4NwNB2idCcpd/fIqSTq2faxSiSkWzJ1HHjmzZvlWL1KdLCtIHsfRqUJnQD3TBpvFsxqGvbtWQz+/HjDvuo+NfPDgD65EGuCsia5lzecVy7Qq5cbleCwcLz90WOhy0pEPhnqsA+ocv5kL+m4CHJ5yFVTgBw/UqCNF5Gj8uigbu1WYKtXiZaq+az+jcjXUTS0ZFIZ9BIpbBemm+lkiEWS8tFkOXQxZeyvyhWN89bgHJB7SN65QK9e1SfyzFc5Ikif31cBNDA+zgKK+7AH9Fn3RyqA9bRZz6jbUgoxsRr3mXUCdCnhPrrWcqxe1leqI9P/2UAq0XL8wVuEma7Bm7A7xkEix5yzRGirwmqL7QrOnRlyS1XBu6QDur390Kt30OdSgzqbjrtV+SXJj8KyQVT5AvkmXZ6FgQDN6nAMCjX1Fk92WxliNstl5nW7SHb1bq13LO6eXlKC5thpMFR1rQ9IxyuLhg9ppn0UCydex5SA/x3sdU9AP/a8br2xIkyq3cZg9T763rziOoi4DRuCaeI1FFZ4f8MSC69wIjPFFM1csRkFbEdvVT0kvyC42feT9dj20dX76Is8xb4AeXjWofhokAa/wmaH2IsWfWU11mSqw2semzB+Swwo3gT1wqoH+c1Dr95L32wya7RbtA7Jag9C2HYBtFIDh0KdSSSGgxVW/RGLUm9IM83kj+a5j/p8yoRzdYFevZM+OkE608PBS25NwC7fnOdKKcwmHIuOaU3ZULOmuzK2yiM5INPPga7z1FqgV+9JdE3TXe86AGAwLY6BfFjHCQ/Qp1eJyaKaaCWAM7Kky0diB3RNK/U8Uq0FeoXea0elszZwVsmG2Rp+rQqzJWitYk04OTxiN0BLmtB7sDc6fgXoWagiPQa9FujVOxItWudqMhRjo2N0b2KydIdBfIiSsBOdctG0krb0NKJmjMa7XCVNWu8hWlv6/sXeGevIDcNANLgDAhySIOW4IKie//+Bgfk0ENKfXXHOu9ZJK1dviVlSlkffSnSuiKsffUIWAgrLubv7QLQAlFsNYfZ+Z/GSU3/oNAKvzKMqpBU2MdKstht9N9H/IauwtYiwmUacRUrj2nXDFE6jDyWS1z834Mot8bxZLQXZabH3+Xjo0bfq55J5jYJLyynoaGX52YLe1CuChF3wWf92XJQUAV0Ms4+6qLXzTmlmIvTou4mGROOYR4F5DpqRDbATGteKFOk6Pis7aC3ZrTC7h/Doa7FDKSnpjGuAHn23vpo/wJVzbxUOrvgPGqvJ1EqY5AkUpleKbTKUyN8GzuKx4/4ZSeZvshyj7yc6YxsLdoS+IWX1UMNY92E2zz54BFx2N2htj5JuxHHm0Y12GC6n9AVy8tCjB/QVFZveBNadcgNz0PYpvBY6ipKM3QblbEY5+5+izYJpG3OtjFnLMXpEv6JgNbxIWle26OYdLKupFDs9NvJFasQA10E6bhWOpq3GcdvYaMXcUzh6QL/KETbSq/gDM4xNZsU0oF4XlkH9MzEYAGd7j5OTVndKJ2ni4ZwIPXpMn+FEByE3y2ySo/MyI5MuZ/gCRLvt6SBesMtnHJm5b7zONiBTKRw9ok+dNUMgqEYzg+ScSythRI2wEscBs1deqfPbkC8HRPO6uFgwOWYbg9Ez+vjcQCOgo2NJYnCTDoyIdkg60TgOsFxEEYaaXUu5wKy2Gz0jiN7IFSbXeNe6Vphh++yQ+RZMY0qqJykctINtazbM0I5AfoAePSOIJrQGSrXjoEKN7XXgbWQZudU4r8VEYIV+r19yTs9lFq3wEqjJcowe0wf4QWBCXCgpbxvRItqKJy2HiZZddApM01gzk1ZPV+VyvJ4sx+hBfdgMgHTY+8aqFCO+87W4qyp08dCVSAF5bKjDaQ7QlUM74K+9PnUi9OhJ/VTYVdSJsJxqs6oKutkDLBas9hygr/C3gnOP61zNlXZCdv0YjZ7Szxs04XDB0gHXjsHwppfQrdxuQ0rd3F49yTPtqS/DHe3H9/5316TtRo/qC2CB8NhhvMNeT+o+NZVhvHUK3lfPYJp9h1WQLVzH3II1elZf9tD4ZC/PEKaYIeqIpp2QfDxKNeCyWfGbq9945wrxHRmgRw8KoltYZV4EZZ8DFA1sgXe57IIHEV109iw3+33djUnbjV7QL8BLr8gwy3TsQ4GPqONCbLGJzsJze24f8vfh5Ktj0najp/XH4MJyRSLotrEAVodw2KROaEvBAGNnjYi/I1xw8tCj5/UJcAuS4ZEu0y3T24PeaUMYiqviLA+F6ku+kLobuO9jHus2el6fYLoU8qK56nSegTwrp8Hy9EUiJ+mO/+YKYqSCWVXjoUcPi8dzZix7aHNZ5YwHAJOzA1je7UJOtg58bUpKCqnnWBOhR4+LpXdrRVWg8i/BkKCzH0gBnOViS5Ha44+jwjHcO6XTC9KaZ6yMXtEH5ZKCxMb6vxgM1AW7ZUO8efVwtE5H8T+68Z7nFI5eEUQvMCSk0jqALgitODYZ1GGVFph7qnyVfkH2rIcevSCIXhV2D1nVmLLyqFJOxt3NLOeaD64CaJdc5MqKBO6b/gF69Jp+LtcMEw8NhWE5YFfYVRTjEH5K35LsUJgkMTVm99HRi/qdxwwbV4AF3Cp4b7apdjvdzMyztp/pFXxCYrnd7A89elVfO6qa2kCgDbgnZst+OXmDVn8JRNtDPNWiVkbMeujRa/oKWHRxm8ycojavuocZcSyvQHyaV4U5J3jTte5jFieN3tRf8splQu2Tq2gBKqz6npUCXpC3IgniEq7cyzoG6NGr+uUyIEh6Q6STWebFEORzOrfM2oEziY/4erPRzOhl/dk4uwRowE1ldSch2Dwjb+2xmRZXqfYtlBRnX47R+/osG2DO1FM6Z1G3lA7IitrjjtdFsZGJDHK+xcAsThq9rM8DtG2z4aZSSBdvdhPWf0XxZr7apnCawsrodbFQqUK7ZMLpPmyc5ZT0fy5Zxrv8I9AddLY0d6yMXhZEm0aSHpCLzg3hrmaj9PB9WOWl0J5a86Nw9Lo+djgus1k2E8czM1pRjJx+TwXh7bhJ/dX8KPzH3hmsug0DURRkCIS0dJlNmLe///+BxT66DK/9AEuPe5rKtpLuTsVoPB6HWxiFmmVba4qqflMFIUlvCb0Adxj9Vd4SOnH9zkOy4RYe5ZwzyMVF0BoL31GWgSMOz297U5lb3+EWHuz2MFTCbObe1nou1c7UyWnod8crhCb9whUlhg738Cz8A6m4wGpG6Ji5mEX0XqS7qaly6zvcBT3CJEIJoSta9p1tv3QFp/s9hXojNLIzQCkxdLiHp2ZWzl7y6SXbrxrioVm+retKNXN9iN35vVTbhft49SPcvdJygJ68kC99PT3uEOSdTWG4D0rv5M7RdvScuuCAtSeo7KfGxcT1Qe1U24Wb4Sa4w4UWlnIlKMvLh9RIn1V1M/XUQ4ebOao65qDmrsPh8sA6LiqnLX5XI/H30jrVduFWxqGTLrMTyrJGs99D57n8Irt0/q7Xbc0kdTaF4WbGgaD17TlBpigM9fpcKD1DEQvuzJ/1zgod7mUcRBPoidknb46k57AYf6tDlG6CgND6KEKHm6FQ6YTRyDH020Ez3UodOXMAnRPZFIYlGM5wOCxuucsr9wynVb4WQrNP7Op/JQ8dbueBpFNhWeY6z8rKEpggrfeHYKiJTuekcDu/7Kf+SXFguj32l5wzTWKk5NcVpoN/WICnw2Tr7HspNnnuGn2vmyupZN/9W+XVyGEBnjXFdcVGi8osfyVmW2L+uoqpkocOa/BHbgf2meZOmLu+8lzrXdgv/lQ2hWEZ6BEmzQdjGYXgDEQh02VOin/UsXWEDqvwG3FbVIFNvY4e3Vu3U9Tif0EewQqrcJx+WmJSHhxlYRndgWNOlRU/R6V8NCzD0bu/69OpORtLvqNzePxGhCb4nixHWIVx4PCUE1WRlnlrLK7JVnfCT+9PQo6wEOP43iaasiSVF2GCZJDTGlW+DZNnCsNiDPeUcYqjfJcFxy35bAHWXaS7NC8hR1iI4dwFmnZM4T4GKl+KiLmjbH6ftF1YieFsMmu0al7WNQenxdWVpEwTmiTkCIvxsKZoa43xuyPsIqQuhC4v5GljEFbjF1raXckHrhl8K7x6i8iYkCOsxrPEGjzd5WrmM6CDaC/hU/zE0GE9njT76hij7347h/f5uiaZ6hxHYuiwImfpXff290HfDfY3DIX7CTnCiryw1VGx8NYuM8cU33GzMM8UhlU5Pp3Q+JJTecxBFem7fgcczqedbliRA4dLJRVuO4B2atrDCefpnBRWZfSr33hvoZ0t323B934n+EUekg2LMo7qHo3FckxEzRHZ3RGP6UqjmbAs46iuiZZTeM7eMeW/6F0ROizMKHULjuu8O/xfYy/UToPk1ndYmFFzY+iF+cvpOmPjMVxSpS9HWJcHzuJw3yk8p4wjkIvKpjAszcM3UtpaluhSi+5ONErnpLA6T0vcT1mpzfaRb5Rb32F5nmpVXV7n3uasyPzApuep77A2l9H1rbCDE2a9awR9EnKExXnhsCML9brMiXPS1yF9OcLyvISvSIzAJ9dYZKapnc6mMOzAgdDXgLpW3K0au6Q0abuwPofji3O0vI6oT7yEJ4YOOzAOZ6GdeJ6Lc3muy5Yq9dBhecZBqu5E6rEcigiv00437ME4ajpbaOvhw4DXaXgedmEUzWdKXdvRL0zG9HTwD/sw2AjaWvO9kqM+eTVy2ITHfyV2J5hcSJ48dNiIX5e//DnpIjw7nt52YSee6Dv9pT7pi5m5VKedbtiJJ8Z2rOHRpf9KtV3YiT+dbuasL07Hc6cwbMZrStsdwpDZamdTGPbit2MOdPZhfrIpDLtxqOnmMnN9TtoubMeBtrRU0kl5n5iQI+zHOITQ/TBWV0OnjUHYjnF09s4pO5nc+g7bMco295tVBHliJWzIuFQGzuo8Jm0XNmWo3Iy0umWS8tKgsCkPB9BI7LU6K3TYlKfQ2KmOSfpDh015EjWTvptipz902Bd3vbPP51XKR8O+/OmnViZ5NXLYmRchh0naLmzO4SLoSbIcYW8OXUTo8DMYh5N1ETr8BGahUtcqpRVY2JpxCLA51XZhc0b1rjAd/MP+DNnn9IcOP4FHhA4/ir/t3MEJwDAMA0CwIWDabpD95yxNtxB3Mwg/lNjrbznctiPEnMnsPjQp5kuzHpoYs7eHFYJc24QmyXM6aD00KdqEJkoLNEmqBZok1QJNkrJTSJTyl4MoJdBEWQJNlFugiTIv/P1I8gIs/ccAAAAASUVORK5CYII=\"", "import * as React from 'react';\n\nimport { Box, Flex, IconButton, Modal, Typography, LinkButton } from '@strapi/design-system';\nimport { Cross } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport balloonImageSrc from '../assets/balloon.png';\n\nconst CTA_LEARN_MORE_HREF = 'https://strapi.io/pricing-cloud';\nconst CTA_SALES_HREF = 'https://strapi.io/contact-sales';\n\nconst Title: React.FC<React.PropsWithChildren> = ({ children }) => {\n  return <Modal.Title variant=\"alpha\">{children}</Modal.Title>;\n};\n\nconst Body: React.FC<React.PropsWithChildren> = ({ children }) => {\n  return <Typography variant=\"omega\">{children}</Typography>;\n};\n\nconst CallToActions = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex gap={2} paddingTop={4}>\n      <LinkButton variant=\"default\" isExternal href={CTA_LEARN_MORE_HREF}>\n        {formatMessage({\n          id: 'Settings.review-workflows.limit.cta.learn',\n          defaultMessage: 'Learn more',\n        })}\n      </LinkButton>\n\n      <LinkButton variant=\"tertiary\" isExternal href={CTA_SALES_HREF}>\n        {formatMessage({\n          id: 'Settings.review-workflows.limit.cta.sales',\n          defaultMessage: 'Contact Sales',\n        })}\n      </LinkButton>\n    </Flex>\n  );\n};\n\nconst BalloonImage = styled.img`\n  // Margin top|right reverse the padding of ModalBody\n  margin-right: ${({ theme }) => `-${theme.spaces[7]}`};\n  margin-top: ${({ theme }) => `-${theme.spaces[7]}`};\n  width: 360px;\n`;\n\nexport type LimitsModalProps = Pick<Modal.Props, 'open' | 'onOpenChange'>;\n\nconst Root: React.FC<React.PropsWithChildren<LimitsModalProps>> = ({\n  children,\n  open = false,\n  onOpenChange,\n}) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Modal.Root open={open} onOpenChange={onOpenChange}>\n      <Modal.Content>\n        <Modal.Body>\n          <Flex gap={2} paddingLeft={7} position=\"relative\">\n            <Flex alignItems=\"start\" direction=\"column\" gap={2} width=\"60%\">\n              {children}\n\n              <CallToActions />\n            </Flex>\n\n            <Flex justifyContent=\"end\" height=\"100%\" width=\"40%\">\n              <BalloonImage src={balloonImageSrc} aria-hidden alt=\"\" loading=\"lazy\" />\n\n              <Box display=\"flex\" position=\"absolute\" right={0} top={0}>\n                <Modal.Close>\n                  <IconButton\n                    withTooltip={false}\n                    label={formatMessage({\n                      id: 'global.close',\n                      defaultMessage: 'Close',\n                    })}\n                  >\n                    <Cross />\n                  </IconButton>\n                </Modal.Close>\n              </Box>\n            </Flex>\n          </Flex>\n        </Modal.Body>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nconst LimitsModal = {\n  Title,\n  Body,\n  Root,\n};\n\nexport { LimitsModal };\n", "import { lightTheme } from '@strapi/design-system';\nimport { DefaultTheme } from 'styled-components';\n\nconst STAGE_COLORS: Record<string, string> = {\n  primary600: 'Blue',\n  primary200: 'Lilac',\n  alternative600: 'Violet',\n  alternative200: 'Lavender',\n  success600: 'Green',\n  success200: 'Pale Green',\n  danger500: 'Cherry',\n  danger200: 'Pink',\n  warning600: 'Orange',\n  warning200: 'Yellow',\n  secondary600: 'Teal',\n  secondary200: 'Baby Blue',\n  neutral400: 'Gray',\n  neutral0: 'White',\n};\n\nconst getStageColorByHex = (hex?: string) => {\n  if (!hex) {\n    return null;\n  }\n\n  // there are multiple colors with the same hex code in the design tokens. In order to find\n  // the correct one we have to find all matching colors and then check, which ones are usable\n  // for stages.\n  const themeColors: [string, (keyof typeof STAGE_COLORS)[]][] = Object.entries(\n    lightTheme.colors\n  ).filter(([, value]) => value.toUpperCase() === hex.toUpperCase());\n\n  const themeColorName = themeColors.reduce(\n    (acc, [name]) => {\n      if (STAGE_COLORS?.[name]) {\n        acc = name;\n      }\n\n      return acc;\n    },\n    null as keyof typeof STAGE_COLORS | null\n  );\n\n  if (!themeColorName) {\n    return null;\n  }\n\n  return {\n    themeColorName,\n    name: STAGE_COLORS[themeColorName],\n  };\n};\n\nconst AVAILABLE_COLORS = Object.entries(STAGE_COLORS).map(([themeColorName, name]) => ({\n  hex: lightTheme.colors[themeColorName as keyof DefaultTheme['colors']].toUpperCase(),\n  name,\n}));\n\nexport { AVAILABLE_COLORS, getStageColorByHex };\n", "import * as React from 'react';\n\nimport { useNotification, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { unstable_useDocument } from '@strapi/content-manager/strapi-admin';\nimport {\n  SingleSelect,\n  SingleSelectOption,\n  Field,\n  Flex,\n  Loader,\n  Typography,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { LimitsModal } from '../../../../../components/LimitsModal';\nimport {\n  CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME,\n  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,\n} from '../../../../../constants';\nimport { useGetStagesQuery, useUpdateStageMutation } from '../../../../../services/content-manager';\nimport { buildValidParams } from '../../../../../utils/api';\nimport { getStageColorByHex } from '../../../../../utils/colors';\n\nimport { STAGE_ATTRIBUTE_NAME } from './constants';\n\nimport type { Data } from '@strapi/types';\n\nexport const StageSelect = () => {\n  const {\n    collectionType = '',\n    slug: model = '',\n    id = '',\n  } = useParams<{\n    collectionType: string;\n    slug: string;\n    id: string;\n  }>();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const [{ query }] = useQueryParams();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const { document, isLoading: isLoadingDocument } = unstable_useDocument(\n    {\n      collectionType,\n      model,\n      documentId: id,\n    },\n    {\n      skip: !id && collectionType !== 'single-types',\n    }\n  );\n\n  const { data, isLoading: isLoadingStages } = useGetStagesQuery(\n    {\n      slug: collectionType,\n      model: model,\n      // @ts-expect-error – `id` is not correctly typed in the DS.\n      id: document?.documentId,\n      params,\n    },\n    {\n      skip: !document?.documentId,\n    }\n  );\n\n  const { meta, stages = [] } = data ?? {};\n\n  const { getFeature } = useLicenseLimits();\n  const [showLimitModal, setShowLimitModal] = React.useState<'stage' | 'workflow' | null>(null);\n\n  const limits = getFeature<string>('review-workflows') ?? {};\n\n  const activeWorkflowStage = document ? document[STAGE_ATTRIBUTE_NAME] : null;\n\n  const [updateStage, { error }] = useUpdateStageMutation();\n\n  const handleChange = async (stageId: Data.ID) => {\n    try {\n      /**\n       * If the current license has a limit:\n       * check if the total count of workflows exceeds that limit and display\n       * the limits modal.\n       *\n       * If the current license does not have a limit (e.g. offline license):\n       * do nothing (for now).\n       *\n       */\n\n      if (\n        limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME] &&\n        parseInt(limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME], 10) < (meta?.workflowCount ?? 0)\n      ) {\n        setShowLimitModal('workflow');\n\n        /**\n         * If the current license has a limit:\n         * check if the total count of stages exceeds that limit and display\n         * the limits modal.\n         *\n         * If the current license does not have a limit (e.g. offline license):\n         * do nothing (for now).\n         *\n         */\n      } else if (\n        limits?.[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME] &&\n        parseInt(limits[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME], 10) < stages.length\n      ) {\n        setShowLimitModal('stage');\n      } else {\n        if (document?.documentId) {\n          const res = await updateStage({\n            model,\n            id: document.documentId,\n            slug: collectionType,\n            params,\n            data: { id: stageId },\n          });\n\n          if ('data' in res) {\n            toggleNotification({\n              type: 'success',\n              message: formatMessage({\n                id: 'content-manager.reviewWorkflows.stage.notification.saved',\n                defaultMessage: 'Review stage updated',\n              }),\n            });\n          }\n        }\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'content-manager.reviewWorkflows.stage.notification.error',\n          defaultMessage: 'An error occurred while updating the review stage',\n        }),\n      });\n    }\n  };\n\n  const { themeColorName } = getStageColorByHex(activeWorkflowStage?.color) ?? {};\n\n  const isLoading = isLoadingStages || isLoadingDocument;\n\n  return (\n    <>\n      <Field.Root\n        hint={\n          !isLoading &&\n          stages.length === 0 &&\n          formatMessage({\n            id: 'content-manager.reviewWorkflows.stages.no-transition',\n            defaultMessage: 'You don’t have the permission to update this stage.',\n          })\n        }\n        error={(error && formatAPIError(error)) || undefined}\n        name={STAGE_ATTRIBUTE_NAME}\n        id={STAGE_ATTRIBUTE_NAME}\n      >\n        <Field.Label>\n          {formatMessage({\n            id: 'content-manager.reviewWorkflows.stage.label',\n            defaultMessage: 'Review stage',\n          })}\n        </Field.Label>\n        <SingleSelect\n          disabled={stages.length === 0}\n          value={activeWorkflowStage?.id}\n          onChange={handleChange}\n          placeholder={formatMessage({\n            id: 'content-manager.reviewWorkflows.assignee.placeholder',\n            defaultMessage: 'Select…',\n          })}\n          startIcon={\n            activeWorkflowStage && (\n              <Flex\n                tag=\"span\"\n                height={2}\n                background={activeWorkflowStage?.color}\n                borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n                hasRadius\n                shrink={0}\n                width={2}\n                marginRight=\"-3px\"\n              />\n            )\n          }\n          // @ts-expect-error – `customizeContent` is not correctly typed in the DS.\n          customizeContent={() => {\n            return (\n              <Flex tag=\"span\" justifyContent=\"space-between\" alignItems=\"center\" width=\"100%\">\n                <Typography textColor=\"neutral800\" ellipsis>\n                  {activeWorkflowStage?.name ?? ''}\n                </Typography>\n                {isLoading ? (\n                  <Loader small style={{ display: 'flex' }} data-testid=\"loader\" />\n                ) : null}\n              </Flex>\n            );\n          }}\n        >\n          {stages.map(({ id, color, name }) => {\n            const { themeColorName } = getStageColorByHex(color) ?? {};\n\n            return (\n              <SingleSelectOption\n                key={id}\n                startIcon={\n                  <Flex\n                    height={2}\n                    background={color}\n                    borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n                    hasRadius\n                    shrink={0}\n                    width={2}\n                  />\n                }\n                value={id}\n                textValue={name}\n              >\n                {name}\n              </SingleSelectOption>\n            );\n          })}\n        </SingleSelect>\n        <Field.Hint />\n        <Field.Error />\n      </Field.Root>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'workflow'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'content-manager.reviewWorkflows.workflows.limit.title',\n            defaultMessage: 'You’ve reached the limit of workflows in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'content-manager.reviewWorkflows.workflows.limit.body',\n            defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'stage'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'content-manager.reviewWorkflows.stages.limit.title',\n            defaultMessage: 'You have reached the limit of stages for this workflow in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'content-manager.reviewWorkflows.stages.limit.body',\n            defaultMessage: 'Try deleting some stages or contact Sales to enable more stages.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n    </>\n  );\n};\n", "import { unstable_useDocumentLayout as useDocumentLayout } from '@strapi/content-manager/strapi-admin';\nimport { Flex } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { AssigneeSelect } from './AssigneeSelect';\nimport { StageSelect } from './StageSelect';\n\nimport type { PanelComponent } from '@strapi/content-manager/strapi-admin';\n\nconst Panel: PanelComponent = () => {\n  const {\n    slug = '',\n    id,\n    collectionType,\n  } = useParams<{\n    collectionType: string;\n    slug: string;\n    id: string;\n  }>();\n\n  const {\n    edit: { options },\n  } = useDocumentLayout(slug);\n  const { formatMessage } = useIntl();\n\n  if (\n    !window.strapi.isEE ||\n    !options?.reviewWorkflows ||\n    (collectionType !== 'single-types' && !id) ||\n    id === 'create'\n  ) {\n    return null;\n  }\n\n  return {\n    title: formatMessage({\n      id: 'content-manager.containers.edit.panels.review-workflows.title',\n      defaultMessage: 'Review Workflows',\n    }),\n    content: (\n      <Flex direction=\"column\" gap={2} alignItems=\"stretch\" width=\"100%\">\n        <AssigneeSelect />\n        <StageSelect />\n      </Flex>\n    ),\n  };\n};\n\n// @ts-expect-error – this is fine, we like to label the core panels / actions.\nPanel.type = 'review-workflows';\n\nexport { Panel };\n", "import { reviewWorkflowsApi } from './api';\n\nimport type { Create, Update, Delete, GetAll } from '../../../shared/contracts/review-workflows';\n\nconst settingsApi = reviewWorkflowsApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getWorkflows: builder.query<\n      {\n        workflows: GetAll.Response['data'];\n        meta?: GetAll.Response['meta'];\n      },\n      GetWorkflowsParams | void\n    >({\n      query: (args) => {\n        return {\n          url: '/review-workflows/workflows',\n          method: 'GET',\n          config: {\n            params: args ?? {},\n          },\n        };\n      },\n      transformResponse: (res: GetAll.Response) => {\n        return {\n          workflows: res.data,\n          meta: 'meta' in res ? res.meta : undefined,\n        };\n      },\n      providesTags: (res) => {\n        return [\n          ...(res?.workflows.map(({ id }) => ({ type: 'ReviewWorkflow' as const, id })) ?? []),\n          { type: 'ReviewWorkflow' as const, id: 'LIST' },\n        ];\n      },\n    }),\n    createWorkflow: builder.mutation<Create.Response['data'], Create.Request['body']>({\n      query: (data) => ({\n        url: '/review-workflows/workflows',\n        method: 'POST',\n        data,\n      }),\n      transformResponse: (res: Create.Response) => res.data,\n      invalidatesTags(res) {\n        return [\n          { type: 'ReviewWorkflow' as const, id: 'LIST' },\n          'ReviewWorkflowStages',\n          { type: 'Document', id: `ALL_LIST` },\n          { type: 'ContentTypeSettings', id: 'LIST' },\n          ...(res?.contentTypes.map((uid) => ({\n            type: 'Document' as const,\n            id: `${uid}_ALL_ITEMS`,\n          })) ?? []),\n        ];\n      },\n    }),\n    updateWorkflow: builder.mutation<\n      Update.Response['data'],\n      Update.Request['body'] & Update.Params\n    >({\n      query: ({ id, ...data }) => ({\n        url: `/review-workflows/workflows/${id}`,\n        method: 'PUT',\n        data,\n      }),\n      transformResponse: (res: Update.Response) => res.data,\n      invalidatesTags: (res, _err, arg) => [\n        { type: 'ReviewWorkflow' as const, id: arg.id },\n        'ReviewWorkflowStages',\n        { type: 'Document', id: 'ALL_LIST' },\n        { type: 'ContentTypeSettings', id: 'LIST' },\n        ...(res?.contentTypes.map((uid) => ({\n          type: 'Document' as const,\n          id: `${uid}_ALL_ITEMS`,\n        })) ?? []),\n      ],\n    }),\n    deleteWorkflow: builder.mutation<Delete.Response['data'], Delete.Params>({\n      query: ({ id }) => ({\n        url: `/review-workflows/workflows/${id}`,\n        method: 'DELETE',\n      }),\n      transformResponse: (res: Delete.Response) => res.data,\n      invalidatesTags: (res, _err, arg) => [\n        { type: 'ReviewWorkflow' as const, id: arg.id },\n        'ReviewWorkflowStages',\n        { type: 'Document', id: `ALL_LIST` },\n        { type: 'ContentTypeSettings', id: 'LIST' },\n      ],\n    }),\n  }),\n  overrideExisting: false,\n});\n\ntype GetWorkflowsParams = GetAll.Request['query'];\n\nconst {\n  useGetWorkflowsQuery,\n  useCreateWorkflowMutation,\n  useDeleteWorkflowMutation,\n  useUpdateWorkflowMutation,\n} = settingsApi;\n\nexport {\n  useGetWorkflowsQuery,\n  useCreateWorkflowMutation,\n  useDeleteWorkflowMutation,\n  useUpdateWorkflowMutation,\n  type GetWorkflowsParams,\n};\n", "import { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, Typography } from '@strapi/design-system';\n\nimport { STAGE_COLOR_DEFAULT } from '../../../../constants';\nimport { getStageColorByHex } from '../../../../utils/colors';\nimport { getDisplayName } from '../../../../utils/users';\n\ninterface StageColumnProps {\n  documentId?: string;\n  id?: number;\n  strapi_stage?: {\n    color?: string;\n    name: string;\n  };\n}\n\nconst StageColumn = (props: StageColumnProps) => {\n  const { color = STAGE_COLOR_DEFAULT, name } = props.strapi_stage ?? {};\n  const { themeColorName } = getStageColorByHex(color) ?? {};\n\n  return (\n    <Flex alignItems=\"center\" gap={2} maxWidth=\"30rem\">\n      <Box\n        height={2}\n        background={color}\n        borderColor={themeColorName === 'neutral0' ? 'neutral150' : undefined}\n        hasRadius\n        shrink={0}\n        width={2}\n      />\n\n      <Typography fontWeight=\"regular\" textColor=\"neutral700\" ellipsis>\n        {name}\n      </Typography>\n    </Flex>\n  );\n};\n\ninterface AssigneeColumnProps {\n  documentId?: string;\n  id?: number;\n  strapi_assignee?: Pick<\n    SanitizedAdminUser,\n    'firstname' | 'lastname' | 'username' | 'email'\n  > | null;\n}\n\nconst AssigneeColumn = (props: AssigneeColumnProps) => {\n  const { strapi_assignee: user } = props;\n  return <Typography textColor=\"neutral800\">{user ? getDisplayName(user) : '-'}</Typography>;\n};\n\nexport { StageColumn, AssigneeColumn };\nexport type { StageColumnProps, AssigneeColumnProps };\n", "import { ASSIGNEE_ATTRIBUTE_NAME, STAGE_ATTRIBUTE_NAME } from './[id]/components/constants';\nimport { AssigneeFilter } from './components/AssigneeFilter';\nimport { StageFilter } from './components/StageFilter';\nimport { AssigneeColumn, StageColumn } from './components/TableColumns';\n\nimport type { Filters } from '@strapi/admin/strapi-admin';\nimport type { ListFieldLayout } from '@strapi/content-manager/strapi-admin';\nimport type { MessageDescriptor } from 'react-intl';\n\nexport const REVIEW_WORKFLOW_COLUMNS = [\n  {\n    name: STAGE_ATTRIBUTE_NAME,\n    attribute: {\n      type: 'relation',\n      relation: 'oneToMany',\n      target: 'admin::review-workflow-stage',\n    },\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.stage',\n      defaultMessage: 'Review stage',\n    },\n    searchable: false,\n    sortable: true,\n    mainField: {\n      name: 'name',\n      type: 'string',\n    },\n    cellFormatter: (props) => <StageColumn {...props} />,\n  },\n  {\n    name: ASSIGNEE_ATTRIBUTE_NAME,\n    attribute: {\n      type: 'relation',\n      target: 'admin::user',\n      relation: 'oneToMany',\n    },\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.assignee',\n      defaultMessage: 'Assignee',\n    },\n    searchable: false,\n    sortable: true,\n    mainField: {\n      name: 'firstname',\n      type: 'string',\n    },\n    cellFormatter: (props) => <AssigneeColumn {...props} />,\n  },\n] satisfies Array<Omit<ListFieldLayout, 'label'> & { label: MessageDescriptor }>;\n\nexport const REVIEW_WORKFLOW_FILTERS = [\n  {\n    mainField: {\n      name: 'name',\n      type: 'string',\n    },\n    input: StageFilter,\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.stage',\n      defaultMessage: 'Review stage',\n    },\n    name: 'strapi_stage',\n    type: 'relation',\n  },\n\n  {\n    type: 'relation',\n    mainField: {\n      name: 'id',\n      type: 'integer',\n    },\n    input: AssigneeFilter,\n    operators: [\n      {\n        label: {\n          id: 'components.FilterOptions.FILTER_TYPES.$eq',\n          defaultMessage: 'is',\n        },\n        value: '$eq',\n      },\n      {\n        label: {\n          id: 'components.FilterOptions.FILTER_TYPES.$ne',\n          defaultMessage: 'is not',\n        },\n        value: '$ne',\n      },\n    ],\n    label: {\n      id: 'review-workflows.containers.list.table-headers.reviewWorkflows.assignee.label',\n      defaultMessage: 'Assignee',\n    },\n    name: 'strapi_assignee',\n  },\n] satisfies Array<\n  Omit<Filters.Filter, 'label' | 'operators'> & {\n    label: MessageDescriptor;\n    operators?: Array<{ value: string; label: MessageDescriptor }>;\n  }\n>;\n", "/* eslint-disable check-file/filename-naming-convention */\n\nimport { REVIEW_WORKFLOW_COLUMNS } from '../routes/content-manager/[model]/constants';\n\nimport type { ListFieldLayout, ListLayout } from '@strapi/content-manager/strapi-admin';\n\n/* -------------------------------------------------------------------------------------------------\n * addColumnToTableHook\n * -----------------------------------------------------------------------------------------------*/\ninterface AddColumnToTableHookArgs {\n  layout: ListLayout;\n  displayedHeaders: ListFieldLayout[];\n}\n\nconst addColumnToTableHook = ({ displayedHeaders, layout }: AddColumnToTableHookArgs) => {\n  const { options } = layout;\n\n  if (!options.reviewWorkflows) {\n    return { displayedHeaders, layout };\n  }\n\n  return {\n    displayedHeaders: [...displayedHeaders, ...REVIEW_WORKFLOW_COLUMNS],\n    layout,\n  };\n};\n\nexport { addColumnToTableHook };\n", "type TradOptions = Record<string, string>;\n\nconst prefixPluginTranslations = (trad: TradOptions, pluginId: string): TradOptions => {\n  if (!pluginId) {\n    throw new TypeError(\"pluginId can't be empty\");\n  }\n  return Object.keys(trad).reduce((acc, current) => {\n    acc[`${pluginId}.${current}`] = trad[current];\n    return acc;\n  }, {} as TradOptions);\n};\n\nexport { prefixPluginTranslations };\n", "import { PLUGIN_ID, FEATURE_ID } from './constants';\nimport { Panel } from './routes/content-manager/[model]/[id]/components/Panel';\nimport { addColumnToTableHook } from './utils/cm-hooks';\nimport { prefixPluginTranslations } from './utils/translations';\n\nimport type { StrapiApp } from '@strapi/admin/strapi-admin';\nimport type { Plugin } from '@strapi/types';\n\nconst admin: Plugin.Config.AdminInput = {\n  register(app: StrapiApp) {\n    if (window.strapi.features.isEnabled(FEATURE_ID)) {\n      app.registerHook('Admin/CM/pages/ListView/inject-column-in-table', addColumnToTableHook);\n\n      const contentManagerPluginApis = app.getPlugin('content-manager').apis;\n\n      if (\n        'addEditViewSidePanel' in contentManagerPluginApis &&\n        typeof contentManagerPluginApis.addEditViewSidePanel === 'function'\n      ) {\n        contentManagerPluginApis.addEditViewSidePanel([Panel]);\n      }\n\n      app.addSettingsLink('global', {\n        id: PLUGIN_ID,\n        to: `review-workflows`,\n        intlLabel: {\n          id: `${PLUGIN_ID}.plugin.name`,\n          defaultMessage: 'Review Workflows',\n        },\n        permissions: [],\n        async Component() {\n          const { Router } = await import('./router');\n          return { default: Router };\n        },\n      });\n    } else if (!window.strapi.features.isEnabled(FEATURE_ID) && window.strapi?.flags?.promoteEE) {\n      app.addSettingsLink('global', {\n        id: PLUGIN_ID,\n        to: `purchase-review-workflows`,\n        intlLabel: {\n          id: `${PLUGIN_ID}.plugin.name`,\n          defaultMessage: 'Review Workflows',\n        },\n        licenseOnly: true,\n        permissions: [],\n        async Component() {\n          const { PurchaseReviewWorkflows } = await import('./routes/purchase-review-workflows');\n          return { default: PurchaseReviewWorkflows };\n        },\n      });\n    }\n  },\n  async registerTrads({ locales }: { locales: string[] }) {\n    const importedTrads = await Promise.all(\n      locales.map((locale) => {\n        return import(`./translations/${locale}.json`)\n          .then(({ default: data }) => {\n            return {\n              data: prefixPluginTranslations(data, PLUGIN_ID),\n              locale,\n            };\n          })\n          .catch(() => {\n            return {\n              data: {},\n              locale,\n            };\n          });\n      })\n    );\n\n    return Promise.resolve(importedTrads);\n  },\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default admin;\n"], "names": ["error", "id", "themeColorName", "useDocumentLayout"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,YAAY;AAIlB,MAAM,aAAa;AAEZ,MAAM,sCAAsC;AAC5C,MAAM,iDAAiD;AAEjD,MAAA,sBAAsB,WAAW,OAAO;ACHrD,MAAM,mBAAoD;ACNpD,MAAA,qBAAqB,SAAS,iBAAiB;AAAA,EACnD,aAAa,CAAC,kBAAkB,wBAAwB,YAAY,qBAAqB;AAC3F,CAAC;ACYD,MAAM,eAAe;AAErB,MAAM,oBAAoB,mBAAmB,gBAAgB;AAAA,EAC3D,WAAW,CAAC,aAAa;AAAA,IACvB,WAAW,QAAQ,MAMjB;AAAA,MACA,OAAO,CAAC,EAAE,OAAO,MAAM,IAAI,cAAc;AAAA,QACvC,KAAK,qCAAqC,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,QAC7D,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MAAA;AAAA,MAEF,mBAAmB,CAAC,QAA4B;AACvC,eAAA;AAAA,UACL,MAAM,IAAI,QAAQ,EAAE,eAAe,EAAE;AAAA,UACrC,QAAQ,IAAI,QAAQ,CAAC;AAAA,QAAA;AAAA,MAEzB;AAAA,MACA,cAAc,CAAC,sBAAsB;AAAA,IAAA,CACtC;AAAA,IACD,aAAa,QAAQ,SAGnB;AAAA,MACA,OAAO,CAAC,EAAE,OAAO,MAAM,IAAI,QAAQ,GAAG,YAAY;AAAA,QAChD,KAAK,qCAAqC,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,QAC7D,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MAAA;AAAA,MAEF,mBAAmB,CAAC,QAA8B,IAAI;AAAA,MACtD,iBAAiB,CAAC,SAAS,QAAQ,EAAE,MAAM,IAAI,YAAY;AAClD,eAAA;AAAA,UACL;AAAA,YACE,MAAM;AAAA,YACN,IAAI,SAAS,eAAe,GAAG,KAAK,IAAI,EAAE,KAAK;AAAA,UACjD;AAAA,UACA,EAAE,MAAM,YAAY,IAAI,GAAG,KAAK,QAAQ;AAAA,UACxC;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA,CACD;AAAA,IACD,gBAAgB,QAAQ,SAGtB;AAAA,MACA,OAAO,CAAC,EAAE,OAAO,MAAM,IAAI,QAAQ,GAAG,YAAY;AAAA,QAChD,KAAK,qCAAqC,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,QAC7D,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MAAA;AAAA,MAEF,mBAAmB,CAAC,QAAiC,IAAI;AAAA,MACzD,iBAAiB,CAAC,SAAS,QAAQ,EAAE,MAAM,IAAI,YAAY;AAClD,eAAA;AAAA,UACL;AAAA,YACE,MAAM;AAAA,YACN,IAAI,SAAS,eAAe,GAAG,KAAK,IAAI,EAAE,KAAK;AAAA,UACjD;AAAA,UACA,EAAE,MAAM,YAAY,IAAI,GAAG,KAAK,QAAQ;AAAA,QAAA;AAAA,MAE5C;AAAA,IAAA,CACD;AAAA,IACD,iBAAiB,QAAQ,MAA0B;AAAA,MACjD,OAAO,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,MAAA;AAAA,MAEV,mBAAmB,CAAC,QAAwD;AAC1E,eAAO,IAAI,KAAK;AAAA,UACd,CAAC,KAAK,SAAS;AACb,gBAAI,KAAK,aAAa;AACpB,kBAAI,KAAK,IAAI,EAAE,KAAK,IAAI;AAAA,YAC1B;AACO,mBAAA;AAAA,UACT;AAAA,UACA;AAAA,YACE,gBAAgB,CAAC;AAAA,YACjB,YAAY,CAAC;AAAA,UACf;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA,CACD;AAAA,EAAA;AAAA,EAEH,kBAAkB;AACpB,CAAC;AAEK,MAAA;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;ACjHE,MAAA,mBAAmB,CAAC,UAA+D;AACvF,SAAO,MAAM,SAAS;AACxB;AAqBA,MAAM,mBAAmB,CAAuB,UAA4C;AAC1F,MAAI,CAAC;AAAc,WAAA;AAGnB,QAAM,EAAE,SAAS,GAAG,GAAG,qBAAqB;AAAA,IAC1C,GAAG;AAAA,IACH,GAAG,OAAO,OAAO,OAAO,WAAW,CAAA,CAAE,EAAE;AAAA,MACrC,CAAC,KAAK,YAAY,OAAO,OAAO,KAAK,OAAO;AAAA,MAC5C,CAAC;AAAA,IACH;AAAA,EAAA;AAGF,MAAI,QAAQ,kBAAkB;AAGX,qBAAA,KAAK,mBAAmB,iBAAiB,EAAE;AAAA,EAC9D;AAEO,SAAA;AACT;AC1CA,MAAM,iBAAiB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAEI,OAAe;AACjB,MAAI,UAAU;AACL,WAAA;AAAA,EACT;AAGA,MAAI,WAAW;AACb,WAAO,GAAG,SAAS,IAAI,YAAY,EAAE,GAAG;EAC1C;AAEA,SAAO,SAAS;AAClB;ACvBO,MAAM,uBAAuB;AAC7B,MAAM,0BAA0B;ACoBvC,MAAM,iBAAiB,MAAM;AACrB,QAAA;AAAA,IACJ,iBAAiB;AAAA,IACjB;AAAA,IACA,MAAM,QAAQ;AAAA,MACZ,UAAgE;AACpE,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,WAAW;AACrE,QAAA,EAAE,kBAAkB;AAC1B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AACjE,QAAA,EAAE,uBAAuB;AACzB,QAAA;AAAA,IACJ,gBAAgB,EAAE,QAAQ;AAAA,IAC1B,WAAW;AAAA,EACT,IAAA,QAAQ,YAAY,UAAU,KAAK;AACvC,QAAM,CAAC,EAAE,OAAO,IAAI,eAAe;AAC7B,QAAA,SAAS,MAAM,QAAQ,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,CAAC;AACnE,QAAM,EAAE,MAAM,WAAW,QAAQ,IAAI,cAAc,QAAW;AAAA,IAC5D,MAAM,wBAAwB,CAAC;AAAA,EAAA,CAChC;AACK,QAAA,EAAE,aAAa;AAAA,IACnB;AAAA,MACE;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,MAAM,CAAC,MAAM,mBAAmB;AAAA,IAClC;AAAA,EAAA;AAGI,QAAA,QAAQ,MAAM,SAAS;AAE7B,QAAM,kBAAkB,WAAW,SAAS,uBAAuB,IAAI;AAEjE,QAAA,CAAC,gBAAgB,EAAE,OAAO,WAAW,WAAY,CAAA,IAAI;AAE3D,MAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,UAAU,YAAY;AAC/C,WAAA;AAAA,EACT;AAEM,QAAA,eAAe,OAAO,eAA8B;AAEpD,QAAA,iBAAiB,OAAO,YAAY;AACtC;AAAA,IACF;AAEM,UAAA,MAAM,MAAM,eAAe;AAAA,MAC/B,MAAM;AAAA,MACN;AAAA,MACA,IAAI,SAAS;AAAA,MACb;AAAA,MACA,MAAM;AAAA,QACJ,IAAI,aAAa,SAAS,YAAY,EAAE,IAAI;AAAA,MAC9C;AAAA,IAAA,CACD;AAED,QAAI,UAAU,KAAK;AACE,yBAAA;AAAA,QACjB,MAAM;AAAA,QACN,SAAS,cAAc;AAAA,UACrB,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAAA,CACjB;AAAA,MAAA,CACF;AAAA,IACH;AAAA,EAAA;AAIA,SAAA;AAAA,IAAC,MAAM;AAAA,IAAN;AAAA,MACC,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,QACI,WACA,WACA,cAAc;AAAA,QACZ,IAAI;AAAA,QACJ,gBAAgB;AAAA,MACjB,CAAA,KACA,SAAS,eAAe,KAAK,MAChC;AAAA,MAGF,UAAA;AAAA,QAAC,oBAAA,MAAM,OAAN,EACE,UAAc,cAAA;AAAA,UACb,IAAI;AAAA,UACJ,gBAAgB;AAAA,QACjB,CAAA,GACH;AAAA,QACA;AAAA,UAAC;AAAA,UAAA;AAAA,YACC,YAAY,cAAc;AAAA,cACxB,IAAI;AAAA,cACJ,gBAAgB;AAAA,YAAA,CACjB;AAAA,YACD,UACG,CAAC,wBAAwB,CAAC,aAAa,MAAM,WAAW,KAAM,CAAC,SAAS;AAAA,YAE3E,OAAO,kBAAkB,gBAAgB,GAAG,SAAa,IAAA;AAAA,YACzD,UAAU;AAAA,YACV,SAAS,MAAM,aAAa,IAAI;AAAA,YAChC,aAAa,cAAc;AAAA,cACzB,IAAI;AAAA,cACJ,gBAAgB;AAAA,YAAA,CACjB;AAAA,YACD,SAAS,aAAa,wBAAwB;AAAA,YAE7C,UAAA,MAAM,IAAI,CAAC,SAAS;AAEjB,qBAAA;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBAEC,OAAO,KAAK,GAAG,SAAS;AAAA,kBACxB,WAAW,eAAe,IAAI;AAAA,kBAE7B,yBAAe,IAAI;AAAA,gBAAA;AAAA,gBAJf,KAAK;AAAA,cAAA;AAAA,YAKZ,CAEH;AAAA,UAAA;AAAA,QACH;AAAA,QACA,oBAAC,MAAM,OAAN,EAAY;AAAA,MAAA;AAAA,IAAA;AAAA,EAAA;AAGnB;AC7IA,MAAe,kBAAA;ACSf,MAAM,sBAAsB;AAC5B,MAAM,iBAAiB;AAEvB,MAAM,QAA2C,CAAC,EAAE,eAAe;AACjE,6BAAQ,MAAM,OAAN,EAAY,SAAQ,SAAS,SAAS,CAAA;AAChD;AAEA,MAAM,OAA0C,CAAC,EAAE,eAAe;AAChE,SAAQ,oBAAA,YAAA,EAAW,SAAQ,SAAS,SAAS,CAAA;AAC/C;AAEA,MAAM,gBAAgB,MAAM;AACpB,QAAA,EAAE,kBAAkB;AAE1B,SACG,qBAAA,MAAA,EAAK,KAAK,GAAG,YAAY,GACxB,UAAA;AAAA,IAAA,oBAAC,cAAW,SAAQ,WAAU,YAAU,MAAC,MAAM,qBAC5C,UAAc,cAAA;AAAA,MACb,IAAI;AAAA,MACJ,gBAAgB;AAAA,IACjB,CAAA,GACH;AAAA,IAEA,oBAAC,cAAW,SAAQ,YAAW,YAAU,MAAC,MAAM,gBAC7C,UAAc,cAAA;AAAA,MACb,IAAI;AAAA,MACJ,gBAAgB;AAAA,IACjB,CAAA,GACH;AAAA,EACF,EAAA,CAAA;AAEJ;AAEA,MAAM,eAAe,OAAO;AAAA;AAAA,kBAEV,CAAC,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC,EAAE;AAAA,gBACtC,CAAC,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC,EAAE;AAAA;AAAA;AAMpD,MAAM,OAA4D,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AAAA,EACP;AACF,MAAM;AACE,QAAA,EAAE,kBAAkB;AAGxB,SAAA,oBAAC,MAAM,MAAN,EAAW,MAAY,cACtB,UAAA,oBAAC,MAAM,SAAN,EACC,8BAAC,MAAM,MAAN,EACC,UAAC,qBAAA,MAAA,EAAK,KAAK,GAAG,aAAa,GAAG,UAAS,YACrC,UAAA;AAAA,IAAC,qBAAA,MAAA,EAAK,YAAW,SAAQ,WAAU,UAAS,KAAK,GAAG,OAAM,OACvD,UAAA;AAAA,MAAA;AAAA,0BAEA,eAAc,EAAA;AAAA,IAAA,GACjB;AAAA,yBAEC,MAAK,EAAA,gBAAe,OAAM,QAAO,QAAO,OAAM,OAC7C,UAAA;AAAA,MAAC,oBAAA,cAAA,EAAa,KAAK,iBAAiB,eAAW,MAAC,KAAI,IAAG,SAAQ,OAAO,CAAA;AAAA,MAErE,oBAAA,KAAA,EAAI,SAAQ,QAAO,UAAS,YAAW,OAAO,GAAG,KAAK,GACrD,UAAC,oBAAA,MAAM,OAAN,EACC,UAAA;AAAA,QAAC;AAAA,QAAA;AAAA,UACC,aAAa;AAAA,UACb,OAAO,cAAc;AAAA,YACnB,IAAI;AAAA,YACJ,gBAAgB;AAAA,UAAA,CACjB;AAAA,UAED,8BAAC,OAAM,EAAA;AAAA,QAAA;AAAA,SAEX,EACF,CAAA;AAAA,IAAA,GACF;AAAA,EACF,EAAA,CAAA,EACF,CAAA,GACF,EACF,CAAA;AAEJ;AAEA,MAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF;AC9FA,MAAM,eAAuC;AAAA,EAC3C,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AACZ;AAEM,MAAA,qBAAqB,CAAC,QAAiB;AAC3C,MAAI,CAAC,KAAK;AACD,WAAA;AAAA,EACT;AAKA,QAAM,cAAyD,OAAO;AAAA,IACpE,WAAW;AAAA,EAAA,EACX,OAAO,CAAC,CAAG,EAAA,KAAK,MAAM,MAAM,kBAAkB,IAAI,YAAA,CAAa;AAEjE,QAAM,iBAAiB,YAAY;AAAA,IACjC,CAAC,KAAK,CAAC,IAAI,MAAM;AACX,UAAA,eAAe,IAAI,GAAG;AAClB,cAAA;AAAA,MACR;AAEO,aAAA;AAAA,IACT;AAAA,IACA;AAAA,EAAA;AAGF,MAAI,CAAC,gBAAgB;AACZ,WAAA;AAAA,EACT;AAEO,SAAA;AAAA,IACL;AAAA,IACA,MAAM,aAAa,cAAc;AAAA,EAAA;AAErC;AAEM,MAAA,mBAAmB,OAAO,QAAQ,YAAY,EAAE,IAAI,CAAC,CAAC,gBAAgB,IAAI,OAAO;AAAA,EACrF,KAAK,WAAW,OAAO,cAA8C,EAAE,YAAY;AAAA,EACnF;AACF,EAAE;AC3BK,MAAM,cAAc,MAAM;AACzB,QAAA;AAAA,IACJ,iBAAiB;AAAA,IACjB,MAAM,QAAQ;AAAA,IACd,KAAK;AAAA,MACH,UAID;AACG,QAAA,EAAE,kBAAkB;AAC1B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AACjE,QAAA,EAAE,uBAAuB;AAC/B,QAAM,CAAC,EAAE,OAAO,IAAI,eAAe;AAC7B,QAAA,SAAS,MAAM,QAAQ,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,CAAC;AACnE,QAAM,EAAE,UAAU,WAAW,kBAAsB,IAAA;AAAA,IACjD;AAAA,MACE;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AAAA,IACA;AAAA,MACE,MAAM,CAAC,MAAM,mBAAmB;AAAA,IAClC;AAAA,EAAA;AAGF,QAAM,EAAE,MAAM,WAAW,gBAAoB,IAAA;AAAA,IAC3C;AAAA,MACE,MAAM;AAAA,MACN;AAAA;AAAA,MAEA,IAAI,UAAU;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,MAAM,CAAC,UAAU;AAAA,IACnB;AAAA,EAAA;AAGF,QAAM,EAAE,MAAM,SAAS,CAAG,EAAA,IAAI,QAAQ,CAAA;AAEhC,QAAA,EAAE,eAAe;AACvB,QAAM,CAAC,gBAAgB,iBAAiB,IAAI,MAAM,SAAsC,IAAI;AAE5F,QAAM,SAAS,WAAmB,kBAAkB,KAAK,CAAA;AAEzD,QAAM,sBAAsB,WAAW,SAAS,oBAAoB,IAAI;AAExE,QAAM,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,uBAAuB;AAElD,QAAA,eAAe,OAAO,YAAqB;AAC3C,QAAA;AAYA,UAAA,SAAS,mCAAmC,KAC5C,SAAS,OAAO,mCAAmC,GAAG,EAAE,KAAK,MAAM,iBAAiB,IACpF;AACA,0BAAkB,UAAU;AAAA,MAW9B,WACE,SAAS,8CAA8C,KACvD,SAAS,OAAO,8CAA8C,GAAG,EAAE,IAAI,OAAO,QAC9E;AACA,0BAAkB,OAAO;AAAA,MAAA,OACpB;AACL,YAAI,UAAU,YAAY;AAClB,gBAAA,MAAM,MAAM,YAAY;AAAA,YAC5B;AAAA,YACA,IAAI,SAAS;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA,MAAM,EAAE,IAAI,QAAQ;AAAA,UAAA,CACrB;AAED,cAAI,UAAU,KAAK;AACE,+BAAA;AAAA,cACjB,MAAM;AAAA,cACN,SAAS,cAAc;AAAA,gBACrB,IAAI;AAAA,gBACJ,gBAAgB;AAAA,cAAA,CACjB;AAAA,YAAA,CACF;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,aACOA,QAAO;AACK,yBAAA;AAAA,QACjB,MAAM;AAAA,QACN,SAAS,cAAc;AAAA,UACrB,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAAA,CACjB;AAAA,MAAA,CACF;AAAA,IACH;AAAA,EAAA;AAGF,QAAM,EAAE,eAAe,IAAI,mBAAmB,qBAAqB,KAAK,KAAK;AAE7E,QAAM,YAAY,mBAAmB;AAErC,SAEI,qBAAA,UAAA,EAAA,UAAA;AAAA,IAAA;AAAA,MAAC,MAAM;AAAA,MAAN;AAAA,QACC,MACE,CAAC,aACD,OAAO,WAAW,KAClB,cAAc;AAAA,UACZ,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAAA,CACjB;AAAA,QAEH,OAAQ,SAAS,eAAe,KAAK,KAAM;AAAA,QAC3C,MAAM;AAAA,QACN,IAAI;AAAA,QAEJ,UAAA;AAAA,UAAC,oBAAA,MAAM,OAAN,EACE,UAAc,cAAA;AAAA,YACb,IAAI;AAAA,YACJ,gBAAgB;AAAA,UACjB,CAAA,GACH;AAAA,UACA;AAAA,YAAC;AAAA,YAAA;AAAA,cACC,UAAU,OAAO,WAAW;AAAA,cAC5B,OAAO,qBAAqB;AAAA,cAC5B,UAAU;AAAA,cACV,aAAa,cAAc;AAAA,gBACzB,IAAI;AAAA,gBACJ,gBAAgB;AAAA,cAAA,CACjB;AAAA,cACD,WACE,uBACE;AAAA,gBAAC;AAAA,gBAAA;AAAA,kBACC,KAAI;AAAA,kBACJ,QAAQ;AAAA,kBACR,YAAY,qBAAqB;AAAA,kBACjC,aAAa,mBAAmB,aAAa,eAAe;AAAA,kBAC5D,WAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,OAAO;AAAA,kBACP,aAAY;AAAA,gBAAA;AAAA,cACd;AAAA,cAIJ,kBAAkB,MAAM;AAEpB,uBAAA,qBAAC,QAAK,KAAI,QAAO,gBAAe,iBAAgB,YAAW,UAAS,OAAM,QACxE,UAAA;AAAA,kBAAA,oBAAC,cAAW,WAAU,cAAa,UAAQ,MACxC,UAAA,qBAAqB,QAAQ,GAChC,CAAA;AAAA,kBACC,YACC,oBAAC,QAAO,EAAA,OAAK,MAAC,OAAO,EAAE,SAAS,OAAO,GAAG,eAAY,SAAA,CAAS,IAC7D;AAAA,gBACN,EAAA,CAAA;AAAA,cAEJ;AAAA,cAEC,UAAA,OAAO,IAAI,CAAC,EAAE,IAAAC,KAAI,OAAO,WAAW;AACnC,sBAAM,EAAE,gBAAAC,gBAAAA,IAAmB,mBAAmB,KAAK,KAAK;AAGtD,uBAAA;AAAA,kBAAC;AAAA,kBAAA;AAAA,oBAEC,WACE;AAAA,sBAAC;AAAA,sBAAA;AAAA,wBACC,QAAQ;AAAA,wBACR,YAAY;AAAA,wBACZ,aAAaA,oBAAmB,aAAa,eAAe;AAAA,wBAC5D,WAAS;AAAA,wBACT,QAAQ;AAAA,wBACR,OAAO;AAAA,sBAAA;AAAA,oBACT;AAAA,oBAEF,OAAOD;AAAAA,oBACP,WAAW;AAAA,oBAEV,UAAA;AAAA,kBAAA;AAAA,kBAdIA;AAAAA,gBAAA;AAAA,cAeP,CAEH;AAAA,YAAA;AAAA,UACH;AAAA,UACA,oBAAC,MAAM,MAAN,EAAW;AAAA,UACZ,oBAAC,MAAM,OAAN,EAAY;AAAA,QAAA;AAAA,MAAA;AAAA,IACf;AAAA,IAEA;AAAA,MAAC,YAAY;AAAA,MAAZ;AAAA,QACC,MAAM,mBAAmB;AAAA,QACzB,cAAc,MAAM,kBAAkB,IAAI;AAAA,QAE1C,UAAA;AAAA,UAAC,oBAAA,YAAY,OAAZ,EACE,UAAc,cAAA;AAAA,YACb,IAAI;AAAA,YACJ,gBAAgB;AAAA,UACjB,CAAA,GACH;AAAA,UAEC,oBAAA,YAAY,MAAZ,EACE,UAAc,cAAA;AAAA,YACb,IAAI;AAAA,YACJ,gBAAgB;AAAA,UACjB,CAAA,GACH;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,IAEA;AAAA,MAAC,YAAY;AAAA,MAAZ;AAAA,QACC,MAAM,mBAAmB;AAAA,QACzB,cAAc,MAAM,kBAAkB,IAAI;AAAA,QAE1C,UAAA;AAAA,UAAC,oBAAA,YAAY,OAAZ,EACE,UAAc,cAAA;AAAA,YACb,IAAI;AAAA,YACJ,gBAAgB;AAAA,UACjB,CAAA,GACH;AAAA,UAEC,oBAAA,YAAY,MAAZ,EACE,UAAc,cAAA;AAAA,YACb,IAAI;AAAA,YACJ,gBAAgB;AAAA,UACjB,CAAA,GACH;AAAA,QAAA;AAAA,MAAA;AAAA,IACF;AAAA,EACF,EAAA,CAAA;AAEJ;ACrQA,MAAM,QAAwB,MAAM;AAC5B,QAAA;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA;AAAA,MACE,UAID;AAEG,QAAA;AAAA,IACJ,MAAM,EAAE,QAAQ;AAAA,EAAA,IACdE,2BAAkB,IAAI;AACpB,QAAA,EAAE,kBAAkB;AAE1B,MACE,CAAC,OAAO,OAAO,QACf,CAAC,SAAS,mBACT,mBAAmB,kBAAkB,CAAC,MACvC,OAAO,UACP;AACO,WAAA;AAAA,EACT;AAEO,SAAA;AAAA,IACL,OAAO,cAAc;AAAA,MACnB,IAAI;AAAA,MACJ,gBAAgB;AAAA,IAAA,CACjB;AAAA,IACD,SACG,qBAAA,MAAA,EAAK,WAAU,UAAS,KAAK,GAAG,YAAW,WAAU,OAAM,QAC1D,UAAA;AAAA,MAAA,oBAAC,gBAAe,EAAA;AAAA,0BACf,aAAY,EAAA;AAAA,IAAA,GACf;AAAA,EAAA;AAGN;AAGA,MAAM,OAAO;AC9Cb,MAAM,cAAc,mBAAmB,gBAAgB;AAAA,EACrD,WAAW,CAAC,aAAa;AAAA,IACvB,cAAc,QAAQ,MAMpB;AAAA,MACA,OAAO,CAAC,SAAS;AACR,eAAA;AAAA,UACL,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,QAAQ,QAAQ,CAAC;AAAA,UACnB;AAAA,QAAA;AAAA,MAEJ;AAAA,MACA,mBAAmB,CAAC,QAAyB;AACpC,eAAA;AAAA,UACL,WAAW,IAAI;AAAA,UACf,MAAM,UAAU,MAAM,IAAI,OAAO;AAAA,QAAA;AAAA,MAErC;AAAA,MACA,cAAc,CAAC,QAAQ;AACd,eAAA;AAAA,UACL,GAAI,KAAK,UAAU,IAAI,CAAC,EAAE,GAAA,OAAU,EAAE,MAAM,kBAA2B,GAAG,EAAE,KAAK,CAAC;AAAA,UAClF,EAAE,MAAM,kBAA2B,IAAI,OAAO;AAAA,QAAA;AAAA,MAElD;AAAA,IAAA,CACD;AAAA,IACD,gBAAgB,QAAQ,SAA0D;AAAA,MAChF,OAAO,CAAC,UAAU;AAAA,QAChB,KAAK;AAAA,QACL,QAAQ;AAAA,QACR;AAAA,MAAA;AAAA,MAEF,mBAAmB,CAAC,QAAyB,IAAI;AAAA,MACjD,gBAAgB,KAAK;AACZ,eAAA;AAAA,UACL,EAAE,MAAM,kBAA2B,IAAI,OAAO;AAAA,UAC9C;AAAA,UACA,EAAE,MAAM,YAAY,IAAI,WAAW;AAAA,UACnC,EAAE,MAAM,uBAAuB,IAAI,OAAO;AAAA,UAC1C,GAAI,KAAK,aAAa,IAAI,CAAC,SAAS;AAAA,YAClC,MAAM;AAAA,YACN,IAAI,GAAG,GAAG;AAAA,UACZ,EAAE,KAAK,CAAC;AAAA,QAAA;AAAA,MAEZ;AAAA,IAAA,CACD;AAAA,IACD,gBAAgB,QAAQ,SAGtB;AAAA,MACA,OAAO,CAAC,EAAE,IAAI,GAAG,YAAY;AAAA,QAC3B,KAAK,+BAA+B,EAAE;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MAAA;AAAA,MAEF,mBAAmB,CAAC,QAAyB,IAAI;AAAA,MACjD,iBAAiB,CAAC,KAAK,MAAM,QAAQ;AAAA,QACnC,EAAE,MAAM,kBAA2B,IAAI,IAAI,GAAG;AAAA,QAC9C;AAAA,QACA,EAAE,MAAM,YAAY,IAAI,WAAW;AAAA,QACnC,EAAE,MAAM,uBAAuB,IAAI,OAAO;AAAA,QAC1C,GAAI,KAAK,aAAa,IAAI,CAAC,SAAS;AAAA,UAClC,MAAM;AAAA,UACN,IAAI,GAAG,GAAG;AAAA,QACZ,EAAE,KAAK,CAAC;AAAA,MACV;AAAA,IAAA,CACD;AAAA,IACD,gBAAgB,QAAQ,SAAiD;AAAA,MACvE,OAAO,CAAC,EAAE,UAAU;AAAA,QAClB,KAAK,+BAA+B,EAAE;AAAA,QACtC,QAAQ;AAAA,MAAA;AAAA,MAEV,mBAAmB,CAAC,QAAyB,IAAI;AAAA,MACjD,iBAAiB,CAAC,KAAK,MAAM,QAAQ;AAAA,QACnC,EAAE,MAAM,kBAA2B,IAAI,IAAI,GAAG;AAAA,QAC9C;AAAA,QACA,EAAE,MAAM,YAAY,IAAI,WAAW;AAAA,QACnC,EAAE,MAAM,uBAAuB,IAAI,OAAO;AAAA,MAC5C;AAAA,IAAA,CACD;AAAA,EAAA;AAAA,EAEH,kBAAkB;AACpB,CAAC;AAIK,MAAA;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;ACpFJ,MAAM,cAAc,CAAC,UAA4B;AAC/C,QAAM,EAAE,QAAQ,qBAAqB,KAAS,IAAA,MAAM,gBAAgB;AACpE,QAAM,EAAE,eAAe,IAAI,mBAAmB,KAAK,KAAK,CAAA;AAExD,8BACG,MAAK,EAAA,YAAW,UAAS,KAAK,GAAG,UAAS,SACzC,UAAA;AAAA,IAAA;AAAA,MAAC;AAAA,MAAA;AAAA,QACC,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa,mBAAmB,aAAa,eAAe;AAAA,QAC5D,WAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MAAA;AAAA,IACT;AAAA,IAEA,oBAAC,cAAW,YAAW,WAAU,WAAU,cAAa,UAAQ,MAC7D,UACH,KAAA,CAAA;AAAA,EACF,EAAA,CAAA;AAEJ;AAWA,MAAM,iBAAiB,CAAC,UAA+B;AAC/C,QAAA,EAAE,iBAAiB,KAAS,IAAA;AAC3B,SAAA,oBAAC,cAAW,WAAU,cAAc,iBAAO,eAAe,IAAI,IAAI,IAAI,CAAA;AAC/E;ACzCO,MAAM,0BAA0B;AAAA,EACrC;AAAA,IACE,MAAM;AAAA,IACN,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,IAAI;AAAA,MACJ,gBAAgB;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,eAAe,CAAC,UAAW,oBAAA,aAAA,EAAa,GAAG,OAAO;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,WAAW;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,IAAI;AAAA,MACJ,gBAAgB;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,WAAW;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,eAAe,CAAC,UAAW,oBAAA,gBAAA,EAAgB,GAAG,OAAO;AAAA,EACvD;AACF;AClCA,MAAM,uBAAuB,CAAC,EAAE,kBAAkB,aAAuC;AACjF,QAAA,EAAE,QAAY,IAAA;AAEhB,MAAA,CAAC,QAAQ,iBAAiB;AACrB,WAAA,EAAE,kBAAkB;EAC7B;AAEO,SAAA;AAAA,IACL,kBAAkB,CAAC,GAAG,kBAAkB,GAAG,uBAAuB;AAAA,IAClE;AAAA,EAAA;AAEJ;ACvBA,MAAM,2BAA2B,CAAC,MAAmB,aAAkC;AACrF,MAAI,CAAC,UAAU;AACP,UAAA,IAAI,UAAU,yBAAyB;AAAA,EAC/C;AACA,SAAO,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC,KAAK,YAAY;AAChD,QAAI,GAAG,QAAQ,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO;AACrC,WAAA;AAAA,EACT,GAAG,CAAiB,CAAA;AACtB;ACFA,MAAM,QAAkC;AAAA,EACtC,SAAS,KAAgB;AACvB,QAAI,OAAO,OAAO,SAAS,UAAU,UAAU,GAAG;AAC5C,UAAA,aAAa,kDAAkD,oBAAoB;AAEvF,YAAM,2BAA2B,IAAI,UAAU,iBAAiB,EAAE;AAElE,UACE,0BAA0B,4BAC1B,OAAO,yBAAyB,yBAAyB,YACzD;AACyB,iCAAA,qBAAqB,CAAC,KAAK,CAAC;AAAA,MACvD;AAEA,UAAI,gBAAgB,UAAU;AAAA,QAC5B,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,WAAW;AAAA,UACT,IAAI,GAAG,SAAS;AAAA,UAChB,gBAAgB;AAAA,QAClB;AAAA,QACA,aAAa,CAAC;AAAA,QACd,MAAM,YAAY;AAChB,gBAAM,EAAE,OAAA,IAAW,MAAM,OAAO,uBAAU;AACnC,iBAAA,EAAE,SAAS;QACpB;AAAA,MAAA,CACD;AAAA,IACH,WAAW,CAAC,OAAO,OAAO,SAAS,UAAU,UAAU,KAAK,OAAO,QAAQ,OAAO,WAAW;AAC3F,UAAI,gBAAgB,UAAU;AAAA,QAC5B,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,WAAW;AAAA,UACT,IAAI,GAAG,SAAS;AAAA,UAChB,gBAAgB;AAAA,QAClB;AAAA,QACA,aAAa;AAAA,QACb,aAAa,CAAC;AAAA,QACd,MAAM,YAAY;AAChB,gBAAM,EAAE,wBAAA,IAA4B,MAAM,OAAO,0CAAoC;AAC9E,iBAAA,EAAE,SAAS;QACpB;AAAA,MAAA,CACD;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,cAAc,EAAE,WAAkC;AAChD,UAAA,gBAAgB,MAAM,QAAQ;AAAA,MAClC,QAAQ,IAAI,CAAC,WAAW;AACf,eAAA,qCAA+B,uBAAA,OAAA,EAAA,0BAAA,MAAA,OAAA,mBAAA,EAAA,CAAA,GAAA,kBAAA,MAAA,OAAA,EACnC,KAAK,CAAC,EAAE,SAAS,WAAW;AACpB,iBAAA;AAAA,YACL,MAAM,yBAAyB,MAAM,SAAS;AAAA,YAC9C;AAAA,UAAA;AAAA,QACF,CACD,EACA,MAAM,MAAM;AACJ,iBAAA;AAAA,YACL,MAAM,CAAC;AAAA,YACP;AAAA,UAAA;AAAA,QACF,CACD;AAAA,MAAA,CACJ;AAAA,IAAA;AAGI,WAAA,QAAQ,QAAQ,aAAa;AAAA,EACtC;AACF;"}