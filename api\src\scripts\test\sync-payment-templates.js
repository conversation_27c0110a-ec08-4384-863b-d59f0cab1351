'use strict';

// Este script ejecuta la sincronización de plantillas de correo electrónico
// específicamente para las plantillas de pagos verificados y rechazados

const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// Cargar variables de entorno
dotenv.config();

// Importar el script de sincronización
const syncEmailTemplates = require('../sync-email-templates').default;

// Función principal
const syncPaymentTemplates = async () => {
  try {
    console.log('🔄 Iniciando sincronización de plantillas de pagos...');
    
    // Verificar que las plantillas existan
    const templatesDir = path.join(process.cwd(), 'config', 'email-templates');
    const paymentVerifiedPath = path.join(templatesDir, 'payment-verified.html');
    const paymentRejectedPath = path.join(templatesDir, 'payment-rejected.html');
    
    if (!fs.existsSync(paymentVerifiedPath)) {
      console.error('❌ Error: La plantilla payment-verified.html no existe');
      return;
    }
    
    if (!fs.existsSync(paymentRejectedPath)) {
      console.error('❌ Error: La plantilla payment-rejected.html no existe');
      return;
    }
    
    console.log('✅ Plantillas encontradas, iniciando sincronización con Strapi...');
    
    // Crear un objeto strapi simulado para el script de sincronización
    const strapi = {
      plugin: (name) => {
        if (name === 'email') {
          return {
            service: (serviceName) => {
              if (serviceName === 'email') {
                return {
                  updateTemplate: async (templateName, data) => {
                    console.log(`Actualizando plantilla ${templateName}...`);
                    console.log(`Datos: ${JSON.stringify(data)}`);
                    return true;
                  }
                };
              }
              return {};
            }
          };
        }
        return {};
      }
    };
    
    // Ejecutar la sincronización
    await syncEmailTemplates({ strapi });
    
    console.log('✅ Sincronización completada');
    console.log('📋 Ahora reinicia el servidor Strapi para ver los cambios');
    
  } catch (error) {
    console.error('❌ Error durante la sincronización:', error);
  }
};

// Ejecutar la función principal
syncPaymentTemplates();
