/**
 * warning-rule controller
 */

import { factories } from '@strapi/strapi';

export default {
  async find(ctx) {
    try {
      const warningRules = await strapi.entityService.findMany('api::warning-rule.warning-rule', {
        ...ctx.query,
        populate: ['warnings']
      });

      const sanitizedRules = warningRules.map(rule => ({
        id: rule.id,
        article: rule.article,
        description: rule.description,
        chapter: rule.chapter
      }));

      return {
        data: sanitizedRules
      };
    } catch (error) {
      console.error('Error in warning-rule.find:', error);
      ctx.throw(500, { message: 'Error al obtener las reglas de advertencia' });
    }
  },

  async findOne(ctx) {
    try {
      const { id } = ctx.params;
      const warningRule = await strapi.entityService.findOne('api::warning-rule.warning-rule', id, {
        ...ctx.query,
        populate: ['warnings']
      });

      if (!warningRule) {
        return ctx.notFound('Warning rule not found');
      }

      const sanitizedRule = {
        id: warningRule.id,
        article: warningRule.article,
        description: warningRule.description,
        chapter: warningRule.chapter
      };

      return {
        data: sanitizedRule
      };
    } catch (error) {
      console.error('Error in warning-rule.findOne:', error);
      ctx.throw(500, { message: 'Error al obtener la regla de advertencia' });
    }
  },

  async create(ctx) {
    try {
      const warningRule = await strapi.entityService.create('api::warning-rule.warning-rule', {
        data: ctx.request.body.data
      });
      return { data: warningRule };
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  async update(ctx) {
    try {
      const { id } = ctx.params;
      const warningRule = await strapi.entityService.update('api::warning-rule.warning-rule', id, {
        data: ctx.request.body.data
      });
      return { data: warningRule };
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  async delete(ctx) {
    try {
      const { id } = ctx.params;
      const warningRule = await strapi.entityService.delete('api::warning-rule.warning-rule', id);
      return { data: warningRule };
    } catch (error) {
      ctx.throw(500, error);
    }
  }
};
