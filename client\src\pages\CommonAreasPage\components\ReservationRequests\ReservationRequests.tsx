import {
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>wo<PERSON><PERSON>,
  <PERSON><PERSON>wo<PERSON>one,
  TeamOutlined,
} from "@ant-design/icons";
import { BaseCard } from "@app/components/common/BaseCard/BaseCard";
import { BaseTable } from "@app/components/common/BaseTable/BaseTable";
import { PageTitle } from "@app/components/common/PageTitle/PageTitle";
import { Guest } from "@app/components/dashboard/socialAreaCard/types/reservation.types";
import { Animation } from "@app/components/lotties/Animation";
import { Http } from "@app/config/http";
import { useCurrentUser } from "@app/hooks/useCurrentUser";
import { useQuery } from "@tanstack/react-query";
import {
  Button,
  List,
  Modal,
  Result,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

const convertTo12Hour = (time24: string) => {
  const [hours24, minutes] = time24.split(":");
  const hours = parseInt(hours24, 10);
  const meridiem = hours >= 12 ? "PM" : "AM";
  const hours12 = hours % 12 || 12;
  return `${hours12}:${minutes} ${meridiem}`;
};

interface Reservation {
  id: string;
  documentId: string;
  date: string;
  startTime: string;
  endTime: string;
  socialArea: string;
  eventType: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  rejectionReason: string | null;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    username: string;
    imgUrl: string;
    isSolvente: boolean;
  };
  guests: Guest[];
}

interface ApiResponse {
  data: Reservation[];
  meta?: any;
}

export const ReservationRequestsPage = () => {
  const navigate = useNavigate();

  const { data, isLoading } = useQuery<ApiResponse>({
    queryKey: ["reservations", "admin"],
    queryFn: () => Http.get("/api/reservations").then((res) => res.data),
  });

  const { user } = useCurrentUser();

  const canViewGuests = (record: { user: { id: number } }) => {
    if (!user) return false;
    if (user.role?.type === "admin") return true;
    return user.id === record.user.id;
  };

  // Extraer las reservas del objeto de respuesta y asegurar que siempre es un array
  const reservations = useMemo(() => {
    if (!data) return [];
    if (Array.isArray(data)) return data;
    if (Array.isArray(data.data)) return data.data;
    return [];
  }, [data]);

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      pending: "warning",
      approved: "success",
      rejected: "error",
    };
    return colors[status] || "default";
  };

  const getStatusText = (status: string) => {
    const texts: Record<string, string> = {
      pending: "Pendiente",
      approved: "Confirmada",
      rejected: "Rechazada",
    };
    return texts[status] || status;
  };

  const columns: ColumnsType<Reservation> = [
    {
      title: "Fecha de Solicitud",
      dataIndex: "createdAt",
      key: "createdAt",
      align: "center",
      render: (date: string) => dayjs(date).format("DD/MM/YYYY HH:mm A"),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: "Área",
      dataIndex: "socialArea",
      key: "socialArea",
      align: "center",
      render: (socialArea: string) => {
        if (!socialArea) return "N/A";
        const areaNames = {
          communalHall: "Salón comunal",
          pool: "Piscina",
          bbq: "BBQ",
          terrace: "Terraza",
        };
        return (
          areaNames[socialArea as keyof typeof areaNames] || socialArea || "N/A"
        );
      },
    },
    {
      title: "Solicitante",
      dataIndex: "user",
      key: "user",
      align: "center",
      render: (user) => `${user.firstName} ${user.lastName}`,
      sorter: (a, b) =>
        `${a.user.firstName} ${a.user.lastName}`.localeCompare(
          `${b.user.firstName} ${b.user.lastName}`,
        ),
    },
    {
      title: "Fecha de Reserva",
      dataIndex: "date",
      key: "date",
      align: "center",
      render: (_, record) => dayjs(record.date).format("DD/MM/YYYY"),
      sorter: (a, b) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: "Horario",
      key: "time",
      align: "center",
      render: (_, record) => (
        <div>
          {convertTo12Hour(record.startTime)} -{" "}
          {convertTo12Hour(record.endTime)}
        </div>
      ),
    },
    {
      title: "Estado",
      dataIndex: "status",
      key: "status",
      align: "center",
      render: (status: string) => (
        <Tag
          bordered={false}
          style={{ textAlign: "center", width: "100%", padding: 4 }}
          color={getStatusColor(status)}
        >
          <b>{getStatusText(status)}</b>
        </Tag>
      ),
      filters: [
        { text: "Pendiente", value: "pending" },
        { text: "Confirmada", value: "approved" },
        { text: "Rechazada", value: "rejected" },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: "Acciones",
      key: "actions",
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space.Compact size="small">
          {canViewGuests(record) && (
            <Tooltip title="Ver invitados">
              <Button
                icon={<TeamOutlined />}
                onClick={() => {
                  Modal.info({
                    title: (
                      <Typography.Title level={4}>Invitados</Typography.Title>
                    ),
                    content: (
                      <List
                        size="small"
                        dataSource={record.guests || ([] as Guest[])}
                        locale={{ emptyText: "Sin invitados registrados" }}
                        renderItem={(guest: Guest, index) => (
                          <List.Item
                            spellCheck
                            key={index}
                            extra={
                              guest.isConfirmed ? (
                                <SmileTwoTone twoToneColor="#52c41a" />
                              ) : (
                                <FrownTwoTone twoToneColor="#f5222d" />
                              )
                            }
                          >
                            <div
                              style={{
                                wordBreak: "break-all",
                                whiteSpace: "pre-wrap",
                                width: "100%",
                              }}
                            >
                              {index + 1}. {guest.name} {guest.lastName}
                            </div>
                          </List.Item>
                        )}
                      />
                    ),
                    width: 400,
                  });
                }}
              />
            </Tooltip>
          )}
          <Tooltip title="Ver detalles">
            <Button
              icon={<EyeOutlined />}
              onClick={() => navigate(`/reservations/${record.id}`)}
            />
          </Tooltip>
        </Space.Compact>
      ),
    },
  ];

  return (
    <BaseCard title="Mis Reservaciones">
      <PageTitle>Mis Reservaciones</PageTitle>
      {reservations.length === 0 ? (
        <Result
          style={{ width: "50dvw", margin: "auto" }}
          icon={<Animation src="/animations/calendar.lottie" />}
          subTitle="No hay reservasiones registradas"
        />
      ) : (
        <BaseTable
          size="small"
          columns={columns}
          dataSource={reservations}
          rowKey="id"
          loading={isLoading}
          scroll={{ x: "max-content" }}
          pagination={false}
        />
      )}
    </BaseCard>
  );
};

export default ReservationRequestsPage;
