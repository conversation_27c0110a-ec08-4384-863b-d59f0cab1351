/**
 * comment router
 */

export default {
  routes: [
    // Obtener todos los comentarios
    {
      method: 'GET',
      path: '/comments',
      handler: 'comment.find',
      config: {
        auth: false,
      },
    },
    // Obtener un comentario específico
    {
      method: 'GET',
      path: '/comments/:id',
      handler: 'comment.findOne',
      config: {
        auth: false,
      },
    },
    // Crear un nuevo comentario
    {
      method: 'POST',
      path: '/comments',
      handler: 'comment.create',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    // Actualizar un comentario
    {
      method: 'PUT',
      path: '/comments/:id',
      handler: 'comment.update',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    // Eliminar un comentario
    {
      method: 'DELETE',
      path: '/comments/:id',
      handler: 'comment.delete',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    // Reaccionar a un comentario (like/dislike)
    {
      method: 'POST',
      path: '/comments/:id/reaction',
      handler: 'comment.toggleReaction',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
  ],
};
