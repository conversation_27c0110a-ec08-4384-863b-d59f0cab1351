import type { Config } from './core/config';
import type { CommonCLIOptions } from '../types';
type WatchCLIOptions = CommonCLIOptions;
interface WatchOptionsWithoutConfig extends WatchCLIOptions {
    configFile?: true;
    config?: never;
    cwd?: string;
}
interface WatchOptionsWithConfig extends WatchCLIOptions {
    configFile: false;
    config?: Config;
    cwd?: string;
}
type WatchOptions = WatchOptionsWithConfig | WatchOptionsWithoutConfig;
declare const watch: (opts: WatchOptions) => Promise<void>;
export { watch };
export type { WatchOptions, WatchOptionsWithConfig, WatchOptionsWithoutConfig, WatchCLIOptions };
