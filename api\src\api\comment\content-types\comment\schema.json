{"kind": "collectionType", "collectionName": "comments", "info": {"singularName": "comment", "pluralName": "comments", "displayName": "Comment", "description": "Comments for articles"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"content": {"type": "text", "required": true}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "comments", "configurable": false}, "article": {"type": "relation", "relation": "manyToOne", "target": "api::article.article", "inversedBy": "comments"}, "threadOf": {"type": "relation", "relation": "manyToOne", "target": "api::comment.comment", "inversedBy": "replies"}, "replies": {"type": "relation", "relation": "oneToMany", "target": "api::comment.comment", "mappedBy": "threadOf"}, "likes": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "likedComments"}, "isDeleted": {"type": "boolean", "default": false}, "dislikes": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "dislikedComments"}}}