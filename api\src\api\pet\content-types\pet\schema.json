{"kind": "collectionType", "collectionName": "pets", "info": {"singularName": "pet", "pluralName": "pets", "displayName": "Pet", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "description": "Nombre de la mascota"}, "breed": {"type": "string", "description": "Raza de la mascota"}, "color": {"type": "string", "description": "Color de la mascota"}, "gender": {"type": "enumeration", "enum": ["FEMALE", "MALE"], "required": false, "description": "<PERSON><PERSON><PERSON>"}, "type": {"type": "enumeration", "enum": ["DOG", "CAT", "OTHER"], "description": "Tipo de mascota"}, "imgUrl": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"], "description": "Imagen de la mascota"}, "owner": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "pets", "description": "Propietario de la mascota"}, "isVaccinated": {"type": "boolean", "default": false, "required": true, "description": "Indica si la mascota está vacunada"}, "isSterilized": {"type": "boolean", "default": false, "required": true, "description": "Indica si la mascota está esterilizada"}, "vaccinationRecords": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"], "description": "Registros de vacunación de la mascota"}}}