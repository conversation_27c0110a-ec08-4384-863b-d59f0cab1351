import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Http } from "@app/config/http";
import { message } from "antd";

interface ConfirmGuestParams {
  reservationId: string | number;
  guestIndex: number;
  isConfirmed: boolean;
}

interface ConfirmGuestResponse {
  data: {
    id: number;
    attributes: {
      guests: Array<{
        name: string;
        lastName: string;
        isConfirmed: boolean;
      }>;
    };
  };
  message: string;
}

interface UseConfirmGuestOptions {
  onLocalUpdate?: (
    reservationId: string | number,
    guestIndex: number,
    isConfirmed: boolean,
  ) => void;
}

export const useConfirmGuest = (options?: UseConfirmGuestOptions) => {
  const queryClient = useQueryClient();

  return useMutation<ConfirmGuestResponse, Error, ConfirmGuestParams>({
    mutationFn: async ({ reservationId, guestIndex, isConfirmed }) => {
      const response = await Http.put(
        `/api/reservations/${reservationId}/guests/${guestIndex}/confirm`,
        { isConfirmed },
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Actualizar estado local si se proporciona el callback
      if (options?.onLocalUpdate) {
        options.onLocalUpdate(
          variables.reservationId,
          variables.guestIndex,
          variables.isConfirmed,
        );
      }

      // Invalidar las queries relacionadas con reservaciones
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
      queryClient.invalidateQueries({ queryKey: ["reservations", "admin"] });
      queryClient.invalidateQueries({ queryKey: ["all-reservations"] });
      queryClient.invalidateQueries({ queryKey: ["user-reservations"] });
      queryClient.invalidateQueries({ queryKey: ["admin-reservations"] });

      // Mostrar mensaje de éxito
      message.success(data.message);
    },
    onError: (error) => {
      console.error("Error al confirmar guest:", error);
      message.error(
        error.message || "Error al actualizar el estado del invitado",
      );
    },
  });
};
