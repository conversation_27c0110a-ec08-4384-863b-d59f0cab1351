export default {
  routes: [
    {
      method: "GET",
      path: "/budgets",
      handler: "budget.find",
    },
    {
      method: "GET",
      path: "/budgets/active",
      handler: "budget.getActive",
    },
    {
      method: "GET",
      path: "/budgets/calculations",
      handler: "budget.getCalculations",
    },
    {
      method: "GET",
      path: "/budgets/:id",
      handler: "budget.findOne",
    },
    {
      method: "POST",
      path: "/budgets",
      handler: "budget.create",
    },
    {
      method: "PUT",
      path: "/budgets/:id",
      handler: "budget.update",
    },
    {
      method: "DELETE",
      path: "/budgets/:id",
      handler: "budget.delete",
    },
    {
      method: "GET",
      path: "/budgets/user/:id/calculation",
      handler: "budget.getUserCalculation",
    },
  ],
};
