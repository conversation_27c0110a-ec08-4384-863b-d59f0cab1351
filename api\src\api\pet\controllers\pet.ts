/**
 * pet controller
 */

import { factories } from "@strapi/strapi";

// Interfaces basadas en el esquema de Strapi
type PetGender = "FEMALE" | "MALE";
type PetType = "DOG" | "CAT" | "OTHER";

interface Owner {
  id: number;
  firstName?: string;
  lastName?: string;
  address?: string;
}

interface MediaFile {
  id: number;
  url: string;
  formats?: {
    thumbnail?: {
      url: string;
    };
  };
}

interface PetEntity {
  id: number;
  name?: string;
  breed?: string;
  color?: string;
  type?: PetType;
  gender?: PetGender;
  isVaccinated: boolean;
  isSterilized: boolean;
  owner: Owner;
  imgUrl?: MediaFile;
  vaccinationRecords?: MediaFile;
}

interface PetCreateData {
  name?: string;
  breed?: string;
  color?: string;
  type?: PetType;
  gender?: PetGender;
  owner: number;
  isVaccinated: boolean;
  isSterilized: boolean;
  imgUrl?: number;
  vaccinationRecords?: number;
}

interface PetUpdateData {
  name?: string;
  breed?: string;
  color?: string;
  type?: PetType;
  gender?: PetGender;
  owner?: number;
  isVaccinated?: boolean;
  isSterilized?: boolean;
  imgUrl?: number;
  vaccinationRecords?: number;
}

interface ProcessedPet {
  id: number;
  name: string;
  color: string;
  breed: string;
  type: PetType;
  gender: PetGender;
  isVaccinated: boolean;
  isSterilized: boolean;
  imgUrl: string | null;
  vaccinationRecords: string | null;
  owner?: {
    id: number;
    name: string;
  } | null;
  address?: string | null;
  ownerName?: string | null;
  ownerId?: number;
}

export default factories.createCoreController("api::pet.pet", ({ strapi }) => ({
  async create(ctx) {
    try {
      const data = JSON.parse(ctx.request.body.data || "{}");

      const imgUrl = ctx.request.files?.imgUrl;

      if (imgUrl) {
        const uploadedFiles = await strapi.plugins.upload
          .service("upload")
          .upload({
            data: {
              ref: "api::pet.pet",
              field: "imgUrl",
            },
            files: imgUrl,
          });

        if (uploadedFiles && uploadedFiles.length > 0) {
          const petData: PetCreateData = {
            ...data,
            owner: data.owner,
            isVaccinated: data.isVaccinated ?? false,
            isSterilized: data.isSterilized ?? false,
            imgUrl: uploadedFiles[0].id,
          };

          // Subir archivo de vacunación si existe
          if (data.isVaccinated && ctx.request.files?.vaccinationRecords) {
            const vaxFiles = await strapi.plugins.upload
              .service("upload")
              .upload({
                data: {
                  ref: "api::pet.pet",
                  field: "vaccinationRecords",
                },
                files: ctx.request.files.vaccinationRecords,
              });
            if (vaxFiles?.[0]?.id) {
              petData.vaccinationRecords = vaxFiles[0].id;
            }
          }

          const createdPet = await strapi.entityService.create("api::pet.pet", {
            data: petData,
          });

          return ctx.send(createdPet);
        } else {
          throw new Error("No se pudo subir la imagen.");
        }
      } else {
        const petData: PetCreateData = {
          ...data,
          owner: data.owner,
          isVaccinated: data.isVaccinated ?? false,
          isSterilized: data.isSterilized ?? false,
        };

        const createdPet = await strapi.entityService.create("api::pet.pet", {
          data: petData,
        });

        return ctx.send(createdPet);
      }
    } catch (error) {
      console.error("Error en createWithImage para Pet:", error);
      return ctx.badRequest({
        error: error.message + " Error al crear mascota.",
      });
    }
  },

  async findByOwner(ctx) {
    const { ownerId } = ctx.params;
    try {
      const query = {
        where: {
          owner: parseInt(ownerId),
          publishedAt: { $notNull: true },
        },
        populate: {
          imgUrl: true,
          vaccinationRecords: true,
          owner: true,
        },
      };

      const pets: PetEntity[] = await strapi.db
        .query("api::pet.pet")
        .findMany(query);

      const processedPets: ProcessedPet[] = pets.map((pet) => ({
        id: pet.id,
        name: pet.name,
        color: pet.color,
        breed: pet.breed,
        type: pet.type,
        gender: pet.gender,
        isVaccinated: pet.isVaccinated || false,
        isSterilized: pet.isSterilized || false,
        vaccinationRecords: pet.vaccinationRecords?.url || null,
        imgUrl: pet.imgUrl?.formats?.thumbnail?.url || pet.imgUrl?.url || null,
        owner: pet.owner
          ? {
              id: pet.owner.id,
              name: `${pet.owner.firstName || ""} ${
                pet.owner.lastName || ""
              }`.trim(),
            }
          : null,
      }));

      return ctx.send(processedPets);
    } catch (error) {
      console.error("Error en findByOwner:", error);
      ctx.throw(500, error);
    }
  },

  async delete(ctx) {
    const { id } = ctx.params;

    try {
      const pet = await strapi.db.query("api::pet.pet").findOne({
        where: { id },
      });

      if (!pet) {
        return ctx.notFound("La mascota no existe.");
      }

      await strapi.db.query("api::pet.pet").delete({
        where: { id },
      });

      return { message: "Mascota eliminada exitosamente." };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  async update(ctx) {
    try {
      const { id } = ctx.params;
      const data = ctx.request.body.data
        ? typeof ctx.request.body.data === "string"
          ? JSON.parse(ctx.request.body.data)
          : ctx.request.body.data
        : {};

      // Get the current pet data to preserve existing file references
      interface PetWithRelations {
        imgUrl?: MediaFile | null;
        vaccinationRecords?: MediaFile | null;
        owner?: {
          id: number;
          firstName?: string;
          lastName?: string;
        } | null;
      }

      const currentPet = (await strapi.entityService.findOne(
        "api::pet.pet",
        id,
        {
          populate: ["imgUrl", "vaccinationRecords", "owner"],
        }
      )) as unknown as PetWithRelations;

      // Start with basic data
      const updateData: PetUpdateData = {
        name: data.name,
        breed: data.breed,
        color: data.color,
        type: data.type,
        gender: data.gender,
        isVaccinated: data.isVaccinated,
        isSterilized: data.isSterilized,
        owner: data.owner?.id || data.owner,
      };

      // Handle vaccination records update if needed
      if (data.isVaccinated && ctx.request.files?.vaccinationRecords) {
        const vaxFiles = await strapi.plugins.upload.service("upload").upload({
          data: {
            ref: "api::pet.pet",
            refId: id,
            field: "vaccinationRecords",
          },
          files: ctx.request.files.vaccinationRecords,
        });
        if (vaxFiles?.[0]?.id) {
          updateData.vaccinationRecords = vaxFiles[0].id;
        }
      } else if (data.isVaccinated === false) {
        // If vaccination is unchecked, remove the file
        updateData.vaccinationRecords = null;
      } else if (currentPet?.vaccinationRecords?.id) {
        // Preserve existing vaccination record if not being updated
        updateData.vaccinationRecords = currentPet.vaccinationRecords.id;
      }

      // Handle image upload if needed
      if (ctx.request.files?.imgUrl) {
        const uploadedFiles = await strapi.plugins.upload
          .service("upload")
          .upload({
            data: {
              ref: "api::pet.pet",
              refId: id,
              field: "imgUrl",
            },
            files: ctx.request.files.imgUrl,
          });
        if (uploadedFiles?.[0]?.id) {
          updateData.imgUrl = uploadedFiles[0].id;
        }
      } else if (currentPet?.imgUrl?.id) {
        // Preserve existing image if not being updated
        updateData.imgUrl = currentPet.imgUrl.id;
      }

      // Update the pet with all the data
      const updatedPet = await strapi.entityService.update("api::pet.pet", id, {
        data: updateData,
      });

      return ctx.send(updatedPet);
    } catch (error) {
      console.error("Error al actualizar mascota:", error);
      return ctx.badRequest({
        error: error.message + " Error al actualizar mascota",
      });
    }
  },

  async findAll(ctx) {
    try {
      const pets: PetEntity[] = await strapi.db.query("api::pet.pet").findMany({
        populate: {
          imgUrl: true,
          vaccinationRecords: true,
          owner: {
            fields: ["address", "id", "firstName", "lastName"],
          },
        },
      });

      // Transformar los datos al formato esperado por el frontend
      const processedPets: ProcessedPet[] = pets.map((pet) => ({
        id: pet.id,
        name: pet.name,
        color: pet.color,
        breed: pet.breed,
        type: pet.type,
        gender: pet.gender,
        imgUrl: pet.imgUrl?.formats?.thumbnail?.url || pet.imgUrl?.url || null,
        isVaccinated: pet.isVaccinated || false,
        isSterilized: pet.isSterilized || false,
        vaccinationRecords: pet.vaccinationRecords?.url || null,
        address: pet.owner?.address || null,
        ownerName: `${pet.owner?.firstName || ""} ${
          pet.owner?.lastName || ""
        }`.trim(),
        ownerId: pet.owner?.id,
      }));

      return ctx.send(processedPets);
    } catch (error) {
      console.error("Error en findAll:", error);
      ctx.throw(500, error);
    }
  },
}));
