export default {
  routes: [
    {
      method: 'GET',
      path: '/complaint-templates',
      handler: 'complaint-template.find',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/complaint-templates/:id',
      handler: 'complaint-template.findOne',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    {
      method: 'POST',
      path: '/complaint-templates',
      handler: 'complaint-template.create',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
    {
      method: 'PUT',
      path: '/complaint-templates/:id',
      handler: 'complaint-template.update',
      config: {
        policies: ['global::isAuthenticated'],
      },
    },
  ],
};
