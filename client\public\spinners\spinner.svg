<svg class="pl" width="128px" height="128px" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
    <circle class="pl__ring1" cx="64" cy="64" r="60" fill="none" stroke="hsl(245, 89%, 69%)" stroke-width="8" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="377 377" stroke-dashoffset="-376.4"/>
    <circle class="pl__ring2" cx="64" cy="64" r="52.5" fill="none" stroke="hsl(255, 89%, 69%)" stroke-width="7" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="329.9 329.9" stroke-dashoffset="-329.3"/>
    <circle class="pl__ring3" cx="64" cy="64" r="46" fill="none" stroke="hsl(265, 89%, 69%)" stroke-width="6" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="289 289" stroke-dashoffset="-288.6"/>
    <circle class="pl__ring4" cx="64" cy="64" r="40.5" fill="none" stroke="hsl(275, 89%, 69%)" stroke-width="5" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="254.5 254.5" stroke-dashoffset="-254"/>
    <circle class="pl__ring5" cx="64" cy="64" r="36" fill="none" stroke="hsl(285, 89%, 69%)" stroke-width="4" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="226.2 226.2" stroke-dashoffset="-225.8"/>
    <circle class="pl__ring6" cx="64" cy="64" r="32.5" fill="none" stroke="hsl(295, 89%, 69%)" stroke-width="3" transform="rotate(-90,64,64)" stroke-linecap="round" stroke-dasharray="204.2 204.2" stroke-dashoffset="-203.9"/>

    <style>
        .pl {
        width: 8em;
        height: 8em;
        }
        .pl circle {
        transform-box: fill-box;
        transform-origin: 50% 50%;
        }
        .pl__ring1 {
        animation: ring1 4s 0s ease-in-out infinite;
        }
        .pl__ring2 {
        animation: ring2 4s 0.04s ease-in-out infinite;
        }
        .pl__ring3 {
        animation: ring3 4s 0.08s ease-in-out infinite;
        }
        .pl__ring4 {
        animation: ring4 4s 0.12s ease-in-out infinite;
        }
        .pl__ring5 {
        animation: ring5 4s 0.16s ease-in-out infinite;
        }
        .pl__ring6 {
        animation: ring6 4s 0.2s ease-in-out infinite;
        }

        /* Animations */
        @keyframes ring1 {
        from {
        stroke-dashoffset: -376.237129776;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -94.247778;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -376.237129776;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -94.247778;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -376.237129776;
        transform: rotate(4.75turn);
        }
        }
        @keyframes ring2 {
        from {
        stroke-dashoffset: -329.207488554;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -82.46680575;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -329.207488554;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -82.46680575;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -329.207488554;
        transform: rotate(4.75turn);
        }
        }
        @keyframes ring3 {
        from {
        stroke-dashoffset: -288.4484661616;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -72.2566298;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -288.4484661616;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -72.2566298;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -288.4484661616;
        transform: rotate(4.75turn);
        }
        }
        @keyframes ring4 {
        from {
        stroke-dashoffset: -253.9600625988;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -63.61725015;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -253.9600625988;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -63.61725015;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -253.9600625988;
        transform: rotate(4.75turn);
        }
        }
        @keyframes ring5 {
        from {
        stroke-dashoffset: -225.7422778656;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -56.5486668;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -225.7422778656;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -56.5486668;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -225.7422778656;
        transform: rotate(4.75turn);
        }
        }
        @keyframes ring6 {
        from {
        stroke-dashoffset: -203.795111962;
        transform: rotate(-0.25turn);
        animation-timing-function: ease-in;
        }
        23% {
        stroke-dashoffset: -51.05087975;
        transform: rotate(1turn);
        animation-timing-function: ease-out;
        }
        46%,
        50% {
        stroke-dashoffset: -203.795111962;
        transform: rotate(2.25turn);
        animation-timing-function: ease-in;
        }
        73% {
        stroke-dashoffset: -51.05087975;
        transform: rotate(3.5turn);
        animation-timing-function: ease-out;
        }
        96%,
        to {
        stroke-dashoffset: -203.795111962;
        transform: rotate(4.75turn);
        }
        }
    </style>
</svg>
