{"version": 3, "file": "action.mjs", "sources": ["../../../../src/cli/commands/import/action.ts"], "sourcesContent": ["import type { Core } from '@strapi/types';\nimport { isObject } from 'lodash/fp';\nimport chalk from 'chalk';\n\nimport {\n  engine as engineDataTransfer,\n  strapi as strapiDataTransfer,\n  file as fileDataTransfer,\n} from '@strapi/data-transfer';\n\nimport {\n  buildTransferTable,\n  DEFAULT_IGNORED_CONTENT_TYPES,\n  createStrapiInstance,\n  formatDiagnostic,\n  loadersFactory,\n  exitMessageText,\n  abortTransfer,\n  getTransferTelemetryPayload,\n  setSignalHandler,\n  getDiffHandler,\n  parseRestoreFromOptions,\n} from '../../utils/data-transfer';\nimport { exitWith } from '../../utils/helpers';\n\nconst {\n  providers: { createLocalFileSourceProvider },\n} = fileDataTransfer;\n\nconst {\n  providers: { createLocalStrapiDestinationProvider, DEFAULT_CONFLICT_STRATEGY },\n} = strapiDataTransfer;\n\nconst { createTransferEngine, DEFAULT_VERSION_STRATEGY, DEFAULT_SCHEMA_STRATEGY } =\n  engineDataTransfer;\n\ninterface CmdOptions {\n  file?: string;\n  decompress?: boolean;\n  decrypt?: boolean;\n  key?: string;\n  conflictStrategy?: 'restore';\n  force?: boolean;\n  only?: (keyof engineDataTransfer.TransferGroupFilter)[];\n  exclude?: (keyof engineDataTransfer.TransferGroupFilter)[];\n  throttle?: number;\n}\n\ntype EngineOptions = Parameters<typeof createTransferEngine>[2];\n\n/**\n * Import command.\n *\n * It transfers data from a file to a local Strapi instance\n */\nexport default async (opts: CmdOptions) => {\n  // validate inputs from Commander\n  if (!isObject(opts)) {\n    exitWith(1, 'Could not parse arguments');\n  }\n\n  /**\n   * From strapi backup file\n   */\n  const sourceOptions = getLocalFileSourceOptions(opts);\n\n  const source = createLocalFileSourceProvider(sourceOptions);\n\n  /**\n   * To local Strapi instance\n   */\n  const strapiInstance = await createStrapiInstance();\n\n  /**\n   * Configure and run the transfer engine\n   */\n  const engineOptions: EngineOptions = {\n    versionStrategy: DEFAULT_VERSION_STRATEGY,\n    schemaStrategy: DEFAULT_SCHEMA_STRATEGY,\n    exclude: opts.exclude,\n    only: opts.only,\n    throttle: opts.throttle,\n    transforms: {\n      links: [\n        {\n          filter(link) {\n            return (\n              !DEFAULT_IGNORED_CONTENT_TYPES.includes(link.left.type) &&\n              !DEFAULT_IGNORED_CONTENT_TYPES.includes(link.right.type)\n            );\n          },\n        },\n      ],\n      entities: [\n        {\n          filter: (entity) => !DEFAULT_IGNORED_CONTENT_TYPES.includes(entity.type),\n        },\n      ],\n    },\n  };\n\n  const destinationOptions = {\n    async getStrapi() {\n      return strapiInstance;\n    },\n    autoDestroy: false,\n    strategy: opts.conflictStrategy || DEFAULT_CONFLICT_STRATEGY,\n    restore: parseRestoreFromOptions(engineOptions),\n  };\n\n  const destination = createLocalStrapiDestinationProvider(destinationOptions);\n  destination.onWarning = (message) => console.warn(`\\n${chalk.yellow('warn')}: ${message}`);\n\n  const engine = createTransferEngine(source, destination, engineOptions);\n\n  engine.diagnostics.onDiagnostic(formatDiagnostic('import'));\n\n  const progress = engine.progress.stream;\n\n  const { updateLoader } = loadersFactory();\n\n  engine.onSchemaDiff(getDiffHandler(engine, { force: opts.force, action: 'import' }));\n\n  progress.on(`stage::start`, ({ stage, data }) => {\n    updateLoader(stage, data).start();\n  });\n\n  progress.on('stage::finish', ({ stage, data }) => {\n    updateLoader(stage, data).succeed();\n  });\n\n  progress.on('stage::progress', ({ stage, data }) => {\n    updateLoader(stage, data);\n  });\n\n  progress.on('transfer::start', async () => {\n    console.log('Starting import...');\n    await strapiInstance.telemetry.send(\n      'didDEITSProcessStart',\n      getTransferTelemetryPayload(engine)\n    );\n  });\n\n  let results: engineDataTransfer.ITransferResults<typeof source, typeof destination>;\n  try {\n    // Abort transfer if user interrupts process\n    setSignalHandler(() => abortTransfer({ engine, strapi: strapi as Core.Strapi }));\n\n    results = await engine.transfer();\n\n    try {\n      const table = buildTransferTable(results.engine);\n      console.log(table?.toString());\n    } catch (e) {\n      console.error('There was an error displaying the results of the transfer.');\n    }\n\n    // Note: we need to await telemetry or else the process ends before it is sent\n    await strapiInstance.telemetry.send(\n      'didDEITSProcessFinish',\n      getTransferTelemetryPayload(engine)\n    );\n    await strapiInstance.destroy();\n\n    exitWith(0, exitMessageText('import'));\n  } catch (e) {\n    await strapiInstance.telemetry.send('didDEITSProcessFail', getTransferTelemetryPayload(engine));\n    exitWith(1, exitMessageText('import', true));\n  }\n};\n\n/**\n * Infer local file source provider options based on a given filename\n */\nconst getLocalFileSourceOptions = (opts: {\n  file?: string;\n  decompress?: boolean;\n  decrypt?: boolean;\n  key?: string;\n}) => {\n  const options: fileDataTransfer.providers.ILocalFileSourceProviderOptions = {\n    file: { path: opts.file ?? '' },\n    compression: { enabled: !!opts.decompress },\n    encryption: { enabled: !!opts.decrypt, key: opts.key },\n  };\n\n  return options;\n};\n"], "names": ["fileDataTransfer", "strapiDataTransfer", "engineDataTransfer", "engine"], "mappings": ";;;;;AAyBA,MAAM;AAAA,EACJ,WAAW,EAAE,8BAA8B;AAC7C,IAAIA;AAEJ,MAAM;AAAA,EACJ,WAAW,EAAE,sCAAsC,0BAA0B;AAC/E,IAAIC;AAEJ,MAAM,EAAE,sBAAsB,0BAA0B,wBAAA,IACtDC;AAqBF,MAAe,SAAA,OAAO,SAAqB;AAErC,MAAA,CAAC,SAAS,IAAI,GAAG;AACnB,aAAS,GAAG,2BAA2B;AAAA,EACzC;AAKM,QAAA,gBAAgB,0BAA0B,IAAI;AAE9C,QAAA,SAAS,8BAA8B,aAAa;AAKpD,QAAA,iBAAiB,MAAM;AAK7B,QAAM,gBAA+B;AAAA,IACnC,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,SAAS,KAAK;AAAA,IACd,MAAM,KAAK;AAAA,IACX,UAAU,KAAK;AAAA,IACf,YAAY;AAAA,MACV,OAAO;AAAA,QACL;AAAA,UACE,OAAO,MAAM;AACX,mBACE,CAAC,8BAA8B,SAAS,KAAK,KAAK,IAAI,KACtD,CAAC,8BAA8B,SAAS,KAAK,MAAM,IAAI;AAAA,UAE3D;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,UACE,QAAQ,CAAC,WAAW,CAAC,8BAA8B,SAAS,OAAO,IAAI;AAAA,QACzE;AAAA,MACF;AAAA,IACF;AAAA,EAAA;AAGF,QAAM,qBAAqB;AAAA,IACzB,MAAM,YAAY;AACT,aAAA;AAAA,IACT;AAAA,IACA,aAAa;AAAA,IACb,UAAU,KAAK,oBAAoB;AAAA,IACnC,SAAS,wBAAwB,aAAa;AAAA,EAAA;AAG1C,QAAA,cAAc,qCAAqC,kBAAkB;AAC3E,cAAY,YAAY,CAAC,YAAY,QAAQ,KAAK;AAAA,EAAK,MAAM,OAAO,MAAM,CAAC,KAAK,OAAO,EAAE;AAEzF,QAAMC,UAAS,qBAAqB,QAAQ,aAAa,aAAa;AAEtE,EAAAA,QAAO,YAAY,aAAa,iBAAiB,QAAQ,CAAC;AAEpD,QAAA,WAAWA,QAAO,SAAS;AAE3B,QAAA,EAAE,iBAAiB;AAElB,EAAAA,QAAA,aAAa,eAAeA,SAAQ,EAAE,OAAO,KAAK,OAAO,QAAQ,SAAS,CAAC,CAAC;AAEnF,WAAS,GAAG,gBAAgB,CAAC,EAAE,OAAO,WAAW;AAClC,iBAAA,OAAO,IAAI,EAAE,MAAM;AAAA,EAAA,CACjC;AAED,WAAS,GAAG,iBAAiB,CAAC,EAAE,OAAO,WAAW;AACnC,iBAAA,OAAO,IAAI,EAAE,QAAQ;AAAA,EAAA,CACnC;AAED,WAAS,GAAG,mBAAmB,CAAC,EAAE,OAAO,WAAW;AAClD,iBAAa,OAAO,IAAI;AAAA,EAAA,CACzB;AAEQ,WAAA,GAAG,mBAAmB,YAAY;AACzC,YAAQ,IAAI,oBAAoB;AAChC,UAAM,eAAe,UAAU;AAAA,MAC7B;AAAA,MACA,4BAA4BA,OAAM;AAAA,IAAA;AAAA,EACpC,CACD;AAEG,MAAA;AACA,MAAA;AAEF,qBAAiB,MAAM,cAAc,EAAE,QAAAA,SAAQ,OAAA,CAA+B,CAAC;AAErE,cAAA,MAAMA,QAAO;AAEnB,QAAA;AACI,YAAA,QAAQ,mBAAmB,QAAQ,MAAM;AACvC,cAAA,IAAI,OAAO,SAAU,CAAA;AAAA,aACtB,GAAG;AACV,cAAQ,MAAM,4DAA4D;AAAA,IAC5E;AAGA,UAAM,eAAe,UAAU;AAAA,MAC7B;AAAA,MACA,4BAA4BA,OAAM;AAAA,IAAA;AAEpC,UAAM,eAAe;AAEZ,aAAA,GAAG,gBAAgB,QAAQ,CAAC;AAAA,WAC9B,GAAG;AACV,UAAM,eAAe,UAAU,KAAK,uBAAuB,4BAA4BA,OAAM,CAAC;AAC9F,aAAS,GAAG,gBAAgB,UAAU,IAAI,CAAC;AAAA,EAC7C;AACF;AAKA,MAAM,4BAA4B,CAAC,SAK7B;AACJ,QAAM,UAAsE;AAAA,IAC1E,MAAM,EAAE,MAAM,KAAK,QAAQ,GAAG;AAAA,IAC9B,aAAa,EAAE,SAAS,CAAC,CAAC,KAAK,WAAW;AAAA,IAC1C,YAAY,EAAE,SAAS,CAAC,CAAC,KAAK,SAAS,KAAK,KAAK,IAAI;AAAA,EAAA;AAGhD,SAAA;AACT;"}