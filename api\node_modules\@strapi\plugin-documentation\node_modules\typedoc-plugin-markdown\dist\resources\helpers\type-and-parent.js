"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const Handlebars = __importStar(require("handlebars"));
const typedoc_1 = require("typedoc");
const utils_1 = require("../../utils");
function default_1() {
    Handlebars.registerHelper('typeAndParent', function () {
        var _a, _b, _c;
        const getUrl = (name, url) => `[${name}](${Handlebars.helpers.relativeURL(url)})`;
        if (this) {
            if ('elementType' in this) {
                return Handlebars.helpers.typeAndParent.call(this.elementType) + '[]';
            }
            else {
                if (this.reflection) {
                    const md = [];
                    if (this.reflection instanceof typedoc_1.SignatureReflection) {
                        if ((_b = (_a = this.reflection.parent) === null || _a === void 0 ? void 0 : _a.parent) === null || _b === void 0 ? void 0 : _b.url) {
                            md.push(getUrl(this.reflection.parent.parent.name, this.reflection.parent.parent.url));
                            if (this.reflection.parent.url) {
                                md.push(getUrl(this.reflection.parent.name, this.reflection.parent.url));
                            }
                        }
                    }
                    else {
                        if ((_c = this.reflection.parent) === null || _c === void 0 ? void 0 : _c.url) {
                            md.push(getUrl(this.reflection.parent.name, this.reflection.parent.url));
                            if (this.reflection.url) {
                                md.push(getUrl(this.reflection.name, this.reflection.url));
                            }
                        }
                    }
                    return md.join('.');
                }
                else {
                    return (0, utils_1.escapeChars)(this.toString());
                }
            }
        }
        return 'void';
    });
}
exports.default = default_1;
