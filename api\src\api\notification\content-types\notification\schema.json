{"kind": "collectionType", "collectionName": "notifications", "info": {"singularName": "notification", "pluralName": "notifications", "displayName": "Notification", "description": "Sistema de notificaciones para usuarios"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "message": {"type": "text", "required": true}, "read": {"type": "boolean", "default": false}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}}}