"use strict";
/**
 * WebSocket controller para notificaciones
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const websocket_1 = __importDefault(require("../../../services/websocket"));
exports.default = {
    /**
     * Enviar notificación a un usuario específico vía WebSocket
     */
    async sendToUser(ctx) {
        try {
            const { userId } = ctx.params;
            const { title, message, type = "info", data = {} } = ctx.request.body;
            if (!userId) {
                return ctx.badRequest("Se requiere el ID del usuario");
            }
            if (!title || !message) {
                return ctx.badRequest("Se requieren título y mensaje");
            }
            // Crear la notificación en la base de datos
            const notification = await strapi.entityService.create("api::notification.notification", {
                data: {
                    title,
                    message,
                    type,
                    data,
                    user: parseInt(userId),
                    read: false,
                },
                populate: ["user"],
            });
            // Enviar vía WebSocket
            const sent = websocket_1.default.sendNotificationToUser(parseInt(userId), notification);
            if (sent) {
                console.log(`📤 Notificación WebSocket enviada a usuario ${userId}`);
                ctx.send({
                    success: true,
                    message: "Notificación enviada correctamente",
                    notification,
                    sentViaWebSocket: true,
                });
            }
            else {
                console.log(`📭 Usuario ${userId} no está conectado vía WebSocket`);
                ctx.send({
                    success: true,
                    message: "Notificación creada (usuario no conectado)",
                    notification,
                    sentViaWebSocket: false,
                });
            }
        }
        catch (error) {
            console.error("❌ Error enviando notificación WebSocket:", error);
            ctx.internalServerError("Error enviando notificación");
        }
    },
    /**
     * Broadcast a todos los usuarios conectados
     */
    async broadcast(ctx) {
        try {
            const { title, message, type = "info", data = {} } = ctx.request.body;
            if (!title || !message) {
                return ctx.badRequest("Se requieren título y mensaje");
            }
            // Crear notificación para broadcast
            const broadcastData = {
                type: "broadcast",
                notification: {
                    title,
                    message,
                    type,
                    data,
                    timestamp: new Date().toISOString(),
                },
            };
            // Enviar vía WebSocket
            const sentCount = websocket_1.default.broadcast(broadcastData);
            console.log(`📡 Broadcast enviado a ${sentCount} conexión(es)`);
            ctx.send({
                success: true,
                message: `Broadcast enviado a ${sentCount} conexión(es)`,
                sentCount,
            });
        }
        catch (error) {
            console.error("❌ Error enviando broadcast WebSocket:", error);
            ctx.internalServerError("Error enviando broadcast");
        }
    },
    /**
     * Obtener estadísticas de conexiones WebSocket
     */
    async getStats(ctx) {
        try {
            const stats = websocket_1.default.getStats();
            ctx.send({
                success: true,
                stats,
            });
        }
        catch (error) {
            console.error("❌ Error obteniendo estadísticas WebSocket:", error);
            ctx.internalServerError("Error obteniendo estadísticas");
        }
    },
    /**
     * Enviar notificación de prueba
     */
    async test(ctx) {
        try {
            const { userId } = ctx.params;
            if (!userId) {
                return ctx.badRequest("Se requiere el ID del usuario");
            }
            const testNotification = {
                id: Date.now(),
                title: "🧪 Notificación de Prueba",
                message: "Esta es una notificación de prueba enviada vía WebSocket",
                type: "info",
                data: {
                    test: true,
                    timestamp: new Date().toISOString(),
                },
                read: false,
                createdAt: new Date().toISOString(),
            };
            // Enviar vía WebSocket
            const sent = websocket_1.default.sendNotificationToUser(parseInt(userId), testNotification);
            if (sent) {
                console.log(`🧪 Notificación de prueba enviada a usuario ${userId}`);
                ctx.send({
                    success: true,
                    message: "Notificación de prueba enviada correctamente",
                    notification: testNotification,
                    sentViaWebSocket: true,
                });
            }
            else {
                console.log(`📭 Usuario ${userId} no está conectado vía WebSocket`);
                ctx.send({
                    success: false,
                    message: "Usuario no está conectado vía WebSocket",
                    notification: testNotification,
                    sentViaWebSocket: false,
                });
            }
        }
        catch (error) {
            console.error("❌ Error enviando notificación de prueba:", error);
            ctx.internalServerError("Error enviando notificación de prueba");
        }
    },
    /**
     * Probar notificación de confirmación de huésped
     */
    async testGuestConfirmation(ctx) {
        try {
            console.log("🧪 Probando notificación de confirmación de huésped");
            // Crear notificación de prueba directamente
            const testNotification = {
                id: Date.now(),
                title: "🏨 Confirmación de Huésped",
                message: "Juan Pérez ha sido confirmado en el área de piscina",
                type: "guest_confirmation",
                data: {
                    guestName: "Juan Pérez",
                    socialArea: "pool",
                    confirmedBy: "admin",
                    timestamp: new Date().toISOString(),
                },
                read: false,
                createdAt: new Date().toISOString(),
            };
            // Enviar vía WebSocket a todas las conexiones
            const sent = websocket_1.default.broadcast({
                type: "notification",
                data: testNotification,
                timestamp: new Date().toISOString(),
            });
            console.log(`📤 Notificación de confirmación enviada a ${sent} conexión(es)`);
            // También intentar enviar a usuarios específicos (admins)
            try {
                // Enviar a usuario específico si está conectado
                const userSent = websocket_1.default.sendNotificationToUser(127, testNotification);
                console.log(`📤 Notificación enviada específicamente a usuario 127: ${userSent}`);
            }
            catch (error) {
                console.log(`⚠️ No se pudo enviar a usuario específico:`, error);
            }
            ctx.send({
                success: true,
                message: `Notificación de confirmación de huésped enviada a ${sent} conexión(es)`,
                notification: testNotification,
                sent: sent > 0,
            });
        }
        catch (error) {
            console.error("Error enviando notificación de confirmación:", error);
            ctx.internalServerError("Error enviando notificación de confirmación");
        }
    },
    /**
     * Probar notificación de nueva reserva
     */
    async testNewReservation(ctx) {
        try {
            console.log("🧪 Probando notificación de nueva reserva");
            // Crear notificación de prueba directamente
            const testNotification = {
                id: Date.now(),
                title: "📅 Nueva Reserva",
                message: "Nueva reserva de María García para hoy",
                type: "new_reservation",
                data: {
                    guestName: "María García",
                    checkInDate: new Date().toISOString(),
                    roomType: "Suite",
                    totalAmount: 150.0,
                },
                read: false,
                createdAt: new Date().toISOString(),
            };
            // Enviar vía WebSocket a todas las conexiones
            const sent = websocket_1.default.broadcast({
                type: "notification",
                data: testNotification,
                timestamp: new Date().toISOString(),
            });
            console.log(`📤 Notificación de nueva reserva enviada a ${sent} conexión(es)`);
            ctx.send({
                success: true,
                message: `Notificación de nueva reserva enviada a ${sent} conexión(es)`,
                notification: testNotification,
                sent: sent > 0,
            });
        }
        catch (error) {
            console.error("Error enviando notificación de nueva reserva:", error);
            ctx.internalServerError("Error enviando notificación de nueva reserva");
        }
    },
    /**
     * Probar alerta de seguridad
     */
    async testSecurityAlert(ctx) {
        try {
            console.log("🧪 Probando alerta de seguridad");
            // Crear notificación de prueba directamente
            const testNotification = {
                id: Date.now(),
                title: "🚨 Alerta de Seguridad",
                message: "Actividad sospechosa detectada en el área de la piscina",
                type: "security_alert",
                data: {
                    location: "Área de Piscina",
                    severity: "high",
                    reportedBy: "security",
                    timestamp: new Date().toISOString(),
                },
                read: false,
                createdAt: new Date().toISOString(),
            };
            // Enviar vía WebSocket a todas las conexiones
            const sent = websocket_1.default.broadcast({
                type: "notification",
                data: testNotification,
                timestamp: new Date().toISOString(),
            });
            console.log(`📤 Alerta de seguridad enviada a ${sent} conexión(es)`);
            ctx.send({
                success: true,
                message: `Alerta de seguridad enviada a ${sent} conexión(es)`,
                notification: testNotification,
                sent: sent > 0,
            });
        }
        catch (error) {
            console.error("Error enviando alerta de seguridad:", error);
            ctx.internalServerError("Error enviando alerta de seguridad");
        }
    },
};
