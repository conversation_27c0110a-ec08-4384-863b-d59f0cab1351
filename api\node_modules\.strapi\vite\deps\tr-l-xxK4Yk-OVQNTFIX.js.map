{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/tr-l-xxK4Yk.mjs"], "sourcesContent": ["const tr = {\n  \"coming-soon\": \"Bu içerik şuanda düzenleniyor. Bir kaç hafta sonra yayında olacak!\",\n  \"components.Row.open\": \"Aç\",\n  \"components.Row.regenerate\": \"Yeniden üret\",\n  \"containers.HomePage.Block.title\": \"Versiyonlar\",\n  \"containers.HomePage.Button.update\": \"Güncelle\",\n  \"containers.HomePage.PluginHeader.title\": \"Dokümantasyon — Ayarlar\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"Anladım\",\n  \"containers.HomePage.PopUpWarning.message\": \"Bu versiyonu silmek istediğinden emin misin?\",\n  \"containers.HomePage.copied\": \"Token panoya kopyalandı\",\n  \"containers.HomePage.form.jwtToken\": \"JWT tokenını al\",\n  \"containers.HomePage.form.jwtToken.description\": \"İstek atmak için bu tokenı kopyala ve swaggerda kullan\",\n  \"containers.HomePage.form.password\": \"<PERSON><PERSON><PERSON>\",\n  \"containers.HomePage.form.password.inputDescription\": \"Dokümantasyona erişmek için şifreyi belirle\",\n  \"containers.HomePage.form.restrictedAccess\": \"Kısıtlı erişim\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"Dokümantasyon uç noktasını gizle. Varsayılan olarak erişim herkese açıktır\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"Üretilen dosyaları göster\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"Üretilen dokümantasyonun üzerine yazmak istediğinde kullanışlıdır. \\nEklenti dosyaları model ve eklentilere göre ayrı olarak üretecektir. \\nBu seçeneği etkinleştirerek dokümantasyonu özelleştirmen kolaylaşacaktır.\",\n  \"error.deleteDoc.versionMissing\": \"Silmek istediğin versiyon bulunmuyor.\",\n  \"error.noVersion\": \"Bir versiyon gerekli.\",\n  \"error.regenerateDoc\": \"Dokümanı yeniden üretirken bir hata oluştu\",\n  \"error.regenerateDoc.versionMissing\": \"Üretmeye çalıştığın versiyon bulunmuyor\",\n  \"notification.delete.success\": \"Doküman silindi\",\n  \"notification.generate.success\": \"Doküman üretildi\",\n  \"notification.update.success\": \"Ayarlar başarıyla güncellendi\",\n  \"pages.PluginPage.Button.open\": \"Dokümanı aç\",\n  \"pages.PluginPage.header.description\": \"Dokümantasyon eklentisini ayarla\",\n  \"pages.PluginPage.table.generated\": \"Son üretilme\",\n  \"pages.PluginPage.table.icon.regenerate\": \"Yeniden üret: {target}\",\n  \"pages.PluginPage.table.icon.show\": \"Aç: {target}\",\n  \"pages.PluginPage.table.version\": \"Versiyon\",\n  \"pages.SettingsPage.header.description\": \"Dokümantasyon eklentisini ayarla\",\n  \"pages.SettingsPage.header.save\": \"Kaydet\",\n  \"pages.SettingsPage.toggle.hint\": \"Dokümantasyon uç noktasını gizli yap\",\n  \"pages.SettingsPage.toggle.label\": \"Kısıtlı Erişim\",\n  \"plugin.description.long\": \"Bir OpenAPI Dokümanı oluştur ve Swagger UI ile APIni görselleştir.\",\n  \"plugin.description.short\": \"Bir OpenAPI Dokümanı oluştur ve Swagger UI ile APIni görselleştir.\",\n  \"plugin.name\": \"Dokümantasyon\"\n};\nexport {\n  tr as default\n};\n//# sourceMappingURL=tr-l-xxK4Yk.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}