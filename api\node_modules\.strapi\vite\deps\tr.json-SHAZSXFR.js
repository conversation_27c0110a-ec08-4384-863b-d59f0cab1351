import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/email/dist/admin/translations/tr.json.mjs
var link = "Bağlantı";
var tr = {
  link,
  "Settings.email.plugin.button.test-email": "Deneme e-postası gönder",
  "Settings.email.plugin.label.defaultFrom": "Varsayılan gönderim adresi",
  "Settings.email.plugin.label.defaultReplyTo": "Varsayılan yanıt adresi",
  "Settings.email.plugin.label.provider": "E-Posta sağlayıcı",
  "Settings.email.plugin.label.testAddress": "Alıcı e-posta adresi",
  "Settings.email.plugin.notification.config.error": "E-posta ayarlarını okuma hatası",
  "Settings.email.plugin.notification.data.loaded": "E-posta ayarları yüklendi",
  "Settings.email.plugin.notification.test.error": "{to} adresine deneme e-postası gönderimi başarıs<PERSON>z oldu",
  "Settings.email.plugin.notification.test.success": "E-posta denemesi başarılı. {to} adresinin posta kutusunu kontrol edin",
  "Settings.email.plugin.placeholder.defaultFrom": "ör: Strapi Yanıtlama <<EMAIL>>",
  "Settings.email.plugin.placeholder.defaultReplyTo": "ör: Strapi <<EMAIL>>",
  "Settings.email.plugin.placeholder.testAddress": "ör: <EMAIL>",
  "Settings.email.plugin.subTitle": "E-posta eklentisinin ayarlarını deneyin",
  "Settings.email.plugin.text.configuration": "Plugin {file} dosyası üzerinden ayarlanıyor. Detaylar için şu bağlantıya bakın: {link}",
  "Settings.email.plugin.title": "Kurulum",
  "Settings.email.plugin.title.config": "Kurulum",
  "Settings.email.plugin.title.test": "E-posta gönderimini dene",
  "SettingsNav.link.settings": "Ayarlar",
  "SettingsNav.section-label": "E-posta eklentisi",
  "components.Input.error.validation.email": "Geçersiz e-posta adresi"
};
export {
  tr as default,
  link
};
//# sourceMappingURL=tr.json-SHAZSXFR.js.map
