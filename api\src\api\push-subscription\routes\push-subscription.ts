/**
 * push-subscription router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/push-subscriptions",
      handler: "push-subscription.create",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "DELETE",
      path: "/push-subscriptions/:endpoint",
      handler: "push-subscription.delete",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "POST",
      path: "/push-notifications/send",
      handler: "push-subscription.sendNotification",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
  ],
};
