{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/ru-DGSjru5m.mjs"], "sourcesContent": ["const configurations = \"конфигурации\";\nconst from = \"из\";\nconst ru = {\n  \"attribute.boolean\": \"Boolean\",\n  \"attribute.boolean.description\": \"Да или нет, 1 или 0, Истина или Ложь\",\n  \"attribute.component\": \"Компонент\",\n  \"attribute.component.description\": \"Компонент - группа полей, доступных для повторения или повторного использования\",\n  \"attribute.date\": \"Date\",\n  \"attribute.date.description\": \"Элемент управления датой и временем\",\n  \"attribute.datetime\": \"Datetime\",\n  \"attribute.dynamiczone\": \"Dynamic zone\",\n  \"attribute.dynamiczone.description\": \"Компоненты с динамическим редактированием\",\n  \"attribute.email\": \"Email\",\n  \"attribute.email.description\": \"Поле электронной почты с проверкой формата\",\n  \"attribute.enumeration\": \"Enumeration\",\n  \"attribute.enumeration.description\": \"Перечень значений, выбирается одно\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"Данные в формате JSON\",\n  \"attribute.media\": \"Media\",\n  \"attribute.media.description\": \"Аудио- видео- и прочие медиафайлы\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Number\",\n  \"attribute.number.description\": \"Числа (integer, float, decimal)\",\n  \"attribute.password\": \"Password\",\n  \"attribute.password.description\": \"Поле пароля с шифрованием\",\n  \"attribute.relation\": \"Relation\",\n  \"attribute.relation.description\": \"Ссылка на какой-либо тип контента\",\n  \"attribute.richtext\": \"Rich text\",\n  \"attribute.richtext.description\": \"Элемент управления для редактирования текста с форматированием\",\n  \"attribute.text\": \"Text\",\n  \"attribute.text.description\": \"Маленький или длинный текст, например заголовок или описание\",\n  \"attribute.time\": \"Time\",\n  \"attribute.timestamp\": \"Timestamp\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Уникальный идентификатор\",\n  \"button.attributes.add.another\": \"Ещё поле\",\n  \"button.component.add\": \"Добавить компонент\",\n  \"button.component.create\": \"Создать компонент\",\n  \"button.model.create\": \"Создать новый тип контента\",\n  \"button.single-types.create\": \"Создать новый единичный тип\",\n  \"component.repeatable\": \"(повторяется)\",\n  \"components.componentSelect.no-component-available\": \"Вы уже добавили все имеющиеся группы полей\",\n  \"components.componentSelect.no-component-available.with-search\": \"Подходящих групп полей не найдено\",\n  \"components.componentSelect.value-component\": \"Выбрано компонентов - {number} (наберите для поиска)\",\n  \"components.componentSelect.value-components\": \"Компонентов выбрано - {number}\",\n  configurations,\n  \"contentType.collectionName.description\": \"Полезно, когда имя вашего типа контента и имя вашей таблицы различаются\",\n  \"contentType.collectionName.label\": \"Название коллекции\",\n  \"contentType.displayName.label\": \"Отображаемое имя\",\n  \"contentType.kind.change.warning\": \"Вы только что изменили тип типа контента: API будет сброшен (маршруты, контроллеры и сервисы будут перезаписаны).\",\n  \"error.attributeName.reserved-name\": \"Это имя нельзя использовать в вашем типе контента, так как это может нарушить другие функции.\",\n  \"error.contentTypeName.reserved-name\": \"Это название зарезервировано и не может быть использовано в проекте\",\n  \"error.validation.enum-duplicate\": \"Повторяющиеся значения не допускаются\",\n  \"error.validation.enum-empty-string\": \"Не может быть пустой строкой\",\n  \"error.validation.minSupMax\": \"Не может выходить за ограничения\",\n  \"error.validation.regex\": \"Шаблон регулярного выражения недействителен\",\n  \"error.validation.relation.targetAttribute-taken\": \"Это имя существует в целевом объекте\",\n  \"form.attribute.component.option.add\": \"Добавление компонента\",\n  \"form.attribute.component.option.create\": \"Создание нового компонента\",\n  \"form.attribute.component.option.create.description\": \"Компонент предоставляется в разных типах и группах и будет доступен отовсюду\",\n  \"form.attribute.component.option.repeatable\": \"Повторяющийся компонент\",\n  \"form.attribute.component.option.repeatable.description\": \"Применимо для множественных вхождений (массивов) ингредиентов, мета-тегов и т.д.\",\n  \"form.attribute.component.option.reuse-existing\": \"Использовать существующий компонент\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Использовать повторно созданный ранее компонент, чтобы обеспечить согласованность данных в разных типах контента.\",\n  \"form.attribute.component.option.single\": \"Одиночный компонент\",\n  \"form.attribute.component.option.single.description\": \"Применимо для группировки полей, таких как полный адрес, основная информация и т.д.\",\n  \"form.attribute.item.customColumnName\": \"Названия столбцов\",\n  \"form.attribute.item.customColumnName.description\": \"Может быть полезно переименовать названия столбцов для более читаемых ответов API.\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Название поля\",\n  \"form.attribute.item.enumeration.graphql\": \"Название поля в GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"Позволяет переопределить название поля в GraphQL, сгенерированное по умолчанию\",\n  \"form.attribute.item.enumeration.placeholder\": \"Например:\\nутро\\nполдень\\nвечер\",\n  \"form.attribute.item.enumeration.rules\": \"Значения (одна линия на значение)\",\n  \"form.attribute.item.maximum\": \"Максимальное значение\",\n  \"form.attribute.item.maximumLength\": \"Максимальная длина\",\n  \"form.attribute.item.minimum\": \"Минимальное значение\",\n  \"form.attribute.item.minimumLength\": \"Минимальная длина\",\n  \"form.attribute.item.number.type\": \"Числовой формат\",\n  \"form.attribute.item.number.type.biginteger\": \"Большое целое (ex: 123456789)\",\n  \"form.attribute.item.number.type.decimal\": \"Десятичное (ex: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"С плавающей точкой (ex: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"Целое (ex: 10)\",\n  \"form.attribute.item.privateField\": \"Закрытое поле\",\n  \"form.attribute.item.privateField.description\": \"Это поле не будет отображаться в ответе API\",\n  \"form.attribute.item.requiredField\": \"Обязательное поле\",\n  \"form.attribute.item.requiredField.description\": \"Вы не сможете создать запись, если это поле не заполнено\",\n  \"form.attribute.item.text.regex\": \"Шаблон регулярного выражения\",\n  \"form.attribute.item.text.regex.description\": \"Текст регулярного выражения\",\n  \"form.attribute.item.uniqueField\": \"Уникальное поле\",\n  \"form.attribute.item.uniqueField.description\": \"Вы не сможете создать запись, если уже существует запись с таким значением\",\n  \"form.attribute.media.allowed-types\": \"Выберите разрешенные типы медиа\",\n  \"form.attribute.media.allowed-types.option-files\": \"Файлы\",\n  \"form.attribute.media.allowed-types.option-images\": \"Изображения\",\n  \"form.attribute.media.allowed-types.option-videos\": \"Видео\",\n  \"form.attribute.media.option.multiple\": \"Множественные медиа\",\n  \"form.attribute.media.option.multiple.description\": \"Применимо для слайдеров и каруселей\",\n  \"form.attribute.media.option.single\": \"Одиночное медиа\",\n  \"form.attribute.media.option.single.description\": \"Применимо для аватаров, картинок профиля и пр.\",\n  \"form.attribute.settings.default\": \"Стандартное значение\",\n  \"form.attribute.text.option.long-text\": \"Большой текст\",\n  \"form.attribute.text.option.long-text.description\": \"Применимо для описания, биографии... (не учествует в поиске)\",\n  \"form.attribute.text.option.short-text\": \"Короткий текст\",\n  \"form.attribute.text.option.short-text.description\": \"Применимо для названий, заголовков, ссылок... (участвует в поиске)\",\n  \"form.button.add-components-to-dynamiczone\": \"Добавить компоненты в зону\",\n  \"form.button.add-field\": \"Еще поле\",\n  \"form.button.add-first-field-to-created-component\": \"Добавить первое поле в компонент\",\n  \"form.button.add.field.to.collectionType\": \"Добавить поле в коллекцию\",\n  \"form.button.add.field.to.component\": \"Добавить поле в компонент\",\n  \"form.button.add.field.to.contentType\": \"Добавить поле в тип контента\",\n  \"form.button.add.field.to.singleType\": \"Добавить поле в тип\",\n  \"form.button.cancel\": \"Отменить\",\n  \"form.button.collection-type.description\": \"Лучше всего подходит для нескольких экземпляров, таких как статьи, товары, комментарии и т.д.\",\n  \"form.button.configure-component\": \"настроить компонент\",\n  \"form.button.configure-view\": \"Настроить отображение\",\n  \"form.button.select-component\": \"Выбрать компонент\",\n  \"form.button.single-type.description\": \"Лучше всего подходит для одного экземпляра, например, о нас, домашняя страница и т.д.\",\n  from,\n  \"modalForm.attribute.form.base.name.description\": \"Пробелы в имени атрибута недопустимы\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"e.g. slug, seoUrl, canonicalUrl\",\n  \"modalForm.attribute.target-field\": \"Добавленные поля\",\n  \"modalForm.attributes.select-component\": \"Выбор компонента\",\n  \"modalForm.attributes.select-components\": \"Выбор компонентов\",\n  \"modalForm.component.header-create\": \"Создание компонента\",\n  \"modalForm.components.create-component.category.label\": \"Выберите категорию или введите имя новой\",\n  \"modalForm.components.icon.label\": \"Иконка\",\n  \"modalForm.editCategory.base.name.description\": \"Пробелы в имени категории недопустимы\",\n  \"modalForm.header-edit\": \"Редактирование {name}\",\n  \"modalForm.header.categories\": \"Категории\",\n  \"modalForm.header.back\": \"Назад\",\n  \"modalForm.singleType.header-create\": \"Создание одиночного типа\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Добавить компонент в динамическую зону\",\n  \"modalForm.sub-header.attribute.create\": \"Добавить новое поле типа {type}\",\n  \"modalForm.sub-header.attribute.create.step\": \"Добавить новый компонент ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Изменение {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Выбрать имя поля типа контента\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Выбрать имя поля компонента\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Выберите поле для вашего отдельного типа\",\n  \"modelPage.attribute.relation-polymorphic\": \"Связь (полиморфная)\",\n  \"modelPage.attribute.relationWith\": \"Связь с\",\n  \"notification.info.autoreaload-disable\": \"Для использования этого плагина требуется функция автоматической загрузки. Запустите свой сервер с помощью `strapi develop`\",\n  \"notification.info.creating.notSaved\": \"Пожалуйста, сохраните изменения перед созданием нового компонента типа контента \",\n  \"plugin.description.long\": \"Моделируйте структуру данных вашего API. Создавайте новые поля и связи всего за минуту. Файлы в вашем проекте создаются и обновляются автоматически.\",\n  \"plugin.description.short\": \"Моделируйте структуру данных вашего API.\",\n  \"popUpForm.navContainer.advanced\": \"Расширенные настройки\",\n  \"popUpForm.navContainer.base\": \"Базовые настройки\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Вы уверены, что хотите отменить изменения?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Вы уверены, что хотите отменить сделанные изменения? Некоторые компоненты были созданы или изменены...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Вы уверены, что хотите удалить категорию? Все входящие в нее компоненты будут также удалены.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Вы уверены, что хотите удалить этот компонент?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Вы уверены, что хотите удалить этот тип контента?\",\n  \"popUpWarning.draft-publish.button.confirm\": \"Да, отключить\",\n  \"popUpWarning.draft-publish.message\": \"Если вы отключите систему черновиков/публикаций, ваши черновики будут удалены.\",\n  \"popUpWarning.draft-publish.second-message\": \"Вы уверены, что хотите отключить его?\",\n  \"prompt.unsaved\": \"Вы уверены, что хотите выйти? Все изменения будут потеряны.\",\n  \"relation.attributeName.placeholder\": \"Пример: автор, категория, тег\",\n  \"relation.manyToMany\": \"имеет и принадлежит многим\",\n  \"relation.manyToOne\": \"имеет много\",\n  \"relation.manyWay\": \"имеет много\",\n  \"relation.oneToMany\": \"принадлежит многим\",\n  \"relation.oneToOne\": \"имеет один и принадлежит одному\",\n  \"relation.oneWay\": \"имеет один\"\n};\nexport {\n  configurations,\n  ru as default,\n  from\n};\n//# sourceMappingURL=ru-DGSjru5m.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB;", "names": []}