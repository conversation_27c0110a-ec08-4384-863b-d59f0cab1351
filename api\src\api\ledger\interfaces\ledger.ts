export type LedgerOwner = {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  address?: string;
  name?: string;
};

export type LedgerAttributes = {
  month: string;
  expensa: number;
  honorarios: number;
  interes: number;
  pagos: number;
  saldo: number;
  fecha: Date;
  estado: 'pendiente' | 'pagado' | 'rechazado';
  comprobante?: any;
  comentarios?: string;
  owner?: LedgerOwner;
  apartmentAddress?: string;
};

export type LedgerEntity = LedgerAttributes & {
  id: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
};

export type LedgerEntry = {
  month: string;
  expensa: number;
  honorarios: number;
  interes: number;
  pagos: number;
  saldo: number;
};

export type GroupedLedgerEntry = {
  id: string;
  propietario: string;
  ledger: LedgerEntry[];
};

export type GroupedLedgerResponse = {
  data: GroupedLedgerEntry[];
};

export type BulkUploadResponse = {
  success: boolean;
  count: number;
  data: GroupedLedgerEntry[];
};
