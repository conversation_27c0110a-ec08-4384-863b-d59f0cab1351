{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": "Organize your content into categories"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "slug": {"type": "uid"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "category"}, "description": {"type": "text"}}}