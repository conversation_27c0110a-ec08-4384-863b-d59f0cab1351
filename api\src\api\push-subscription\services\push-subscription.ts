/**
 * push-subscription service
 */

import { factories } from '@strapi/strapi';

interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  user: number;
  expirationTime?: number;
}

export default factories.createCoreService('api::push-subscription.push-subscription', ({ strapi }) => ({
  async findByEndpoint(endpoint: string) {
    return await strapi.db.query('api::push-subscription.push-subscription').findOne({
      where: { endpoint }
    });
  },

  async findByUser(userId: number) {
    return await strapi.db.query('api::push-subscription.push-subscription').findOne({
      where: { user: userId }
    });
  },

  async createOrUpdate(data: PushSubscriptionData) {
    try {
      const existingSubscription = await this.findByUser(data.user);

      if (existingSubscription) {
        return await strapi.entityService.update('api::push-subscription.push-subscription', existingSubscription.id, {
          data: {
            endpoint: data.endpoint,
            keys: data.keys,
            expirationTime: data.expirationTime
          }
        });
      }

      return await strapi.entityService.create('api::push-subscription.push-subscription', {
        data: {
          ...data,
          publishedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error en createOrUpdate:', error);
      throw error;
    }
  }
}));
