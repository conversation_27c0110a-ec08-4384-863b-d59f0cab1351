import { createReadStream } from "fs";
import { join } from "path";
import { ReadableOptions } from "stream";

export const streamUtils = {
  getTemplatePath(type: string): string {
    return join(
      process.cwd(),
      "config",
      "email-templates",
      `reservation-${type}.html`
    );
  },
  async readTemplate(
    templatePath: string,
    options: ReadableOptions = {}
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      let content = "";
      const stream = createReadStream(templatePath, {
        ...options,
        encoding: "utf8",
      });

      stream.on("data", (chunk) => {
        content += chunk;
      });
      stream.on("error", (error) => reject(error));
      stream.on("end", () => resolve(content));
    });
  },

  async processTemplate(content: string, data: any): Promise<string> {
    let processedContent = content;

    // Procesar variables simples
    Object.entries(data).forEach(([key, value]) => {
      if (typeof value === "string" || typeof value === "number") {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, "g");
        processedContent = processedContent.replace(regex, String(value));
      }
    });

    // Procesar objetos anidados
    const flattenObject = (obj: any, prefix = ""): Record<string, string> => {
      return Object.entries(obj).reduce(
        (acc: Record<string, string>, [key, value]) => {
          const newKey = prefix ? `${prefix}.${key}` : key;

          if (value && typeof value === "object" && !Array.isArray(value)) {
            Object.assign(acc, flattenObject(value, newKey));
          } else if (
            typeof value === "string" ||
            typeof value === "number" ||
            typeof value === "boolean"
          ) {
            acc[newKey] = String(value);
          }

          return acc;
        },
        {}
      );
    };

    const flatData = flattenObject(data);
    Object.entries(flatData).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, "g");
      processedContent = processedContent.replace(regex, value);
    });

    return processedContent;
  },
};
