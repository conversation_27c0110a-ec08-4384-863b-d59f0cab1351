/**
 * pool-cleaning controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::pool-cleaning.pool-cleaning" as any,
  ({ strapi }) => ({
    async create(ctx: Context) {
      try {
        const { type, ph, chlorine, notes, cleanedAt } = ctx.request.body.data;

        const entry = await strapi.entityService.create(
          "api::pool-cleaning.pool-cleaning" as any,
          {
            data: {
              type,
              ph,
              chlorine,
              notes,
              cleanedAt: cleanedAt || new Date().toISOString(),
            },
          }
        );

        return ctx.send(entry);
      } catch (error) {
        console.error("Error al crear registro de limpieza:", error);
        return ctx.badRequest({
          error: error.message + " Error al crear registro de limpieza",
        });
      }
    },

    async update(ctx: Context) {
      try {
        const { id } = ctx.params;
        const { type, ph, chlorine, notes, cleanedAt } = ctx.request.body.data;

        const entry = await strapi.entityService.update(
          "api::pool-cleaning.pool-cleaning" as any,
          id,
          {
            data: {
              type,
              ph,
              chlorine,
              notes,
              cleanedAt,
            },
          }
        );

        return ctx.send(entry);
      } catch (error) {
        console.error("Error al actualizar registro de limpieza:", error);
        return ctx.badRequest({
          error: error.message + " Error al actualizar registro de limpieza",
        });
      }
    },

    async delete(ctx: Context) {
      try {
        const { id } = ctx.params;

        const entry = await strapi.entityService.delete(
          "api::pool-cleaning.pool-cleaning" as any,
          id
        );

        return ctx.send(entry);
      } catch (error) {
        console.error("Error al eliminar registro de limpieza:", error);
        return ctx.badRequest({
          error: error.message + " Error al eliminar registro de limpieza",
        });
      }
    },

    async findAll(ctx: Context) {
      try {
        const entries = await strapi.db
          .query("api::pool-cleaning.pool-cleaning" as any)
          .findMany({
            orderBy: { cleanedAt: "DESC" },
          });

        // Transformar los datos al formato esperado por el frontend
        const processedEntries = entries.map((entry) => ({
          id: entry.id,
          type: entry.type,
          ph: entry.ph,
          chlorine: entry.chlorine,
          notes: entry.notes,
          cleanedAt: entry.cleanedAt,
        }));

        return ctx.send(processedEntries);
      } catch (error) {
        console.error("Error en findAll:", error);
        ctx.throw(500, error);
      }
    },
  })
);
