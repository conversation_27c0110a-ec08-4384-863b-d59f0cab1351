export default {
  routes: [
    // Rutas públicas
    {
      method: "GET",
      path: "/ledger",
      handler: "ledger.find",
      config: {
        auth: false,
      },
    },
    {
      method: "GET",
      path: "/ledger/owner/:ownerId",
      handler: "ledger.findByOwner",
      config: {
        auth: false,
      },
    },

    // Rutas protegidas
    {
      method: "POST",
      path: "/ledger",
      handler: "ledger.create",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
    {
      method: "PUT",
      path: "/ledger/:id",
      handler: "ledger.update",
      config: {
        policies: ["global::isAuthenticated"],
      },
    },
  ],
};
