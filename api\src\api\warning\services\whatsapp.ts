/**
 * WhatsApp service using SendGrid
 */

import { Strapi } from '@strapi/strapi';
import sgMail, { MailDataRequired } from '@sendgrid/mail';

interface WhatsAppConfig {
  apiKey: string;
  fromNumber: string;
}

export default ({ strapi }: { strapi: Strapi }) => ({
  initialize() {
    const config = strapi.config.get('sendgrid') as WhatsAppConfig;
    
    if (!config?.apiKey) {
      console.warn('SendGrid configuration is missing. WhatsApp notifications will be disabled.');
      return;
    }

    sgMail.setApiKey(config.apiKey);
  },

  async sendWhatsApp(to: string, message: string) {
    const config = strapi.config.get('sendgrid') as WhatsAppConfig;

    try {
      // SendGrid tiene una API específica para WhatsApp
      const msg: MailDataRequired = {
        to,
        from: config.fromNumber,
        subject: 'WhatsApp Message', // Required by SendGrid API
        text: message,
        channel: 'whatsapp' as 'whatsapp' // Type assertion to match MailDataRequired
      };

      const result = await sgMail.send(msg);
      return result;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      throw error;
    }
  }
});
