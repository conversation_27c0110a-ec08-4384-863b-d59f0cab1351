/**
 * Middleware para validar que un bloque (apartamento) sea único al registrar usuarios
 * Solo se valida la dirección para roles específicos (ej. residentes)
 */

import { Context, Next } from "koa";

export default (config, { strapi }) => {
  return async (ctx: Context, next: Next) => {
    // Solo validar en el registro de usuarios
    if (
      ctx.request.url === "/api/auth/local/register" &&
      ctx.request.method === "POST"
    ) {
      const { address, role } = ctx.request.body;

      // Si no hay rol definido o es un arrendatario, omitir validación de dirección
      if (!role) {
        return await next();
      }

      // Obtener el ID del rol
      let roleId = typeof role === 'number' 
        ? role 
        : (typeof role === 'object' ? role.id : null);

      // Si no se pudo determinar el ID del rol, intentar obtenerlo por tipo
      if (!roleId && typeof role === 'string') {
        const roleEntity = await strapi.db.query('plugin::users-permissions.role')
          .findOne({ where: { type: role } });
        roleId = roleEntity?.id;
      }

      // Roles que requieren validación de dirección (residentes)
      const rolesQueRequierenDireccion = ['residente', 'residential'];
      
      // Verificar si el rol del usuario requiere validación de dirección
      const roleRequiereDireccion = roleId 
        ? await strapi.db.query('plugin::users-permissions.role')
            .findOne({ 
              where: { 
                id: roleId,
                type: { $in: rolesQueRequierenDireccion }
              } 
            })
        : false;

      if (roleRequiereDireccion) {
        if (!address) {
          return ctx.throw(400, "El campo address es requerido para este tipo de usuario");
        }

        // Verificar si el bloque ya está asignado
        const existingUser = await strapi.db
          .query("plugin::users-permissions.user")
          .findOne({
            where: { address },
          });

        if (existingUser) {
          return ctx.throw(
            400,
            `El bloque ${address} ya está asignado a otro usuario`
          );
        }

        // Verificar si el bloque existe
        const block = await strapi.db.query("api::block.block").findOne({
          where: { block: address },
        });

        if (!block) {
          return ctx.throw(400, `El bloque ${address} no existe`);
        }
      }
    }

    await next();
  };
};
