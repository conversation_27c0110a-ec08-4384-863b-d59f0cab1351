{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/th--u3VqsON.mjs"], "sourcesContent": ["const configurations = \"การตั้งค่า\";\nconst from = \"จาก\";\nconst th = {\n  \"attribute.boolean\": \"บูลีน\",\n  \"attribute.boolean.description\": \"Yes หรือ no, 1 หรือ 0, true หรือ false\",\n  \"attribute.component\": \"คอมโพเนนต์\",\n  \"attribute.component.description\": \"กลุ่มของฟิลด์ที่คุณสามารถซ้ำหรือนำกลับมาใช้ใหม่\",\n  \"attribute.date\": \"วันที่\",\n  \"attribute.date.description\": \"เครื่องมือเลือกวันที่ที่มีชั่วโมงนาทีและวินาที\",\n  \"attribute.datetime\": \"วันที่เวลา\",\n  \"attribute.dynamiczone\": \"ไดนามิกโซน\",\n  \"attribute.dynamiczone.description\": \"เลือกคอมโพเนนต์แบบไดนามิกเมื่อแก้ไขเนื้อหา\",\n  \"attribute.email\": \"อีเมล\",\n  \"attribute.email.description\": \"ฟิลด์อีเมลที่มีรูปแบบการตรวจสอบความถูกต้อง\",\n  \"attribute.enumeration\": \"การแจงนับ\",\n  \"attribute.enumeration.description\": \"รายการค่าจากนั้นเลือกค่าหนึ่ง\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"ข้อมูลในรูปแบบ JSON\",\n  \"attribute.media\": \"สื่อ\",\n  \"attribute.media.description\": \"ไฟล์เช่นอิมเมจ, วิดีโอ, เป็นต้น\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"หมายเลข\",\n  \"attribute.number.description\": \"ตัวเลข (integer, float, decimal)\",\n  \"attribute.password\": \"รหัสผ่าน\",\n  \"attribute.password.description\": \"ฟิลด์รหัสผ่านที่มีการเข้ารหัส\",\n  \"attribute.relation\": \"ความสัมพันธ์\",\n  \"attribute.relation.description\": \"อ้างถึงชนิดคอลเล็กชัน\",\n  \"attribute.richtext\": \"ข้อความแบบ Rich\",\n  \"attribute.richtext.description\": \"เครื่องมือแก้ไขข้อความ ที่มีอ็อพชันการจัดรูปแบบ\",\n  \"attribute.text\": \"ข้อความ\",\n  \"attribute.text.description\": \"ข้อความเล็กหรือยาวเช่นหัวเรื่องหรือคำอธิบาย\",\n  \"attribute.time\": \"เวลา\",\n  \"attribute.timestamp\": \"การประทับเวลา\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"ตัวระบุเฉพาะ\",\n  \"button.attributes.add.another\": \"เพิ่มฟิลด์อื่น\",\n  \"button.component.add\": \"เพิ่มคอมโพเนนต์\",\n  \"button.component.create\": \"สร้างคอมโพเนนต์ใหม่\",\n  \"button.model.create\": \"สร้างชนิดการรวบรวมใหม่\",\n  \"button.single-types.create\": \"สร้างชนิดเดี่ยวใหม่\",\n  \"component.repeatable\": \"(ทำซ้ำได้)\",\n  \"components.componentSelect.no-component-available\": \"คุณได้เพิ่มส่วนประกอบทั้งหมดของคุณแล้ว\",\n  \"components.componentSelect.no-component-available.with-search\": \"ไม่มีคอมโพเนนต์ที่ตรงกับการค้นหาของคุณ\",\n  \"components.componentSelect.value-component\": \"{number} คอมโพเนนต์ที่เลือก (ชนิดที่จะค้นหาสำหรับคอมโพเนนต์)\",\n  \"components.componentSelect.value-components\": \"เลือกคอมโพเนนต์ {number} แล้ว\",\n  configurations,\n  \"contentType.collectionName.description\": \"มีประโยชน์เมื่อชื่อของชนิดเนื้อหาและชื่อตารางของคุณแตกต่างกัน\",\n  \"contentType.collectionName.label\": \"ชื่อคอลเล็กชัน\",\n  \"contentType.displayName.label\": \"ชื่อที่แสดง\",\n  \"contentType.kind.change.warning\": \"คุณเพียงแค่เปลี่ยนชนิดของชนิดเนื้อหา: API จะถูกรีเซ็ต (เส้นทางตัวควบคุมและเซอร์วิสจะถูกเขียนทับ)\",\n  \"error.attributeName.reserved-name\": \"ชื่อนี้ไม่สามารถใช้ในชนิดเนื้อหาของคุณได้ เนื่องจากอาจทำให้ฟังก์ชันอื่นเสียหายได้\",\n  \"error.contentTypeName.reserved-name\": \"ชื่อนี้ไม่สามารถใช้ในโปรเจ็กต์ของคุณ เนื่องจากอาจหยุดการทำงานของฟังก์ชันอื่น\",\n  \"error.validation.enum-duplicate\": \"ไม่อนุญาตให้ใช้ค่าซ้ำ\",\n  \"error.validation.minSupMax\": \"ไม่สามารถเหนือกว่า\",\n  \"error.validation.regex\": \"รูปแบบ Regex ไม่ถูกต้อง\",\n  \"error.validation.relation.targetAttribute-taken\": \"ชื่อนี้มีอยู่ในเป้าหมาย\",\n  \"form.attribute.component.option.add\": \"เพิ่มคอมโพเนนต์\",\n  \"form.attribute.component.option.create\": \"สร้างคอมโพเนนต์ใหม่\",\n  \"form.attribute.component.option.create.description\": \"คอมโพเนนต์ที่แบ่งใช้ระหว่างชนิดและคอมโพเนนต์จะพร้อมใช้งานและสามารถเข้าถึงได้ทุกที่\",\n  \"form.attribute.component.option.repeatable\": \"คอมโพเนนต์ที่ทำซ้ำได้\",\n  \"form.attribute.component.option.repeatable.description\": \"ดีที่สุดสำหรับหลายอินสแตนซ์ (ชุดข้อมูล) ของส่วนผสมเมตาแท็กและอื่นๆ\",\n  \"form.attribute.component.option.reuse-existing\": \"ใช้คอมโพเนนต์ที่มีอยู่\",\n  \"form.attribute.component.option.reuse-existing.description\": \"ใช้คอมโพเนนต์ที่สร้างไว้แล้วเพื่อทำให้ข้อมูลของคุณสอดคล้องกันระหว่างชนิดเนื้อหา\",\n  \"form.attribute.component.option.single\": \"คอมโพเนนต์เดียว\",\n  \"form.attribute.component.option.single.description\": \"ดีที่สุดสำหรับการจัดกลุ่มฟิลด์เช่นแอดเดรสแบบเต็มข้อมูลหลักและอื่นๆ\",\n  \"form.attribute.item.customColumnName\": \"ชื่อคอลัมน์แบบกำหนดเอง\",\n  \"form.attribute.item.customColumnName.description\": \"สิ่งนี้มีประโยชน์ในการเปลี่ยนชื่อคอลัมน์ฐานข้อมูลในรูปแบบที่ครอบคลุมมากขึ้นสำหรับการตอบกลับของ API\",\n  \"form.attribute.item.defineRelation.fieldName\": \"ชื่อฟิลด์\",\n  \"form.attribute.item.enumeration.graphql\": \"การเขียนทับชื่อสำหรับ GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"อนุญาตให้คุณเขียนทับชื่อที่สร้างขึ้นพื้นฐานสำหรับ GraphQL\",\n  \"form.attribute.item.enumeration.placeholder\": \"ตัวอย่าง:\\nเช้า\\nเที่ยง\\nเย็น\",\n  \"form.attribute.item.enumeration.rules\": \"ค่า (หนึ่งบรรทัดต่อค่า)\",\n  \"form.attribute.item.maximum\": \"ค่าสูงสุด\",\n  \"form.attribute.item.maximumLength\": \"ความยาวสูงสุด\",\n  \"form.attribute.item.minimum\": \"ค่าต่ำสุด\",\n  \"form.attribute.item.minimumLength\": \"ความยาวต่ำสุด\",\n  \"form.attribute.item.number.type\": \"รูปแบบตัวเลข\",\n  \"form.attribute.item.number.type.biginteger\": \"จำนวนเต็มขนาดใหญ่ (ตัวอย่าง: 1 2 3 4 5 6 7 8 9)\",\n  \"form.attribute.item.number.type.decimal\": \"เลขฐานสิบ (ตัวอย่าง: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"จำนวนจริง (ตัวอย่าง: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"จำนวนเต็ม (ตัวอย่าง: 10)\",\n  \"form.attribute.item.privateField\": \"ไพรเวตฟิลด์\",\n  \"form.attribute.item.privateField.description\": \"ฟิลด์นี้จะไม่แสดงขึ้นในการตอบกลับ API\",\n  \"form.attribute.item.requiredField\": \"ฟิลด์ที่จำเป็น\",\n  \"form.attribute.item.requiredField.description\": \"คุณจะไม่สามารถสร้างรายการได้หากฟิลด์นี้ว่างอยู่\",\n  \"form.attribute.item.text.regex\": \"รูปแบบ RegExp\",\n  \"form.attribute.item.text.regex.description\": \"ข้อความของนิพจน์ปกติ\",\n  \"form.attribute.item.uniqueField\": \"ฟิลด์เฉพาะ\",\n  \"form.attribute.item.uniqueField.description\": \"คุณจะไม่สามารถสร้างรายการได้ถ้ามีรายการที่มีเนื้อหาที่เหมือนกันอยู่แล้ว\",\n  \"form.attribute.media.allowed-types\": \"เลือกชนิดของสื่อบันทึกที่อนุญาต\",\n  \"form.attribute.media.allowed-types.option-files\": \"ไฟล์\",\n  \"form.attribute.media.allowed-types.option-images\": \"รูปภาพ\",\n  \"form.attribute.media.allowed-types.option-videos\": \"วิดีโอ\",\n  \"form.attribute.media.option.multiple\": \"สื่อบันทึกหลายสื่อ\",\n  \"form.attribute.media.option.multiple.description\": \"ดีที่สุดสำหรับสไลเดอร์, การหมุนหรือการดาวน์โหลดหลายไฟล์\",\n  \"form.attribute.media.option.single\": \"สื่อบันทึกเดี่ยว\",\n  \"form.attribute.media.option.single.description\": \"ดีที่สุดสำหรับ รูปภาพของโปรไฟล์หรือรูปภาพปก\",\n  \"form.attribute.settings.default\": \"ค่าพื้นฐาน\",\n  \"form.attribute.text.option.long-text\": \"ข้อความแบบยาว\",\n  \"form.attribute.text.option.long-text.description\": \"ดีที่สุดสำหรับคำอธิบายชีวประวัติ การค้นหาที่แท้จริงถูกปิดใช้งาน\",\n  \"form.attribute.text.option.short-text\": \"ข้อความแบบสั้น\",\n  \"form.attribute.text.option.short-text.description\": \"ดีที่สุดสำหรับหัวเรื่อง, ชื่อ, ลิงก์ (URL) และยังเปิดใช้งานการค้นหาที่แน่นอนบนฟิลด์\",\n  \"form.button.add-components-to-dynamiczone\": \"เพิ่มคอมโพเนนต์ลงในโซน\",\n  \"form.button.add-field\": \"เพิ่มฟิลด์อื่น\",\n  \"form.button.add-first-field-to-created-component\": \"เพิ่มฟิลด์แรกในคอมโพเนนต์\",\n  \"form.button.add.field.to.collectionType\": \"เพิ่มฟิลด์อื่นให้กับชนิดคอลเล็กชันนี้\",\n  \"form.button.add.field.to.component\": \"เพิ่มฟิลด์อื่นลงในคอมโพเนนต์นี้\",\n  \"form.button.add.field.to.contentType\": \"เพิ่มฟิลด์อื่นลงในชนิดเนื้อหานี้\",\n  \"form.button.add.field.to.singleType\": \"เพิ่มฟิลด์อื่นให้กับชนิดเดี่ยวนี้\",\n  \"form.button.cancel\": \"ยกเลิก\",\n  \"form.button.collection-type.description\": \"ดีที่สุดสำหรับหลายอินสแตนซ์เช่น บทความ, ผลิตภัณฑ์, ข้อคิดเห็น เป็นต้น\",\n  \"form.button.configure-component\": \"ตั้งค่าคอมโพเนนต์\",\n  \"form.button.configure-view\": \"กำหนดคอนฟิกมุมมอง\",\n  \"form.button.select-component\": \"เลือกคอมโพเนนต์\",\n  \"form.button.single-type.description\": \"ที่ดีที่สุดสำหรับอินสแตนซ์เดี่ยวเช่น เกี่ยวกับเรา, โฮมเพจ, เป็นต้น\",\n  from,\n  \"modalForm.attribute.form.base.name.description\": \"ไม่มีพื้นที่ว่างที่อนุญาตให้ใช้สำหรับชื่อของแอ็ตทริบิวต์\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"เช่น slug, seoUrl, canonicalUrl\",\n  \"modalForm.attribute.target-field\": \"ฟิลด์ที่แนบ\",\n  \"modalForm.attributes.select-component\": \"เลือกคอมโพเนนต์\",\n  \"modalForm.attributes.select-components\": \"เลือกคอมโพเนนต์\",\n  \"modalForm.component.header-create\": \"สร้างคอมโพเนนต์\",\n  \"modalForm.components.create-component.category.label\": \"เลือกหมวดหมู่หรือป้อนชื่อเพื่อสร้างหมวดหมู่ใหม่หรือป้อนชื่อ\",\n  \"modalForm.components.icon.label\": \"ไอคอน\",\n  \"modalForm.editCategory.base.name.description\": \"ไม่อนุญาตให้มีช่องว่างสำหรับชื่อของหมวดหมู่\",\n  \"modalForm.header-edit\": \"แก้ไข {name}\",\n  \"modalForm.header.categories\": \"ประเภท\",\n  \"modalForm.header.back\": \"กลับ\",\n  \"modalForm.singleType.header-create\": \"สร้างชนิดเดี่ยว\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"เพิ่มคอมโพเนนต์ใหม่ให้กับไดนามิกโซน\",\n  \"modalForm.sub-header.attribute.create\": \"เพิ่มฟิลด์ {type} ใหม่\",\n  \"modalForm.sub-header.attribute.create.step\": \"เพิ่มคอมโพเนนต์ใหม่ ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"แก้ไข {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"เลือกฟิลด์สำหรับชนิดคอลเล็กชันของคุณ\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"เลือกฟิลด์สำหรับคอมโพเนนต์ของคุณ\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"เลือกฟิลด์สำหรับชนิดเดี่ยวของคุณ\",\n  \"modelPage.attribute.relation-polymorphic\": \"ความสัมพันธ์ (polymorphic)\",\n  \"modelPage.attribute.relationWith\": \"ความสัมพันธ์กับ\",\n  \"notification.info.autoreaload-disable\": \"คุณลักษณะ autoReload จำเป็นต้องมีเพื่อใช้ปลั๊กอินนี้ เริ่มทำงานเซิร์ฟเวอร์ของคุณด้วย `strapi develop`\",\n  \"notification.info.creating.notSaved\": \"โปรดบันทึกงานของคุณก่อนการสร้างชนิดคอลเล็กชันหรือคอมโพเนนต์ใหม่\",\n  \"plugin.description.long\": \"โมเดลโครงสร้างข้อมูลของ API ของคุณ สร้างฟิลด์ใหม่และความสัมพันธ์ในเวลาเพียงหนึ่งนาที ไฟล์จะถูกสร้างและอัพเดตโดยอัตโนมัติในโปรเจ็กต์ของคุณ\",\n  \"plugin.description.short\": \"โมเดลโครงสร้างข้อมูลของ API ของคุณ\",\n  \"popUpForm.navContainer.advanced\": \"การตั้งค่าขั้นสูง\",\n  \"popUpForm.navContainer.base\": \"ค่าติดตั้งพื้นฐาน\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"คุณแน่ใจว่าต้องการยกเลิกการแก้ไขของคุณหรือไม่?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"คุณแน่ใจว่าต้องการยกเลิกการแก้ไขของคุณหรือไม่? บางคอมโพเนนต์ถูกสร้างหรือแก้ไข...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"คุณแน่ใจว่าต้องการลบหมวดหมู่นี้หรือไม่? คอมโพเนนต์ทั้งหมดจะถูกลบออกด้วย\",\n  \"popUpWarning.bodyMessage.component.delete\": \"คุณแน่ใจว่าต้องการลบคอมโพเนนต์นี้หรือไม่?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"คุณแน่ใจว่าต้องการลบชนิดคอลเล็กชันนี้หรือไม่?\",\n  \"prompt.unsaved\": \"คุณแน่ใจว่าคุณต้องการออกหรือไม่? การปรับเปลี่ยนทั้งหมดของคุณจะหายไป\",\n  \"relation.attributeName.placeholder\": \"ตัวอย่าง: author, category, tag\",\n  \"relation.manyToMany\": \"มีและเป็นของหลาย\",\n  \"relation.manyToOne\": \"มีจำนวนมาก\",\n  \"relation.manyWay\": \"มีจำนวนมาก\",\n  \"relation.oneToMany\": \"เป็นของหลาย\",\n  \"relation.oneToOne\": \"มีและเป็นของหนึ่ง\",\n  \"relation.oneWay\": \"มีหนึ่ง\"\n};\nexport {\n  configurations,\n  th as default,\n  from\n};\n//# sourceMappingURL=th--u3VqsON.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB;", "names": []}