/**
 * Hook para manejar notificaciones en tiempo real con WebSockets
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { message } from "antd";
import { useAuthContext } from "@app/context/auth/AuthContext";

interface WebSocketNotification {
  id: number;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  data?: any;
  read: boolean;
  createdAt: string;
  user?: any;
}

interface WebSocketMessage {
  type: "connection" | "notification" | "heartbeat" | "broadcast" | "pong";
  data?: WebSocketNotification;
  notification?: WebSocketNotification;
  message?: string;
  timestamp?: string;
}

type ConnectionStatus = "CONNECTING" | "OPEN" | "CLOSED" | "ERROR";

interface UseWebSocketNotificationsReturn {
  connectionStatus: ConnectionStatus;
  connectionError: string | null;
  isConnected: boolean;
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => boolean;
  lastMessage: WebSocketMessage | null;
}

const MAX_RETRIES = 10;
const RETRY_INTERVAL = 3000; // 3 segundos
const HEARTBEAT_INTERVAL = 30000; // 30 segundos

export const useWebSocketNotifications =
  (): UseWebSocketNotificationsReturn => {
    const { user, token, isAuthenticated } = useAuthContext();
    const queryClient = useQueryClient();

    // Estados
    const [connectionStatus, setConnectionStatus] =
      useState<ConnectionStatus>("CLOSED");
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(
      null,
    );

    // Referencias
    const wsRef = useRef<WebSocket | null>(null);
    const retryCountRef = useRef(0);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const reconnectAttemptsRef = useRef(0);

    /**
     * Conectar WebSocket
     */
    const connect = useCallback(() => {
      if (!user || !token || !isAuthenticated) {
        console.log("❌ WebSocket: Usuario no autenticado");
        return;
      }

      if (wsRef.current?.readyState === WebSocket.OPEN) {
        console.log("✅ WebSocket: Ya conectado");
        return;
      }

      try {
        console.log(`🔗 Conectando WebSocket para usuario ${user.id}...`);
        setConnectionStatus("CONNECTING");
        setConnectionError(null);

        // Construir URL del WebSocket
        const wsUrl = `ws://localhost:1337/ws/notifications?token=${token}`;

        // Crear nueva conexión WebSocket
        const ws = new WebSocket(wsUrl);
        wsRef.current = ws;

        // Manejar apertura de conexión
        ws.onopen = () => {
          console.log("✅ WebSocket conectado exitosamente");
          setConnectionStatus("OPEN");
          setConnectionError(null);
          retryCountRef.current = 0;
          reconnectAttemptsRef.current = 0;

          // Limpiar timeout de reintento
          if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
            retryTimeoutRef.current = null;
          }

          // Iniciar heartbeat
          startHeartbeat();

          // Enviar mensaje de suscripción
          sendMessage({
            type: "subscribe",
            topics: ["notifications", "broadcasts"],
          });
        };

        // Manejar mensajes
        ws.onmessage = (event) => {
          try {
            const data: WebSocketMessage = JSON.parse(event.data);
            console.log("📨 Mensaje WebSocket recibido:", data);

            setLastMessage(data);

            switch (data.type) {
              case "connection":
                console.log("🔗 Conexión WebSocket establecida");
                break;

              case "notification":
                handleNotification(data.data || data.notification);
                break;

              case "broadcast":
                handleBroadcast(data);
                break;

              case "heartbeat":
                console.log("💓 Heartbeat recibido");
                break;

              case "pong":
                console.log("🏓 Pong recibido");
                break;

              default:
                console.log("❓ Tipo de mensaje desconocido:", data.type);
            }
          } catch (error) {
            console.error("❌ Error procesando mensaje WebSocket:", error);
          }
        };

        // Manejar errores
        ws.onerror = (error) => {
          console.error("❌ Error WebSocket:", error);
          setConnectionStatus("ERROR");
          setConnectionError("Error en la conexión WebSocket");
        };

        // Manejar cierre de conexión
        ws.onclose = (event) => {
          console.log(
            `🔌 WebSocket cerrado. Código: ${event.code}, Razón: ${event.reason}`,
          );
          setConnectionStatus("CLOSED");

          // Limpiar heartbeat
          stopHeartbeat();

          // Intentar reconectar si no fue un cierre intencional
          if (event.code !== 1000 && retryCountRef.current < MAX_RETRIES) {
            scheduleReconnect();
          } else if (retryCountRef.current >= MAX_RETRIES) {
            setConnectionError(
              "No se pudo reconectar después de varios intentos",
            );
          }
        };
      } catch (error) {
        console.error("❌ Error creando WebSocket:", error);
        setConnectionStatus("ERROR");
        setConnectionError("Error creando conexión WebSocket");
      }
    }, [user?.id, token, isAuthenticated]);

    /**
     * Desconectar WebSocket
     */
    const disconnect = useCallback(() => {
      console.log("🔌 Desconectando WebSocket...");

      // Limpiar timeouts
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }

      stopHeartbeat();

      // Cerrar conexión
      if (wsRef.current) {
        wsRef.current.close(1000, "Desconexión intencional");
        wsRef.current = null;
      }

      setConnectionStatus("CLOSED");
      setConnectionError(null);
      retryCountRef.current = 0;
      reconnectAttemptsRef.current = 0;
    }, []);

    /**
     * Enviar mensaje al servidor
     */
    const sendMessage = useCallback((message: any): boolean => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        try {
          wsRef.current.send(JSON.stringify(message));
          console.log("📤 Mensaje enviado:", message);
          return true;
        } catch (error) {
          console.error("❌ Error enviando mensaje:", error);
          return false;
        }
      } else {
        console.log("❌ WebSocket no está conectado");
        return false;
      }
    }, []);

    /**
     * Programar reconexión
     */
    const scheduleReconnect = useCallback(() => {
      if (retryCountRef.current >= MAX_RETRIES) {
        console.log("❌ Máximo número de reintentos alcanzado");
        return;
      }

      retryCountRef.current++;
      reconnectAttemptsRef.current++;

      const delay = Math.min(
        RETRY_INTERVAL * Math.pow(2, reconnectAttemptsRef.current - 1),
        30000,
      );

      console.log(
        `⏳ Reintentando conexión en ${delay}ms (intento ${retryCountRef.current}/${MAX_RETRIES})`,
      );

      retryTimeoutRef.current = setTimeout(() => {
        connect();
      }, delay);
    }, [connect]);

    /**
     * Iniciar heartbeat
     */
    const startHeartbeat = useCallback(() => {
      stopHeartbeat();

      heartbeatTimeoutRef.current = setInterval(() => {
        sendMessage({ type: "ping", timestamp: new Date().toISOString() });
      }, HEARTBEAT_INTERVAL);
    }, [sendMessage]);

    /**
     * Detener heartbeat
     */
    const stopHeartbeat = useCallback(() => {
      if (heartbeatTimeoutRef.current) {
        clearInterval(heartbeatTimeoutRef.current);
        heartbeatTimeoutRef.current = null;
      }
    }, []);

    /**
     * Manejar notificación recibida
     */
    const handleNotification = useCallback(
      (notification: WebSocketNotification | undefined) => {
        if (!notification) return;

        console.log("🔔 Nueva notificación:", notification);

        // Actualizar caché de React Query
        queryClient.setQueryData(
          ["notifications", user?.id],
          (oldData: any) => {
            if (!oldData) return { data: [notification] };

            // Verificar si la notificación ya existe
            const exists = oldData.data.some(
              (n: any) => n.id === notification.id,
            );

            if (exists) {
              // Actualizar notificación existente
              return {
                ...oldData,
                data: oldData.data.map((n: any) =>
                  n.id === notification.id ? notification : n,
                ),
              };
            } else {
              // Agregar nueva notificación al principio
              return {
                ...oldData,
                data: [notification, ...oldData.data],
              };
            }
          },
        );

        // Mostrar notificación en la UI
        message.info({
          content: notification.title,
          duration: 4,
        });
      },
      [queryClient, user?.id],
    );

    /**
     * Manejar broadcast
     */
    const handleBroadcast = useCallback((data: WebSocketMessage) => {
      console.log("📡 Broadcast recibido:", data);

      if (data.notification) {
        // Mostrar notificación de broadcast
        message.info({
          content: `📡 ${data.notification.title}`,
          duration: 6,
        });
      }
    }, []);

    // Efecto para conectar/desconectar automáticamente
    useEffect(() => {
      if (user && isAuthenticated && token) {
        connect();
      } else {
        disconnect();
      }

      // Cleanup al desmontar
      return () => {
        disconnect();
      };
    }, [user?.id, isAuthenticated, token, connect, disconnect]);

    // Estado de conexión
    const isConnected = connectionStatus === "OPEN";

    return {
      connectionStatus,
      connectionError,
      isConnected,
      connect,
      disconnect,
      sendMessage,
      lastMessage,
    };
  };
