'use strict';

import { Context, Next } from 'koa';

/**
 * Middleware para registrar actividades de usuarios, con énfasis en arrendatarios
 */
export default (config, { strapi }) => {
  return async (ctx: Context, next: Next) => {
    // Continuar con la solicitud
    await next();

    // Solo registrar actividades después de que la solicitud se haya completado
    // y solo si hay un usuario autenticado
    if (ctx.state?.user?.id) {
      try {
        // Obtener información del usuario
        const userId = ctx.state.user.id;
        if (!userId) {
          console.warn('No se pudo obtener el ID del usuario del estado');
          return;
        }

        const user = await strapi.entityService.findOne(
          'plugin::users-permissions.user',
          userId,
          { 
            populate: ['usuarioPrincipal'],
            fields: ['id', 'username', 'email']
          }
        );

        if (!user) {
          console.warn(`Usuario con ID ${userId} no encontrado`);
          return;
        }

        // Obtener el rol del usuario
        const userRole = await strapi.db.query('plugin::users-permissions.role')
          .findOne({
            where: { users: { id: userId } },
            populate: ['users'],
          });
          
        if (!userRole) {
          console.warn(`No se pudo encontrar el rol para el usuario ${userId}`);
          return;
        }
          
        // Siempre registrar actividades de arrendatarios
        // Para otros usuarios, solo registrar actividades importantes (POST, PUT, DELETE)
        const shouldLog =
          (userRole?.type === 'arrendatario') ||
          ['POST', 'PUT', 'DELETE'].includes(ctx.request.method);

        if (shouldLog) {
          // Extraer información de la solicitud
          const { method, url, body } = ctx.request;
          const route = url.split('?')[0]; // Eliminar parámetros de consulta
          const ip = ctx.request.ip;

          // Determinar la acción basada en la ruta y el método
          let action = 'acceso';
          let description = `El usuario accedió a ${route}`;

          if (method === 'POST') {
            action = 'creación';
            description = `El usuario creó un nuevo recurso en ${route}`;
          } else if (method === 'PUT') {
            action = 'actualización';
            description = `El usuario actualizó un recurso en ${route}`;
          } else if (method === 'DELETE') {
            action = 'eliminación';
            description = `El usuario eliminó un recurso en ${route}`;
          }

          // Preparar metadatos adicionales
          const metadata = {
            statusCode: ctx.response.status,
            // Incluir información del residente principal si es un arrendatario
            usuarioPrincipal:
              userRole?.type === 'arrendatario' && user.usuarioPrincipal
                ? {
                    id: user.usuarioPrincipal.id,
                    username: user.usuarioPrincipal.username,
                  }
                : null,
          };

          try {
            // Utilizar el servicio para registrar la actividad
            await strapi.service('api::activity-log.activity-log').logActivity(
              {
                action,
                method,
                route,
                description,
                ip,
                metadata,
                user: userId, // Asegurarse de incluir el ID del usuario
              },
              userId
            );
          } catch (logError) {
            console.error('Error al guardar el registro de actividad:', logError);
            // No lanzar el error para no interrumpir la respuesta principal
          }
        }
      } catch (error) {
        // No interrumpir la respuesta si hay un error al registrar la actividad
        console.error('Error al registrar actividad:', error);
      }
    }
  };
};
