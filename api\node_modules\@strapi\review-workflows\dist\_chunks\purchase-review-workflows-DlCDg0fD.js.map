{"version": 3, "file": "purchase-review-workflows-DlCDg0fD.js", "sources": ["../../admin/src/routes/purchase-review-workflows.tsx"], "sourcesContent": ["import { Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Main, EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nconst PurchaseReviewWorkflows = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'Settings.review-workflows.list.page.title',\n            defaultMessage: 'Review Workflows',\n          })}\n          subtitle={formatMessage({\n            id: 'Settings.review-workflows.list.page.subtitle',\n            defaultMessage: 'Manage your content review process',\n          })}\n        />\n        <Box paddingLeft={10} paddingRight={10}>\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'Settings.review-workflows.not-available',\n              defaultMessage:\n                'Review Workflows is only available as part of a paid plan. Upgrade to create and manage workflows.',\n            })}\n            action={\n              <LinkButton\n                variant=\"default\"\n                endIcon={<ExternalLink />}\n                href=\"https://strp.cc/3tdNfJq\"\n                isExternal\n                target=\"_blank\"\n              >\n                {formatMessage({\n                  id: 'global.learn-more',\n                  defaultMessage: 'Learn more',\n                })}\n              </LinkButton>\n            }\n          />\n        </Box>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { PurchaseReviewWorkflows };\n"], "names": ["useIntl", "jsx", "Layouts", "Main", "Box", "EmptyStateLayout", "EmptyPermissions", "LinkButton", "ExternalLink"], "mappings": ";;;;;;;;AAMA,MAAM,0BAA0B,MAAM;AAC9B,QAAA,EAAE,kBAAkBA,UAAAA;AAE1B,SACGC,2BAAA,IAAAC,YAAA,QAAQ,MAAR,EACC,0CAACC,aAAAA,MACC,EAAA,UAAA;AAAA,IAAAF,2BAAA;AAAA,MAACC,YAAAA,QAAQ;AAAA,MAAR;AAAA,QACC,OAAO,cAAc;AAAA,UACnB,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAAA,CACjB;AAAA,QACD,UAAU,cAAc;AAAA,UACtB,IAAI;AAAA,UACJ,gBAAgB;AAAA,QAAA,CACjB;AAAA,MAAA;AAAA,IACH;AAAA,IACCD,2BAAA,IAAAG,aAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,UAAAH,2BAAA;AAAA,MAACI,aAAA;AAAA,MAAA;AAAA,QACC,MAAMJ,2BAAAA,IAACK,QAAAA,kBAAiB,EAAA,OAAM,QAAQ,CAAA;AAAA,QACtC,SAAS,cAAc;AAAA,UACrB,IAAI;AAAA,UACJ,gBACE;AAAA,QAAA,CACH;AAAA,QACD,QACEL,2BAAA;AAAA,UAACM,aAAA;AAAA,UAAA;AAAA,YACC,SAAQ;AAAA,YACR,wCAAUC,MAAa,cAAA,EAAA;AAAA,YACvB,MAAK;AAAA,YACL,YAAU;AAAA,YACV,QAAO;AAAA,YAEN,UAAc,cAAA;AAAA,cACb,IAAI;AAAA,cACJ,gBAAgB;AAAA,YAAA,CACjB;AAAA,UAAA;AAAA,QACH;AAAA,MAAA;AAAA,IAAA,GAGN;AAAA,EAAA,EACF,CAAA,EACF,CAAA;AAEJ;;"}