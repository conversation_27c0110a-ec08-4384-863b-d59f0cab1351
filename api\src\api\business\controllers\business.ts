import { factories } from "@strapi/strapi";
import { BusinessPopulated } from "../types/business";

function generateDocumentId(): string {
  const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 24; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export default factories.createCoreController(
  "api::business.business",
  ({ strapi }) => ({
    async create(ctx) {
      try {
        const data = JSON.parse(ctx.request.body.data || "{}");
        const logo = ctx.request.files?.["files.logo"];
        const photoOne = ctx.request.files?.["files.photoOne"];
        const photoTwo = ctx.request.files?.["files.photoTwo"];
        const photoThree = ctx.request.files?.["files.photoThree"];
        const photoFour = ctx.request.files?.["files.photoFour"];

        const entity = await strapi.entityService.create(
          "api::business.business",
          {
            data: {
              ...data,
              documentId: generateDocumentId(),
              businessUser: ctx.state.user?.id,
            },
          }
        );

        const filesToUpload = [
          { field: "logo", file: logo },
          { field: "photoOne", file: photoOne },
          { field: "photoTwo", file: photoTwo },
          { field: "photoThree", file: photoThree },
          { field: "photoFour", file: photoFour },
        ];

        const uploadPromises = filesToUpload
          .filter(({ file }) => !!file)
          .map(({ field, file }) =>
            strapi.plugins.upload.service("upload").upload({
              data: { ref: "api::business.business", refId: entity.id, field },
              files: file,
            })
          );

        await Promise.all(uploadPromises);

        const updatedEntity = (await strapi.db
          .query("api::business.business")
          .findOne({
            where: { id: entity.id },
            populate: {
              logo: true,
              photoOne: true,
              photoTwo: true,
              photoThree: true,
              photoFour: true,
              businessUser: true,
            },
          })) as BusinessPopulated;

        return {
          ...updatedEntity,
          logo: updatedEntity.logo?.url || null,
          photoOne: updatedEntity.photoOne?.url || null,
          photoTwo: updatedEntity.photoTwo?.url || null,
          photoThree: updatedEntity.photoThree?.url || null,
          photoFour: updatedEntity.photoFour?.url || null,
          photosCount: [
            updatedEntity.photoOne,
            updatedEntity.photoTwo,
            updatedEntity.photoThree,
            updatedEntity.photoFour,
          ].filter(Boolean).length,
          userName: updatedEntity.businessUser?.username || null,
        };
      } catch (error) {
        console.error("Error al crear emprendimiento:", error);
        ctx.throw(500, error);
      }
    },

    async update(ctx) {
      try {
        const { id } = ctx.params;
        const { data } = ctx.request.body;
        const parsedData = typeof data === "string" ? JSON.parse(data) : data;

        const logo = ctx.request.files?.["files.logo"];
        const photoOne = ctx.request.files?.["files.photoOne"];
        const photoTwo = ctx.request.files?.["files.photoTwo"];
        const photoThree = ctx.request.files?.["files.photoThree"];
        const photoFour = ctx.request.files?.["files.photoFour"];

        await strapi.entityService.update("api::business.business", id, {
          data: parsedData,
        });

        const filesToUpload = [
          { field: "logo", file: logo },
          { field: "photoOne", file: photoOne },
          { field: "photoTwo", file: photoTwo },
          { field: "photoThree", file: photoThree },
          { field: "photoFour", file: photoFour },
        ];

        const uploadPromises = filesToUpload
          .filter(({ file }) => !!file)
          .map(({ field, file }) =>
            strapi.plugins.upload.service("upload").upload({
              data: { ref: "api::business.business", refId: id, field },
              files: file,
            })
          );

        await Promise.all(uploadPromises);

        const finalEntity = (await strapi.db
          .query("api::business.business")
          .findOne({
            where: { id },
            populate: {
              logo: true,
              photoOne: true,
              photoTwo: true,
              photoThree: true,
              photoFour: true,
              businessUser: true,
            },
          })) as BusinessPopulated;

        return {
          ...finalEntity,
          logo: finalEntity.logo?.url || null,
          photoOne: finalEntity.photoOne?.url || null,
          photoTwo: finalEntity.photoTwo?.url || null,
          photoThree: finalEntity.photoThree?.url || null,
          photoFour: finalEntity.photoFour?.url || null,
          photosCount: [
            finalEntity.photoOne,
            finalEntity.photoTwo,
            finalEntity.photoThree,
            finalEntity.photoFour,
          ].filter(Boolean).length,
          userName: finalEntity.businessUser?.username || null,
        };
      } catch (error) {
        console.error("Error al actualizar negocio:", error);
        return ctx.badRequest(`Error al actualizar negocio: ${error.message}`);
      }
    },

    async findById(ctx) {
      try {
        const { ownerId } = ctx.params;

        if (!ownerId) {
          return ctx.badRequest("ID de propietario no proporcionado");
        }

        const entities = await strapi.entityService.findMany(
          "api::business.business",
          {
            filters: { businessUser: ownerId },
            populate: {
              logo: true,
              photoOne: true,
              photoTwo: true,
              photoThree: true,
              photoFour: true,
              businessUser: true,
            },
          }
        );

        const results = entities.map((entity: any) => ({
          id: entity.id,
          documentId: entity.documentId,
          name: entity.name,
          category: entity.category,
          description: entity.description,
          phone: entity.phone,
          email: entity.email,
          address: entity.address,
          schedule: entity.schedule,
          website: entity.website,
          facebook: entity.facebook,
          instagram: entity.instagram,
          featured: entity.featured,
          createdAt: entity.createdAt,
          updatedAt: entity.updatedAt,
          publishedAt: entity.publishedAt,
          logo: entity.logo?.url || null,
          photoOne: entity.photoOne?.url || null,
          photoTwo: entity.photoTwo?.url || null,
          photoThree: entity.photoThree?.url || null,
          photoFour: entity.photoFour?.url || null,
          photosCount: [
            entity.photoOne,
            entity.photoTwo,
            entity.photoThree,
            entity.photoFour,
          ].filter(Boolean).length,
          userName: entity.businessUser?.username || null,
          businessUser: entity.businessUser
            ? {
                id: entity.businessUser.id,
                username: entity.businessUser.username,
                email: entity.businessUser.email,
                firstName: entity.businessUser.firstName,
                lastName: entity.businessUser.lastName,
                address: entity.businessUser.address,
                phone: entity.businessUser.phone,
              }
            : null,
        }));

        return {
          data: results,
          meta: { count: results.length },
        };
      } catch (error) {
        console.error("Error en findById:", error);
        return ctx.badRequest(`Error en findById: ${error.message}`);
      }
    },

    async find(ctx) {
      try {
        const { _q, _sort, _limit, _start, ...filters } = ctx.query;
        const limit = parseInt(_limit as string, 10) || 25;
        const start = parseInt(_start as string, 10) || 0;

        const [entities, count] = await Promise.all([
          strapi.entityService.findMany("api::business.business", {
            filters,
            sort: _sort ?? undefined,
            limit,
            start,
            populate: [
              "logo",
              "photoOne",
              "photoTwo",
              "photoThree",
              "photoFour",
              "businessUser",
            ],
          }),
          strapi.entityService.count("api::business.business", {
            filters,
          }),
        ]);

        const results = entities.map((entity: any) => ({
          id: entity.id,
          documentId: entity.documentId,
          name: entity.name,
          category: entity.category,
          description: entity.description,
          phone: entity.phone,
          email: entity.email,
          address: entity.address,
          schedule: entity.schedule,
          website: entity.website,
          facebook: entity.facebook,
          instagram: entity.instagram,
          featured: entity.featured,
          createdAt: entity.createdAt,
          updatedAt: entity.updatedAt,
          publishedAt: entity.publishedAt,
          logo: entity.logo?.url || null,
          photoOne: entity.photoOne?.url || null,
          photoTwo: entity.photoTwo?.url || null,
          photoThree: entity.photoThree?.url || null,
          photoFour: entity.photoFour?.url || null,
          photosCount: [
            entity.photoOne,
            entity.photoTwo,
            entity.photoThree,
            entity.photoFour,
          ].filter(Boolean).length,
          userName: entity.businessUser?.username || null,
          businessUser: entity.businessUser
            ? {
                id: entity.businessUser.id,
                username: entity.businessUser.username,
                email: entity.businessUser.email,
                firstName: entity.businessUser.firstName,
                lastName: entity.businessUser.lastName,
                address: entity.businessUser.address,
                phone: entity.businessUser.phone,
              }
            : null,
        }));

        return {
          data: results,
          meta: {
            pagination: {
              page: Math.floor(start / limit) + 1,
              pageSize: limit,
              pageCount: Math.ceil(count / limit),
              total: count,
            },
          },
        };
      } catch (error) {
        console.error("Error al obtener emprendimientos:", error);
        ctx.throw(500, error);
      }
    },

    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const entity = await strapi.entityService.findOne(
          "api::business.business",
          id,
          {
            populate: [
              "logo",
              "photoOne",
              "photoTwo",
              "photoThree",
              "photoFour",
              "businessUser",
            ],
          }
        );

        if (!entity) return ctx.notFound("Emprendimiento no encontrado");

        const e = entity as any;
        const processedEntity = {
          id: e.id,
          documentId: e.documentId,
          name: e.name,
          category: e.category,
          description: e.description,
          phone: e.phone,
          email: e.email,
          address: e.address,
          schedule: e.schedule,
          website: e.website,
          facebook: e.facebook,
          instagram: e.instagram,
          featured: e.featured,
          createdAt: e.createdAt,
          updatedAt: e.updatedAt,
          publishedAt: e.publishedAt,
          logo: e.logo?.url || null,
          photoOne: e.photoOne?.url || null,
          photoTwo: e.photoTwo?.url || null,
          photoThree: e.photoThree?.url || null,
          photoFour: e.photoFour?.url || null,
          photosCount: [
            e.photoOne,
            e.photoTwo,
            e.photoThree,
            e.photoFour,
          ].filter(Boolean).length,
          userName: e.businessUser?.username || null,
          businessUser: e.businessUser
            ? {
                id: e.businessUser.id,
                username: e.businessUser.username,
                email: e.businessUser.email,
                firstName: e.businessUser.firstName,
                lastName: e.businessUser.lastName,
                address: e.businessUser.address,
                phone: e.businessUser.phone,
              }
            : null,
        };

        return processedEntity;
      } catch (error) {
        console.error("Error al obtener emprendimiento:", error);
        ctx.throw(500, error);
      }
    },

    async delete(ctx) {
      try {
        const { id } = ctx.params;
        if (!id)
          return ctx.badRequest("Se requiere un ID de negocio para eliminar");

        const business = await strapi.entityService.findOne(
          "api::business.business",
          id
        );
        if (!business) return ctx.notFound("Negocio no encontrado");

        const deleted = await strapi.entityService.delete(
          "api::business.business",
          id
        );

        return {
          data: deleted,
          meta: { message: "Negocio eliminado exitosamente" },
        };
      } catch (error) {
        console.error("Error al eliminar negocio:", error);
        return ctx.badRequest(`Error al eliminar negocio: ${error.message}`);
      }
    },
  })
);
