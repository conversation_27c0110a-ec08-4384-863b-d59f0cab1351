import { EventEmitter } from 'events';

// Interfaz para los datos de notificación
export interface NotificationData {
  type: string;
  message: string;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

// Interfaz para la respuesta SSE que extiende EventEmitter
export interface SSEResponse extends EventEmitter {
  write(data: string): void;
  on(event: 'close', callback: () => void): this;
  on(event: string, callback: () => void): this;
}

export type SSEConnection = SSEResponse | ((data: string) => void);

// Extender el objeto global con tipado fuerte
declare global {
  interface Global {
    sseConnections: Map<string, Set<SSEConnection>>;
  }
}

// Interfaz para el servicio SSE
export interface SSEService {
  initialize(): {
    registerConnection(userId: string, res: SSEResponse): void;
    sendNotification(userId: string, data: NotificationData): boolean;
  };
}
