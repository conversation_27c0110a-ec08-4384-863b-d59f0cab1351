{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/vi-ClWGrFm9.mjs"], "sourcesContent": ["const vi = {\n  \"components.Row.open\": \"Mở\",\n  \"components.Row.regenerate\": \"Sinh lại\",\n  \"containers.HomePage.Block.title\": \"Các <PERSON>\",\n  \"containers.HomePage.Button.update\": \"Cập nhật\",\n  \"containers.HomePage.PluginHeader.title\": \"Tài liệu — Cài đặt\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"Tôi hiểu\",\n  \"containers.HomePage.PopUpWarning.message\": \"Bạn có chắc là muốn xóa phiên bản này không?\",\n  \"containers.HomePage.copied\": \"Chuỗi khoá đã được sao chép vào bộ nhớ tạm\",\n  \"containers.HomePage.form.jwtToken\": \"Lấy lại chuỗi khóa JWT của bạn\",\n  \"containers.HomePage.form.jwtToken.description\": \"Sao chép chuỗi khóa và sử dụng nó trong swagger để truy vấn\",\n  \"containers.HomePage.form.password\": \"Mật khẩu\",\n  \"containers.HomePage.form.password.inputDescription\": \"Cài đặt mật khẩu để truy cập tài liệu\",\n  \"containers.HomePage.form.restrictedAccess\": \"Truy cập bị giới hạn\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"Làm cho điểm truy cập tài liệu thành riêng tư. Mặc định, truy cập là công khai.\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"Hiển thị tập tin đã sinh ra\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"Hữu ích khi bạn muốn ghi đè lên tài liệu đã sinh ra. \\nPlugin này sẽ tạo ra các tập tin phân chia bởi cấu hình và plugin. \\nBằng việc kích hoạt tuỳ chọn này, sẽ dễ dàng hơn cho bạn tùy chỉnh tài liệu của bạn.\",\n  \"error.deleteDoc.versionMissing\": \"Phiên bản bạn đang cố xóa không tồn tại.\",\n  \"error.noVersion\": \"Bắt buộc phải có một phiên bản\",\n  \"error.regenerateDoc\": \"Một lỗi đã xảy ra trong khi sinh ra tài liệu\",\n  \"error.regenerateDoc.versionMissing\": \"Phiên bản bạn đang cố sinh ra không tồn tại\",\n  \"notification.update.success\": \"Các cài đặt đã được cập nhật thành công\",\n  \"plugin.name\": \"Tài liệu\"\n};\nexport {\n  vi as default\n};\n//# sourceMappingURL=vi-ClWGrFm9.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,eAAe;AACjB;", "names": []}