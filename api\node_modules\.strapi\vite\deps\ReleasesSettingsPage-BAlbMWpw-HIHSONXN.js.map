{"version": 3, "sources": ["../../../@strapi/content-releases/admin/src/modules/hooks.ts", "../../../@strapi/content-releases/admin/src/pages/ReleasesSettingsPage.tsx"], "sourcesContent": ["import { Dispatch } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\n\nimport type { Store } from '@strapi/admin/strapi-admin';\n\ntype RootState = ReturnType<Store['getState']>;\n\nconst useTypedDispatch: () => Dispatch = useDispatch;\nconst useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;\n\nexport { useTypedSelector, useTypedDispatch };\n", "import {\n  Form,\n  Layouts,\n  Page,\n  useAPIError<PERSON>andler,\n  isFetchError,\n  useNotification,\n  useField,\n  useRBAC,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Button,\n  Combobox,\n  ComboboxOption,\n  Field,\n  Flex,\n  Grid,\n  Typography,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useTypedSelector } from '../modules/hooks';\nimport { useGetReleaseSettingsQuery, useUpdateReleaseSettingsMutation } from '../services/release';\nimport { getTimezones } from '../utils/time';\nimport { SETTINGS_SCHEMA } from '../validation/schemas';\n\nimport type { UpdateSettings } from '../../../shared/contracts/settings';\n\nconst ReleasesSettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const { data, isLoading: isLoadingSettings } = useGetReleaseSettingsQuery();\n  const [updateReleaseSettings, { isLoading: isSubmittingForm }] =\n    useUpdateReleaseSettingsMutation();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']\n  );\n  const {\n    allowedActions: { canUpdate },\n  } = useRBAC(permissions);\n\n  const { timezoneList } = getTimezones(new Date());\n\n  const handleSubmit = async (body: UpdateSettings.Request['body']) => {\n    const { defaultTimezone } = body;\n    const isBodyTimezoneValid = timezoneList.some((timezone) => timezone.value === defaultTimezone);\n    const newBody =\n      !defaultTimezone || !isBodyTimezoneValid ? { defaultTimezone: null } : { ...body };\n    try {\n      const response = await updateReleaseSettings(newBody);\n\n      if ('data' in response) {\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'content-releases.pages.Settings.releases.setting.default-timezone-notification-success',\n            defaultMessage: 'Default timezone updated.',\n          }),\n        });\n      } else if (isFetchError(response.error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(response.error),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n        });\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  if (isLoadingSettings) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Releases',\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isLoadingSettings} tabIndex={-1}>\n        <Form\n          method=\"PUT\"\n          initialValues={{\n            defaultTimezone: data?.data.defaultTimezone,\n          }}\n          onSubmit={handleSubmit}\n          validationSchema={SETTINGS_SCHEMA}\n        >\n          {({ modified, isSubmitting }: { modified: boolean; isSubmitting: boolean }) => {\n            return (\n              <>\n                <Layouts.Header\n                  primaryAction={\n                    canUpdate ? (\n                      <Button\n                        disabled={!modified || isSubmittingForm}\n                        loading={isSubmitting}\n                        startIcon={<Check />}\n                        type=\"submit\"\n                      >\n                        {formatMessage({\n                          id: 'global.save',\n                          defaultMessage: 'Save',\n                        })}\n                      </Button>\n                    ) : null\n                  }\n                  title={formatMessage({\n                    id: 'content-releases.pages.Settings.releases.title',\n                    defaultMessage: 'Releases',\n                  })}\n                  subtitle={formatMessage({\n                    id: 'content-releases.pages.Settings.releases.description',\n                    defaultMessage: 'Create and manage content updates',\n                  })}\n                />\n                <Layouts.Content>\n                  <Flex\n                    direction=\"column\"\n                    background=\"neutral0\"\n                    alignItems=\"stretch\"\n                    padding={6}\n                    gap={6}\n                    shadow=\"filterShadow\"\n                    hasRadius\n                  >\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'content-releases.pages.Settings.releases.preferences.title',\n                        defaultMessage: 'Preferences',\n                      })}\n                    </Typography>\n                    <Grid.Root>\n                      <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                        <TimezoneDropdown />\n                      </Grid.Item>\n                    </Grid.Root>\n                  </Flex>\n                </Layouts.Content>\n              </>\n            );\n          }}\n        </Form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nconst TimezoneDropdown = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']\n  );\n  const {\n    allowedActions: { canUpdate },\n  } = useRBAC(permissions);\n  const { formatMessage } = useIntl();\n  const { timezoneList } = getTimezones(new Date());\n  const field = useField('defaultTimezone');\n  return (\n    <Field.Root\n      name=\"defaultTimezone\"\n      hint={formatMessage({\n        id: 'content-releases.pages.Settings.releases.timezone.hint',\n        defaultMessage: 'The timezone of every release can still be changed individually. ',\n      })}\n      error={field.error}\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'content-releases.pages.Settings.releases.timezone.label',\n          defaultMessage: 'Default timezone',\n        })}\n      </Field.Label>\n      <Combobox\n        autocomplete={{ type: 'list', filter: 'contains' }}\n        onChange={(value) => field.onChange('defaultTimezone', value)}\n        onTextValueChange={(value) => field.onChange('defaultTimezone', value)}\n        onClear={() => field.onChange('defaultTimezone', '')}\n        value={field.value}\n        disabled={!canUpdate}\n      >\n        {timezoneList.map((timezone) => (\n          <ComboboxOption key={timezone.value} value={timezone.value}>\n            {timezone.value.replace(/&/, ' ')}\n          </ComboboxOption>\n        ))}\n      </Combobox>\n      <Field.Hint />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedSettingsPage\n * -----------------------------------------------------------------------------------------------*/\n\nexport const ProtectedReleasesSettingsPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['releases']?.read\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ReleasesSettingsPage />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,mBAAoD;ACqB1D,IAAM,uBAAuB,MAAM;AAC3B,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,eAAA,IAAmB,mBAAA;AACrB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,MAAM,WAAW,kBAAA,IAAsB,2BAA2B;AAC1E,QAAM,CAAC,uBAAuB,EAAE,WAAW,iBAAkB,CAAA,IAC3D,iCAAA;AACF,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,yBAAM,UAAU,YAAY,UAAU,MAAtC,mBAA0C;;EAAU;AAE3D,QAAA;IACJ,gBAAgB,EAAE,UAAU;EAAA,IAC1B,QAAQ,WAAW;AAEvB,QAAM,EAAE,aAAa,IAAI,aAAa,oBAAI,KAAM,CAAA;AAE1C,QAAA,eAAe,OAAO,SAAyC;AAC7D,UAAA,EAAE,gBAAoB,IAAA;AAC5B,UAAM,sBAAsB,aAAa,KAAK,CAAC,aAAa,SAAS,UAAU,eAAe;AACxF,UAAA,UACJ,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,iBAAiB,KAAK,IAAI,EAAE,GAAG,KAAA;AAC1E,QAAA;AACI,YAAA,WAAW,MAAM,sBAAsB,OAAO;AAEpD,UAAI,UAAU,UAAU;AACH,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;MACQ,WAAA,aAAa,SAAS,KAAK,GAAG;AACpB,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,SAAS,KAAK;QAAA,CACvC;MAAA,OACI;AACc,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,oBAAA,CAAqB;QAAA,CACzF;MACH;IAAA,SACO,OAAO;AACK,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,oBAAA,CAAqB;MAAA,CACzF;IACH;EAAA;AAGF,MAAI,mBAAmB;AACd,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGE,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM;MACR;IAAA,EAAA,CAEJ;QAAA,wBACC,KAAK,MAAL,EAAU,aAAW,mBAAmB,UAAU,IACjD,cAAA;MAAC;MAAA;QACC,QAAO;QACP,eAAe;UACb,iBAAiB,6BAAM,KAAK;QAC9B;QACA,UAAU;QACV,kBAAkB;QAEjB,UAAC,CAAA,EAAE,UAAU,aAAA,MAAiE;AAC7E,qBAEI,yBAAA,6BAAA,EAAA,UAAA;gBAAA;cAAC,QAAQ;cAAR;gBACC,eACE,gBACE;kBAAC;kBAAA;oBACC,UAAU,CAAC,YAAY;oBACvB,SAAS;oBACT,eAAA,wBAAY,eAAM,CAAA,CAAA;oBAClB,MAAK;oBAEJ,UAAc,cAAA;sBACb,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;kBAAA;gBAAA,IAED;gBAEN,OAAO,cAAc;kBACnB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;gBACD,UAAU,cAAc;kBACtB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;cAAA;YACH;gBACA,wBAAC,QAAQ,SAAR,EACC,cAAA;cAAC;cAAA;gBACC,WAAU;gBACV,YAAW;gBACX,YAAW;gBACX,SAAS;gBACT,KAAK;gBACL,QAAO;gBACP,WAAS;gBAET,UAAA;sBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,EAAA,CACH;sBAAA,wBACC,KAAK,MAAL,EACC,cAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAC,wBAAA,kBAAA,CAAA,CAAiB,EACpB,CAAA,EAAA,CACF;gBAAA;cAAA;YAAA,EAAA,CAEJ;UACF,EAAA,CAAA;QAEJ;MAAA;IAAA,EAAA,CAEJ;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,mBAAmB,MAAM;AAC7B,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,yBAAM,UAAU,YAAY,UAAU,MAAtC,mBAA0C;;EAAU;AAE3D,QAAA;IACJ,gBAAgB,EAAE,UAAU;EAAA,IAC1B,QAAQ,WAAW;AACjB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,aAAa,IAAI,aAAa,oBAAI,KAAM,CAAA;AAC1C,QAAA,QAAQ,SAAS,iBAAiB;AAEtC,aAAA;IAAC,MAAM;IAAN;MACC,MAAK;MACL,MAAM,cAAc;QAClB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,OAAO,MAAM;MAEb,UAAA;YAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;YACA;UAAC;UAAA;YACC,cAAc,EAAE,MAAM,QAAQ,QAAQ,WAAW;YACjD,UAAU,CAAC,UAAU,MAAM,SAAS,mBAAmB,KAAK;YAC5D,mBAAmB,CAAC,UAAU,MAAM,SAAS,mBAAmB,KAAK;YACrE,SAAS,MAAM,MAAM,SAAS,mBAAmB,EAAE;YACnD,OAAO,MAAM;YACb,UAAU,CAAC;YAEV,UAAA,aAAa,IAAI,CAAC,iBACjB,wBAAC,QAAA,EAAoC,OAAO,SAAS,OAClD,UAAA,SAAS,MAAM,QAAQ,KAAK,GAAG,EADb,GAAA,SAAS,KAE9B,CACD;UAAA;QACH;YACA,wBAAC,MAAM,MAAN,CAAA,CAAW;YACZ,wBAAC,MAAM,OAAN,CAAA,CAAY;MAAA;IAAA;EAAA;AAGnB;AAMO,IAAM,gCAAgC,MAAM;AACjD,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,+BAAM,UAAU,YAAY,UAAU,MAAtC,mBAA0C,gBAA1C,mBAAuD;;EAAA;AAGpE,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,sBAAA,CAAA,CAAqB,EACxB,CAAA;AAEJ;", "names": []}