import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/sk-BmT4uZTG.mjs
var sk = {
  "components.Row.open": "Otvoriť",
  "components.Row.regenerate": "Znova vygenerovať",
  "containers.HomePage.Block.title": "Verzie",
  "containers.HomePage.Button.update": "Upraviť",
  "containers.HomePage.PluginHeader.title": "Dokumentácia — Nastavenia",
  "containers.HomePage.PopUpWarning.confirm": "Rozumiem",
  "containers.HomePage.PopUpWarning.message": "<PERSON>e si istý, že chcete odstrániť túto verziu?",
  "containers.HomePage.copied": "Token bol skopírovaný do schránky",
  "containers.HomePage.form.jwtToken": "Získať JWT token",
  "containers.HomePage.form.jwtToken.description": "Skopírujte tento token a použijte ho v Swaggeri pri volaniach API",
  "containers.HomePage.form.password": "<PERSON><PERSON><PERSON>",
  "containers.HomePage.form.password.inputDescription": "Nastavte heslo pre prístup k dokumentácii",
  "containers.HomePage.form.restrictedAccess": "Obmedzený prístup",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Nastavte dokumentáciu ako súkromnú. Predvolene je verejne prístupná.",
  "containers.HomePage.form.showGeneratedFiles": "Zobraziť vygenerované súbory",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Užitočné, keď potrebujete prepísať vygenerovanú dokumentáciu. \nPlugin vygeneruje súbory rozdelené podľa modelu a pluginu. \nToto uľahčí úpravy dokumentácie.",
  "error.deleteDoc.versionMissing": "Verzia, ktorú sa snažíte vymazať, neexistuje.",
  "error.noVersion": "Verzia je povinná",
  "error.regenerateDoc": "Nastala chyba pri generovaní dokumentácie",
  "error.regenerateDoc.versionMissing": "Verzia, ktorú sa snažíte vygenerovať, neexistuje.",
  "notification.update.success": "Nastavenia boli upravené",
  "plugin.name": "Dokumentácia"
};
export {
  sk as default
};
//# sourceMappingURL=sk-BmT4uZTG-LKNTD6IC.js.map
