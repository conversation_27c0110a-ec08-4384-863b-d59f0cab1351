import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/sv-D_-KBtcw.mjs
var sv = {
  "coming-soon": "Det här innehållet är för närvarande under uppbyggnad och kommer tillbaka om några veckor!",
  "components.Row.open": "Öppen",
  "components.Row.regenerate": "Återskapa",
  "containers.HomePage.Block.title": "Versioner",
  "containers.HomePage.Button.update": "Uppdatera",
  "containers.HomePage.PluginHeader.title": "Dokumentation — Inställningar",
  "containers.HomePage.PopUpWarning.confirm": "Jag förstår",
  "containers.HomePage.PopUpWarning.message": "Är du säker på att du vill ta bort denna versionen?",
  "containers.HomePage.copied": "Token har kopierats till urklipp",
  "containers.HomePage.form.jwtToken": "Hämta din JWT-token",
  "containers.HomePage.form.jwtToken.description": "Kopiera denna token och använd den i swagger för att göra förfrågningar",
  "containers.HomePage.form.password": "Lösenord",
  "containers.HomePage.form.password.inputDescription": "Ställ in lösenordet som ska behövas för att komma åt dokumentationen",
  "containers.HomePage.form.restrictedAccess": "Begränsad åtkomst",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Gör dokumentationensrutten privat. Som standard är åtkomsten publik",
  "containers.HomePage.form.showGeneratedFiles": "Visa genererade filer",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Användbart när du vill överskrida den automatiskt genererade dokumentationen. \nPluginet genererar filer uppdelade efter modell och plugin. \nGenom att aktivera det här blir det lättare att anpassa din dokumentation",
  "error.deleteDoc.versionMissing": "Den version du försöker ta bort finns inte.",
  "error.noVersion": "En version krävs",
  "error.regenerateDoc": "Ett fel uppstod när dokumentet återskapades",
  "error.regenerateDoc.versionMissing": "Den version du försöker generera finns inte",
  "notification.delete.success": "Dokument raderat",
  "notification.generate.success": "Dokument genererat",
  "notification.update.success": "Inställningarna har uppdaterats",
  "pages.PluginPage.Button.open": "Öppna dokumentation",
  "pages.PluginPage.header.description": "Konfigurera dokumentationspluginet",
  "pages.PluginPage.table.generated": "Senast genererade",
  "pages.PluginPage.table.icon.regenerate": "Återskapa {target}",
  "pages.PluginPage.table.icon.show": "Öppna {target}",
  "pages.PluginPage.table.version": "Version",
  "pages.SettingsPage.header.description": "Konfigurera dokumentationspluginet",
  "pages.SettingsPage.header.save": "Spara",
  "pages.SettingsPage.toggle.hint": "Gör dokumentationensrutten privat",
  "pages.SettingsPage.toggle.label": "Begränsad åtkomst",
  "plugin.description.long": "Skapa ett OpenAPI-dokument och visualisera din API med Swagger UI.",
  "plugin.description.short": "Skapa ett OpenAPI-dokument och visualisera din API med Swagger UI.",
  "plugin.name": "Dokumentation"
};
export {
  sv as default
};
//# sourceMappingURL=sv-D_-KBtcw-NYUHDDPO.js.map
