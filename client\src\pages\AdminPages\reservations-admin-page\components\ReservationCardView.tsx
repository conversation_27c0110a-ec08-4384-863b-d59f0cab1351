import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  HomeFilled,
  PhoneFilled,
  UserOutlined,
} from "@ant-design/icons";
import { BaseCard } from "@app/components/common/BaseCard/BaseCard";
import { useUserRole } from "@app/hooks/useRole";
import {
  Avatar,
  Badge,
  Button,
  Col,
  Empty,
  Flex,
  Row,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { Reservation } from "../hooks/useListReservations";
import { formatPhoneNumber } from "@app/pages/HousesPage/components/HouseCard";

interface ReservationCardViewProps {
  reservations: Reservation[];
  onReject: (reservation: Reservation) => void;
  onApprove: (id: number) => void;
  onDelete: (id: number) => void;
  onViewDetails: (reservation: Reservation) => void;
}

export const ReservationCardView = ({
  reservations,
  onReject,
  onApprove,
  onDelete,
  onViewDetails,
}: ReservationCardViewProps) => {
  const { isAdmin } = useUserRole();

  if (!reservations || reservations.length === 0) {
    return <Empty description="No hay reservaciones registradas" />;
  }

  const getAreaName = (area: string) => {
    const areaNames: { [key: string]: string } = {
      communalHall: "Salón comunal",
      pool: "Piscina",
      bbq: "BBQ",
      terrace: "Terraza",
    };
    return areaNames[area] || area;
  };

  const getStatusTag = (status: string) => {
    const statusColors = {
      pending: "gold",
      approved: "green",
      rejected: "red",
    };
    const statusNames = {
      pending: "Pendiente",
      approved: "Aprobada",
      rejected: "Rechazada",
    };
    return (
      <Tag color={statusColors[status as keyof typeof statusColors]}>
        {statusNames[status as keyof typeof statusNames]}
      </Tag>
    );
  };

  const getLabelPropuse = (purpose: string) => {
    const purposeLabels: Record<string, string> = {
      birthday: "Fiesta de cumpleaños",
      meeting: "Reunión familiar",
      celebration: "Celebración especial",
      babyShower: "Baby Shower",
      graduation: "Graduación",
      other: "Otro",
    };
    return purposeLabels[purpose] || "Otro";
  };

  return (
    <Row gutter={[32, 32]} justify="space-around">
      {reservations.map((reservation) => (
        <Col
          xs={24}
          sm={12}
          md={8}
          lg={6}
          xl={6}
          key={`reservation-${reservation.id}`}
        >
          <div
            onClick={() => onViewDetails(reservation)}
            style={{ cursor: "pointer" }}
          >
            <BaseCard size="small" padding={[16, 16]}>
              <Flex vertical gap={12}>
                {/* Usuario */}
                <Flex gap={12} align="center">
                  <Badge
                    dot
                    status={reservation.user?.status ? "success" : "error"}
                    offset={[-5, 42]}
                  >
                    <Avatar
                      size={48}
                      icon={<UserOutlined />}
                      src={reservation.user?.imgUrl}
                    />
                  </Badge>
                  <Flex vertical>
                    <Typography.Text strong>
                      {reservation.user
                        ? `${reservation.user.firstName || ""} ${reservation.user.lastName || ""}`.trim() ||
                          "Nombre no especificado"
                        : "Usuario no disponible"}
                    </Typography.Text>
                    <Space size={4}>
                      {getStatusTag(reservation.status)}
                      <Tag
                        color={reservation.user?.status ? "success" : "error"}
                      >
                        {reservation.user?.status ? "Al día" : "Moroso"}
                      </Tag>
                    </Space>
                  </Flex>
                </Flex>

                {/* Casa */}
                <Tag
                  color="green-inverse"
                  style={{ width: "100%", textAlign: "center", fontSize: 18 }}
                  icon={<HomeFilled />}
                >
                  CASA: {reservation.user?.address || "No disponible"}
                </Tag>
                <Tag
                  color="green"
                  style={{ width: "100%", textAlign: "center", fontSize: 18 }}
                  icon={<PhoneFilled />}
                >
                  Telefono:{" "}
                  <small>{formatPhoneNumber(reservation.user?.phone)}</small>
                </Tag>

                {/* Zona y Evento */}
                <Typography.Text type="secondary">
                  <strong>Lugar:</strong> {getAreaName(reservation.socialArea)}
                </Typography.Text>

                {/* Proposito */}
                <Typography.Text type="secondary">
                  <strong>Evento:</strong>{" "}
                  {getLabelPropuse(reservation.eventType) || "No especificado"}
                </Typography.Text>

                {/* Fecha de solicitud */}
                <Typography.Text type="secondary">
                  <strong>Solictud:</strong>{" "}
                  {dayjs(reservation.createdAt).format("DD/MM/YYYY")}
                </Typography.Text>
                {/* Fecha y hora */}
                <Typography.Text type="secondary">
                  <strong>Reservación:</strong>{" "}
                  {dayjs(reservation.date).format("DD/MM/YYYY")} <br />
                </Typography.Text>
                <Typography.Text type="secondary">
                  <strong>Hora:</strong>{" "}
                  {dayjs(`${reservation.date} ${reservation.startTime}`).format(
                    "hh:mm A",
                  )}{" "}
                  -{" "}
                  {dayjs(`${reservation.date} ${reservation.endTime}`).format(
                    "hh:mm A",
                  )}
                </Typography.Text>

                {/* Número de asistentes */}
                <Typography.Text type="secondary">
                  <strong>Asistentes:</strong> {reservation.guests.length || 0}
                </Typography.Text>

                {/* ACCIONES PARA ADMINISTRADORES */}
                {isAdmin && (
                  <Flex justify="space-between" align="center" wrap>
                    <Tooltip title="Ver detalles">
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewDetails(reservation);
                        }}
                      />
                    </Tooltip>

                    <Tooltip title="Aprobar">
                      <Button
                        type="link"
                        icon={<CheckCircleOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          onApprove(reservation.id);
                        }}
                        disabled={
                          reservation.status === "approved" ||
                          !reservation.user?.status
                        }
                      />
                    </Tooltip>

                    <Tooltip title="Rechazar">
                      <Button
                        type="link"
                        danger
                        icon={<CloseCircleOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          onReject(reservation);
                        }}
                        disabled={reservation.status === "rejected"}
                      />
                    </Tooltip>

                    <Tooltip title="Eliminar">
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(reservation.id);
                        }}
                      />
                    </Tooltip>
                  </Flex>
                )}
              </Flex>
            </BaseCard>
          </div>
        </Col>
      ))}
    </Row>
  );
};
