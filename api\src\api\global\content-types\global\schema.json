{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global", "description": "Define global settings"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"siteName": {"type": "string", "required": true}, "favicon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos"]}, "siteDescription": {"type": "text", "required": true}, "defaultSeo": {"type": "component", "repeatable": false, "component": "shared.seo"}}}