{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/sa-KUwV8aRB.mjs"], "sourcesContent": ["const Analytics = \"विश्लेषिकी\";\nconst Documentation = \"दस्तावेजीकरणम्\";\nconst Email = \"ईमेल\";\nconst Password = \"समाभाष्\";\nconst Provider = \"प्रदाता\";\nconst ResetPasswordToken = \"पासवर्ड टोकन रीसेट कुर्वन्तु \";\nconst Role = \"भूमिका\";\nconst Username = \"उपयोक्तृनाम\";\nconst Users = \"उपयोक्तारः\";\nconst anErrorOccurred = \"वूप्स्! किमपि भ्रष्टं जातम्। कृपया, पुनः प्रयासं कुरुत।\";\nconst clearLabel = \"स्पष्ट करें\";\nconst or = \"अथवा\";\nconst skipToContent = \"सामग्री प्रति गच्छतु\";\nconst submit = \"सबमिट\";\nconst sa = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"भवतः खाता स्थगितम् अस्ति\",\n\t\"Auth.components.Oops.text.admin\": \"यदि एषा त्रुटिः अस्ति तर्हि कृपया स्वप्रशासकेन सह सम्पर्कं कुर्वन्तु\",\n\t\"Auth.components.Oops.title\": \"ऊप्स्..\",\n\t\"Auth.form.button.forgot-password\": \"ईमेल प्रेषयन्तु\",\n\t\"Auth.form.button.go-home\": \"गृहं प्रति गच्छतु\",\n\t\"Auth.form.button.login\": \"लॉगिन इति\",\n\t\"Auth.form.button.login.providers.error\": \"चयनितप्रदातृद्वारा भवन्तं संयोजयितुं न शक्नुमः।\",\n\t\"Auth.form.button.login.strapi\": \"स्ट्रैपी मार्गेण लॉग इन कुर्वन्तु\",\n\t\"Auth.form.button.password-recovery\": \"पासवर्ड पुनर्प्राप्ति\",\n\t\"Auth.form.button.register\": \"आरभेम\",\n\t\"Auth.form.confirmPassword.label\": \"पुष्टिकरण गुप्तशब्द\",\n\t\"Auth.form.currentPassword.label\": \"वर्तमान गुप्तशब्द\",\n\t\"Auth.form.email.label\": \"ईमेल\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL> इति\",\n\t\"Auth.form.error.blocked\": \"भवतः खाता प्रशासकेन अवरुद्धः अस्ति।\",\n\t\"Auth.form.error.code.provide\": \"अशुद्धः कोडः प्रदत्तः।\",\n\t\"Auth.form.error.confirmed\": \"भवतः खातेः ईमेलः सत्यापितः नास्ति।\",\n\t\"Auth.form.error.email.invalid\": \"एतत् ईमेल अमान्यम् अस्ति ।\",\n\t\"Auth.form.error.email.provide\": \"कृपया स्वस्य उपयोक्तृनाम अथवा स्वस्य ईमेलं प्रदातव्यम्।\",\n\t\"Auth.form.error.email.taken\": \"ईमेल पूर्वमेव गृहीतम् अस्ति।\",\n\t\"Auth.form.error.invalid\": \"अमान्य परिचयकर्ता अथवा गुप्तशब्द।\",\n\t\"Auth.form.error.params.provide\": \"अशुद्धानि मापदण्डानि प्रदत्तानि आसन्।\",\n\t\"Auth.form.error.password.format\": \"भवतः गुप्तशब्दे '$' चिह्नं त्रिवारं अधिकं भवितुं न शक्यते ।\",\n\t\"Auth.form.error.password.local\": \"अयं उपयोक्ता कदापि स्थानीयगुप्तशब्दं न सेट् कृतवान्, कृपया खातानिर्माणकाले उपयुज्यमानस्य प्रदातुः माध्यमेन प्रवेशं कुर्वन्तु ।\",\n\t\"Auth.form.error.password.matching\": \"गुप्तशब्दाः न मेलन्ति।\",\n\t\"Auth.form.error.password.provide\": \"कृपया स्वस्य कूटशब्दं प्रदातव्यम्।\",\n\t\"Auth.form.error.ratelimit\": \"अत्यधिकप्रयासाः, कृपया एकनिमेषेण पुनः प्रयासं कुर्वन्तु।\",\n\t\"Auth.form.error.user.not-exist\": \"इदं ईमेल नास्ति।\",\n\t\"Auth.form.error.username.taken\": \"उपयोक्तृनाम पूर्वमेव गृहीतम् अस्ति।\",\n\t\"Auth.form.firstname.label\": \"प्रथम नाम्ना\",\n\t\"Auth.form.firstname.placeholder\": \"उदा. कै\",\n\t\"Auth.form.forgot-password.email.label\": \"स्वस्य ईमेलं प्रविशतु\",\n\t\"Auth.form.forgot-password.email.label.success\": \"ईमेल सफलतया प्रेषितम्\",\n\t\"Auth.form.lastname.label\": \"अंतिम नाम्ना\",\n\t\"Auth.form.lastname.placeholder\": \"उदा. डोए\",\n\t\"Auth.form.password.hide-password\": \"गुप्तशब्दं गोपयतु\",\n\t\"Auth.form.password.hint\": \"न्यूनातिन्यूनं ८ वर्णाः, १ बृहत्, १ लघुः & १ संख्याः भवितुमर्हति\",\n\t\"Auth.form.password.show-password\": \"गुप्तशब्दं दर्शयतु\",\n\t\"Auth.form.register.news.label\": \"नूतनानां विशेषतानां & आगामिसुधारानाम् विषये मां अद्यतनं कुरुत (एतत् कृत्वा भवान् {उपधा} तथा {नीति} स्वीकुर्वति)।\",\n\t\"Auth.form.register.subtitle\": \"प्रमाणपत्राणि केवलं स्ट्रैपी मध्ये प्रमाणीकरणाय उपयुज्यन्ते। सर्वाणि रक्षितानि दत्तांशानि भवतः दत्तांशकोशे संगृहीतानि भविष्यन्ति।\",\n\t\"Auth.form.rememberMe.label\": \"मां स्मर्यताम्\",\n\t\"Auth.form.username.label\": \"उपयोक्तृनाम\",\n\t\"Auth.form.username.placeholder\": \"उदा. कै_दोए\",\n\t\"Auth.form.welcome.subtitle\": \"भवतः स्ट्रैपी  खाते प्रवेश कुर्वन्तु\",\n\t\"Auth.form.welcome.title\": \"स्ट्रैपी इत्यत्र स्वागतम् !\",\n\t\"Auth.link.forgot-password\": \"भवतः गुप्तशब्दं विस्मृतवान् वा?\",\n\t\"Auth.link.ready\": \"Rप्रवेशार्थं सज्जाः सन्ति वा?\",\n\t\"Auth.link.signin\": \"साइन इन इति\",\n\t\"Auth.link.signin.account\": \"पूर्वमेव खाता अस्ति वा ?\",\n\t\"Auth.login.sso.divider\": \"अथवा लॉगिन इति \",\n\t\"Auth.login.sso.loading\": \"प्रदाता लोड हो रहे हैं...\",\n\t\"Auth.login.sso.subtitle\": \"SSO मार्गेण स्वखाते प्रवेशं कुर्वन्तु\",\n\t\"Auth.privacy-policy-agreement.policy\": \"गोपनीयता नीति\",\n\t\"Auth.privacy-policy-agreement.terms\": \"उपधा\",\n\t\"Auth.reset-password.title\": \"गुप्तशब्दं पुनः सेट् कुर्वन्तु\",\n\t\"Content Manager\": \"सामग्री प्रबन्धक:\",\n\t\"Content Type Builder\": \"सामग्री-प्रकार निर्माता\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"सञ्चिकाः अपलोड् कुर्वन्तु\",\n\t\"HomePage.head.title\": \"मुखपृष्ठ\",\n\t\"HomePage.roadmap\": \"अस्माकं मार्गचित्रं पश्यन्तु\",\n\t\"HomePage.welcome.congrats\": \"भिनन्दनम् !\",\n\t\"HomePage.welcome.congrats.content\": \"भवान् प्रथमप्रशासकरूपेण प्रवेशितः अस्ति । स्ट्रैपी इत्यनेन प्रदत्तानां शक्तिशालिनां विशेषतानां आविष्कारार्थं इति ।\",\n\t\"HomePage.welcome.congrats.content.bold\": \"वयं भवन्तं प्रथमं संग्रह-प्रकार रचयितुं अनुशंसयामः।\",\n\t\"Media Library\": \"मीडिया पुस्तकालय\",\n\t\"New entry\": \"नवीन प्रविष्टिः\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"भूमिकाएँ एवं अनुमतियाँ\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"काश्चन भूमिकाः उपयोक्तृभिः सह सम्बद्धाः इति कारणतः लोपयितुं न शक्यते स्म\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"उपयोक्तृभिः सह सम्बद्धा चेत् भूमिका विलोपयितुं न शक्यते\",\n\t\"Roles.RoleRow.select-all\": \"बल्क क्रियाणां कृते {name} चिनोतु\",\n\t\"Roles.RoleRow.user-count\": \"{संख्या, बहुवचनम्, =0 {# उपयोक्ता} एकः {# उपयोक्ता} अन्ये {# उपयोक्तारः}}\",\n\t\"Roles.components.List.empty.withSearch\": \"अन्वेषणस्य ({अन्वेषण}) अनुरूपं भूमिका नास्ति...\",\n\t\"Settings.PageTitle\": \"सेटिंग्स् - {नाम}\",\n\t\"Settings.apiTokens.addFirstToken\": \"स्वस्य प्रथमं एपिआइ टोकनं योजयन्तु\",\n\t\"Settings.apiTokens.addNewToken\": \"नव एपीआई टोकन जोड़ें\",\n\t\"Settings.tokens.copy.editMessage\": \"सुरक्षाकारणात्, भवान् केवलं एकवारं एव स्वस्य टोकनं द्रष्टुं शक्नोति।\",\n\t\"Settings.tokens.copy.editTitle\": \"इदं टोकनम् इतः परं सुलभं नास्ति।\",\n\t\"Settings.tokens.copy.lastWarning\": \"एतत् टोकनं प्रतिलिख्यताम् अवश्यं कुरुत, पुनः द्रष्टुं न शक्ष्यति!\",\n\t\"Settings.apiTokens.create\": \"नवीन एपिआइ टोकन रचयतु\",\n\t\"Settings.apiTokens.description\": \"एपिआइ उपभोगार्थं उत्पन्नटोकनस्य सूची\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"भवतः अद्यापि किमपि सामग्री नास्ति...\",\n\t\"Settings.tokens.notification.copied\": \"टोकनः क्लिप्बोर्ड् मध्ये प्रतिलिपितः।\",\n\t\"Settings.apiTokens.title\": \"एपीआई टोकन\",\n\t\"Settings.tokens.types.full-access\": \"पूर्णाभिगमनम्\",\n\t\"Settings.tokens.types.read-only\": \"केवल-पठनीयम्\",\n\t\"Settings.application.description\": \"प्रशासनपटलस्य वैश्विकसूचना\",\n\t\"Settings.application.edition-title\": \"वर्तमान योजना\",\n\t\"Settings.application.get-help\": \"सहायतां प्राप्नुवन्तु\",\n\t\"Settings.application.link-pricing\": \"सर्वमूल्यनिर्धारणयोजनानि पश्यन्तु\",\n\t\"Settings.application.link-upgrade\": \"स्वप्रशासकपटलस्य उन्नयनम्\",\n\t\"Settings.application.node-version\": \"नोड संस्करणम्\",\n\t\"Settings.application.strapi-version\": \"स्ट्रैपी संस्करणम्\",\n\t\"Settings.application.strapiVersion\": \"स्ट्रैपीसंस्करण\",\n\t\"Settings.application.title\": \"अवलोकन\",\n\t\"Settings.error\": \"त्रुटि\",\n\t\"Settings.global\": \"वैश्विक सेटिंग्स्\",\n\t\"Settings.permissions\": \"प्रशासनपटल\",\n\t\"Settings.permissions.category\": \"{कोटी} कृते अनुमतिसेटिंग्स्\",\n\t\"Settings.permissions.category.plugins\": \"{कोटी} प्लगिन् कृते अनुमतिसेटिंग्स्\",\n\t\"Settings.permissions.conditions.anytime\": \"कदापि\",\n\t\"Settings.permissions.conditions.apply\": \"प्रयोजयन्तु\",\n\t\"Settings.permissions.conditions.can\": \"कर्तुं शक्नुवन्ति\",\n\t\"Settings.permissions.conditions.conditions\": \"शर्ताः परिभाषयन्तु\",\n\t\"Settings.permissions.conditions.links\": \"लिङ्क्स्\",\n\t\"Settings.permissions.conditions.no-actions\": \"भवतः प्रथमं क्रियाः (निर्माणं, पठनं, अद्यतनीकरणं, ...) चयनं कर्तव्यं तेषु शर्ताः परिभाषयितुं पूर्वं।\",\n\t\"Settings.permissions.conditions.none-selected\": \"कदापि\",\n\t\"Settings.permissions.conditions.or\": \"वा\",\n\t\"Settings.permissions.conditions.when\": \"कदा\",\n\t\"Settings.permissions.select-all-by-permission\": \"सर्व {लेबल} अनुमतिः चिनोतु\",\n\t\"Settings.permissions.select-by-permission\": \"{नामपत्र} अनुमतिं चिनोतु\",\n\t\"Settings.permissions.users.create\": \"नवप्रयोक्तारं आमन्त्रयतु\",\n\t\"Settings.permissions.users.email\": \"ईमेल\",\n\t\"Settings.permissions.users.firstname\": \"प्रथमनाम\",\n\t\"Settings.permissions.users.lastname\": \"अन्तिनाम\",\n\t\"Settings.permissions.users.form.sso\": \"SSO इत्यनेन सह संयोजयन्तु\",\n\t\"Settings.permissions.users.form.sso.description\": \"यदा सक्षम (ON) भवति तदा उपयोक्तारः SSO मार्गेण प्रवेशं कर्तुं शक्नुवन्ति\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"सर्वप्रयोक्तारः येषां Strapi व्यवस्थापकपटले प्रवेशः अस्ति\",\n\t\"Settings.permissions.users.tabs.label\": \"टैब्स् अनुमतिः\",\n\t\"Settings.profile.form.notify.data.loaded\": \"भवतः प्रोफाइल-दत्तांशः लोड् कृतः\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"चयनित-अन्तरफलक-भाषां स्वच्छं कुरुत\",\n\t\"Settings.profile.form.section.experience.here\": \"अत्र\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"अन्तरफलकभाषा\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"एतत् केवलं चयनितभाषायां भवतः स्वकीयं अन्तरफलकं प्रदर्शयिष्यति।\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"प्राथमिकतापरिवर्तनानि केवलं भवतः कृते एव प्रवर्तन्ते। अधिका सूचना {अत्र} उपलभ्यते।\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"अन्तरफलक मोड\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"चयनितविधाने भवतः अन्तरफलकं प्रदर्शयति।\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{नाम} मोड\",\n\t\"Settings.profile.form.section.experience.title\": \"अनुभव\",\n\t\"Settings.profile.form.section.head.title\": \"उपयोक्तृप्रोफाइल\",\n\t\"Settings.profile.form.section.profile.page.title\": \"प्रोफाइल पृष्ठ\",\n\t\"Settings.roles.create.description\": \"भूमिकायां दत्तान् अधिकारान् परिभाषयतु\",\n\t\"Settings.roles.create.title\": \"भूमिका रचयतु\",\n\t\"Settings.roles.created\": \"भूमिका निर्मितम्\",\n\t\"Settings.roles.edit.title\": \"एकां भूमिकां सम्पादयतु\",\n\t\"Settings.roles.form.button.users-with-role\": \"{संख्या, बहुवचनम्, =0 {# उपयोक्तारः} एकः {# उपयोक्तारः} अन्ये {# उपयोक्तारः}} एतया भूमिकायाः सह\",\n\t\"Settings.roles.form.created\": \"निर्मितम्\",\n\t\"Settings.roles.form.description\": \"भूमिकायाः नाम वर्णनं च\",\n\t\"Settings.roles.form.permission.property-label\": \"{लेबल} अनुमतिः\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"क्षेत्राणि अनुमतिः\",\n\t\"Settings.roles.form.permissions.create\": \"रचयतु\",\n\t\"Settings.roles.form.permissions.delete\": \"लुप्\",\n\t\"Settings.roles.form.permissions.publish\": \"प्रकाशनम्\",\n\t\"Settings.roles.form.permissions.read\": \"पठन्तु\",\n\t\"Settings.roles.form.permissions.update\": \"अद्यतन\",\n\t\"Settings.roles.list.button.add\": \"नवीन भूमिका योजयन्तु\",\n\t\"Settings.roles.list.description\": \"भूमिकानां सूची\",\n\t\"Settings.roles.title.singular\": \"भूमिका\",\n\t\"Settings.sso.description\": \"एकल-प्रवेश-विशेषतायाः सेटिङ्ग्स् विन्यस्यताम्।\",\n\t\"Settings.sso.form.defaultRole.description\": \"इदं चयनितभूमिकायां नूतनं प्रमाणीकृतं उपयोक्तारं संलग्नं करिष्यति\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"प्रशासकभूमिकाः पठितुं भवतः अनुमतिः आवश्यकी अस्ति\",\n\t\"Settings.sso.form.defaultRole.label\": \"पूर्वनिर्धारित भूमिका\",\n\t\"Settings.sso.form.registration.description\": \"यदि खाता नास्ति तर्हि SSO प्रवेशे नूतनं उपयोक्तारं रचयतु\",\n\t\"Settings.sso.form.registration.label\": \"स्वयं-पञ्जीकरणम्\",\n\t\"Settings.sso.title\": \"एकल-साइन-ऑन\",\n\t\"Settings.webhooks.create\": \"जालपुटं रचयतु\",\n\t\"Settings.webhooks.create.header\": \"नवीनशीर्षकं रचयतु\",\n\t\"Settings.webhooks.created\": \"जालपुटं निर्मितम्\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"एषा घटना केवलं मसौदा/प्रकाशनप्रणाली सक्षमीकृतसामग्रीणां कृते विद्यते\",\n\t\"Settings.webhooks.events.create\": \"रचयतु\",\n\t\"Settings.webhooks.events.update\": \"अद्यतन\",\n\t\"Settings.webhooks.form.events\": \"इवेण्ट्स्\",\n\t\"Settings.webhooks.form.headers\": \"शीर्षकाणि\",\n\t\"Settings.webhooks.form.url\": \"यूआरएल\",\n\t\"Settings.webhooks.headers.remove\": \"शीर्षकपङ्क्तिं {संख्या} निष्कासयतु\",\n\t\"Settings.webhooks.key\": \"की\",\n\t\"Settings.webhooks.list.button.add\": \"नवीन जालपुटं रचयतु\",\n\t\"Settings.webhooks.list.description\": \"POST परिवर्तनसूचनाः प्राप्नुवन्तु\",\n\t\"Settings.webhooks.list.empty.description\": \"कोऽपि जालपुटाः न प्राप्ताः\",\n\t\"Settings.webhooks.list.empty.link\": \"अस्माकं दस्तावेजीकरणं पश्यन्तु\",\n\t\"Settings.webhooks.list.empty.title\": \"अद्यापि कोऽपि जालपुटः नास्ति\",\n\t\"Settings.webhooks.list.th.actions\": \"क्रियाः\",\n\t\"Settings.webhooks.list.th.status\": \"स्थितिः\",\n\t\"Settings.webhooks.singular\": \"वेबहूक\",\n\t\"Settings.webhooks.title\": \"वेबहूक्स\",\n\t\"Settings.webhooks.to.delete\": \"बहुवचनम्, एकं {# सम्पत्ति} अन्ये {# सम्पत्तिः}} चयनितम्\",\n\t\"Settings.webhooks.trigger\": \"ट्रिगर\",\n\t\"Settings.webhooks.trigger.cancel\": \"ट्रिगर रद्द करें\",\n\t\"Settings.webhooks.trigger.pending\": \"लंबित...\",\n\t\"Settings.webhooks.trigger.save\": \"कृपया ट्रिगर कृते रक्षतु\",\n\t\"Settings.webhooks.trigger.success\": \"सफलता!\",\n\t\"Settings.webhooks.trigger.success.label\": \"ट्रिगर सफलः अभवत्\",\n\t\"Settings.webhooks.trigger.test\": \"परीक्षण-ट्रिगर\",\n\t\"Settings.webhooks.trigger.title\": \"ट्रिगरात् पूर्वं रक्षतु\",\n\t\"Settings.webhooks.value\": \"मूल्यम्\",\n\t\"Usecase.back-end\": \"पृष्ठ-अन्तविकासकः\",\n\t\"Usecase.button.skip\": \"एतत् प्रश्नं त्यजतु\",\n\t\"Usecase.content-creator\": \"सामग्री निर्माता\",\n\t\"Usecase.front-end\": \"अग्र-अन्त-विकासकः\",\n\t\"Usecase.full-stack\": \"पूर्ण-स्टैक विकासक\",\n\t\"Usecase.input.work-type\": \"भवन्तः किं प्रकारस्य कार्यं कुर्वन्ति?\",\n\t\"Usecase.notification.success.project-created\": \"परियोजना सफलतया निर्मितवती\",\n\t\"Usecase.other\": \"अन्य\",\n\t\"Usecase.title\": \"स्वविषये किञ्चित् अधिकं वदतु\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"उपयोक्तारः अनुमतिः च\",\n\t\"Users.components.List.empty\": \"प्रयोक्तारः नास्ति...\",\n\t\"Users.components.List.empty.withFilters\": \"प्रयुक्तैः फ़िल्टरैः सह उपयोक्तारः नास्ति...\",\n\t\"Users.components.List.empty.withSearch\": \"अन्वेषणस्य ({search}) अनुरूपाः उपयोक्तारः नास्ति...\",\n\t\"admin.pages.MarketPlacePage.head\": \"बाजारस्थानम् - प्लगिन्\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"भवन्तः अफलाइनाः सन्ति\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Strapi Market - मध्ये प्रवेशार्थं भवान् अन्तर्जालसङ्गणकेन सह सम्बद्धः भवितुम् अर्हति ।\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"संस्थापन आदेशं प्रतिलिख्यताम्\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"स्वस्य टर्मिनले चिनोतुम् सज्जं आदेशं संस्थापयतु\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"अधिकं ज्ञातुं\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} विषये अधिकं ज्ञातुं\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"अधिकं ज्ञातुं\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"स्थापितं\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"स्ट्रैपी द्वारा निर्मित\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"प्लगिन् स्ट्रैपी द्वारा सत्यापित\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"प्लगइन अन्वेषणं स्वच्छं कुरुत\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"\\\"{लक्ष्य}\\\" कृते कोऽपि परिणामः नास्ति\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"प्लगिन् अन्वेष्टुम्\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"स्वस्य प्लगइनं प्रस्तौतु\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"स्ट्रैपी इत्यस्मात् अधिकं प्राप्तुम्\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"प्लगइनं गम्यते?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"अस्मान् कथयतु यत् भवान् किं प्लगिन् अन्विष्यति तथा च वयं अस्माकं समुदायस्य प्लगिन् विकासकान् ज्ञापयिष्यामः यद्यपि ते प्रेरणायाः अन्वेषणं कुर्वन्ति!\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"क्लिपबोर्ड मध्ये प्रतिलिपिं कुर्वन्तु\",\n\t\"app.component.search.label\": \"{लक्ष्य} इति अन्वेषणं कुर्वन्तु\",\n\t\"app.component.table.duplicate\": \"{लक्ष्य} डुप्लिकेट करें\",\n\t\"app.component.table.edit\": \"{लक्ष्य} सम्पादयतु\",\n\t\"app.component.table.select.one-entry\": \"{लक्ष्य} चयनं कुर्वन्तु\",\n\t\"app.components.BlockLink.blog\": \"ब्लॉग\",\n\t\"app.components.BlockLink.blog.content\": \"स्ट्रैपी तथा पारिस्थितिकीतन्त्रस्य विषये नवीनतमवार्ताः पठन्तु।\",\n\t\"app.components.BlockLink.code\": \"कोड उदाहरणानि\",\n\t\"app.components.BlockLink.code.content\": \"समुदायस्य विकासेन वास्तविकपरियोजनानां परीक्षणेन शिक्षन्तु।\",\n\t\"app.components.BlockLink.documentation.content\": \"अत्यावश्यकसंकल्पनाः, मार्गदर्शिकाः, निर्देशाः च अन्वेषयन्तु।\",\n\t\"app.components.BlockLink.tutorial\": \"पाठ्यक्रम\",\n\t\"app.components.BlockLink.tutorial.content\": \"Strapi इत्यस्य उपयोगाय अनुकूलितुं च चरण-दर-चरण-निर्देशानां अनुसरणं कुर्वन्तु।\",\n\t\"app.components.Button.cancel\": \"रद्द करें\",\n\t\"app.components.Button.confirm\": \"पुष्टि करें\",\n\t\"app.components.Button.reset\": \"पुनर्स्थापनम्\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"शीघ्रमेव आगमिष्यति\",\n\t\"app.components.ConfirmDialog.title\": \"पुष्टिकरणम्\",\n\t\"app.components.DownloadInfo.download\": \"अवलोकनं प्रचलति...\",\n\t\"app.components.DownloadInfo.text\": \"एतत् एकं निमेषं यावत् समयं गृह्णीयात्। भवतः धैर्यस्य कृते धन्यवादः।\",\n\t\"app.components.EmptyAttributes.title\": \"अद्यापि क्षेत्राणि नास्ति\",\n\t\"app.components.EmptyStateLayout.content-document\": \"कोऽपि सामग्री न प्राप्ता\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"तत् सामग्रीं प्राप्तुं भवतः अनुमतिः नास्ति\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>अत्र सामग्रीप्रबन्धके सर्वाणि सामग्रीनि रचयन्तु प्रबन्धयन्तु च।</p><p>उदाहरणम्: ब्लॉग् वेबसाइट् उदाहरणम् अग्रे गृहीत्वा, कश्चन लेखितुं शक्नोति लेखं, यथा रोचते तथा रक्षित्वा प्रकाशयन्तु।</p><p>💡 त्वरितसूचना - भवता निर्मितसामग्रीयां प्रकाशनं मारयितुं न विस्मरन्तु।</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ सामग्री बनाएँ\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>भयानकं, एकं अन्तिमं सोपानं गन्तव्यम्!</p><b>🚀 कार्ये सामग्रीं पश्यन्तु</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"एपीआई परीक्षणं कुर्वन्तु\",\n\t\"app.components.GuidedTour.CM.success.title\": \"चरण 2: सम्पन्न ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>संग्रहप्रकाराः भवन्तं अनेकप्रविष्टीनां प्रबन्धने सहायकाः भवन्ति, एकप्रकाराः केवलं एकस्य प्रविष्टिप्रबन्धनाय उपयुक्ताः भवन्ति ।</p> <p>उदाहरणम्: ब्लॉगजालस्थलस्य कृते, लेखाः संग्रहप्रकारः स्यात् यदा तु मुखपृष्ठं एकलप्रकारः स्यात् ।</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"एकं संग्रहप्रकारं निर्मायताम्\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 प्रथमं संग्रहप्रकारं रचयतु\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>सुप्रचलति!</p><b>⚡️ भवान् जगति किं साझां कर्तुम् इच्छति?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"चरणम् 1: सम्पन्नम् ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>अत्र प्रमाणीकरणचिह्नं जनयतु तथा च भवता अधुना निर्मितां सामग्रीं पुनः प्राप्तुम्।</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"एपीआई टोकन उत्पन्न करें\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 सामग्रीं क्रियायां पश्यन्तु\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>HTTP अनुरोधं कृत्वा कार्ये सामग्रीं पश्यन्तु:</p><ul><li><p>अस्मिन् URL प्रति: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>शीर्षक के साथ: <light>अधिकार: वाहक '<'. YOUR_API_TOKEN'>'</light></p></li></ul><p>सामग्रीभिः सह अन्तरक्रियायाः अधिकमार्गाणां कृते <documentationLink>documentation</documentationLink> पश्यन्तु ।</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"मुखपृष्ठं प्रति गच्छतु\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"चरण 3: सम्पन्न ✅\",\n\t\"app.components.GuidedTour.create-content\": \"सामग्री रचयतु\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ भवान् विश्वेन सह किं साझां कर्तुम् इच्छति?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"सामग्री प्रकार निर्माता पर जाएँ\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 सामग्री संरचना का निर्माण\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"एपीआई परीक्षणं कुर्वन्तु\",\n\t\"app.components.GuidedTour.skip\": \"भ्रमणं त्यजतु\",\n\t\"app.components.GuidedTour.title\": \"आरम्भार्थं ३ चरणाः\",\n\t\"app.components.HomePage.button.blog\": \"ब्लॉग् मध्ये अधिकं पश्यन्तु\",\n\t\"app.components.HomePage.community\": \"समुदाये सम्मिलितं भवतु\",\n\t\"app.components.HomePage.community.content\": \"विभिन्न-चैनेल्-मध्ये दलस्य सदस्यैः, योगदातृभिः, विकासकैः च सह चर्चां कुर्वन्तु।\",\n\t\"app.components.HomePage.create\": \"स्वस्य प्रथमं सामग्रीप्रकारं रचयतु\",\n\t\"app.components.HomePage.roadmap\": \"अस्माकं मार्गचित्रं पश्यन्तु\",\n\t\"app.components.HomePage.welcome\": \"जहाज पर स्वागतम् 👋\",\n\t\"app.components.HomePage.welcome.again\": \"स्वागतम् 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"अभिनन्दनम्! भवान् प्रथमप्रशासकरूपेण लॉग् कृतः अस्ति। Strapi द्वारा प्रदत्तानां शक्तिशालिनां विशेषतानां आविष्कारार्थं, वयं भवतां प्रथमं सामग्रीप्रकारं निर्मातुं अनुशंसयामः!\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"अस्माकं आशास्ति यत् भवान् स्वस्य परियोजनायां प्रगतिम् करोति! Strapi विषये नवीनतमवार्ताः पठितुं निःशङ्कं भवन्तु। भवतः प्रतिक्रियायाः आधारेण उत्पादस्य सुधारार्थं वयं सर्वोत्तमं ददामः।\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"मुद्दे।\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" अथवा \",\n\t\"app.components.ImgPreview.hint\": \"स्वसञ्चिकां अस्मिन् क्षेत्रे कर्षयतु & पातयतु अथवा अपलोड् कर्तुं सञ्चिकायाः ​​कृते {browse} कुर्वन्तु\",\n\t\"app.components.ImgPreview.hint.browse\": \"ब्राउज़ करें\",\n\t\"app.components.InputFile.newFile\": \"नवीनसञ्चिकां योजयन्तु\",\n\t\"app.components.InputFileDetails.open\": \"नवीन ट्याब् मध्ये उद्घाट्यताम्\",\n\t\"app.components.InputFileDetails.originalName\": \"मूल नाम:\",\n\t\"app.components.InputFileDetails.remove\": \"एताम् सञ्चिकां निष्कासयतु\",\n\t\"app.components.InputFileDetails.size\": \"आकार:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"प्लगिन् डाउनलोड् कृत्वा संस्थापयितुं कतिचन सेकेण्ड् यावत् समयः भवितुं शक्नोति।\",\n\t\"app.components.InstallPluginPage.Download.title\": \"अवलोकनं भवति...\",\n\t\"app.components.InstallPluginPage.description\": \"अप्रयत्नेन स्वस्य एप्लिकेशनं विस्तारयतु।\",\n\t\"app.components.LeftMenu.collapse\": \"नवपट्टिकां संकुचयतु\",\n\t\"app.components.LeftMenu.expand\": \"नवपट्टिकां विस्तारयतु\",\n\t\"app.components.LeftMenu.logout\": \"लॉगआउट\",\n\t\"app.components.LeftMenu.navbrand.title\": \"स्ट्रैपी डैशबोर्ड\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"कार्यस्थानम्\",\n\t\"app.components.LeftMenuFooter.help\": \"सहायता\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"द्वारा संचालितम् \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"संग्रह प्रकार\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"विन्यासाः\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"सामान्य\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"अद्यापि कोऽपि प्लगिन् संस्थापितः नास्ति\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"प्लगिन्स्\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"एकल प्रकार\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"प्लगिन् विस्थापयितुं कतिचन सेकेण्ड् यावत् समयः भवितुं शक्नोति।\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"अस्थापनम्\",\n\t\"app.components.ListPluginsPage.description\": \"प्रकल्पे संस्थापितानां प्लगिन्स् सूची।\",\n\t\"app.components.ListPluginsPage.head.title\": \"प्लगिन्स् सूचीकरणम्\",\n\t\"app.components.Logout.logout\": \"लॉगआउट\",\n\t\"app.components.Logout.profile\": \"प्रोफाइल\",\n\t\"app.components.MarketplaceBanner\": \"समुदायेन निर्मिताः प्लगिन्स्, अपि च भवतः परियोजनायाः किकस्टार्ट् कर्तुं अनेकानि भयानकवस्तूनि, Strapi Awesome इत्यत्र अन्वेष्यताम्।\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"एक स्ट्रैपी रॉकेट लोगो\",\n\t\"app.components.MarketplaceBanner.link\": \"अधुना तत् पश्यन्तु\",\n\t\"app.components.NotFoundPage.back\": \"मुखपृष्ठं प्रति गच्छतु\",\n\t\"app.components.NotFoundPage.description\": \"न प्राप्तम्\",\n\t\"app.components.Official\": \"आधिकारिक\",\n\t\"app.components.Onboarding.help.button\": \"सहायता बटन\",\n\t\"app.components.Onboarding.label.completed\": \"% पूर्णम्\",\n\t\"app.components.Onboarding.title\": \"प्रारम्भं कुर्वन्तु वीडियो\",\n\t\"app.components.PluginCard.Button.label.download\": \"अवलोकन\",\n\t\"app.components.PluginCard.Button.label.install\": \"पूर्वमेव संस्थापितम्\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"autoReload सुविधां सक्षमं कर्तुं आवश्यकम्। कृपया `yarn develop` इत्यनेन स्वस्य एप् आरभत।\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"अहं अवगच्छामि!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"सुरक्षाकारणात्, प्लगिन् केवलं विकासवातावरणे एव डाउनलोड् कर्तुं शक्यते।\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"अवलोकनं असम्भवम्\",\n\t\"app.components.PluginCard.compatible\": \"भवतः एप्लिकेशनेन सह संगतम्\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"समुदायेन सह संगतम्\",\n\t\"app.components.PluginCard.more-details\": \"अधिकविवरणम्\",\n\t\"app.components.ToggleCheckbox.off-label\": \"मिथ्या\",\n\t\"app.components.ToggleCheckbox.on-label\": \"सत्यम्\",\n\t\"app.components.Users.MagicLink.connect\": \"अस्य उपयोक्त्रे प्रवेशं दातुं एतत् लिङ्क् प्रतिलिख्य साझां कुर्वन्तु\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"इदं लिङ्कं उपयोक्त्रे प्रेषयन्तु, प्रथमं प्रवेशं SSO प्रदातृद्वारा कर्तुं शक्यते\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"उपयोक्तृविवरणम्\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"उपयोक्तृ भूमिकाः\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"एकस्य उपयोक्तुः एकं वा अनेकं वा भूमिकां भवितुम् अर्हति\",\n\t\"app.components.Users.SortPicker.button-label\": \"द्वारा क्रमबद्ध करें\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"ईमेल (ए तः जेड)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"ईमेल (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"प्रथम नाम (ए से जेड)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"प्रथम नाम (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"अंतिम नाम (ए से जेड)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"अन्तिमनाम (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"उपयोक्तृनाम (ए से जेड)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"उपयोक्तृनाम (Z to A)\",\n\t\"app.components.listPlugins.button\": \"नवीन प्लगइन जोड़ें\",\n\t\"app.components.listPlugins.title.none\": \"कोऽपि प्लगिन्स् संस्थापिताः न सन्ति\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"प्लगिन् विस्थापयति समये त्रुटिः अभवत्\",\n\t\"app.containers.App.notification.error.init\": \"एपिआइ अनुरोधं कुर्वन् त्रुटिः अभवत्\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"यदि भवान् एतत् लिङ्क् न प्राप्नोति तर्हि कृपया स्वप्रशासकेन सह सम्पर्कं कुर्वन्तु।\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"भवतः गुप्तशब्दपुनर्प्राप्तिलिङ्क् प्राप्तुं कतिपयानि निमेषाणि यावत् समयः भवितुं शक्नोति।\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"ईमेल प्रेषितम्\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"सक्रियम्\",\n\t\"app.containers.Users.EditPage.header.label\": \"{नाम} सम्पादयतु\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"उपयोक्तारं सम्पादयतु\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"विशेषित भूमिकाएँ\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"उपयोक्तारं आमन्त्रयतु\",\n\t\"app.links.configure-view\": \"दृश्यं विन्यस्यताम्\",\n\t\"app.page.not.found\": \"अफ! भवन्तः यत् पृष्ठं भ्रमन्ति तत् वयं न प्राप्नुमः इव दृश्यन्ते...\",\n\t\"app.static.links.cheasheet\": \"चीटशीट\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"फ़िल्टर जोड़ें\",\n\t\"app.utils.close-label\": \"बन्द करें\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"डुप्लिकेट\",\n\t\"app.utils.edit\": \"सम्पादन\",\n\t\"app.utils.errors.file-too-big.message\": \"सञ्चिका अतीव विशाला अस्ति\",\n\t\"app.utils.filter-value\": \"फ़िल्टर मान\",\n\t\"app.utils.filters\": \"फ़िल्टर\",\n\t\"app.utils.notify.data-loaded\": \"{लक्ष्य} लोड् कृतम्\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"प्रकाशित करें\",\n\t\"app.utils.select-all\": \"सर्वं चयनं कुरुत\",\n\t\"app.utils.select-field\": \"क्षेत्रं चयनं कुर्वन्तु\",\n\t\"app.utils.select-filter\": \"फ़िल्टर चयन करें\",\n\t\"app.utils.unpublish\": \"अप्रकाशित करें\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"इयं सामग्री सम्प्रति निर्माणाधीना अस्ति, कतिपयेषु सप्ताहेषु पुनः आगमिष्यति!\",\n\t\"component.Input.error.validation.integer\": \"मूल्यं पूर्णाङ्कं भवितुमर्हति\",\n\t\"components.AutoReloadBlocker.description\": \"निम्नलिखित आदेशेषु एकेन सह Strapi चालयन्तु:\",\n\t\"components.AutoReloadBlocker.header\": \"अस्य प्लगिन् कृते पुनः लोड्-विशेषता आवश्यकी अस्ति।\",\n\t\"components.ErrorBoundary.title\": \"किञ्चित् त्रुटिः अभवत्...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"शामिल है\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"शामिल है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"सहितं समाप्तं भवति\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"सहितं समाप्तं भवति (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"हैं\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"हैं (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"अपेक्षया अधिकम् अस्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"अधिकं वा समानं वा अस्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"अपेक्षया न्यूनम् अस्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"अपेक्षया न्यूनं वा समानं वा अस्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"न भवति\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"न भवति (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"नास्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"नास्ति (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"शून्यं नास्ति\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"शून्य है\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"सहितं आरभ्यते\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"सहितं आरभ्यते (case insensitive)\",\n\t\"components.Input.error.attribute.key.taken\": \"एतत् मूल्यं पूर्वमेव अस्ति\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"समानं न भवितुम् अर्हति\",\n\t\"components.Input.error.attribute.taken\": \"एतत् क्षेत्रनाम पूर्वमेव अस्ति\",\n\t\"components.Input.error.contain.lowercase\": \"गुप्तशब्दे न्यूनातिन्यूनम् एकं लघुवर्णं भवितुमर्हति\",\n\t\"components.Input.error.contain.number\": \"गुप्तशब्दे न्यूनातिन्यूनम् एकः संख्या भवितुमर्हति\",\n\t\"components.Input.error.contain.uppercase\": \"गुप्तशब्दे न्यूनातिन्यूनम् एकं दीर्घवर्णं भवितुमर्हति\",\n\t\"components.Input.error.contentTypeName.taken\": \"एतत् नाम पूर्वमेव अस्ति\",\n\t\"components.Input.error.custom-error\": \"{त्रुटिसंदेश} \",\n\t\"components.Input.error.password.noMatch\": \"गुप्तशब्दाः न मेलन्ति\",\n\t\"components.Input.error.validation.email\": \"एषः अमान्यः ईमेलः अस्ति\",\n\t\"components.Input.error.validation.json\": \"एतत् JSON प्रारूपेण सह न मेलति\",\n\t\"components.Input.error.validation.lowercase\": \"मूल्यं लघुवर्णीयं स्ट्रिंग् भवितुमर्हति\",\n\t\"components.Input.error.validation.max\": \"मूल्यं बहु उच्चम् अस्ति। {max}\",\n\t\"components.Input.error.validation.maxLength\": \"मूल्यं बहु दीर्घम् अस्ति। {max}\",\n\t\"components.Input.error.validation.min\": \"मूल्यं बहु न्यूनम् अस्ति। {min}\",\n\t\"components.Input.error.validation.minLength\": \"मूल्यम् अतीव लघु अस्ति। {min}\",\n\t\"components.Input.error.validation.minSupMax\": \"श्रेष्ठं न भवितुम् अर्हति\",\n\t\"components.Input.error.validation.regex\": \"मूल्यं regex इत्यनेन सह न मेलति।\",\n\t\"components.Input.error.validation.required\": \"एतत् मूल्यम् आवश्यकम्।\",\n\t\"components.Input.error.validation.unique\": \"एतत् मूल्यं पूर्वमेव उपयुज्यते।\",\n\t\"components.InputSelect.option.placeholder\": \"अत्र चिनोतु\",\n\t\"components.ListRow.empty\": \"प्रदर्शनीयः कोऽपि दत्तांशः नास्ति।\",\n\t\"components.NotAllowedInput.text\": \"एतत् क्षेत्रं द्रष्टुं कोऽपि अनुमतिः नास्ति\",\n\t\"components.OverlayBlocker.description\": \"भवन्तः एकं विशेषतां उपयुञ्जते यस्य सर्वरस्य पुनः आरम्भस्य आवश्यकता अस्ति। कृपया सर्वरस्य उपरि यावत् प्रतीक्ष्यताम्।\",\n\t\"components.OverlayBlocker.description.serverError\": \"सर्वरः पुनः आरम्भः भवितुम् अर्हति स्म, कृपया टर्मिनल् मध्ये स्वस्य लॉग्स् परीक्ष्यताम्।\",\n\t\"components.OverlayBlocker.title\": \"पुनः आरम्भस्य प्रतीक्षा अस्ति...\",\n\t\"components.OverlayBlocker.title.serverError\": \"पुनः आरम्भः अपेक्षितापेक्षया अधिकं समयं गृह्णाति\",\n\t\"components.PageFooter.select\": \"प्रति पृष्ठ प्रविष्टियाँ\",\n\t\"components.ProductionBlocker.description\": \"सुरक्षाप्रयोजनार्थं अस्माभिः अन्येषु वातावरणेषु एतत् प्लगिन् निष्क्रियं कर्तव्यम्।\",\n\t\"components.ProductionBlocker.header\": \"इदं प्लगिन् केवलं विकासे एव उपलभ्यते।\",\n\t\"components.Search.placeholder\": \"अन्वेषणं...\",\n\t\"components.TableHeader.sort\": \"{लेबल} पर क्रमबद्ध करें\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"मार्कडाउन मोड\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"पूर्वावलोकन मोड\",\n\t\"components.Wysiwyg.collapse\": \"संकुचितम्\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"शीर्षक H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"शीर्षक H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"शीर्षक H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"शीर्षक H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"शीर्षक H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"शीर्षक H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"एकं शीर्षकं योजयन्तु\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"वर्ण\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"विस्तार\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"सञ्चिकाः कर्षयतु & पातयतु, क्लिप्बोर्डतः चिनोतु अथवा {ब्राउज्} करोतु।\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"तेषां चयनं कुर्वन्तु\",\n\t\"components.pagination.go-to\": \"पृष्ठं {पृष्ठं} प्रति गच्छतु\",\n\t\"components.pagination.go-to-next\": \"अग्रे पृष्ठं प्रति गच्छतु\",\n\t\"components.pagination.go-to-previous\": \"पूर्वपृष्ठं गच्छतु\",\n\t\"components.pagination.remaining-links\": \"अन्ये च {संख्या} अन्ये लिङ्कानि\",\n\t\"components.popUpWarning.button.cancel\": \"न, रद्द करें\",\n\t\"components.popUpWarning.button.confirm\": \"हाँ, पुष्टि करें\",\n\t\"components.popUpWarning.message\": \"किं भवान् निश्चयेन एतत् विलोपयितुम् इच्छति?\",\n\t\"components.popUpWarning.title\": \"कृपया पुष्टि करें\",\n\t\"form.button.continue\": \"अग्रेसर\",\n\t\"form.button.done\": \"कृतम्\",\n\t\"global.actions\": \"क्रियाः\",\n\t\"global.back\": \"पृष्ठम्\",\n\t\"global.change-password\": \"गुप्तशब्दं परिवर्तयतु\",\n\t\"global.content-manager\": \"सामग्री प्रबन्धक\",\n\t\"global.continue\": \"अग्रे गच्छतु\",\n\t\"global.delete\": \"विलोपनम्\",\n\t\"global.delete-target\": \"{लक्ष्य} को हटाएँ\",\n\t\"global.description\": \"विवरण\",\n\t\"global.details\": \"विवरणम्\",\n\t\"global.disabled\": \"अक्षम\",\n\t\"global.documentation\": \"दस्तावेजीकरणम्\",\n\t\"global.enabled\": \"सक्षमम्\",\n\t\"global.finish\": \"समाप्त\",\n\t\"global.marketplace\": \"बाजारस्थान\",\n\t\"global.name\": \"नाम\",\n\t\"global.none\": \"कोऽपि नास्ति\",\n\t\"global.password\": \"गुप्तशब्द\",\n\t\"global.plugins\": \"प्लगिन्स्\",\n\t\"global.profile\": \"प्रोफाइल\",\n\t\"global.prompt.unsaved\": \"किं भवान् निश्चयेन एतत् पृष्ठं त्यक्तुम् इच्छति? भवतः सर्वे परिवर्तनानि नष्टानि भविष्यन्ति\",\n\t\"global.reset-password\": \"गुप्तशब्दं पुनः सेट् कुर्वन्तु\",\n\t\"global.roles\": \"भूमिका\",\n\t\"global.save\": \"रक्षतु\",\n\t\"global.see-more\": \"अधिकं पश्यन्तु\",\n\t\"global.select\": \"चयन\",\n\t\"global.select-all-entries\": \"सर्वप्रविष्टीनां चयनं कुर्वन्तु\",\n\t\"global.settings\": \"सेटिंग्स्\",\n\t\"global.type\": \"प्रकार\",\n\t\"global.users\": \"उपयोक्तारः\",\n\t\"notification.contentType.relations.conflict\": \"सामग्रीप्रकारस्य परस्परविरोधिनः सम्बन्धाः सन्ति\",\n\t\"notification.default.title\": \"सूचना:\",\n\t\"notification.error\": \"त्रुटिः अभवत्\",\n\t\"notification.error.layout\": \"विन्यासं पुनः प्राप्तुं न शक्यते\",\n\t\"notification.form.error.fields\": \"प्रपत्रे केचन त्रुटयः सन्ति\",\n\t\"notification.form.success.fields\": \"परिवर्तनानि रक्षितानि\",\n\t\"notification.link-copied\": \"लिङ्क् क्लिप्बोर्ड् मध्ये प्रतिलिपिता\",\n\t\"notification.permission.not-allowed-read\": \"भवता एतत् दस्तावेजं द्रष्टुं न अनुमतम्\",\n\t\"notification.success.delete\": \"द्रव्यं विलोपितम्\",\n\t\"notification.success.saved\": \"रक्षितम्\",\n\t\"notification.success.title\": \"सफलता:\",\n\t\"notification.version.update.message\": \"Strapi इत्यस्य नूतनं संस्करणं उपलब्धम् अस्ति!\",\n\t\"notification.warning.title\": \"चेतावनी:\",\n\tor: or,\n\t\"request.error.model.unknown\": \"एतत् प्रतिरूपं नास्ति\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, sa as default, or, skipToContent, submit };\n//# sourceMappingURL=sa-KUwV8aRB.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}