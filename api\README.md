# 🚀 Getting started with Strapi

Strapi comes with a full featured [Command Line Interface](https://docs.strapi.io/dev-docs/cli) (CLI) which lets you scaffold and manage your project in seconds.

### `develop`

Start your Strapi application with autoReload enabled. [Learn more](https://docs.strapi.io/dev-docs/cli#strapi-develop)

```
npm run develop
# or
yarn develop
```

### `start`

Start your Strapi application with autoReload disabled. [Learn more](https://docs.strapi.io/dev-docs/cli#strapi-start)

```
npm run start
# or
yarn start
```

### `build`

Build your admin panel. [Learn more](https://docs.strapi.io/dev-docs/cli#strapi-build)

```
npm run build
# or
yarn build
```

## ⚙️ Deployment

Strapi gives you many possible deployment options for your project including [Strapi Cloud](https://cloud.strapi.io). Browse the [deployment section of the documentation](https://docs.strapi.io/dev-docs/deployment) to find the best solution for your use case.

```
yarn strapi deploy
```

## 📚 Learn more

- [Resource center](https://strapi.io/resource-center) - Strapi resource center.
- [Strapi documentation](https://docs.strapi.io) - Official Strapi documentation.
- [Strapi tutorials](https://strapi.io/tutorials) - List of tutorials made by the core team and the community.
- [Strapi blog](https://strapi.io/blog) - Official Strapi blog containing articles made by the Strapi team and the community.
- [Changelog](https://strapi.io/changelog) - Find out about the Strapi product updates, new features and general improvements.

Feel free to check out the [Strapi GitHub repository](https://github.com/strapi/strapi). Your feedback and contributions are welcome!

## ✨ Community

- [Discord](https://discord.strapi.io) - Come chat with the Strapi community including the core team.
- [Forum](https://forum.strapi.io/) - Place to discuss, ask questions and find answers, show your Strapi project and get feedback or just talk with other Community members.
- [Awesome Strapi](https://github.com/strapi/awesome-strapi) - A curated list of awesome things related to Strapi.

---

<sub>🤫 Psst! [Strapi is hiring](https://strapi.io/careers).</sub>
# Palmas - Sistema de Administración de Condominios

## 📱 Sistema de Notificaciones

El sistema utiliza múltiples canales para enviar notificaciones a los residentes:

1. **Notificaciones Internas**: Usando el servicio de notificaciones de Strapi
2. **Server-Sent Events (SSE)**: Para actualizaciones en tiempo real
3. **Email**: Usando el servicio de email configurado
4. **WhatsApp**: Usando SendGrid

### Configuración de SendGrid

1. Instalar la dependencia:
```bash
yarn add @sendgrid/mail
```

2. Configurar las variables de entorno en `.env`:
```bash
# SendGrid Configuration
SENDGRID_API_KEY=tu_api_key_de_sendgrid
SENDGRID_FROM_NUMBER=tu_numero_de_whatsapp

# Opcional: IDs de plantillas de SendGrid
SENDGRID_WARNING_TEMPLATE_ID=id_plantilla_advertencias
SENDGRID_VISIT_TEMPLATE_ID=id_plantilla_visitas
SENDGRID_PAYMENT_TEMPLATE_ID=id_plantilla_pagos
```

3. Verificar que el número de WhatsApp esté registrado en SendGrid
4. Asegurarse de que las plantillas estén creadas en SendGrid si se van a usar

### Estructura de Notificaciones

- `/api/notification`: API REST para gestión de notificaciones
- `/services/notifications`: Servicios globales de envío
  - `base.ts`: Servicio base que maneja todos los canales
  - `whatsapp.ts`: Servicio específico para WhatsApp
