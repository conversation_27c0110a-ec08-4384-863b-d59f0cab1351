/**
 * push-subscription controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import webpush from "web-push";

interface PushSubscription {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  data?: any;
}

export default factories.createCoreController(
  "api::push-subscription.push-subscription",
  ({ strapi }) => ({
    async create(ctx: Context) {
      const { subscription, userId } = ctx.request.body as {
        subscription: PushSubscription;
        userId: number;
      };

      try {
        const result = await strapi
          .service("api::push-subscription.push-subscription")
          .createOrUpdate({
            endpoint: subscription.endpoint,
            keys: subscription.keys,
            user: userId,
          });

        return ctx.send({
          message: "Suscripción guardada exitosamente",
          subscription: result,
        });
      } catch (error) {
        console.error("Error al guardar la suscripción:", error);
        return ctx.badRequest("Error al procesar la suscripción");
      }
    },

    async delete(ctx: Context) {
      const endpoint = decodeURIComponent(ctx.params.endpoint);

      try {
        const subscription = await strapi
          .service("api::push-subscription.push-subscription")
          .findByEndpoint(endpoint);

        if (!subscription) {
          return ctx.notFound("Suscripción no encontrada");
        }

        await strapi.entityService.delete(
          "api::push-subscription.push-subscription",
          subscription.id
        );

        return ctx.send({
          message: "Suscripción eliminada exitosamente",
        });
      } catch (error) {
        console.error("Error al eliminar la suscripción:", error);
        return ctx.badRequest("Error al eliminar la suscripción");
      }
    },

    async sendNotification(ctx: Context) {
      const { userId, notification } = ctx.request.body as {
        userId: number;
        notification: NotificationPayload;
      };

      try {
        const subscription = await strapi
          .service("api::push-subscription.push-subscription")
          .findByUser(userId);

        if (!subscription) {
          return ctx.send({
            message: "Usuario no tiene suscripción a notificaciones push",
            results: [{ success: false, error: "No subscription found" }],
          });
        }

        try {
          await webpush.sendNotification(
            {
              endpoint: subscription.endpoint,
              keys: subscription.keys,
            },
            JSON.stringify(notification)
          );

          return ctx.send({
            message: "Notificación enviada exitosamente",
            results: [{ success: true, endpoint: subscription.endpoint }],
          });
        } catch (error) {
          console.error(
            `Error al enviar notificación a ${subscription.endpoint}:`,
            error
          );

          if (error.statusCode === 410) {
            await strapi.entityService.delete(
              "api::push-subscription.push-subscription",
              subscription.id
            );
          }

          return ctx.send({
            message: "Error al enviar notificación",
            results: [
              {
                success: false,
                endpoint: subscription.endpoint,
                error: error.message,
              },
            ],
          });
        }
      } catch (error) {
        console.error("Error al enviar notificaciones:", error);
        return ctx.badRequest("Error al enviar notificaciones");
      }
    },
  })
);
