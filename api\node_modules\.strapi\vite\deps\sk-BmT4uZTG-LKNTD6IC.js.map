{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/sk-BmT4uZTG.mjs"], "sourcesContent": ["const sk = {\n  \"components.Row.open\": \"Otvoriť\",\n  \"components.Row.regenerate\": \"Znova vygenerovať\",\n  \"containers.HomePage.Block.title\": \"Verzie\",\n  \"containers.HomePage.Button.update\": \"Upraviť\",\n  \"containers.HomePage.PluginHeader.title\": \"Dokumentácia — Nastavenia\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"Rozumiem\",\n  \"containers.HomePage.PopUpWarning.message\": \"Ste si istý, že chcete odstrániť túto verziu?\",\n  \"containers.HomePage.copied\": \"Token bol skopírovaný do schránky\",\n  \"containers.HomePage.form.jwtToken\": \"Získať JWT token\",\n  \"containers.HomePage.form.jwtToken.description\": \"Skopírujte tento token a použijte ho v Swaggeri pri volaniach API\",\n  \"containers.HomePage.form.password\": \"<PERSON><PERSON><PERSON>\",\n  \"containers.HomePage.form.password.inputDescription\": \"Nastavte heslo pre prístup k dokumentácii\",\n  \"containers.HomePage.form.restrictedAccess\": \"Obmedzený prístup\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"Nastavte dokumentáciu ako súkromnú. Predvolene je verejne prístupná.\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"Zobraziť vygenerované súbory\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"Užitočné, keď potrebujete prepísať vygenerovanú dokumentáciu. \\nPlugin vygeneruje súbory rozdelené podľa modelu a pluginu. \\nToto uľahčí úpravy dokumentácie.\",\n  \"error.deleteDoc.versionMissing\": \"Verzia, ktorú sa snažíte vymazať, neexistuje.\",\n  \"error.noVersion\": \"Verzia je povinná\",\n  \"error.regenerateDoc\": \"Nastala chyba pri generovaní dokumentácie\",\n  \"error.regenerateDoc.versionMissing\": \"Verzia, ktorú sa snažíte vygenerovať, neexistuje.\",\n  \"notification.update.success\": \"Nastavenia boli upravené\",\n  \"plugin.name\": \"Dokumentácia\"\n};\nexport {\n  sk as default\n};\n//# sourceMappingURL=sk-BmT4uZTG.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,eAAe;AACjB;", "names": []}