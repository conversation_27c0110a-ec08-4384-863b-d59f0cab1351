{"version": 3, "sources": ["../../../@strapi/plugin-documentation/dist/_chunks/zh-Hans-XLMwjASk.mjs"], "sourcesContent": ["const zhHans = {\n  \"components.Row.open\": \"打开\",\n  \"components.Row.regenerate\": \"重新生成\",\n  \"containers.HomePage.Block.title\": \"版本号\",\n  \"containers.HomePage.Button.update\": \"更新\",\n  \"containers.HomePage.PluginHeader.title\": \"文档 — 设置\",\n  \"containers.HomePage.PopUpWarning.confirm\": \"我确定\",\n  \"containers.HomePage.PopUpWarning.message\": \"您确定要删除此版本吗？\",\n  \"containers.HomePage.copied\": \"令牌已被复制到粘贴板\",\n  \"containers.HomePage.form.jwtToken\": \"检索您的 JWT 令牌\",\n  \"containers.HomePage.form.jwtToken.description\": \"复制此令牌，并用它在 Swagger 中发出请求\",\n  \"containers.HomePage.form.password\": \"密码\",\n  \"containers.HomePage.form.password.inputDescription\": \"设置密码以供访问文档\",\n  \"containers.HomePage.form.restrictedAccess\": \"禁止访问\",\n  \"containers.HomePage.form.restrictedAccess.inputDescription\": \"将文档端点设为私有。默认情况下，访问权限是公共的\",\n  \"containers.HomePage.form.showGeneratedFiles\": \"显示生成的文件\",\n  \"containers.HomePage.form.showGeneratedFiles.inputDescription\": \"当您想要覆盖生成的文档的时候很有用。 \\n该插件将生成按型号和插件拆分的文件。 \\n通过启用此选项，可以更轻松地自定义文档\",\n  \"error.deleteDoc.versionMissing\": \"您尝试删除的版本不存在。\",\n  \"error.noVersion\": \"需要一个版本\",\n  \"error.regenerateDoc\": \"重新生成文件时发生错误\",\n  \"error.regenerateDoc.versionMissing\": \"您尝试重新生成的版本不存在\",\n  \"notification.delete.success\": \"文档删除成功\",\n  \"notification.generate.success\": \"文档生成成功\",\n  \"notification.update.success\": \"设置更新成功\",\n  \"plugin.description.long\": \"创建OpenAPI文档并使用SwaggerUI可视化你的API\",\n  \"plugin.description.short\": \"创建OpenAPI文档并使用SwaggerUI可视化你的API\",\n  \"plugin.name\": \"文档\"\n};\nexport {\n  zhHans as default\n};\n//# sourceMappingURL=zh-Hans-XLMwjASk.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AAAA,EACb,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,qCAAqC;AAAA,EACrC,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,8DAA8D;AAAA,EAC9D,+CAA+C;AAAA,EAC/C,gEAAgE;AAAA,EAChE,kCAAkC;AAAA,EAClC,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}