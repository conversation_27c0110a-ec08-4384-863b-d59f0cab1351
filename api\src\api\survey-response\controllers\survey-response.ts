"use strict";

/**
 * survey-response controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
  "api::survey-response.survey-response",
  ({ strapi }) => ({
    async find(ctx) {
      try {
        const user = ctx.state.user;
        const isAdmin = user?.role?.type === "admin";

        let entities;
        if (isAdmin) {
          // Admin puede ver todas las respuestas
          entities = await strapi.entityService.findMany(
            "api::survey-response.survey-response",
            {
              populate: ["survey", "respondent"],
            }
          );
        } else {
          // Usuario normal solo ve sus propias respuestas
          entities = await strapi.db
            .query("api::survey-response.survey-response")
            .findMany({
              where: { respondent: user.id },
              populate: ["survey", "respondent"],
            });
        }

        const sanitizedResults = await this.sanitizeOutput(entities, ctx);
        return this.transformResponse(sanitizedResults);
      } catch (error) {
        return ctx.badRequest(`Error: ${error.message}`);
      }
    },

    async findOne(ctx) {
      try {
        const user = ctx.state.user;
        const isAdmin = user?.role?.type === "admin";
        const { id } = ctx.params;

        const entity = await strapi.db
          .query("api::survey-response.survey-response")
          .findOne({
            where: { id },
            populate: ["survey", "respondent"],
          });

        if (!entity) {
          return ctx.notFound("Survey response not found");
        }

        // Verificar permisos - solo admin o el propio usuario pueden ver la respuesta
        if (!isAdmin && entity.respondent?.id !== user.id) {
          return ctx.forbidden("No tienes permiso para ver esta respuesta");
        }

        const sanitizedResults = await this.sanitizeOutput(entity, ctx);
        return this.transformResponse(sanitizedResults);
      } catch (error) {
        return ctx.badRequest(`Error: ${error.message}`);
      }
    },

    async findBySurvey(ctx) {
      try {
        const { id } = ctx.params;
        const user = ctx.state.user;
        const isAdmin = user?.role?.type === "admin";

        if (!id) {
          return ctx.badRequest("Survey ID is required");
        }

        // Verificar que la encuesta existe
        const survey = await strapi.db.query("api::survey.survey").findOne({
          where: { id },
        });

        if (!survey) {
          return ctx.notFound("Survey not found");
        }

        let responses;
        if (isAdmin) {
          // Admin ve todas las respuestas de la encuesta
          responses = await strapi.db
            .query("api::survey-response.survey-response")
            .findMany({
              where: { survey: id },
              populate: ["survey", "respondent"],
            });
        } else {
          // Usuario normal solo ve sus propias respuestas de la encuesta
          responses = await strapi.db
            .query("api::survey-response.survey-response")
            .findMany({
              where: {
                survey: id,
                respondent: user.id,
              },
              populate: ["survey", "respondent"],
            });
        }

        const sanitizedResults = await this.sanitizeOutput(responses, ctx);
        return this.transformResponse(sanitizedResults);
      } catch (error) {
        return ctx.badRequest(`Error: ${error.message}`);
      }
    },

    async create(ctx) {
      try {
        const user = ctx.state.user;
        if (!user) {
          return ctx.unauthorized(
            "Debe estar autenticado para responder encuestas"
          );
        }

        const { data } = ctx.request.body;
        const surveyId = data.survey;

        // Verificar que la encuesta existe y está activa
        const survey = await strapi.db.query("api::survey.survey").findOne({
          where: { id: surveyId, isActive: true },
        });

        if (!survey) {
          return ctx.badRequest("La encuesta no existe o no está activa");
        }

        // Verificar si el usuario ya respondió esta encuesta
        const existingResponse = await strapi.db
          .query("api::survey-response.survey-response")
          .findOne({
            where: {
              survey: surveyId,
              respondent: user.id,
            },
          });

        if (existingResponse) {
          return ctx.badRequest("Ya has respondido esta encuesta");
        }

        // Crear la respuesta
        const response = await strapi.entityService.create(
          "api::survey-response.survey-response",
          {
            data: {
              ...data,
              respondent: user.id,
              publishedAt: new Date(),
            },
          }
        );

        return this.transformResponse(response);
      } catch (error) {
        return ctx.badRequest(`Error al crear la respuesta: ${error.message}`);
      }
    },
  })
);
