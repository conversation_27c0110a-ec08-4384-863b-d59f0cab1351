import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/vi-ClWGrFm9.mjs
var vi = {
  "components.Row.open": "Mở",
  "components.Row.regenerate": "Sinh lại",
  "containers.HomePage.Block.title": "Các <PERSON>",
  "containers.HomePage.Button.update": "Cập nhật",
  "containers.HomePage.PluginHeader.title": "Tài liệu — Cài đặt",
  "containers.HomePage.PopUpWarning.confirm": "Tôi hiểu",
  "containers.HomePage.PopUpWarning.message": "Bạn có chắc là muốn xóa phiên bản này không?",
  "containers.HomePage.copied": "Chuỗi khoá đã được sao chép vào bộ nhớ tạm",
  "containers.HomePage.form.jwtToken": "Lấy lại chuỗi khóa JWT của bạn",
  "containers.HomePage.form.jwtToken.description": "Sao chép chuỗi khóa và sử dụng nó trong swagger để truy vấn",
  "containers.HomePage.form.password": "Mật khẩu",
  "containers.HomePage.form.password.inputDescription": "Cài đặt mật khẩu để truy cập tài liệu",
  "containers.HomePage.form.restrictedAccess": "Truy cập bị giới hạn",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Làm cho điểm truy cập tài liệu thành riêng tư. Mặc định, truy cập là công khai.",
  "containers.HomePage.form.showGeneratedFiles": "Hiển thị tập tin đã sinh ra",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Hữu ích khi bạn muốn ghi đè lên tài liệu đã sinh ra. \nPlugin này sẽ tạo ra các tập tin phân chia bởi cấu hình và plugin. \nBằng việc kích hoạt tuỳ chọn này, sẽ dễ dàng hơn cho bạn tùy chỉnh tài liệu của bạn.",
  "error.deleteDoc.versionMissing": "Phiên bản bạn đang cố xóa không tồn tại.",
  "error.noVersion": "Bắt buộc phải có một phiên bản",
  "error.regenerateDoc": "Một lỗi đã xảy ra trong khi sinh ra tài liệu",
  "error.regenerateDoc.versionMissing": "Phiên bản bạn đang cố sinh ra không tồn tại",
  "notification.update.success": "Các cài đặt đã được cập nhật thành công",
  "plugin.name": "Tài liệu"
};
export {
  vi as default
};
//# sourceMappingURL=vi-ClWGrFm9-DMCPJJFS.js.map
