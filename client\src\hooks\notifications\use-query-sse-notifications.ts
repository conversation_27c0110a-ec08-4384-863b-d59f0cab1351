import { useCallback, useEffect, useRef, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthContext } from "@app/context/auth/AuthContext";
import { API_URL } from "@app/config/api";
import { message, Modal } from "antd";

export interface Notification {
  id: number | string;
  title: string;
  message?: string;
  description?: string;
  type: string;
  read: boolean;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

interface NotificationsResponse {
  data: Notification[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

const NOTIFICATIONS_QUERY_KEY = "notifications";
const RETRY_INTERVAL = 5000; // 5 segundos entre intentos de reconexión
const MAX_RETRIES = 3; // Número máximo de intentos de reconexión
const STALE_TIME = 60000; // 60 segundos
const REFRESH_INTERVAL = 600000; // 10 minutos

/**
 * Hook personalizado que combina React Query con SSE para gestionar notificaciones
 * @returns Objeto con notificaciones y funciones para gestionarlas
 */
export const useQuerySSENotifications = () => {
  const { user, token, isAuthenticated } = useAuthContext();
  const queryClient = useQueryClient();
  const eventSourceRef = useRef<EventSource | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<
    "CONNECTING" | "OPEN" | "CLOSED" | "ERROR"
  >("CLOSED");
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const retryCountRef = useRef(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastConnectionAttemptRef = useRef<number>(0);
  const connectionAttemptsInLastMinute = useRef<number[]>([]);

  // Consulta para obtener notificaciones
  const {
    data: notificationsData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery<NotificationsResponse>({
    queryKey: [NOTIFICATIONS_QUERY_KEY, user?.id],
    queryFn: async () => {
      if (!user || !token) {
        throw new Error("Usuario no autenticado");
      }

      try {
        const response = await fetch(
          `${API_URL}/api/notifications/user/${user.id}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          },
        );

        if (!response.ok) {
          throw new Error(`Error al cargar notificaciones: ${response.status}`);
        }

        return await response.json();
      } catch (err) {
        console.error("Error al cargar notificaciones:", err);
        throw err;
      }
    },
    enabled: !!user && !!token && isAuthenticated,
    staleTime: STALE_TIME,
    refetchInterval: REFRESH_INTERVAL,
    refetchOnWindowFocus: false,
  });

  // Mutación para marcar una notificación como leída
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: number | string) => {
      if (!user || !token) {
        throw new Error("Usuario no autenticado");
      }

      const response = await fetch(
        `${API_URL}/api/notifications/${notificationId}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ data: { read: true } }),
        },
      );

      if (!response.ok) {
        throw new Error(
          `Error al marcar notificación como leída: ${response.status}`,
        );
      }

      return await response.json();
    },
    onSuccess: (data) => {
      // Actualizar la caché de notificaciones
      queryClient.setQueryData<NotificationsResponse | undefined>(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData) => {
          if (!oldData) return undefined;

          return {
            ...oldData,
            data: oldData.data.map((notification) =>
              notification.id === data.data.id
                ? { ...notification, read: true }
                : notification,
            ),
          };
        },
      );

      message.success("Notificación marcada como leída");
    },
    onError: (error) => {
      console.error("Error al marcar notificación como leída:", error);
      message.error("Error al marcar notificación como leída");
    },
  });

  // Mutación para marcar todas las notificaciones como leídas
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      if (!user || !token) {
        throw new Error("Usuario no autenticado");
      }

      const response = await fetch(
        `${API_URL}/api/notifications/mark-all-read/${user.id}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      );

      if (!response.ok) {
        throw new Error(
          `Error al marcar todas las notificaciones como leídas: ${response.status}`,
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      // Actualizar la caché de notificaciones
      queryClient.setQueryData<NotificationsResponse | undefined>(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData) => {
          if (!oldData) return undefined;

          return {
            ...oldData,
            data: oldData.data.map((notification) => ({
              ...notification,
              read: true,
            })),
          };
        },
      );

      message.success("Todas las notificaciones marcadas como leídas");
    },
    onError: (error) => {
      console.error(
        "Error al marcar todas las notificaciones como leídas:",
        error,
      );
      message.error("Error al marcar todas las notificaciones como leídas");
    },
  });

  // Mutación para eliminar una notificación
  const deleteNotificationMutation = useMutation({
    mutationFn: async (notificationId: number | string) => {
      if (!user || !token) {
        throw new Error("Usuario no autenticado");
      }

      const response = await fetch(
        `${API_URL}/api/notifications/${notificationId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Error al eliminar notificación: ${response.status}`);
      }

      return notificationId;
    },
    onSuccess: (notificationId) => {
      // Actualizar la caché de notificaciones
      queryClient.setQueryData<NotificationsResponse | undefined>(
        [NOTIFICATIONS_QUERY_KEY, user?.id],
        (oldData) => {
          if (!oldData) return undefined;

          return {
            ...oldData,
            data: oldData.data.filter(
              (notification) => notification.id !== notificationId,
            ),
          };
        },
      );

      message.success("Notificación eliminada");
    },
    onError: (error) => {
      console.error("Error al eliminar notificación:", error);
      message.error("Error al eliminar notificación");
    },
  });

  // Mostrar el detalle de la notificación
  const showNotificationDetail = useCallback(
    (notification: Notification) => {
      Modal.info({
        title: notification.title,
        content:
          notification.message || notification.description || "Sin descripción",
        onOk() {
          // Si la notificación no está leída, marcarla como leída
          if (!notification.read) {
            markAsReadMutation.mutate(notification.id);
          }
        },
      });
    },
    [markAsReadMutation],
  );

  // Conectar a SSE
  const connectSSE = useCallback(() => {
    if (!user || !isAuthenticated) {
      return;
    }

    // Prevenir bucle de reconexión rápida
    const now = Date.now();
    const timeSinceLastAttempt = now - lastConnectionAttemptRef.current;

    // Limpiar intentos antiguos (más de 1 minuto)
    connectionAttemptsInLastMinute.current =
      connectionAttemptsInLastMinute.current.filter(
        (attempt) => now - attempt < 60000,
      );

    // Si hay más de 10 intentos en el último minuto, esperar
    if (connectionAttemptsInLastMinute.current.length >= 10) {
      console.log(
        "🚨 BUCLE DETECTADO: Demasiados intentos de reconexión. Esperando 30 segundos...",
      );
      setTimeout(() => {
        connectionAttemptsInLastMinute.current = [];
        connectSSE();
      }, 30000);
      return;
    }

    // Si el último intento fue hace menos de 2 segundos, esperar
    if (timeSinceLastAttempt < 2000) {
      console.log("⏳ Esperando antes de reconectar...");
      setTimeout(connectSSE, 2000 - timeSinceLastAttempt);
      return;
    }

    lastConnectionAttemptRef.current = now;
    connectionAttemptsInLastMinute.current.push(now);

    // Cerrar conexión existente si hay alguna
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    try {
      setConnectionStatus("CONNECTING");
      setConnectionError(null);

      // Crear URL con token para autenticación
      const url = `${API_URL}/api/notifications/sse/${user.id}?token=${token}`;

      console.log(`🔗 Intentando conectar SSE a: ${url}`);

      // Crear nueva conexión SSE
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      // Manejar eventos
      eventSource.onopen = () => {
        setConnectionStatus("OPEN");
        setConnectionError(null);
        retryCountRef.current = 0; // Reiniciar contador de intentos

        // Limpiar timeout de reintento si existe
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
          retryTimeoutRef.current = null;
        }
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Si es un mensaje de conexión, no hacer nada más
          if (data.type === "connection") {
            return;
          }

          // Si es una notificación, actualizar la caché
          if (data.type === "notification") {
            // Actualizar la caché de notificaciones
            queryClient.setQueryData<NotificationsResponse | undefined>(
              [NOTIFICATIONS_QUERY_KEY, user.id],
              (oldData) => {
                if (!oldData) {
                  // Si no hay datos previos, crear una nueva estructura
                  return {
                    data: [data.notification],
                    meta: {
                      pagination: {
                        page: 1,
                        pageSize: 10,
                        pageCount: 1,
                        total: 1,
                      },
                    },
                  };
                }

                // Verificar si la notificación ya existe
                const exists = oldData.data.some(
                  (n) => n.id === data.notification.id,
                );

                if (exists) {
                  // Actualizar la notificación existente
                  return {
                    ...oldData,
                    data: oldData.data.map((n) =>
                      n.id === data.notification.id ? data.notification : n,
                    ),
                  };
                } else {
                  // Agregar la nueva notificación al principio
                  return {
                    ...oldData,
                    data: [data.notification, ...oldData.data],
                  };
                }
              },
            );

            // Mostrar mensaje de notificación
            message.info({
              content: data.notification.title,
              duration: 4,
              onClick: () => showNotificationDetail(data.notification),
            });
          }
        } catch (error) {
          console.error("Error al procesar mensaje SSE:", error);
        }
      };

      eventSource.onerror = (error) => {
        console.error("❌ Error en conexión SSE:", error);
        console.error("❌ EventSource readyState:", eventSource.readyState);
        console.error("❌ URL que falló:", url);
        setConnectionStatus("ERROR");
        setConnectionError(
          "Error en la conexión de notificaciones en tiempo real",
        );

        // Cerrar la conexión actual
        eventSource.close();
        eventSourceRef.current = null;

        // Intentar reconectar después de un tiempo si no excedimos el número máximo de intentos
        if (retryCountRef.current < MAX_RETRIES) {
          retryCountRef.current++;

          retryTimeoutRef.current = setTimeout(() => {
            connectSSE();
          }, RETRY_INTERVAL);
        } else {
          setConnectionError(
            "No se pudo establecer la conexión después de varios intentos. Intente recargar la página.",
          );
        }
      };
    } catch (error) {
      console.error("Error al configurar conexión SSE:", error);
      setConnectionStatus("ERROR");
      setConnectionError(
        `Error al configurar conexión SSE: ${error instanceof Error ? error.message : "Error desconocido"}`,
      );
    }
  }, [user, token, isAuthenticated, queryClient, showNotificationDetail]);

  // Desconectar SSE
  const disconnectSSE = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setConnectionStatus("CLOSED");
    }

    // Limpiar timeout de reintento si existe
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Efecto para conectar/desconectar SSE
  useEffect(() => {
    if (user && isAuthenticated) {
      connectSSE();

      // Configurar intervalo de actualización periódica
      refreshIntervalRef.current = setInterval(() => {
        refetch();
        refetch();
      }, REFRESH_INTERVAL); // 10 minutos
    } else {
      disconnectSSE();

      // Limpiar intervalo de actualización
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }

    // Limpiar al desmontar
    return () => {
      disconnectSSE();

      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [user, isAuthenticated, connectSSE, disconnectSSE, queryClient, refetch]);

  // Extraer notificaciones de la respuesta
  const notifications = notificationsData?.data || [];

  // Contar notificaciones no leídas
  const unreadCount = notifications.filter((n) => !n.read).length;

  // Determinar si estamos conectados
  const isConnected = connectionStatus === "OPEN" || notifications.length > 0;

  return {
    notifications,
    unreadCount,
    isConnected,
    connectionStatus,
    connectionError,
    isLoading,
    isError,
    error,
    markAsRead: (id: number | string) => markAsReadMutation.mutate(id),
    markAllAsRead: () => markAllAsReadMutation.mutate(),
    deleteNotification: (id: number | string) =>
      deleteNotificationMutation.mutate(id),
    showNotificationDetail,
    loadNotifications: refetch,
    connect: connectSSE,
    disconnect: disconnectSSE,
  };
};

export default useQuerySSENotifications;
