import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/uk-Cpmsxyku.mjs
var uk = {
  "components.Row.open": "Відкрити",
  "components.Row.regenerate": "Згенерувати",
  "containers.HomePage.Block.title": "Версії",
  "containers.HomePage.Button.update": "Оновити",
  "containers.HomePage.PluginHeader.title": "Документація — Налаштування",
  "containers.HomePage.PopUpWarning.confirm": "Я усвідомлюю",
  "containers.HomePage.PopUpWarning.message": "Ви впевнені, що хочете видалити цю версію?",
  "containers.HomePage.copied": "Токен скопійовано у буфер обміну",
  "containers.HomePage.form.jwtToken": "Отримайте ваш JWT токен",
  "containers.HomePage.form.jwtToken.description": "Скопіюйте цей токен та застосуйте його в swagger, щоб відправляти запити",
  "containers.HomePage.form.password": "Пароль",
  "containers.HomePage.form.password.inputDescription": "Встановіть пароль доступу до документації",
  "containers.HomePage.form.restrictedAccess": "Обмежений доступ",
  "containers.HomePage.form.restrictedAccess.inputDescription": "Зробіть документацію приватною. За замовченням, документація загальнодоступна",
  "containers.HomePage.form.showGeneratedFiles": "Показати згенеровані файли",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "Корисно, якщо ви хочете переписати згенеровані документи \nПлаґін згенерує файли окремо для моделі та плаґіну. \nУвімкнувши цю опцію, буде легше налаштувати вашу документацію",
  "error.deleteDoc.versionMissing": "Версія, яку ви намагаєтесь видалити не існує.",
  "error.noVersion": "Необхідна версія",
  "error.regenerateDoc": "Під час генерування документації сталася помилка",
  "error.regenerateDoc.versionMissing": "Версія, яку ви намагаєтесь згенерувати не існує.",
  "notification.update.success": "Налаштування оновлено"
};
export {
  uk as default
};
//# sourceMappingURL=uk-Cpmsxyku-MT4MPS3Z.js.map
