export interface BusinessMedia {
  id: number;
  url: string;
  name: string;
}

export interface BusinessUser {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  address: string;
  phone: string;
}

export interface BusinessPopulated {
  id: number;
  name: string;
  category?: string;
  description?: string;
  phone?: string;
  email?: string;
  address?: string;
  schedule?: string;
  website?: string;
  facebook?: string;
  instagram?: string;
  featured?: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId?: string;
  logo?: BusinessMedia | null;
  photoOne?: BusinessMedia | null;
  photoTwo?: BusinessMedia | null;
  photoThree?: BusinessMedia | null;
  photoFour?: BusinessMedia | null;
  businessUser?: BusinessUser | null;
}
