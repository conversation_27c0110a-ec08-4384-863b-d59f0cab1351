// ✅ emailUtils.ts actualizado con EJS y sin createReadStream

import ejs from "ejs";
import { join } from "path";

interface resevationEmailUtils {
  from?: string;
  fromName?: string;
  replyTo?: string;
  baseUrl?: string;
}

export const resevationEmailUtils = {
  getConfig(): resevationEmailUtils {
    return {
      from: process.env.SMTP_DEFAULT_FROM,
      fromName: process.env.SMTP_FROM_NAME || "Administración CCPALCAN",
      replyTo: process.env.SMTP_REPLY_TO || process.env.SMTP_DEFAULT_FROM,
      baseUrl: process.env.FRONTEND_URL || "https://ccpalcan.com",
    };
  },

  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.getConfig();
    if (!config.from) errors.push("Falta SMTP_DEFAULT_FROM");
    return { isValid: errors.length === 0, errors };
  },

  getTemplatePath(type: string): string {
    const map: Record<string, string> = {
      created: "reservation-created.html",
      approved: "reservation-approved.html",
      rejected: "reservation-rejected.html",
    };
    return join(process.cwd(), "config", "email-templates", map[type]);
  },

  async processTemplateEJS(templatePath: string, data: any): Promise<string> {
    return new Promise((resolve, reject) => {
      ejs.renderFile(templatePath, data, { async: true }, (err, str) => {
        if (err) {
          console.error("Error procesando plantilla EJS:", err);
          reject(err);
        } else {
          resolve(str);
        }
      });
    });
  },

  async sendEmail(
    type: "created" | "approved" | "rejected",
    data: any,
    recipientEmail: string,
    maxRetries = 3
  ): Promise<void> {
    const { isValid, errors } = this.validateConfig();
    if (!isValid) {
      console.error("Configuración de email inválida:", errors.join(", "));
      if (process.env.NODE_ENV === "development") {
        console.log("[DEV] Simulando envío de email:", {
          type,
          to: recipientEmail,
          data,
        });
        return;
      }
      throw new Error(`Configuración SMTP incompleta: ${errors.join(", ")}`);
    }

    const config = this.getConfig();
    const subjectMap = {
      created: "Nueva solicitud de reservación - CCPALCAN",
      approved: "Reservación aprobada - CCPALCAN",
      rejected: "Reservación rechazada - CCPALCAN",
    };

    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const templatePath = this.getTemplatePath(type);
        const htmlContent = await this.processTemplateEJS(templatePath, {
          ...data,
          URL: config.baseUrl,
        });

        await strapi.plugins.email.services.email.send({
          to: recipientEmail,
          from: {
            email: config.from!,
            name: config.fromName,
          },
          replyTo: config.replyTo,
          subject: subjectMap[type],
          html: htmlContent,
          text: htmlContent.replace(/<[^>]*>/g, ""),
        });

        console.log(
          `Email de ${type} enviado exitosamente a ${recipientEmail}`
        );
        return;
      } catch (err) {
        lastError = err as Error;
        console.error(
          `Error en envío de email (intento ${attempt}/${maxRetries}):`,
          err
        );
        if (attempt < maxRetries) {
          await new Promise((res) =>
            setTimeout(res, Math.min(1000 * 2 ** (attempt - 1), 10000))
          );
        }
      }
    }

    console.error("Todos los intentos fallaron:", lastError);
    const fallbackHtml = this.generateFallbackEmail(type, data);
    await strapi.plugins.email.services.email.send({
      to: recipientEmail,
      from: {
        email: config.from!,
        name: config.fromName,
      },
      subject: subjectMap[type],
      html: fallbackHtml,
      text: fallbackHtml.replace(/<[^>]*>/g, ""),
    });
    console.log(`Fallback email enviado a ${recipientEmail}`);
  },

  generateFallbackEmail(
    type: "created" | "approved" | "rejected",
    data: any
  ): string {
    return `
      <h1>Notificación de Reservación</h1>
      <p>Se ha ${
        type === "created"
          ? "creado"
          : type === "approved"
          ? "aprobado"
          : "rechazado"
      } una reservación.</p>
      <p><strong>Área:</strong> ${
        data.area || data.socialArea || data.reservation?.area || ""
      }</p>
      <p><strong>Fecha:</strong> ${
        data.date || data.reservation?.date || ""
      }</p>
      <p><strong>Horario:</strong> ${
        data.startTime || data.reservation?.startTime || ""
      } - ${data.endTime || data.reservation?.endTime || ""}</p>
      ${
        type === "rejected"
          ? `<p><strong>Motivo de rechazo:</strong> ${
              data.rejectionReason || data.reservation?.rejectionReason || ""
            }</p>`
          : ""
      }
      <hr>
      <p><small>Email de respaldo generado automáticamente.</small></p>
    `;
  },
};
