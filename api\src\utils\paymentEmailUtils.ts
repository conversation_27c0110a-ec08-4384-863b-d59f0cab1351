// ✅ paymentUtils.ts usando EJS para plantillas dinámicas de correos de pago

import ejs from "ejs";
import { join } from "path";

interface EmailConfig {
  from?: string;
  fromName?: string;
  replyTo?: string;
  baseUrl?: string;
}

export const paymentEmailUtils = {
  getConfig(): EmailConfig {
    return {
      from: process.env.SMTP_DEFAULT_FROM,
      fromName: process.env.SMTP_FROM_NAME || "Administración CCPALCAN",
      replyTo: process.env.SMTP_REPLY_TO || process.env.SMTP_DEFAULT_FROM,
      baseUrl: process.env.FRONTEND_URL || "https://ccpalcan.com",
    };
  },

  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.getConfig();
    if (!config.from) errors.push("Falta SMTP_DEFAULT_FROM");
    return { isValid: errors.length === 0, errors };
  },

  getTemplatePath(type: string): string {
    const map: Record<string, string> = {
      created: "payment-created.html",
      approved: "payment-approved.html",
      rejected: "payment-rejected.html",
    };
    return join(process.cwd(), "config", "email-templates", map[type]);
  },

  async processTemplateEJS(templatePath: string, data: any): Promise<string> {
    return new Promise((resolve, reject) => {
      ejs.renderFile(templatePath, data, { async: true }, (err, str) => {
        if (err) {
          console.error("Error procesando plantilla EJS:", err);
          reject(err);
        } else {
          resolve(str);
        }
      });
    });
  },

  async sendPaymentEmail(
    type: "created" | "approved" | "rejected",
    data: any,
    recipientEmail: string,
    maxRetries = 3
  ): Promise<void> {
    const { isValid, errors } = this.validateConfig();
    if (!isValid) {
      console.error("Configuración de email inválida:", errors.join(", "));
      if (process.env.NODE_ENV === "development") {
        console.log("[DEV] Simulando envío de email:", {
          type,
          to: recipientEmail,
          data,
        });
        return;
      }
      throw new Error(`SMTP incompleto: ${errors.join(", ")}`);
    }

    const config = this.getConfig();
    const subjectMap = {
      created: "Nuevo registro de pago - CCPALCAN",
      approved: "Pago aprobado - CCPALCAN",
      rejected: "Pago rechazado - CCPALCAN",
    };

    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const templatePath = this.getTemplatePath(type);
        const htmlContent = await this.processTemplateEJS(templatePath, {
          ...data,
          URL: config.baseUrl,
        });

        await strapi.plugins.email.services.email.send({
          to: recipientEmail,
          from: {
            email: config.from!,
            name: config.fromName,
          },
          replyTo: config.replyTo,
          subject: subjectMap[type],
          html: htmlContent,
          text: htmlContent.replace(/<[^>]*>/g, ""),
        });

        console.log(`Email de ${type} de pago enviado a ${recipientEmail}`);
        return;
      } catch (err) {
        lastError = err as Error;
        console.error(
          `Error en email de pago (intento ${attempt}/${maxRetries}):`,
          err
        );
        if (attempt < maxRetries) {
          await new Promise((res) =>
            setTimeout(res, Math.min(1000 * 2 ** (attempt - 1), 10000))
          );
        }
      }
    }

    console.error("Fallaron todos los intentos de email de pago:", lastError);
    const fallbackHtml = this.generateFallbackEmail(type, data);
    await strapi.plugins.email.services.email.send({
      to: recipientEmail,
      from: {
        email: config.from!,
        name: config.fromName,
      },
      subject: subjectMap[type],
      html: fallbackHtml,
      text: fallbackHtml.replace(/<[^>]*>/g, ""),
    });
    console.log(`Fallback email de pago enviado a ${recipientEmail}`);
  },

  generateFallbackEmail(
    type: "created" | "approved" | "rejected",
    data: any
  ): string {
    return `
      <h1>Notificación de Pago</h1>
      <p>Tu pago ha sido ${
        type === "created"
          ? "registrado"
          : type === "approved"
          ? "aprobado"
          : "rechazado"
      }.</p>
      <p><strong>Monto:</strong> ${
        data.amount || data.payment?.amount || ""
      }</p>
      <p><strong>Periodo:</strong> ${
        data.period || data.payment?.period || ""
      }</p>
      ${
        type === "rejected"
          ? `<p><strong>Motivo de rechazo:</strong> ${
              data.rejectionReason || ""
            }</p>`
          : ""
      }
      <hr>
      <p><small>Este email fue generado automáticamente como respaldo.</small></p>
    `;
  },
};

export default paymentEmailUtils;
