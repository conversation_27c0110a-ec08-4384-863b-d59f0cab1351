export interface Guest {
  name: string;
  lastName: string;
}

export interface Owner {
  id: number;
  firstName: string;
  lastName: string;
  address: string;
}

export interface ReservationAttributes {
  documentId: string;
  date: string;
  startTime: string;
  endTime: string;
  socialArea: string;
  eventType: string;
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string | null;
  guests?: Guest[];
  owner: Owner;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
}

export interface SanitizedReservation {
  id: number;
  attributes: ReservationAttributes;
}
