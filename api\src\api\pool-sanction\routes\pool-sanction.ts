export default {
  routes: [
    {
      method: "GET",
      path: "/pool-sanctions",
      handler: "pool-sanction.find",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/pool-sanctions/active",
      handler: "pool-sanction.findActive",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/pool-sanctions",
      handler: "pool-sanction.create",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/pool-sanctions/:id/complete",
      handler: "pool-sanction.complete",
      config: {
        policies: ["global::isAuthenticated"],
        middlewares: [],
      },
    },
  ],
};
