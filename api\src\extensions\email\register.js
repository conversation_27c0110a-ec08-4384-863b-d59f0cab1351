'use strict';

const fs = require('fs');
const path = require('path');

module.exports = ({ strapi }) => {
  // Verificar si el plugin de email está instalado
  if (!strapi.plugin('email')) {
    console.warn('El plugin de email no está instalado. Por favor, instálalo ejecutando: npm install @strapi/plugin-email');
    return;
  }

  // Registrar las plantillas personalizadas
  const registerTemplates = async () => {
    try {
      const emailService = strapi.plugin('email').service('email');
      
      // Verificar si el servicio de email tiene la propiedad templates
      if (!emailService.templates) {
        emailService.templates = {};
      }
      
      // Leer la configuración de las plantillas
      const templatesConfig = require('./config/settings').templates;
      
      // Registrar cada plantilla
      for (const [templateName, templateConfig] of Object.entries(templatesConfig)) {
        if (templateConfig.html) {
          console.log(`Registrando plantilla de correo: ${templateName}`);
          
          // Registrar la plantilla en el servicio de email
          emailService.templates[templateName] = (data) => ({
            subject: templateConfig.subject,
            text: templateConfig.text,
            html: templateConfig.html,
            ...data,
          });
          
          console.log(`Plantilla ${templateName} registrada correctamente`);
        } else {
          console.warn(`No se pudo cargar la plantilla ${templateName}: Contenido HTML no encontrado`);
        }
      }
      
      console.log('Todas las plantillas de correo han sido registradas correctamente');
    } catch (error) {
      console.error('Error al registrar las plantillas de correo:', error);
    }
  };

  // Ejecutar el registro de plantillas cuando el servidor esté listo
  strapi.server.on('listening', () => {
    registerTemplates();
  });

  // Eliminamos la parte de GraphQL que podría estar causando problemas
  // ya que no es necesaria para las plantillas de correo
};
