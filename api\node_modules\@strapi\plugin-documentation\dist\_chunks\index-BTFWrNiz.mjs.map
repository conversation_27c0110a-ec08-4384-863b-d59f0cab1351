{"version": 3, "file": "index-BTFWrNiz.mjs", "sources": ["../../server/src/public/index.html?raw"], "sourcesContent": ["export default \"<!-- HTML for static distribution bundle build --><!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n  <head>\\n    <meta charset=\\\"UTF-8\\\" />\\n    <title>Swagger UI</title>\\n    <link\\n      rel=\\\"stylesheet\\\"\\n      type=\\\"text/css\\\"\\n      href=\\\"<%=backendUrl%>/plugins/documentation/swagger-ui.css\\\"\\n    />\\n    <link\\n      rel=\\\"icon\\\"\\n      type=\\\"image/png\\\"\\n      href=\\\"<%=backendUrl%>/plugins/documentation/favicon-32x32.png\\\"\\n      sizes=\\\"32x32\\\"\\n    />\\n    <link\\n      rel=\\\"icon\\\"\\n      type=\\\"image/png\\\"\\n      href=\\\"<%=backendUrl%>/plugins/documentation/favicon-16x16.png\\\"\\n      sizes=\\\"16x16\\\"\\n    />\\n    <style>\\n      html {\\n        box-sizing: border-box;\\n        overflow: -moz-scrollbars-vertical;\\n        overflow-y: scroll;\\n      }\\n\\n      *,\\n      *:before,\\n      *:after {\\n        box-sizing: inherit;\\n      }\\n\\n      body {\\n        margin: 0;\\n        background: #fafafa;\\n      }\\n    </style>\\n  </head>\\n\\n  <body>\\n    <div id=\\\"swagger-ui\\\"></div>\\n    <script class=\\\"custom-swagger-ui\\\">\\n      window.onload = function() {\\n        const ui = SwaggerUIBundle({\\n          url: \\\"https://petstore.swagger.io/v2/swagger.json\\\",\\n          spec: <%=spec%>,\\n          dom_id: '#swagger-ui',\\n          docExpansion: \\\"none\\\",\\n          deepLinking: true,\\n          presets: [\\n            SwaggerUIBundle.presets.apis,\\n            SwaggerUIStandalonePreset,\\n          ],\\n          plugins: [\\n            SwaggerUIBundle.plugins.DownloadUrl,\\n          ],\\n          layout: \\\"StandaloneLayout\\\",\\n        });\\n\\n        window.ui = ui;\\n      }\\n    </script>\\n\\n    <script src=\\\"<%=backendUrl%>/plugins/documentation/swagger-ui-bundle.js\\\"></script>\\n    <script src=\\\"<%=backendUrl%>/plugins/documentation/swagger-ui-standalone-preset.js\\\"></script>\\n  </body>\\n</html>\\n\""], "names": [], "mappings": "AAAA,MAAe,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;"}