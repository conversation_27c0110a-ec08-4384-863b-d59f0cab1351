import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-documentation/dist/_chunks/zh-Glkg1L2g.mjs
var zh = {
  "coming-soon": "此內容目前正在構建，將在幾周後回歸！",
  "components.Row.open": "開啟",
  "components.Row.regenerate": "重新產生",
  "containers.HomePage.Block.title": "版本",
  "containers.HomePage.Button.update": "更新",
  "containers.HomePage.PluginHeader.title": "文件資料 - 設定",
  "containers.HomePage.PopUpWarning.confirm": "我了解了",
  "containers.HomePage.PopUpWarning.message": "你確定要刪除這個版本嗎？",
  "containers.HomePage.copied": "權杖已經複製到剪貼簿",
  "containers.HomePage.form.jwtToken": "存取你的 jwt 權杖",
  "containers.HomePage.form.jwtToken.description": "複製這個權杖並於 swagger 中使用於發出請求",
  "containers.HomePage.form.password": "密碼",
  "containers.HomePage.form.password.inputDescription": "設定密碼存取權限",
  "containers.HomePage.form.restrictedAccess": "存取受到限制",
  "containers.HomePage.form.restrictedAccess.inputDescription": "將 endpoints 設定為私密的。預設是對外公開的",
  "containers.HomePage.form.showGeneratedFiles": "顯示產生的檔案",
  "containers.HomePage.form.showGeneratedFiles.inputDescription": "用於覆寫產生的文件檔案時。\n擴充套件會依照model及plugin分別產生檔案。\n啟用這個選項將會方便你客製化文件資料",
  "error.deleteDoc.versionMissing": "你想刪除的版本不存在",
  "error.noVersion": "版本是必填欄位",
  "error.regenerateDoc": "重新產生文件資料時發生了錯誤",
  "error.regenerateDoc.versionMissing": "你想產生的版本不存在",
  "notification.delete.success": "文件已刪除",
  "notification.generate.success": "文件已重新產生",
  "notification.update.success": "更新設定成功",
  "pages.PluginPage.Button.open": "開啟說明文件",
  "pages.PluginPage.header.description": "設定說明文件外掛程式",
  "pages.PluginPage.table.generated": "最後產生時間",
  "pages.PluginPage.table.icon.regenerate": "重新產生 {target}",
  "pages.PluginPage.table.icon.show": "開啟 {target}",
  "pages.PluginPage.table.version": "版本",
  "pages.SettingsPage.header.description": "設定說明文件外掛程式",
  "pages.SettingsPage.header.save": "儲存",
  "pages.SettingsPage.toggle.hint": "將說明文件端點設為私人",
  "pages.SettingsPage.toggle.label": "受限存取",
  "plugin.description.long": "建立 OpenAPI 文件，並透過 SWAGGER UI 可視化您的 API。",
  "plugin.description.short": "建立 OpenAPI 文件，並透過 SWAGGER UI 可視化您的 API。",
  "plugin.name": "文件資料"
};
export {
  zh as default
};
//# sourceMappingURL=zh-Glkg1L2g-HYT2TOHG.js.map
